using System;
using System.Collections.Generic;
using System.Linq;
using Amazon.Lambda.Core;
using Common.DocItemModel;
using Convert.Helper;
using Convert.Model;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace Convert.Tests.Helper.Test
{
    public class ProcessorTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly DocumentInfo _documentInfo;

        public ProcessorTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _documentInfo = CreateTestDocumentInfo();
        }

        private DocumentInfo CreateTestDocumentInfo()
        {
            return new DocumentInfo
            {
                Version = "1.0",
                Elements = new List<DocItem>(),
                Annotations = new Annotations
                {
                    Hyperlinks = new Dictionary<string, DocHyperlink>(),
                    Comments = new Dictionary<string, DocComment>()
                },
                NativeRevisions = new Dictionary<string, DocItem>()
            };
        }

        #region ProcessParagraphUnified Tests

        [Fact]
        public void ProcessParagraphUnified_ShouldReturnParagraphElement_WhenValidParagraphProvided()
        {
            // Arrange
            var paragraph = CreateTestParagraph("Test content");
            var paraId = "test-para-1";

            // Act
            var result = Processor.ProcessParagraphUnified(_documentInfo, paragraph, paraId, null);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paraId, result.ElementId);
            Assert.IsType<ParagraphElement>(result);
        }

        [Fact]
        public void ProcessParagraphUnified_ShouldHandleTableCell_WhenIsTableCellTrue()
        {
            // Arrange
            var paragraph = CreateTestParagraph("Cell content");
            var paraId = "table-cell-para-1";

            // Act
            var result = Processor.ProcessParagraphUnified(_documentInfo, paragraph, paraId, null, true);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paraId, result.ElementId);
        }

        [Fact]
        public void ProcessParagraphUnified_ShouldProcessListParagraph_WhenParagraphIsList()
        {
            // Arrange
            var paragraph = CreateTestListParagraph("List item", 1, 0);
            var paraId = "list-para-1";
            var numbering = CreateTestNumbering();

            // Act
            var result = Processor.ProcessParagraphUnified(_documentInfo, paragraph, paraId, numbering);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ElementType);
            Assert.Equal(paraId, result.ElementId);
            Assert.NotNull(result.Properties.ListInfo);
            Assert.Equal(0, result.Properties.ListInfo.Level);
        }

        #endregion

        #region ProcessNormalRun Tests

        [Fact]
        public void ProcessNormalRun_ShouldCreateSegment_WhenValidRunProvided()
        {
            // Arrange
            var run = CreateTestRun("Normal text");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 0;

            // Act
            var result = Processor.ProcessNormalRun(_documentInfo, paraItem, run, startPosition);

            // Assert
            Assert.Equal("Normal text", result);
            Assert.Single(paraItem.Segments);
            Assert.Equal("Normal text", paraItem.Segments[0].SegmentText);
            Assert.Equal(startPosition, paraItem.Segments[0].Start);
        }

        [Fact]
        public void ProcessNormalRun_ShouldHandleEmptyRun()
        {
            // Arrange
            var run = CreateTestRun("");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 10;

            // Act
            var result = Processor.ProcessNormalRun(_documentInfo, paraItem, run, startPosition);

            // Assert
            Assert.Equal("", result);
            Assert.Single(paraItem.Segments);
            Assert.Equal("", paraItem.Segments[0].SegmentText);
        }

        [Fact]
        public void ProcessNormalRun_ShouldHandleRunWithFieldChar()
        {
            // Arrange
            var run = CreateTestRunWithFieldChar("begin");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 0;

            // Act
            var result = Processor.ProcessNormalRun(_documentInfo, paraItem, run, startPosition);

            // Assert
            Assert.NotNull(result);
            Assert.Single(paraItem.Segments);
            Assert.Equal("begin", paraItem.Segments[0].FieldId);
        }

        #endregion

        #region ProcessRevisionRun Tests

        [Fact]
        public void ProcessRevisionRun_ShouldProcessInsertedRun()
        {
            // Arrange
            var insertedRun = CreateTestInsertedRun("Inserted text", "rev-1");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 0;
            var activeCommentRanges = new Dictionary<string, int>();

            // Act
            var result = Processor.ProcessRevisionRun(_documentInfo, paraItem, insertedRun, startPosition, activeCommentRanges);

            // Assert
            Assert.Equal("Inserted text", result);
            Assert.Single(paraItem.Segments);
            Assert.Equal("rev-1", paraItem.Segments[0].ContentRevisionId);
            Assert.Contains("rev-1", _documentInfo.NativeRevisions.Keys);
        }

        [Fact]
        public void ProcessRevisionRun_ShouldProcessDeletedRun()
        {
            // Arrange
            var deletedRun = CreateTestDeletedRun("Deleted text", "rev-2");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 0;
            var activeCommentRanges = new Dictionary<string, int>();

            // Act
            var result = Processor.ProcessRevisionRun(_documentInfo, paraItem, deletedRun, startPosition, activeCommentRanges);

            // Assert
            Assert.Equal("", result);
        }

        #endregion

        #region ProcessHyperLink Tests

        [Fact]
        public void ProcessHyperLink_ShouldCreateHyperlinkSegment()
        {
            // Arrange
            var hyperlink = CreateTestHyperlink("Click here", "link-1");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 0;

            // Act
            var result = Processor.ProcessHyperLink(_documentInfo, paraItem, hyperlink, startPosition, "");

            // Assert
            Assert.Equal("Click here", result);
            Assert.Single(paraItem.Segments);
            Assert.Equal("link-1", paraItem.Segments[0].HyperlinkId);
            Assert.Contains("link-1", _documentInfo.Annotations.Hyperlinks.Keys);
        }

        [Fact]
        public void ProcessHyperLink_ShouldHandleEmptyHyperlinkId()
        {
            // Arrange
            var hyperlink = CreateTestHyperlink("No ID link", "");
            var paraItem = CreateTestParagraphElement();
            var startPosition = 0;

            // Act
            var result = Processor.ProcessHyperLink(_documentInfo, paraItem, hyperlink, startPosition, "");

            // Assert
            Assert.Equal("No ID link", result);
            Assert.Single(paraItem.Segments);
            Assert.Equal("", paraItem.Segments[0].HyperlinkId);
        }

        #endregion

        #region ProcessFieldChar Tests

        [Fact]
        public void ProcessFieldChar_ShouldCreateHyperlinkGroup_WhenHyperlinkFieldDetected()
        {
            // Arrange
            var paraItem = CreateTestParagraphElement();

            // Add field segments
            AddFieldSegmentToParagraph(paraItem, "begin", 0, "");
            AddFieldSegmentToParagraph(paraItem, "", 10, "HYPERLINK \"https://example.com\"");
            AddFieldSegmentToParagraph(paraItem, "", 15, "Link text");
            AddFieldSegmentToParagraph(paraItem, "end", 25, "");

            // Act
            Processor.ProcessFieldChar(_documentInfo, paraItem, "");

            // Assert
            var expectedKey = $"{paraItem.ElementId}-0";
            Assert.Contains(expectedKey, _documentInfo.Annotations.Hyperlinks.Keys);

            var hyperlink = _documentInfo.Annotations.Hyperlinks[expectedKey];
            Assert.Equal("https://example.com", hyperlink.Uri);
            Assert.Equal(4, hyperlink.Targets.Count);

            // Verify all segments' HyperlinkId are set
            Assert.All(paraItem.Segments, s => Assert.Equal(expectedKey, s.HyperlinkId));
        }

        [Fact]
        public void ProcessFieldChar_ShouldHandleMultipleFieldPairs()
        {
            // Arrange
            var paraItem = CreateTestParagraphElement();

            // First field pair
            AddFieldSegmentToParagraph(paraItem, "begin", 0, "");
            AddFieldSegmentToParagraph(paraItem, "", 5, "HYPERLINK \"https://first.com\"");
            AddFieldSegmentToParagraph(paraItem, "end", 10, "");

            // Second field pair
            AddFieldSegmentToParagraph(paraItem, "begin", 15, "");
            AddFieldSegmentToParagraph(paraItem, "", 20, "HYPERLINK \"https://second.com\"");
            AddFieldSegmentToParagraph(paraItem, "end", 25, "");

            // Act
            Processor.ProcessFieldChar(_documentInfo, paraItem, "");

            // Assert
            Assert.Equal(2, _documentInfo.Annotations.Hyperlinks.Count);

            var firstLink = _documentInfo.Annotations.Hyperlinks[$"{paraItem.ElementId}-0"];
            Assert.Equal("https://first.com", firstLink.Uri);

            var secondLink = _documentInfo.Annotations.Hyperlinks[$"{paraItem.ElementId}-1"];
            Assert.Equal("https://second.com", secondLink.Uri);
        }

        [Fact]
        public void ProcessFieldChar_ShouldHandleFieldWithoutHyperlink()
        {
            // Arrange
            var paraItem = CreateTestParagraphElement();

            AddFieldSegmentToParagraph(paraItem, "begin", 0, "");
            AddFieldSegmentToParagraph(paraItem, "", 5, "Some other field");
            AddFieldSegmentToParagraph(paraItem, "end", 10, "");

            // Act
            Processor.ProcessFieldChar(_documentInfo, paraItem, "");

            // Assert
            var expectedKey = $"{paraItem.ElementId}-0";
            Assert.Contains(expectedKey, _documentInfo.Annotations.Hyperlinks.Keys);

            var hyperlink = _documentInfo.Annotations.Hyperlinks[expectedKey];
            Assert.Equal("", hyperlink.Uri);
        }

        [Fact]
        public void ProcessFieldChar_ShouldHandleMismatchedBeginEnd()
        {
            // Arrange
            var paraItem = CreateTestParagraphElement();

            AddFieldSegmentToParagraph(paraItem, "begin", 0, "");
            AddFieldSegmentToParagraph(paraItem, "", 5, "Incomplete field");

            Processor.ProcessFieldChar(_documentInfo, paraItem, "");

            Assert.Empty(_documentInfo.Annotations.Hyperlinks);
        }

        #endregion

        #region ProcessComments Tests

        [Fact]
        public void ProcessCommentStart_ShouldAddToActiveCommentRanges()
        {
            // Arrange
            var paraItem = CreateTestParagraphElement();
            var commentRangeStart = CreateTestCommentRangeStart("comment-1");
            var activeCommentRanges = new Dictionary<string, int>();

            // Act
            Processor.ProcessCommentStart(paraItem, commentRangeStart, activeCommentRanges);

            // Assert
            Assert.Contains("comment-1", activeCommentRanges.Keys);
            Assert.Equal(0, activeCommentRanges["comment-1"]); // Segments.Count = 0
        }

        [Fact]
        public void ProcessCommentEnd_ShouldAddTargetsToComment()
        {
            // Arrange
            var paraItem = CreateTestParagraphElement();
            AddSegmentToParagraph(paraItem, "Test content", 0);
            AddSegmentToParagraph(paraItem, "Test content", 0);


            var commentRangeEnd = CreateTestCommentRangeEnd("comment-1");
            var activeCommentRanges = new Dictionary<string, int> { { "comment-1", 1 } };

            _documentInfo.Annotations.Comments["comment-1"] = new DocComment
            {
                CommentId = "comment-1",
                Targets = new List<string>()
            };

            // Act
            Processor.ProcessCommentEnd(_documentInfo, paraItem, commentRangeEnd, activeCommentRanges);

            // Assert
            var comment = _documentInfo.Annotations.Comments["comment-1"];
            Assert.Single(comment.Targets);
            Assert.Equal(paraItem.Segments[1].SegmentId, comment.Targets[0]);
        }

        #endregion

        #region ProcessTableRow Tests

        [Fact]
        public void ProcessTableRow_ShouldReturnTableRowElement()
        {
            // Arrange
            var tableRow = CreateTestTableRow(2); // 2 cells
            var tableIdx = 0;
            var rowIdx = 1;

            // Act
            var result = Processor.ProcessTableRow(_documentInfo, null, tableRow, tableIdx, rowIdx);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(rowIdx, result.RowId);
            Assert.Equal(2, result.Cells.Count);
        }

        [Fact]
        public void ProcessTableCell_ShouldReturnTableCellElement()
        {
            // Arrange
            var tableCell = CreateTestTableCell("Cell content");
            var tableIdx = 0;
            var rowIdx = 0;
            var cellIdx = 0;

            // Act
            var result = Processor.ProcessTableCell(_documentInfo, null, tableCell, tableIdx, rowIdx, cellIdx);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(cellIdx, result.CellId);
            Assert.Single(result.Elements);
        }

        #endregion

        #region Helper Methods

        private Paragraph CreateTestParagraph(string text)
        {
            var paragraph = new Paragraph();
            var run = new Run();
            run.AppendChild(new Text(text));
            paragraph.AppendChild(run);
            return paragraph;
        }

        private Run CreateTestRun(string text)
        {
            var run = new Run();
            if (!string.IsNullOrEmpty(text))
            {
                run.AppendChild(new Text(text));
            }
            return run;
        }

        private Run CreateTestRunWithFieldChar(string fieldType)
        {
            var run = new Run();
            var fieldChar = new FieldChar();

            switch (fieldType)
            {
                case "begin":
                    fieldChar.FieldCharType = FieldCharValues.Begin;
                    break;
                case "end":
                    fieldChar.FieldCharType = FieldCharValues.End;
                    break;
                case "separate":
                    fieldChar.FieldCharType = FieldCharValues.Separate;
                    break;
            }

            run.AppendChild(fieldChar);
            return run;
        }

        private InsertedRun CreateTestInsertedRun(string text, string revisionId)
        {
            var insertedRun = new InsertedRun { Id = revisionId };
            var run = new Run();
            run.AppendChild(new Text(text));
            insertedRun.AppendChild(run);
            return insertedRun;
        }

        private DeletedRun CreateTestDeletedRun(string text, string revisionId)
        {
            var deletedRun = new DeletedRun { Id = revisionId };
            var deletedText = new DeletedText(text);
            deletedRun.AppendChild(deletedText);
            return deletedRun;
        }

        private Hyperlink CreateTestHyperlink(string text, string id)
        {
            var hyperlink = new Hyperlink { Id = id };
            var run = new Run();
            run.AppendChild(new Text(text));
            hyperlink.AppendChild(run);
            return hyperlink;
        }

        private CommentRangeStart CreateTestCommentRangeStart(string commentId)
        {
            return new CommentRangeStart { Id = commentId };
        }

        private CommentRangeEnd CreateTestCommentRangeEnd(string commentId)
        {
            return new CommentRangeEnd { Id = commentId };
        }

        private TableRow CreateTestTableRow(int cellCount)
        {
            var tableRow = new TableRow();
            for (int i = 0; i < cellCount; i++)
            {
                var cell = CreateTestTableCell($"Cell {i}");
                tableRow.AppendChild(cell);
            }
            return tableRow;
        }

        private TableCell CreateTestTableCell(string content)
        {
            var tableCell = new TableCell();
            var paragraph = CreateTestParagraph(content);
            tableCell.AppendChild(paragraph);
            return tableCell;
        }

        private ParagraphElement CreateTestParagraphElement()
        {
            return new ParagraphElement
            {
                ElementId = "test-para-" + Guid.NewGuid().ToString("N")[..8],
                Segments = new List<Segment>(),
                Properties = new ParagraphProperty()
            };
        }

        private void AddSegmentToParagraph(ParagraphElement paraItem, string text, int position)
        {
            var segment = new Segment
            {
                SegmentId = $"{paraItem.ElementId}-{paraItem.Segments.Count}",
                SegmentText = text,
                Start = position,
                End = position + text.Length,
                Properties = new RunProperty(),
                PropRevisions = new List<PropertyRevision>()
            };
            paraItem.Segments.Add(segment);
        }

        private void AddFieldSegmentToParagraph(ParagraphElement paraItem, string fieldId, int position, string text)
        {
            var segment = new Segment
            {
                SegmentId = $"{paraItem.ElementId}-{paraItem.Segments.Count}",
                SegmentText = text,
                FieldId = fieldId,
                Start = position,
                End = position + text.Length,
                Properties = new RunProperty(),
                PropRevisions = new List<PropertyRevision>()
            };
            paraItem.Segments.Add(segment);
        }

        private Paragraph CreateTestListParagraph(string text, int numId, int level)
        {
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            numPr.AppendChild(new NumberingId { Val = numId });
            numPr.AppendChild(new NumberingLevelReference { Val = level });
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            if (!string.IsNullOrEmpty(text))
            {
                var run = new Run();
                run.AppendChild(new Text(text));
                paragraph.AppendChild(run);
            }

            return paragraph;
        }

        private Numbering CreateTestNumbering()
        {
            var numbering = new Numbering();

            var abstractNum1 = new AbstractNum { AbstractNumberId = 1 };
            var level1 = new Level { LevelIndex = 0, NumberingFormat = new NumberingFormat { Val = NumberFormatValues.Decimal }, PreviousParagraphProperties = new PreviousParagraphProperties(new Indentation { Left = "720" }) };
            abstractNum1.AppendChild(level1);
            numbering.AppendChild(abstractNum1);

            var abstractNum2 = new AbstractNum { AbstractNumberId = 2 };
            var level2 = new Level { LevelIndex = 1, NumberingFormat = new NumberingFormat { Val = NumberFormatValues.Bullet }, PreviousParagraphProperties = new PreviousParagraphProperties(new Indentation { Left = "1440" }) };
            abstractNum2.AppendChild(level2);
            numbering.AppendChild(abstractNum2);

            var numInstance1 = new NumberingInstance { NumberID = 1 };
            numInstance1.AbstractNumId = new AbstractNumId { Val = 1 };
            numbering.AppendChild(numInstance1);

            var numInstance2 = new NumberingInstance { NumberID = 2 };
            numInstance2.AbstractNumId = new AbstractNumId { Val = 2 };
            numbering.AppendChild(numInstance2);

            return numbering;
        }

        #endregion

        #region Integration Tests

        [Fact]
        public void ProcessParagraphElementsUnified_ShouldHandleComplexParagraph()
        {
            // Arrange
            var paragraph = new Paragraph();

            paragraph.AppendChild(new CommentRangeStart { Id = "comment-1" });

            var run1 = new Run();
            run1.AppendChild(new Text("Normal text "));
            paragraph.AppendChild(run1);

            var insertedRun = new InsertedRun { Id = "rev-1" };
            var revRun = new Run();
            revRun.AppendChild(new Text("inserted text"));
            insertedRun.AppendChild(revRun);
            paragraph.AppendChild(insertedRun);

            paragraph.AppendChild(new CommentRangeEnd { Id = "comment-1" });

            var paraItem = CreateTestParagraphElement();

            _documentInfo.Annotations.Comments["comment-1"] = new DocComment
            {
                CommentId = "comment-1",
                Targets = new List<string>()
            };

            // Act
            var result = Processor.ProcessParagraphElementsUnified(_documentInfo, paraItem, paragraph);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Segments.Count); // 两个文本段
            Assert.Contains("rev-1", _documentInfo.NativeRevisions.Keys);
            Assert.Empty(_documentInfo.Annotations.Comments["comment-1"].Targets);
        }

        #endregion

        [Fact]
        public void FullDocumentProcessing_WithList_ShouldProcessCorrectly()
        {
            // Arrange
            var filePath = "Files/Dog attacks.docx";
            var converter = new DocxConverter(_mockContext.Object);

            // Act
            var documentInfo = converter.ConvertToDocumentInfo(filePath);

            // Assert
            Assert.NotNull(documentInfo);
            Assert.True(documentInfo.Elements.Count > 0, "Should have elements");

            var listItems = documentInfo.Elements
                .OfType<ParagraphElement>()
                .Where(p => p.Properties.ListInfo != null)
                .ToList();

            Assert.True(listItems.Count > 0, "Should have list items");
            // Add more specific assertions about the list structure and content
            var firstListItem = listItems.First();
            Assert.Equal(1, firstListItem.Properties.ListInfo.NumId);
            Assert.Equal(0, firstListItem.Properties.ListInfo.Level);
        }
    }
}