using Convert.Exceptions;
using Convert.Helper;
using DocumentFormat.OpenXml.Wordprocessing;
using Xunit;

namespace Convert.Tests.Helper.Test
{
    public class WordListDetectorTests
    {
        [Fact]
        public void IsListParagraph_ShouldReturnTrue_WhenParagraphHasValidNumPr()
        {
            // Arrange
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            numPr.AppendChild(new NumberingId { Val = 1 });
            numPr.AppendChild(new NumberingLevelReference { Val = 0 });
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            var detector = new WordListDetector();

            // Act
            var result = detector.IsListParagraph(paragraph);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsListParagraph_ShouldReturnFalse_WhenParagraphIsNormal()
        {
            // Arrange
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            // No NumberingProperties
            paragraph.AppendChild(pPr);

            var detector = new WordListDetector();

            // Act
            var result = detector.IsListParagraph(paragraph);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsListParagraph_ShouldReturnFalse_WhenParagraphHasNoProperties()
        {
            // Arrange
            var paragraph = new Paragraph();
            // No ParagraphProperties at all

            var detector = new WordListDetector();

            // Act
            var result = detector.IsListParagraph(paragraph);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ExtractListProperties_ShouldReturnListInfo_WhenValidListParagraph()
        {
            // Arrange
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            numPr.AppendChild(new NumberingId { Val = 1 });
            numPr.AppendChild(new NumberingLevelReference { Val = 0 });
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            var numbering = CreateTestNumbering();
            var detector = new WordListDetector();

            // Act
            var result = detector.ExtractListProperties(paragraph, numbering);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.NumId);
            Assert.Equal(0, result.Level);
            Assert.Equal("decimal", result.ListType);
        }

        [Fact]
        public void ExtractListProperties_ShouldThrowFormatException_WhenNumberingIsNull()
        {
            // Arrange
            var paragraph = new Paragraph();
            var detector = new WordListDetector();

            // Act & Assert
            Assert.Throws<NumberingDefinitionsNotFoundException>(() =>
                detector.ExtractListProperties(paragraph, null));
        }

        [Fact]
        public void ExtractListProperties_ShouldReturnNull_WhenParagraphHasNoNumPr()
        {
            // Arrange
            var paragraph = new Paragraph();
            var numbering = CreateTestNumbering();
            var detector = new WordListDetector();

            // Act
            var result = detector.ExtractListProperties(paragraph, numbering);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ExtractListProperties_ShouldReturnNull_WhenNumIdIsMissing()
        {
            // Arrange
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            // Only level reference, no numId
            numPr.AppendChild(new NumberingLevelReference { Val = 0 });
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            var numbering = CreateTestNumbering();
            var detector = new WordListDetector();

            // Act
            var result = detector.ExtractListProperties(paragraph, numbering);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ExtractListProperties_ShouldReturnNull_WhenLevelIsMissing()
        {
            // Arrange
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            // Only numId, no level
            numPr.AppendChild(new NumberingId { Val = 1 });
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            var numbering = CreateTestNumbering();
            var detector = new WordListDetector();

            // Act
            var result = detector.ExtractListProperties(paragraph, numbering);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ExtractListProperties_ShouldHandleNestedLists()
        {
            // Arrange
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            numPr.AppendChild(new NumberingId { Val = 2 }); // Different list
            numPr.AppendChild(new NumberingLevelReference { Val = 1 }); // Level 1 (nested)
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            var numbering = CreateTestNumbering();
            var detector = new WordListDetector();

            // Act
            var result = detector.ExtractListProperties(paragraph, numbering);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.NumId);
            Assert.Equal(1, result.Level);
            Assert.Equal("bullet", result.ListType);
        }

        private static Numbering CreateTestNumbering()
        {
            var numbering = new Numbering();

            // Create abstract numbering for list 1
            var abstractNum1 = new AbstractNum { AbstractNumberId = 1 };
            var level1 = new Level { LevelIndex = 0 };
            level1.NumberingFormat = new NumberingFormat { Val = NumberFormatValues.Decimal };
            level1.PreviousParagraphProperties = new PreviousParagraphProperties();
            level1.PreviousParagraphProperties.Indentation = new Indentation { Left = "720" };
            abstractNum1.AppendChild(level1);
            numbering.AppendChild(abstractNum1);

            // Create abstract numbering for list 2
            var abstractNum2 = new AbstractNum { AbstractNumberId = 2 };
            var level2 = new Level { LevelIndex = 1 };
            level2.NumberingFormat = new NumberingFormat { Val = NumberFormatValues.Bullet };
            level2.PreviousParagraphProperties = new PreviousParagraphProperties();
            level2.PreviousParagraphProperties.Indentation = new Indentation { Left = "1440" };
            abstractNum2.AppendChild(level2);
            numbering.AppendChild(abstractNum2);

            // Create numbering instances
            var numInstance1 = new NumberingInstance { NumberID = 1 };
            numInstance1.AbstractNumId = new AbstractNumId { Val = 1 };
            numbering.AppendChild(numInstance1);

            var numInstance2 = new NumberingInstance { NumberID = 2 };
            numInstance2.AbstractNumId = new AbstractNumId { Val = 2 };
            numbering.AppendChild(numInstance2);

            return numbering;
        }
    }
}