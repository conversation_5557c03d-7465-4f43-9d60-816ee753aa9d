using System.Collections.Generic;
using System.Linq;
using Amazon.Lambda.Core;
using Moq;
using Xunit;
using Convert.Helper;
using Convert.Model;
using System.IO;
using System.Text.Json;
using Common.DocItemModel;

namespace Convert.Tests.Helper.Test
{
    public class DocxConverterTests
    {
        [Fact]
        public void Convert_ShouldReturnParagraphsWithRevisions_WhenValidDocxProvided()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/test.docx"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.Convert(testFilePath);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<ParagraphInfo>>(result);
            Assert.NotEmpty(result);

        }
        [Fact]
        public void Convert_ShouldCheckParagraphs()
        {
            var FilePath = "Files/test.docx"; // Ensure this file exists in your test project
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);
            var result = new DocxConverter(mockContext.Object).Convert(FilePath);
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            var resultJson = JsonSerializer.Serialize(result, jsonOptions);


            Assert.IsType<List<ParagraphInfo>>(result);
            Assert.Equal(6, result.Count);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldReturnDocumentInfo_WhenValidDocxProvided()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/test.docx"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldDetectTable()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/table.docx"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldDetectRevisions()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/GN13DB.docx"; // Ensure this file exists in your test project

            // Act
            // rev idx 18
            // comment 41 1567253A idx 39
            // 41E9DE5F
            // var result_c = docxConverter.Convert(testFilePath);
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);
            // 562DFB89 22
            // 211B8779 7
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            var resultJson = JsonSerializer.Serialize(result, jsonOptions);
            // save to json
            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.NativeRevisions);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldNotDetectDeleteRevision()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/Dog attacks.docx"; // Ensure this file exists in your test project

            // Act
            // rev idx 18
            // comment 41 1567253A idx 39
            // 41E9DE5F
            // var result_c = docxConverter.Convert(testFilePath);
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);
            // 562DFB89 22
            // 211B8779 7
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            var resultJson = JsonSerializer.Serialize(result, jsonOptions);
            // save to json
            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.NativeRevisions);
            Assert.Equal(83, result.Elements.Count);
            var test_para = result.Elements[18] as ParagraphElement;
            var expectedText = "The Companion Animals Act 1998 (NSW) (Act) sets out the legislation concerning companion animals and the responsibilities to be upheld by the owner and the local council.";
            Assert.Equal(4, test_para.Segments.Count);
            Assert.Equal(expectedText, test_para.PlainText);
            // Conditional check
            var emptyElementsCount = result.Elements.Where(e => e is ParagraphElement pe && string.IsNullOrEmpty(pe.PlainText) && (pe.Segments == null || pe.Segments.Count == 0)).Count();
            var totalElementsCount = result.Elements.Count;
            var maxAllowedEmptyElements = totalElementsCount / 2;

            Assert.True(emptyElementsCount <= maxAllowedEmptyElements);

        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldDetectHyperLinks()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/rev_test.docx"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.Annotations.Hyperlinks);
            Assert.Equal(28, result.Annotations.Hyperlinks.Count);
            var para = result.Elements[5] as ParagraphElement;
            Assert.Equal(3, para.Segments.Count);
            Assert.Equal("rId8", para.Segments[1].HyperlinkId);
            Assert.Equal(" ", para.Segments[1].SegmentText);
            Assert.Equal("rId9", para.Segments[2].HyperlinkId);
            Assert.Equal("Dog attacks", para.Segments[2].SegmentText);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldDetectHyperlinksInFiled()
        {
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/checklink.DOCX"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);
            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                IncludeFields = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
            var resultJson = JsonSerializer.Serialize(result, jsonOptions);
            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.Annotations.Hyperlinks);
            Assert.Equal(11, result.Annotations.Hyperlinks.Count);
            Assert.Equal(16, result.Annotations.Hyperlinks["67307EA1-0"].Targets.Count);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldDetectComments()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/checklink.DOCX"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);
            // 83 4C9DB968 18
            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.Annotations.Comments);
            Assert.Equal("4C9DB968-3", result.Annotations.Comments["83"].Targets[0]);
            Assert.Equal(2, result.Annotations.Comments["83"].Targets.Count);
        }

        [Fact]
        public void ConvertToDocumentInfo_ShouldDetectHyperlinkWithTargets()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/Dog attacks.docx"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);

            // Assert
            // 18
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.NativeRevisions);
            Assert.Equal(28, result.Annotations.Hyperlinks.Count);
            Assert.Equal(1, result.Annotations.Hyperlinks["rId8"].Targets.Count);
        }

        [Fact]
        public void ConvertToDocumentInfo_CommentTestCase()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var docxConverter = new DocxConverter(mockContext.Object);
            string testFilePath = "Files/Time of the essence clauses.docx"; // Ensure this file exists in your test project

            // Act
            var result = docxConverter.ConvertToDocumentInfo(testFilePath);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<DocumentInfo>(result);
            Assert.Equal("1.0", result.Version);
            Assert.NotEmpty(result.Annotations.Comments);
            Assert.Equal(1, result.Annotations.Comments.Count);
        }

    }
}
