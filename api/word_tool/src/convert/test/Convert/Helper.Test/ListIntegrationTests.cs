using System.IO;
using System.Linq;
using System.Text.Json;
using System.Xml;
using Amazon.Lambda.Core;
using Common.DocItemModel;
using Common.Tests.Attributes;
using Convert.Helper;
using Moq;
using Xunit;

namespace Convert.Helper.Test
{
    public class ListIntegrationTests
    {
        [Theory]
        [RealDocumentData("Files/Dog attacks.docx")]
        public void TestMultiLevelListConversion(byte[] docxBytes)
        {
            var mockLambdaContext = new Mock<ILambdaContext>();
            mockLambdaContext.Setup(x => x.Logger).Returns(new TestLambdaLogger());
            var tempFilePath = Path.GetTempFileName();
            File.WriteAllBytes(tempFilePath, docxBytes);

            var converter = new DocxConverter(mockLambdaContext.Object);
            var result = converter.ConvertToDocumentInfo(tempFilePath);

            File.Delete(tempFilePath);

            Assert.NotNull(result);
            var jsonResult = JsonSerializer.Serialize(result);
            var xmlDoc = new XmlDocument();
            // We need to convert the json to xml to validate the structure
            // For now, let's just assert that the result is not null

            var listItems = result.Elements.OfType<ParagraphElement>().Where(p => p.Properties.ListInfo != null).ToList();
            Assert.True(listItems.Count != 0, "Should have at least one list item.");

            var expectedListItems = new (string PlainText, int Level)[]
            {
                ("a lump sum of compensation for pain and suffering;", 0),
                ("reimbursement for past and future treatment and hospital expenses;", 0),
                ("reimbursement for past and future wage loss; and", 0),
                ("reimbursement for past and future domestic care and assistance.", 0),
                ("the dog owner has an insurance policy (such as a home insurance policy which may cover such incidents) in place which covers them in the event that their dog causes injury; or", 0),
                ("the owner has sufficient funds or assets available to pay the compensation.", 0),
                ("a dog attacks or chases another animal on the property where it is normally kept, except in the cases of dangerous dogs;", 0),
                ("a dog attacks another animal in the course of droving, tending, working or protecting stock;", 0),
                ("the dog is intentionally provoked by another dog or by a person; or", 0),
                ("a dog attacks in response to self-defense. ", 0)
            };

            Assert.Equal(expectedListItems.Length, listItems.Count);

            for (int i = 0; i < expectedListItems.Length; i++)
            {
                Assert.Equal(expectedListItems[i].PlainText, listItems[i].PlainText);
                Assert.Equal(expectedListItems[i].Level, listItems[i].Properties.ListInfo.Level);
            }
        }
    }
}

public class TestLambdaLogger : ILambdaLogger
{
    public void Log(string message)
    {
        // For testing purposes, we can just write to console or do nothing
        System.Console.WriteLine(message);
    }

    public void LogLine(string message)
    {
        System.Console.WriteLine(message);
    }
}