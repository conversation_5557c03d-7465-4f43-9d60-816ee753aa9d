using System;
using System.Collections.Generic;
using Common.DocItemModel;
using Convert.Abstract;
using Convert.Helper;
using Convert.Model;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace Convert.Tests.Helper.Test
{
    public class ListProcessorTests
    {
        [Fact]
        public void ProcessListParagraph_ShouldGenerateCorrectXml_WhenStandardListItem()
        {
            // Arrange
            var mockListDetector = new Mock<IListDetector>();
            var processor = new ListProcessor(mockListDetector.Object);
            var documentInfo = new DocumentInfo
            {
                Version = "1.0",
                DocumentId = "test-doc",
                Elements = new List<DocItem>(),
                Annotations = new Annotations
                {
                    Comments = new Dictionary<string, DocComment>(),
                    Hyperlinks = new Dictionary<string, DocHyperlink>()
                },
                NativeRevisions = new Dictionary<string, DocItem>()
            };

            var paragraph = CreateTestListParagraph("Test list item", 1, 0);
            var paraId = "list-para-1";
            var numbering = CreateTestNumbering();

            mockListDetector.Setup(ld => ld.IsListParagraph(paragraph)).Returns(true);
            mockListDetector.Setup(ld => ld.ExtractListProperties(paragraph, numbering))
                .Returns(new ListInfo { NumId = 1, Level = 0, ListType = "decimal", IndentLeft = 720 });

            // Act
            var result = processor.ProcessListParagraph(documentInfo, paragraph, paraId, numbering);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ElementType);
            Assert.Equal(paraId, result.ElementId);
            Assert.NotNull(result.Properties?.ListInfo);
            Assert.Equal(0, result.Properties.ListInfo.Level);
            Assert.Equal(1, result.Properties.ListInfo.NumId);
            Assert.Equal("decimal", result.Properties.ListInfo.ListType);
            Assert.Equal(720, result.Properties.ListInfo.IndentLeft);
        }

        [Fact]
        public void ProcessListParagraph_ShouldMaintainHierarchy_WhenNestedList()
        {
            // Arrange
            var mockListDetector = new Mock<IListDetector>();
            var processor = new ListProcessor(mockListDetector.Object);
            var documentInfo = new DocumentInfo
            {
                Version = "1.0",
                DocumentId = "test-doc",
                Elements = new List<DocItem>(),
                Annotations = new Annotations(),
                NativeRevisions = new Dictionary<string, DocItem>()
            };

            var paragraph = CreateTestListParagraph("Child item", 2, 1);
            var paraId = "list-para-child";
            var numbering = CreateTestNumbering();

            mockListDetector.Setup(ld => ld.IsListParagraph(paragraph)).Returns(true);
            mockListDetector.Setup(ld => ld.ExtractListProperties(paragraph, numbering))
                .Returns(new ListInfo { NumId = 2, Level = 1, ListType = "bullet", IndentLeft = 1440 });

            // Act
            var result = processor.ProcessListParagraph(documentInfo, paragraph, paraId, numbering);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("List", result.ElementType);
            Assert.NotNull(result.Properties?.ListInfo);
            Assert.Equal(1, result.Properties.ListInfo.Level);
            Assert.Equal(2, result.Properties.ListInfo.NumId);
            Assert.Equal("bullet", result.Properties.ListInfo.ListType);
            Assert.Equal(1440, result.Properties.ListInfo.IndentLeft);
        }

        [Fact]
        public void ProcessListParagraph_ShouldThrowInvalidOperationException_WhenNotListParagraph()
        {
            // Arrange
            var mockListDetector = new Mock<IListDetector>();
            var processor = new ListProcessor(mockListDetector.Object);
            var documentInfo = new DocumentInfo { Version = "1.0" };
            var paragraph = new Paragraph(); // Normal paragraph
            var paraId = "para-1";
            var numbering = CreateTestNumbering();

            mockListDetector.Setup(ld => ld.IsListParagraph(paragraph)).Returns(false);

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() =>
                processor.ProcessListParagraph(documentInfo, paragraph, paraId, numbering));
            Assert.Contains("The provided paragraph is not a list paragraph", ex.Message);
        }

        [Fact]
        public void ProcessListParagraph_ShouldThrowArgumentNullException_WhenDocumentInfoIsNull()
        {
            // Arrange
            var mockListDetector = new Mock<IListDetector>();
            var processor = new ListProcessor(mockListDetector.Object);
            var paragraph = new Paragraph();
            var paraId = "test-para";
            var numbering = CreateTestNumbering();

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                processor.ProcessListParagraph(null!, paragraph, paraId, numbering));
        }

        [Fact]
        public void ProcessListParagraph_ShouldThrowArgumentNullException_WhenParagraphIsNull()
        {
            // Arrange
            var mockListDetector = new Mock<IListDetector>();
            var processor = new ListProcessor(mockListDetector.Object);
            var documentInfo = new DocumentInfo { Version = "1.0" };
            var paraId = "test-para";
            var numbering = CreateTestNumbering();

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                processor.ProcessListParagraph(documentInfo, null!, paraId, numbering));
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        public void ProcessListParagraph_ShouldThrowArgumentException_WhenParaIdIsInvalid(string paraId)
        {
            // Arrange
            var mockListDetector = new Mock<IListDetector>();
            var processor = new ListProcessor(mockListDetector.Object);
            var documentInfo = new DocumentInfo { Version = "1.0" };
            var paragraph = new Paragraph();
            var numbering = CreateTestNumbering();

            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
                processor.ProcessListParagraph(documentInfo, paragraph, paraId, numbering));
        }

        private static Paragraph CreateTestListParagraph(string text, int numId, int level)
        {
            var paragraph = new Paragraph();
            var pPr = new ParagraphProperties();
            var numPr = new NumberingProperties();
            numPr.AppendChild(new NumberingId { Val = numId });
            numPr.AppendChild(new NumberingLevelReference { Val = level });
            pPr.AppendChild(numPr);
            paragraph.AppendChild(pPr);

            if (!string.IsNullOrEmpty(text))
            {
                var run = new Run();
                run.AppendChild(new Text(text));
                paragraph.AppendChild(run);
            }

            return paragraph;
        }

        private static Numbering CreateTestNumbering()
        {
            var numbering = new Numbering();

            var abstractNum1 = new AbstractNum { AbstractNumberId = 1 };
            var level1 = new Level { LevelIndex = 0, NumberingFormat = new NumberingFormat { Val = NumberFormatValues.Decimal }, PreviousParagraphProperties = new PreviousParagraphProperties(new Indentation { Left = "720" }) };
            abstractNum1.AppendChild(level1);
            numbering.AppendChild(abstractNum1);

            var abstractNum2 = new AbstractNum { AbstractNumberId = 2 };
            var level2 = new Level { LevelIndex = 1, NumberingFormat = new NumberingFormat { Val = NumberFormatValues.Bullet }, PreviousParagraphProperties = new PreviousParagraphProperties(new Indentation { Left = "1440" }) };
            abstractNum2.AppendChild(level2);
            numbering.AppendChild(abstractNum2);

            var numInstance1 = new NumberingInstance { NumberID = 1 };
            numInstance1.AbstractNumId = new AbstractNumId { Val = 1 };
            numbering.AppendChild(numInstance1);

            var numInstance2 = new NumberingInstance { NumberID = 2 };
            numInstance2.AbstractNumId = new AbstractNumId { Val = 2 };
            numbering.AppendChild(numInstance2);

            return numbering;
        }
    }
}