using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Amazon;
using Amazon.Lambda.Core;
using Amazon.S3.Model;
using Common.Abstract;
using Convert.Executor;
using Convert.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace Convert.Tests.Excutor
{
    public class ConvertExecutorTests
    {
        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenEventIsNull()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(Mock.Of<ILambdaLogger>());

            ConvertExecutor executor = new ConvertExecutor(null, mockContext.Object); // Fix for CS1503

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.Code);
            Assert.Equal("Event is empty.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenBucketOrKeyIsEmpty()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(Mock.Of<ILambdaLogger>());

            var convertEvent = new ConvertEvent { Bucket = "", Key = "" };

            ConvertExecutor executor = new ConvertExecutor(convertEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.Code);
            Assert.Equal("The bucket or key is empty.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenGetDocumentFails()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            mockS3Service.Setup(s => s.GetObjectAsync(It.IsAny<string>(), It.IsAny<string>()))
                         .ReturnsAsync((GetObjectResponse)null);

            var mockContext = new Mock<ILambdaContext>();
            var convertEvent = new ConvertEvent { Bucket = "test-bucket", Key = "test-key" };
            ConvertExecutor executor = new ConvertExecutor(mockS3Service.Object, convertEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.Code);
            Assert.Equal("Get Document failed.", result.Message);
        }

        public MemoryStream GetTestFileStream()
        {
            using var fileStream = new FileStream("Files/test.docx", FileMode.Open, FileAccess.Read);
            var memoryStream = new MemoryStream();
            fileStream.CopyTo(memoryStream);
            memoryStream.Position = 0; // Reset the position to the beginning of the stream  
            return memoryStream;
        }

        public List<ParagraphInfo> GetTestParagraphInfoFromFile()
        {
            using var fileStream = new FileStream("Files/test.json", FileMode.Open, FileAccess.Read);
            using var reader = new StreamReader(fileStream);
            var jsonContent = reader.ReadToEnd();
            var result = System.Text.Json.JsonSerializer.Deserialize<List<ParagraphInfo>>(jsonContent);
            return result;
        }

        [Fact]
        public async Task Run_ShouldReturnSuccessResponse_WhenConversionSucceeds()
        {
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);
            var testDocxContent = CreateTestDocxBytes();
            var mockS3Service = new Mock<IS3Service>();

            var mockS3Response = new GetObjectResponse
            {
                ResponseStream = new MemoryStream(testDocxContent)
            };

            mockS3Service.Setup(s => s.GetObjectAsync(It.IsAny<string>(), It.IsAny<string>()))
                    .ReturnsAsync(mockS3Response);

            var convertEvent = new ConvertEvent
            {
                Bucket = "test-bucket",
                Key = "test-document.docx"
            };
            ConvertExecutor executor = new ConvertExecutor(mockS3Service.Object, convertEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.Code);
            Assert.NotNull(result.Data);
            Assert.Equal("success", result.Message);
            var documentInfo = result.Data as DocumentInfo;
            Assert.Equal("1.0", documentInfo.Version);
        }

        private byte[] CreateTestDocxBytes()
        {
            using (var memoryStream = new MemoryStream())
            {
                using (var document = WordprocessingDocument.Create(
                    memoryStream,
                    WordprocessingDocumentType.Document))
                {
                    var mainDocumentPart = document.AddMainDocumentPart();
                    var doc = new Document();
                    var body = new Body();

                    var paragraph = new Paragraph();
                    var run = new Run();
                    var text = new Text("Test document content.");

                    run.Append(text);
                    paragraph.Append(run);
                    body.Append(paragraph);
                    doc.Append(body);

                    mainDocumentPart.Document = doc;
                    mainDocumentPart.Document.Save();
                }

                return memoryStream.ToArray();
            }
        }

        private void CreateTestDocxFile(string filePath)
        {
            // Ensure directory exists
            var directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            using (var document = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document))
            {
                var mainDocumentPart = document.AddMainDocumentPart();
                var doc = new Document();
                var body = new Body();

                var paragraph = new Paragraph();
                var run = new Run();
                var text = new Text("This is a test document created for local testing.");

                run.Append(text);
                paragraph.Append(run);
                body.Append(paragraph);
                doc.Append(body);

                mainDocumentPart.Document = doc;
                mainDocumentPart.Document.Save();
            }
        }


        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenConversionFailed()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockResponse = new GetObjectResponse
            {
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                ResponseStream = new MemoryStream()
            };

            mockS3Service.Setup(s => s.GetObjectAsync(It.IsAny<string>(), It.IsAny<string>()))
                         .ReturnsAsync(mockResponse);

            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(Mock.Of<ILambdaLogger>());
            var convertEvent = new ConvertEvent { Bucket = "test-bucket", Key = "test-key" };
            ConvertExecutor executor = new ConvertExecutor(mockS3Service.Object, convertEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.Code);
            Assert.NotNull(result.Data);
            Assert.Equal("Convert document changes failed.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnSuccessResponse_WithS3File()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            // 🔧 S3 Configuration - Using real S3 file
            var convertEvent = new ConvertEvent
            {
                Bucket = "5633-pg-dev-styling-agent",
                Key = "jobs/2025-06-17/Dog attacks/local-run_202506171623/Dog attacks_original.docx"
            };
            var executor = new ConvertExecutor(convertEvent, mockContext.Object);

            // Act
            var result = await executor.Run();
            var result_exec = await executor.Execute();

            Assert.NotNull(result);
            // Assert.Equal(200, result.Code);
            Assert.NotNull(result.Data);
            Assert.IsType<DocumentInfo>(result.Data);

            var documentInfo = result.Data as DocumentInfo;
            Assert.Equal("1.0", documentInfo!.Version);
            Assert.NotEmpty(documentInfo.Elements);
        }
    }
}
