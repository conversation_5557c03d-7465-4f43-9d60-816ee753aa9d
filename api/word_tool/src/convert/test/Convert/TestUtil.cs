﻿using System.Collections.Generic;
using Common.Model;
using Convert.Model;
using Xunit;

namespace Convert.Tests
{
    public static class TestUtil
    {
        public static void ParagraphInfoValidation(List<ParagraphInfo> targetParagraphInfos, List<ParagraphInfo> convertParagraphInfos)
        {
            Assert.Equal(targetParagraphInfos.Count, convertParagraphInfos.Count);

            foreach (var item in convertParagraphInfos)
            {
                var targetItem = targetParagraphInfos.Find(x => x.ParaId == item.ParaId);
                Assert.NotNull(targetItem);

                Assert.Equal(targetItem.ParaId, item.ParaId);

                Assert.Equal(targetItem.Text, item.Text);
                Assert.Equal(targetItem.Runs.Count, item.Runs.Count);
                for (int i = 0; i < targetItem.Runs.Count; i++)
                {
                    var targetRun = targetItem.Runs[i];
                    var convertRun = item.Runs[i];
                    if (targetRun is NormalRun)
                    {
                        var tempTarget = targetRun as NormalRun;
                        var tempConvert = convertRun as NormalRun;
                        Assert.Equal(tempTarget.Text, tempConvert.Text);
                    }
                    else if (targetRun is RevisionRun)
                    {
                        var tempTarget = targetRun as RevisionRun;
                        var tempConvert = convertRun as RevisionRun;

                        Assert.Equal(tempTarget.Text, tempConvert.Text);
                        Assert.Equal(tempTarget.Id, tempConvert.Id);
                        Assert.Equal(tempConvert.Author, tempConvert.Author);
                        Assert.Equal(tempConvert.Date, tempConvert.Date);
                        Assert.Equal(tempConvert.Type, tempConvert.Type);
                        Assert.Equal(tempConvert.Links.Count, tempConvert.Links.Count);
                        for (int j = 0; j < tempTarget.Links.Count; j++)
                        {
                            Assert.Equal(tempTarget.Links[j], tempConvert.Links[j]);
                        }
                    }
                }
            }
        }
    }
}
