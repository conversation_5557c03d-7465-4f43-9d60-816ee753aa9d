import json
import argparse
import os
from typing import Any, Dict, List, Union


def remove_key_recursively(data: Union[Dict, List, Any]) -> Union[Dict, List, Any]:
    """
    递归删除JSON数据中所有的"Key"字段

    Args:
        data: JSON数据（字典、列表或其他类型）

    Returns:
        删除"Key"字段后的数据
    """
    if isinstance(data, dict):
        # 创建新字典，排除"Key"字段
        new_dict = {}
        for key, value in data.items():
            if key != "Key":  # 跳过"Key"字段
                new_dict[key] = remove_key_recursively(value)
        return new_dict

    elif isinstance(data, list):
        # 递归处理列表中的每个元素
        return [remove_key_recursively(item) for item in data]

    else:
        # 其他类型（字符串、数字等）直接返回
        return data


def clean_json_file(input_file: str, output_file: str = None, backup: bool = True) -> None:
    """
    清理JSON文件，删除所有"Key"字段

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径（如果为None，则覆盖原文件）
        backup: 是否创建备份文件
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")

        # 读取原始JSON文件
        print(f"正在读取文件: {input_file}")
        with open(input_file, "r", encoding="utf-8") as f:
            data = json.load(f)

        # 统计原始"Key"字段数量
        original_key_count = count_keys(data)
        print(f"发现 {original_key_count} 个 'Key' 字段")

        # 创建备份文件
        if backup and output_file != input_file:
            backup_file = f"{input_file}.backup"
            print(f"创建备份文件: {backup_file}")
            with open(backup_file, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        # 删除所有"Key"字段
        print("正在删除 'Key' 字段...")
        cleaned_data = remove_key_recursively(data)

        # 验证删除结果
        remaining_key_count = count_keys(cleaned_data)
        print(f"删除完成，剩余 {remaining_key_count} 个 'Key' 字段")

        # 确定输出文件路径
        if output_file is None:
            output_file = input_file

        # 写入清理后的数据
        print(f"正在写入文件: {output_file}")
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)

        print(f"✅ 成功删除了 {original_key_count - remaining_key_count} 个 'Key' 字段")
        print(f"✅ 清理后的文件已保存到: {output_file}")

    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
    except FileNotFoundError as e:
        print(f"❌ 文件错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def count_keys(data: Union[Dict, List, Any]) -> int:
    """
    统计JSON数据中"Key"字段的数量

    Args:
        data: JSON数据

    Returns:
        "Key"字段的数量
    """
    count = 0

    if isinstance(data, dict):
        for key, value in data.items():
            if key == "Key":
                count += 1
            count += count_keys(value)

    elif isinstance(data, list):
        for item in data:
            count += count_keys(item)

    return count


def batch_clean_files(directory: str, pattern: str = "*.json") -> None:
    """
    批量清理目录中的JSON文件

    Args:
        directory: 目录路径
        pattern: 文件匹配模式
    """
    import glob

    json_files = glob.glob(os.path.join(directory, pattern))

    if not json_files:
        print(f"在目录 {directory} 中没有找到匹配 {pattern} 的文件")
        return

    print(f"找到 {len(json_files)} 个JSON文件")

    for file_path in json_files:
        print(f"\n处理文件: {file_path}")
        clean_json_file(file_path)


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description="删除JSON文件中所有的'Key'字段",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python remove_keys.py input.json                    # 直接修改原文件
  python remove_keys.py input.json -o output.json     # 输出到新文件
  python remove_keys.py input.json --no-backup        # 不创建备份
  python remove_keys.py -d /path/to/directory          # 批量处理目录中的JSON文件
        """,
    )

    # 互斥组：要么指定文件，要么指定目录
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("input_file", nargs="?", help="输入JSON文件路径")
    group.add_argument("-d", "--directory", help="批量处理目录中的JSON文件")

    parser.add_argument("-o", "--output", help="输出文件路径（默认覆盖原文件）")
    parser.add_argument("--no-backup", action="store_true", help="不创建备份文件")
    parser.add_argument("--pattern", default="*.json", help="批量处理时的文件匹配模式（默认: *.json）")

    args = parser.parse_args()

    if args.directory:
        # 批量处理模式
        batch_clean_files(args.directory, args.pattern)
    else:
        # 单文件处理模式
        clean_json_file(input_file=args.input_file, output_file=args.output, backup=not args.no_backup)


if __name__ == "__main__":
    main()
