{"Version": "1.0", "DocumentId": "rev_test.docx", "Elements": [{"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7AB9EC6A", "PlainText": "", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "7AB9EC6A_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "49AF9040", "PlainText": "User Name: <PERSON>", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "49AF9040_0", "SegmentText": "User Name: ", "Start": 0, "End": 11, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "49AF9040_1", "SegmentText": "<PERSON>", "Start": 11, "End": 22, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "6F3DB6E5", "PlainText": "Date and Time: Tuesday 29 April 2025 2:49 pm AEST", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "6F3DB6E5_0", "SegmentText": "Date and Time: ", "Start": 0, "End": 15, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6F3DB6E5_1", "SegmentText": "Tuesday 29 April 2025 2:49 pm AEST", "Start": 15, "End": 49, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "2D9E729E", "PlainText": "Job Number: 251545970", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "2D9E729E_0", "SegmentText": "Job Number: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2D9E729E_1", "SegmentText": "251545970", "Start": 12, "End": 21, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "329FE1F4", "PlainText": "Document (1)", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "329FE1F4_0", "SegmentText": "Document (1)", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "<PERSON><PERSON>", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "3AAC1D37", "PlainText": "1. Dog attacks", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "3AAC1D37_0", "SegmentText": "1.", "Start": 0, "End": 2, "Properties": {"Color": "", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3AAC1D37_1", "SegmentText": "", "Start": 2, "End": 2, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "false", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId8", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3AAC1D37_2", "SegmentText": "", "Start": 2, "End": 2, "Properties": {"Color": "0077CC", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId9", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "77E99D12", "PlainText": "Client/Matter: -None-", "Properties": {"Style": "Normal0"}, "Segments": [{"SegmentId": "77E99D12_0", "SegmentText": "Client/Matter: ", "Start": 0, "End": 15, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "77E99D12_1", "SegmentText": "-None-", "Start": 15, "End": 21, "Properties": {"Color": "000000", "FontSize": "20", "FontFamily": "<PERSON><PERSON>", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "583BB06D", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "04EC2D3A", "PlainText": "Dog attacks", "Properties": {"Style": "Heading1"}, "Segments": [{"SegmentId": "04EC2D3A_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "0077CC", "FontSize": "28", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId22", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "738FC590", "PlainText": "Maintained", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "738FC590_0", "SegmentText": "Maintained", "Start": 0, "End": 10, "Properties": {"Color": "6D6E71", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "6D9B07B1", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "6F82FE2B", "PlainText": "Jump to section", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "6F82FE2B_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6F82FE2B_1", "SegmentText": "Jump to section", "Start": 0, "End": 15, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "0F5DB4FB", "PlainText": "Who is responsible?   |   Continuation of liability when person dies from dog attack   |   Compensation for injuries arising from a dog attack", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "0F5DB4FB_0", "SegmentText": "Who is responsible?", "Start": 0, "End": 19, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_1", "SegmentText": "   ", "Start": 19, "End": 22, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_2", "SegmentText": "|", "Start": 22, "End": 23, "Properties": {"Color": "D9D9D9", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_3", "SegmentText": "   ", "Start": 23, "End": 26, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_4", "SegmentText": "Continuation of liability when person dies from dog attack", "Start": 26, "End": 84, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_5", "SegmentText": "   ", "Start": 84, "End": 87, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_6", "SegmentText": "|", "Start": 87, "End": 88, "Properties": {"Color": "D9D9D9", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_7", "SegmentText": "   ", "Start": 88, "End": 91, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0F5DB4FB_8", "SegmentText": "Compensation for injuries arising from a dog attack", "Start": 91, "End": 142, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "18F4B051", "PlainText": "Authored by the LexisNexis Legal Writer team.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "18F4B051_0", "SegmentText": "Authored by the LexisNexis Legal Writer team.", "Start": 0, "End": 45, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7E1CD090", "PlainText": "Injuries caused by dogs can occur both unexpectedly and unprovoked. Most dog owners would not anticipate that their dog could injure someone, and in most cases, dog owners do not hold insurance for this type of event.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "7E1CD090_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7E1CD090_1", "SegmentText": "Injuries caused by dogs can occur both unexpectedly and unprovoked. Most dog owners would not anticipate that their dog could injure someone, and in most cases, dog owners do not hold insurance for this type of event.", "Start": 0, "End": 217, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "327A2CE8", "PlainText": "Who is responsible?", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "327A2CE8_0", "SegmentText": "Who is responsible?", "Start": 0, "End": 19, "Properties": {"Color": "000000", "FontSize": "28", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "14199F38", "PlainText": "02540000", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "14199F38_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "35FDE024", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "678C92C7", "PlainText": "The Companion Animals Act 1998 (NSW) (the Act) sets out the legislation concerning companion animals and the responsibilities to be upheld by the owner and the local council.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "678C92C7_0", "SegmentText": "The ", "Start": 0, "End": 4, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "678C92C7_1", "SegmentText": "", "Start": 4, "End": 4, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId23", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "678C92C7_2", "SegmentText": " (NSW) (", "Start": 4, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "678C92C7_3", "SegmentText": "", "Start": 12, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "1", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "678C92C7_4", "SegmentText": "Act) sets out the legislation concerning companion animals and the responsibilities to be upheld by the owner and the local council.", "Start": 12, "End": 144, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "16E7C3FF", "PlainText": "Section 25(1) of the Act provides that:", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "16E7C3FF_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "16E7C3FF_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId24", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "16E7C3FF_2", "SegmentText": "(1)", "Start": 0, "End": 3, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "3", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "16E7C3FF_3", "SegmentText": " of the Act provides that", "Start": 3, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "16E7C3FF_4", "SegmentText": "", "Start": 28, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "4", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "5456D019", "PlainText": "(1) The owner of a dog is liable in damages in respect of:", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "5456D019_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "7", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "217A2D58", "PlainText": "(a) bodily injury to a person caused by the dog wounding or attacking that person, and", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "217A2D58_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "9", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "217A2D58_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "9", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "672A4FC6", "PlainText": "(b) damage to the personal property of a person (including clothing) caused by the dog in the course of attacking that person.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "672A4FC6_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "11", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "672A4FC6_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "11", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "5D4351B8", "PlainText": "(2) This section does not apply in respect of:", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "5D4351B8_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "13", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5D4351B8_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "13", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7AA2E42B", "PlainText": "(a) an attack by a dog occurring on any property or vehicle of which the owner of the dog is an occupier or on which the dog is ordinarily kept, but only if the person attacked was not lawfully on the property or vehicle and the dog was not a dangerous dog, menacing dog or restricted dog at the time of the attack, or", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "7AA2E42B_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "15", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7AA2E42B_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "15", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "56184E24", "PlainText": "(b) an attack by a dog that is in immediate response to, and is wholly induced by, intentional provocation of the dog by a person other than the owner of the dog or the owner's employees or agents.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "56184E24_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "17", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56184E24_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "17", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7D7A931E", "PlainText": "T the dog owner is liable for damages in respect of bodily injury and damage to personal property arising from a dog attack. The dog owner is responsible for the attack in a strict sense, meaning, that regardless of the owner not intending or expecting for the dog attack to occur, they are liable anyway.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "7D7A931E_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "18", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_1", "SegmentText": " t", "Start": 0, "End": 2, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "19", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_2", "SegmentText": "he ", "Start": 2, "End": 5, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_3", "SegmentText": "dog ", "Start": 5, "End": 9, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "20", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_4", "SegmentText": "owner is ", "Start": 9, "End": 18, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_5", "SegmentText": "liable for damages in respect of bodily injury and damage to personal property arising from a dog attack. The dog own", "Start": 18, "End": 135, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "21", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_6", "SegmentText": "er is", "Start": 135, "End": 140, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "22", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_7", "SegmentText": " ", "Start": 140, "End": 141, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "23", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_8", "SegmentText": "responsible for the attack in a strict sense, meaning", "Start": 141, "End": 194, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_9", "SegmentText": "", "Start": 194, "End": 194, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "24", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7D7A931E_10", "SegmentText": " that regardless of the owner not intending or expecting for the dog attack to occur, they are liable anyway.", "Start": 194, "End": 303, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "626AD025", "PlainText": "A local council may also be found liable for a dog attack, if it can be shown that the council had the required knowledge and failed to act upon and declare a dog which was known to them to be dangerous: <PERSON><PERSON><PERSON><PERSON> v Warren Shire Council.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "626AD025_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "626AD025_1", "SegmentText": "A local council may also be found liable for a dog attack, if it can be shown that the council had the required knowledge and failed to act upon and declare a dog which was known to them to be dangerous: ", "Start": 0, "End": 204, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "626AD025_2", "SegmentText": "<PERSON><PERSON><PERSON> b<PERSON> v Warren Shire Council", "Start": 204, "End": 244, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "626AD025_3", "SegmentText": ".", "Start": 244, "End": 245, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "36C00B29", "PlainText": "References: <PERSON><PERSON><PERSON> b<PERSON> v Warren Shire Council [2011] NSWDC 30", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "36C00B29_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "36C00B29_1", "SegmentText": "<PERSON><PERSON><PERSON> b<PERSON> v Warren Shire Council ", "Start": 12, "End": 53, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "36C00B29_2", "SegmentText": "", "Start": 53, "End": 53, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId25", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "0078F712", "PlainText": "The conduct of the dog must involve an element of aggression or other deliberate conduct and not an act without intention: <PERSON><PERSON><PERSON> v <PERSON>.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "0078F712_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0078F712_1", "SegmentText": "The conduct of the dog must involve an element of aggression or other deliberate conduct and not an act without intention: ", "Start": 0, "End": 123, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0078F712_2", "SegmentText": "<PERSON><PERSON><PERSON> v Morrison", "Start": 123, "End": 140, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0078F712_3", "SegmentText": ".", "Start": 140, "End": 141, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "3BAB127B", "PlainText": "References: <PERSON><PERSON><PERSON> v <PERSON> (2013) 64 MVR 433", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "3BAB127B_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3BAB127B_1", "SegmentText": "<PERSON><PERSON><PERSON> v Morrison ", "Start": 12, "End": 30, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3BAB127B_2", "SegmentText": "", "Start": 30, "End": 30, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId26", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "2CF0C138", "PlainText": "There are limited defences available for the owner, which are listed in s 25(2) of the Act.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "2CF0C138_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2CF0C138_1", "SegmentText": "There are limited defences available for the owner, which are listed in ", "Start": 0, "End": 72, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2CF0C138_2", "SegmentText": "", "Start": 72, "End": 72, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId27", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2CF0C138_3", "SegmentText": " of the Act", "Start": 72, "End": 83, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "25", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2CF0C138_4", "SegmentText": ".", "Start": 83, "End": 84, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "2B3139B1", "PlainText": "In <PERSON> v <PERSON><PERSON><PERSON> (<PERSON>), two dogs were found fighting on the respondent’s property. The appellant entered the property in attempt to remove their dog from the fight. The respondent’s dog bit the appellant’s dog on the hand, causing injury. It was confirmed by the Court of Appeal that the respondent was not liable when their dog caused injury to the appellant because the appellant was trespassing (albeit with some necessity) on the respondent’s property at the time.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "2B3139B1_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2B3139B1_1", "SegmentText": "In ", "Start": 0, "End": 3, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2B3139B1_2", "SegmentText": "Simon v Condran", "Start": 3, "End": 18, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2B3139B1_3", "SegmentText": " (", "Start": 18, "End": 20, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "27", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2B3139B1_4", "SegmentText": "<PERSON>)", "Start": 20, "End": 26, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "27", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2B3139B1_5", "SegmentText": ", two dogs were found fighting on the respondent’s property. The appellant entered the property in attempt to remove their dog from the fight. The respondent’s dog bit the appellant’s dog on the hand, causing injury. It was confirmed by the Court of Appeal that the respondent was not liable when their dog caused injury to the appellant because the appellant was trespassing (albeit with some necessity) on the respondent’s property at the time.", "Start": 26, "End": 472, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "6A3102BA", "PlainText": "The judge manually introduced this test quotation to see whether it’d work or not, saying that the case was ‘something that should be described in double quote marks’, and ‘a good test of the automated quotation check’. They also wanted to see if slang terms like g’day, fair dinkum, barbie, arvo and no worries would get caught or not. Finally the judge really wanted to see if a really long quotation like this one would then get caught in the quotation check and set aside as its own thing, rather than being allowed to just sit alongside the rest of the text: “As soon as the great black velvet pall outside my little window was shot with grey, I got up and went downstairs; every board upon the way, and every crack in every board calling after me, \"Stop thief!\" and \"Get up, Mrs. <PERSON>!\" In the pantry, which was far more abundantly supplied than usual, owing to the season, I was very much alarmed by a hare hanging up by the heels, whom I rather thought I caught, when my back was half turned, winking. I had no time for verification, no time for selection, no time for anything, for I had no time to spare. I stole some bread, some rind of cheese, about half a jar of mincemeat (which I tied up in my pocket-handkerchief with my last night’s slice), some brandy from a stone bottle (which I decanted into a glass bottle I had secretly used for making that intoxicating fluid, Spanish-liquorice-water, up in my room: diluting the stone bottle from a jug in the kitchen cupboard), a meat bone with very little on it, and a beautiful round compact pork pie. I was nearly going away without the pie, but I was tempted to mount upon a shelf, to look what it was that was put away so carefully in a covered earthenware dish in a corner, and I found it was the pie, and I took it in the hope that it was not intended for early use, and would not be missed for some time.”", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "6A3102BA_0", "SegmentText": "The ", "Start": 0, "End": 4, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "29", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6A3102BA_1", "SegmentText": "judge manually introduced this test quotation to see whether it’d work or not, saying that the case was ‘something that should be described in double quote marks’, and ‘a good test of the automated quotation check’. They also wanted to see if slang terms l", "Start": 4, "End": 260, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "30", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6A3102BA_2", "SegmentText": "ike g’day, fair dinkum, barbie, arvo and no worries would ", "Start": 260, "End": 318, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "31", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6A3102BA_3", "SegmentText": "get caught or not. Finally the judge really wanted to see if a really long quotation like this one would then get caught in the quotation check and set aside as its own thing, rather than being allowed to just sit alongside the rest of the text: ", "Start": 318, "End": 564, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "32", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6A3102BA_4", "SegmentText": "“", "Start": 564, "End": 565, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "33", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6A3102BA_5", "SegmentText": "As soon as the great black velvet pall outside my little window was shot with grey, I got up and went downstairs; every board upon the way, and every crack in every board calling after me, \"Stop thief!\" and \"Get up, Mrs. <PERSON>!\" In the pantry, which was far more abundantly supplied than usual, owing to the season, I was very much alarmed by a hare hanging up by the heels, whom I rather thought I caught, when my back was half turned, winking. I had no time for verification, no time for selection, no time for anything, for I had no time to spare. I stole some bread, some rind of cheese, about half a jar of mincemeat (which I tied up in my pocket-handkerchief with my last night’s slice), some brandy from a stone bottle (which I decanted into a glass bottle I had secretly used for making that intoxicating fluid, Spanish-liquorice-water, up in my room: diluting the stone bottle from a jug in the kitchen cupboard), a meat bone with very little on it, and a beautiful round compact pork pie. I was nearly going away without the pie, but I was tempted to mount upon a shelf, to look what it was that was put away so carefully in a covered earthenware dish in a corner, and I found it was the pie, and I took it in the hope that it was not intended for early use, and would not be missed for some time.", "Start": 565, "End": 1870, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "34", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "6A3102BA_6", "SegmentText": "”", "Start": 1870, "End": 1871, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "35", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "6D972A08", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1A7F058F", "PlainText": "References: <PERSON> v <PERSON><PERSON> (2013) 85 NSWLR 768;  [2013] NSWCA 388; BC201314902", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "1A7F058F_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1A7F058F_1", "SegmentText": "Simon v Condran ", "Start": 12, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1A7F058F_2", "SegmentText": "", "Start": 28, "End": 28, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId28", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1A7F058F_3", "SegmentText": ";  ", "Start": 28, "End": 31, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1A7F058F_4", "SegmentText": "", "Start": 31, "End": 31, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId29", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1A7F058F_5", "SegmentText": "; ", "Start": 31, "End": 33, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1A7F058F_6", "SegmentText": "", "Start": 33, "End": 33, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId30", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1604F2A7", "PlainText": "“The owner” of the dog is defined in s 7 of the Act.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "1604F2A7_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1604F2A7_1", "SegmentText": "“The owner” of the dog is defined in ", "Start": 0, "End": 37, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1604F2A7_2", "SegmentText": "", "Start": 37, "End": 37, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId31", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1604F2A7_3", "SegmentText": " of the Act", "Start": 37, "End": 48, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "36", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1604F2A7_4", "SegmentText": ".", "Start": 48, "End": 49, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "56DDB0E1", "PlainText": "Practice Tip: Request the file under Government Information (Public Access) Act 2009 (NSW) (GIPA) from the relevant local council for all records relating to the dog attack (your client should be able to provide you with a reference number to supply the same).", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "56DDB0E1_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56DDB0E1_1", "SegmentText": "Practice Tip:", "Start": 0, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56DDB0E1_2", "SegmentText": " Request the file under ", "Start": 13, "End": 37, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56DDB0E1_3", "SegmentText": "", "Start": 37, "End": 37, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId32", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56DDB0E1_4", "SegmentText": " (NSW) (GIPA) from the relevant local council for all records relating to the dog attack", "Start": 37, "End": 125, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56DDB0E1_5", "SegmentText": "", "Start": 125, "End": 125, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "38", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "56DDB0E1_6", "SegmentText": ".", "Start": 125, "End": 126, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "306B6B36", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1567253A", "PlainText": "See Guidance Note: Information from government agencies in the Topic: Preparation of the claim.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "1567253A_0", "SegmentText": "See Guidance Note: ", "Start": 0, "End": 19, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "40", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1567253A_1", "SegmentText": "Information from government agencies ", "Start": 19, "End": 56, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "40", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1567253A_2", "SegmentText": "", "Start": 56, "End": 56, "Properties": {"Color": "", "FontSize": "", "FontFamily": "Times New Roman", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "42", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1567253A_3", "SegmentText": "in the Topic: Preparation of the claim.", "Start": 56, "End": 95, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "43", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "11D79B22", "PlainText": "Continuation of liability when person dies from dog attack", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "11D79B22_0", "SegmentText": "Continuation of liability when person dies from dog attack", "Start": 0, "End": 58, "Properties": {"Color": "000000", "FontSize": "28", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "17671057", "PlainText": "02540000", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "17671057_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "67EA9ACA", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "2E4138A3", "PlainText": "Section 26 of the Act provides that: ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "2E4138A3_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId37", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E4138A3_1", "SegmentText": " of the Act provides that", "Start": 0, "End": 25, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E4138A3_2", "SegmentText": "", "Start": 25, "End": 25, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "46", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E4138A3_3", "SegmentText": " ", "Start": 25, "End": 26, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "47", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1726855B", "PlainText": "Wwhere the death of a person is caused by a dog wounding or attacking the person and the person would (had death not ensued) have been entitled under section 25 of the Act to recover damages from the owner of the dog in respect of bodily injury caused by the wounding or attack, the wounding or attack is, for the purposes of the provisions of the HYPERLINK \"https://advance.lexis.com/api/document?collection=legislation-au&id=urn:contentItem:58X6-XFP1-JS5Y-B3VF-00000-00&context=1201009\"Compensation to Relatives Act 1897 (NSW) (CTR Act), taken to be a wrongful act such as would (had death not ensued) have entitled the injured person to maintain an action against, and recover damages from, the owner of the dog in respect of that act apply to the attack. ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "1726855B_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "49", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_1", "SegmentText": "w", "Start": 0, "End": 1, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "51", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_2", "SegmentText": "here the death of a person is caused by a dog wounding or attacking the person and the person would (had death not ensued) have been entitled under ", "Start": 1, "End": 149, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_3", "SegmentText": "s", "Start": 149, "End": 150, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_4", "SegmentText": "", "Start": 150, "End": 150, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "56", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_5", "SegmentText": " 25 ", "Start": 150, "End": 154, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_6", "SegmentText": "", "Start": 154, "End": 154, "Properties": {"Color": "", "FontSize": "20", "FontFamily": "Times New Roman", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_7", "SegmentText": "of the Act ", "Start": 154, "End": 165, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "60", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_8", "SegmentText": "to recover damages from the owner of the ", "Start": 165, "End": 206, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_9", "SegmentText": "dog in respect of bodily injury caused by the wounding or attack", "Start": 206, "End": 270, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_10", "SegmentText": ", the ", "Start": 270, "End": 276, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_11", "SegmentText": "", "Start": 276, "End": 276, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "65", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_12", "SegmentText": "", "Start": 276, "End": 276, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "67", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_13", "SegmentText": "", "Start": 276, "End": 276, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "69", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_14", "SegmentText": "provision", "Start": 276, "End": 285, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "71", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_15", "SegmentText": "s of the ", "Start": 285, "End": 294, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "73", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_16", "SegmentText": "", "Start": 294, "End": 294, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_17", "SegmentText": "", "Start": 294, "End": 294, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_18", "SegmentText": "", "Start": 294, "End": 294, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_19", "SegmentText": "", "Start": 294, "End": 294, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_20", "SegmentText": "Compensation to Relatives Act 1897", "Start": 294, "End": 328, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_21", "SegmentText": "", "Start": 328, "End": 328, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_22", "SegmentText": " (NSW) (CTR Act)", "Start": 328, "End": 344, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "76", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_23", "SegmentText": "", "Start": 344, "End": 344, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "78", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_24", "SegmentText": " appl", "Start": 344, "End": 349, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "80", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_25", "SegmentText": "y to the attack", "Start": 349, "End": 364, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "82", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1726855B_26", "SegmentText": ". ", "Start": 364, "End": 366, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1179CAE7", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "12825CC1", "PlainText": "See Topic: Claims under the Compensation to Relatives Act. ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "12825CC1_0", "SegmentText": "See Topic: ", "Start": 0, "End": 11, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "87", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "12825CC1_1", "SegmentText": "Claims under the Compensation to Relatives Act", "Start": 11, "End": 57, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "87", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "12825CC1_2", "SegmentText": "", "Start": 57, "End": 57, "Properties": {"Color": "", "FontSize": "", "FontFamily": "Times New Roman", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "89", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "12825CC1_3", "SegmentText": ". ", "Start": 57, "End": 59, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "90", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "3BD6BA1E", "PlainText": "Compensation for injuries arising from a dog attack", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "3BD6BA1E_0", "SegmentText": "Compensation for injuries arising from a dog attack", "Start": 0, "End": 51, "Properties": {"Color": "000000", "FontSize": "28", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "37AEE45F", "PlainText": "02540000", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "37AEE45F_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "4B7BD153", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "681A3B8F", "PlainText": "Despite the innocence of the dog owner, the dog owner can be held liable for any injuries caused to a person or their property by their dog.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "681A3B8F_0", "SegmentText": "Despite the innocence of the dog owner, the dog owner can be held liable for any injuries caused to a person or their property by their dog.", "Start": 0, "End": 140, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "516BD80E", "PlainText": "References: Bouali v Rangihuna [2020] NSWDC 720; BC202041099", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "516BD80E_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "516BD80E_1", "SegmentText": "Bouali v Rangihuna ", "Start": 12, "End": 31, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "516BD80E_2", "SegmentText": "", "Start": 31, "End": 31, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId38", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "516BD80E_3", "SegmentText": "; ", "Start": 31, "End": 33, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "516BD80E_4", "SegmentText": "", "Start": 33, "End": 33, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId39", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "314C7BA3", "PlainText": "The types of compensation that can be claimed as a result of an injury arising from a dog attack are those that are provided under the Civil Liability Act 2002 (NSW) (CLA):", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "314C7BA3_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "314C7BA3_1", "SegmentText": "The types of compensation that can be claimed as a result of an injury arising from a dog attack are those that are provided under the ", "Start": 0, "End": 135, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "314C7BA3_2", "SegmentText": "", "Start": 135, "End": 135, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId40", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "314C7BA3_3", "SegmentText": " (NSW)", "Start": 135, "End": 141, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "314C7BA3_4", "SegmentText": " (CLA)", "Start": 141, "End": 147, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "92", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "314C7BA3_5", "SegmentText": ":", "Start": 147, "End": 148, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "5E2FDAEE", "PlainText": "a lump sum of compensation for your pain and suffering;", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "5E2FDAEE_0", "SegmentText": "a lump sum of compensation for ", "Start": 0, "End": 31, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E2FDAEE_1", "SegmentText": "", "Start": 31, "End": 31, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "93", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E2FDAEE_2", "SegmentText": "pain and suffering;", "Start": 31, "End": 50, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "6CEEB792", "PlainText": "reimbursement for past and future treatment and hospital expenses;", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "6CEEB792_0", "SegmentText": "reimbursement for past and future treatment and hospital expenses;", "Start": 0, "End": 66, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1D055773", "PlainText": "reimbursement for past and future wage loss; and", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "1D055773_0", "SegmentText": "reimbursement for past and future wage loss; and", "Start": 0, "End": 48, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "70F8908B", "PlainText": "reimbursement for past and future domestic care and assistance.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "70F8908B_0", "SegmentText": "reimbursement for past and future domestic care and assistance.", "Start": 0, "End": 63, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "0ACE266D", "PlainText": "To be successful in achieving compensation from the dog owner, it is necessary to ascertain whether:", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "0ACE266D_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0ACE266D_1", "SegmentText": "To be successful in achieving compensation from the dog owner, it is necessary to ascertain whether:", "Start": 0, "End": 100, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "21A4D5E0", "PlainText": "the dog owner has an insurance policy (such as a home insurance policy which may cover such incidents) in place which covers them in the event that their dog causes injury; or", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "21A4D5E0_0", "SegmentText": "the dog owner has an insurance policy (such as a home insurance policy which may cover such incidents) in place which covers them in the event that their dog causes injury; or", "Start": 0, "End": 175, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "4BADC7A5", "PlainText": "the owner has sufficient funds or assets available to pay the compensation.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "4BADC7A5_0", "SegmentText": "the owner has sufficient funds or assets available to pay the compensation.", "Start": 0, "End": 75, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "64D0D38A", "PlainText": "Practice Tip: Request all medical records (including hospital records, clinical notes from doctors, physiotherapists, etc) in relation to the treatment that has been undertaken as a result of the injury. Arrange a medico-legal assessment once the injured claimant is stable to support the level of disability, and therefore compensation claimed as a result of the incident.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "64D0D38A_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_1", "SegmentText": "Practice Tip:", "Start": 0, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_2", "SegmentText": " Request all medical records (including hospital records, clinical notes from doctors, physiotherapists, etc) in relation to the treatment that has been undertaken as a result of the injury. <PERSON><PERSON>nge ", "Start": 13, "End": 212, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_3", "SegmentText": "a ", "Start": 212, "End": 214, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "94", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_4", "SegmentText": "medico-legal assessment once ", "Start": 214, "End": 243, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_5", "SegmentText": "the ", "Start": 243, "End": 247, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "95", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_6", "SegmentText": "injured claimant is ", "Start": 247, "End": 267, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "96", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "64D0D38A_7", "SegmentText": "stable to support the level of disability, and therefore compensation claimed as a result of the incident.", "Start": 267, "End": 373, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "3A5043BA", "PlainText": "Practice Tip: Consider whether there are any witnesses to the dog attack which may have suffered a recognised psychiatric injury as a consequence of witnessing the attack. Witnesses may be entitled to make a nervous shock claim.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "3A5043BA_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3A5043BA_1", "SegmentText": "Practice Tip:", "Start": 0, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3A5043BA_2", "SegmentText": " Consider whether there are any witnesses to the dog attack which may have suffered a recognised psychiatric injury as a consequence of witnessing the attack. Witnesses may be entitled to make a nervous shock claim.", "Start": 13, "End": 228, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "0783A62F", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7E7461FB", "PlainText": "See Heading: Who can obtain damages? in the Guidance Note: Nervous shock claims generally in the Topic: Nervous shock claims.  ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "7E7461FB_0", "SegmentText": "See Heading: ", "Start": 0, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "99", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7E7461FB_1", "SegmentText": "Who can obtain damages?", "Start": 13, "End": 36, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "99", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7E7461FB_2", "SegmentText": "", "Start": 36, "End": 36, "Properties": {"Color": "", "FontSize": "", "FontFamily": "Times New Roman", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "101", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7E7461FB_3", "SegmentText": " in the Guidance Note:", "Start": 36, "End": 58, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "102", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7E7461FB_4", "SegmentText": " Nervous shock claims generally in the Topic: Nervous shock claims.", "Start": 58, "End": 125, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "103", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7E7461FB_5", "SegmentText": "  ", "Start": 125, "End": 127, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "104", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "3443144F", "PlainText": "In <PERSON><PERSON><PERSON><PERSON><PERSON> v Cheum (Meimar<PERSON>oulos), the plaintiff was successful in obtaining compensation when she tripped and fell (causing injury) in attempt to avoid a dog attack.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "3443144F_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3443144F_1", "SegmentText": "In ", "Start": 0, "End": 3, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3443144F_2", "SegmentText": "Meimaropoulos v Cheum", "Start": 3, "End": 24, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3443144F_3", "SegmentText": " (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "Start": 24, "End": 40, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "105", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "3443144F_4", "SegmentText": ", the plaintiff was successful in obtaining compensation when she tripped and fell (causing injury) in attempt to avoid a dog attack.", "Start": 40, "End": 173, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "0C6ED718", "PlainText": "References: <PERSON><PERSON><PERSON><PERSON><PERSON> v Cheum [2014] NSWDC 26; BC201441061", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "0C6ED718_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C6ED718_1", "SegmentText": "Meimaropoulos v Cheum ", "Start": 12, "End": 34, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C6ED718_2", "SegmentText": "", "Start": 34, "End": 34, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId41", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C6ED718_3", "SegmentText": "; ", "Start": 34, "End": 36, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C6ED718_4", "SegmentText": "", "Start": 36, "End": 36, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId42", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "133E76A2", "PlainText": "Liability for injury to animal", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "133E76A2_0", "SegmentText": "Liability for injury to animal", "Start": 0, "End": 30, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "5E59E031", "PlainText": "Section 27(1) of the Act provides that: the dog owner is liable for damages in respect of an injury to another animal arising from a dog attack.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "5E59E031_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E59E031_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId43", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E59E031_2", "SegmentText": "(1)", "Start": 0, "End": 3, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "106", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E59E031_3", "SegmentText": " of the Act provides that", "Start": 3, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E59E031_4", "SegmentText": "", "Start": 28, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "107", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E59E031_5", "SegmentText": " the dog owner is liable for damages in", "Start": 28, "End": 67, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "108", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5E59E031_6", "SegmentText": " respect of an injury to another animal arising from a dog attack.", "Start": 67, "End": 133, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "109", "PropRevisions": [], "CommentIds": []}]}, {"Key": "TableElement", "ElementType": "Table", "ElementId": "tbl_0", "Rows": [{"RowId": 0, "Cells": [{"CellId": 0, "Elements": [{"ElementType": "Paragraph", "ElementId": "38FAB29C", "PlainText": "", "Properties": {"Style": ""}, "Segments": []}]}, {"CellId": 1, "Elements": [{"ElementType": "Paragraph", "ElementId": "04A2D95A", "PlainText": "(1)", "Properties": {"Style": ""}, "Segments": [{"SegmentId": "04A2D95A_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "110", "PropRevisions": [], "CommentIds": []}]}]}, {"CellId": 2, "Elements": [{"ElementType": "Paragraph", "ElementId": "7678FA87", "PlainText": "The owner of a dog is liable in damages in respect of injury (whether or not fatal) to another animal (whether or not a dog, but other than vermin) caused by the dog attacking or chasing it.", "Properties": {"Style": ""}, "Segments": [{"SegmentId": "7678FA87_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "18", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "111", "PropRevisions": [], "CommentIds": []}]}]}]}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "40705BF2", "PlainText": "However, HYPERLINK \"https://advance.lexis.com/api/document?collection=legislation-au&id=urn:contentItem:58X6-XG01-F873-B0S0-00000-00&context=1201009\"subs 27(2) of the Act provides for exceptions such as if:", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "40705BF2_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_1", "SegmentText": "However, ", "Start": 0, "End": 9, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_2", "SegmentText": "", "Start": 9, "End": 9, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_3", "SegmentText": "", "Start": 9, "End": 9, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_4", "SegmentText": "", "Start": 9, "End": 9, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_5", "SegmentText": "s", "Start": 9, "End": 10, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_6", "SegmentText": "", "Start": 10, "End": 10, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "112", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_7", "SegmentText": " ", "Start": 10, "End": 11, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_8", "SegmentText": "27", "Start": 11, "End": 13, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "113", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_9", "SegmentText": "(2)", "Start": 13, "End": 16, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_10", "SegmentText": "", "Start": 16, "End": 16, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_11", "SegmentText": " ", "Start": 16, "End": 17, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_12", "SegmentText": "of the Act ", "Start": 17, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "114", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_13", "SegmentText": "provides for exception", "Start": 28, "End": 50, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_14", "SegmentText": "s", "Start": 50, "End": 51, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "115", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "40705BF2_15", "SegmentText": " such as if:", "Start": 51, "End": 63, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "318438AB", "PlainText": "a dog attacksing or chasesing another animal on their occupier’s property where it is normally kept, except in the cases of dangerous dogs;", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "318438AB_0", "SegmentText": "a dog attack", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_1", "SegmentText": "s", "Start": 12, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "116", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_2", "SegmentText": "", "Start": 13, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "117", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_3", "SegmentText": " or chas", "Start": 13, "End": 21, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_4", "SegmentText": "es", "Start": 21, "End": 23, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "118", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_5", "SegmentText": "", "Start": 23, "End": 23, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "119", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_6", "SegmentText": " another animal on the", "Start": 23, "End": 45, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_7", "SegmentText": "", "Start": 45, "End": 45, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "120", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "318438AB_8", "SegmentText": " property where it is normally kept, except in the cases of dangerous dogs;", "Start": 45, "End": 120, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "387EC539", "PlainText": "if a dog is attacksing another animal in the course of droving, tending, working or protecting stock;", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "387EC539_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "121", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "387EC539_1", "SegmentText": "a dog ", "Start": 0, "End": 6, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "387EC539_2", "SegmentText": "", "Start": 6, "End": 6, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "122", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "387EC539_3", "SegmentText": "attack", "Start": 6, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "387EC539_4", "SegmentText": "s", "Start": 12, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "123", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "387EC539_5", "SegmentText": "", "Start": 13, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "124", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "387EC539_6", "SegmentText": " another animal in the course of droving, tending, working or protecting stock;", "Start": 13, "End": 92, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7260F206", "PlainText": "if the dog is intentionally provoked by another dog or by a person; or", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "7260F206_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "125", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7260F206_1", "SegmentText": "the dog is intentionally provoked by another dog ", "Start": 0, "End": 49, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7260F206_2", "SegmentText": "or ", "Start": 49, "End": 52, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "126", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7260F206_3", "SegmentText": "by a person; or", "Start": 52, "End": 67, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "0C31B39D", "PlainText": "if it a dog is attacksing in response to self-defense. ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "0C31B39D_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "127", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "128", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_2", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "129", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_3", "SegmentText": "a dog ", "Start": 0, "End": 6, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "130", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_4", "SegmentText": "", "Start": 6, "End": 6, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "131", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_5", "SegmentText": "attack", "Start": 6, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_6", "SegmentText": "s", "Start": 12, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "132", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_7", "SegmentText": "", "Start": 13, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "133", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "0C31B39D_8", "SegmentText": " in response to self-defense. ", "Start": 13, "End": 43, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "69C44302", "PlainText": "In <PERSON> v <PERSON><PERSON><PERSON>, it was held that states that HYPERLINK \"https://advance.lexis.com/api/document?collection=legislation-au&id=urn:contentItem:58X6-XG01-F873-B0S0-00000-00&context=1201009\"s 25 are damages to a person or their personal property, and HYPERLINK \"https://advance.lexis.com/api/document?collection=legislation-au&id=urn:contentItem:58X6-XG01-F873-B0S0-00000-00&context=1201009\"s 27 for damage to another animal. Iin construing s 27 of the Act, the court will consider the nature of the dog and location of the attack. In this case, it was confirmed that the dog was on his occupier’s property, where he is ordinarily kept, and was not a dangerous breed. This meants that the owner was not liable for damage to another animal under civil liability. ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "69C44302_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_1", "SegmentText": "In ", "Start": 0, "End": 3, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_2", "SegmentText": "<PERSON>", "Start": 3, "End": 8, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_3", "SegmentText": "", "Start": 8, "End": 8, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "134", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_4", "SegmentText": ", it ", "Start": 8, "End": 13, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_5", "SegmentText": "was held that ", "Start": 13, "End": 27, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "135", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_6", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_7", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_8", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_9", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_10", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_11", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_12", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_13", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_14", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_15", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_16", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_17", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_18", "SegmentText": "", "Start": 27, "End": 27, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "136", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_19", "SegmentText": "i", "Start": 27, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "137", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_20", "SegmentText": "n construing ", "Start": 28, "End": 41, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_21", "SegmentText": "", "Start": 41, "End": 41, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId44", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_22", "SegmentText": " of the Act", "Start": 41, "End": 52, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "138", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_23", "SegmentText": ", the court will consider the nature of the dog and location of the attack. In this case, it was confirmed that the dog was on his occupier’s property, where he is ordinarily kept, and was not a dangerous breed. This mean", "Start": 52, "End": 273, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_24", "SegmentText": "t", "Start": 273, "End": 274, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "139", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_25", "SegmentText": "", "Start": 274, "End": 274, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "140", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_26", "SegmentText": " that", "Start": 274, "End": 279, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "141", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_27", "SegmentText": " the owner was not liable for damage to another animal", "Start": 279, "End": 333, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_28", "SegmentText": "", "Start": 333, "End": 333, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "142", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "69C44302_29", "SegmentText": ". ", "Start": 333, "End": 335, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "294B8DAD", "PlainText": "References: <PERSON> v <PERSON><PERSON><PERSON> [2013] NSWCA 388; BC201314902", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "294B8DAD_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "294B8DAD_1", "SegmentText": "Simon v Condran ", "Start": 12, "End": 28, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "294B8DAD_2", "SegmentText": "", "Start": 28, "End": 28, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId45", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "294B8DAD_3", "SegmentText": "; ", "Start": 28, "End": 30, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "294B8DAD_4", "SegmentText": "", "Start": 30, "End": 30, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId46", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "5286F98B", "PlainText": "Contributory negligence", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "5286F98B_0", "SegmentText": "Contributory negligence", "Start": 0, "End": 23, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "1F201F37", "PlainText": "Section 28 of the Act provides that the court will look at partly the wrong of the owner of the dog and partly atof the contributory negligence of the person who suffered the damage. ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "1F201F37_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1F201F37_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId47", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1F201F37_2", "SegmentText": " of the Act provides that the court will look at partly the wrong of the owner of the dog and partly ", "Start": 0, "End": 101, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1F201F37_3", "SegmentText": "at", "Start": 101, "End": 103, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "143", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1F201F37_4", "SegmentText": "", "Start": 103, "End": 103, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "144", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "1F201F37_5", "SegmentText": " the contributory negligence of the person who suffered the damage. ", "Start": 103, "End": 171, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "2E5A5307", "PlainText": "In <PERSON><PERSON><PERSON><PERSON><PERSON> v Cheum, the court considered whether the plaintiff’s response to the circumstances in which a small barking dog ran around and attempted to jump up on her was appropriate ias a matter going to contributory negligence. It was found the plaintiff did not fall because the dog had knocked her over but rather the fall was caused by the plaintiff stepping backwards and failed to look where she was going. The plaintiff was successful in her A modest allowance of 15% for contributory negligence claim but it was only for a modest allowancewas allowed as she the plaintiff was aware of the dogs on her street, and the event of a small dog barking around her should not have prevented her to looking at where she wais stepping.", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "2E5A5307_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_1", "SegmentText": "In ", "Start": 0, "End": 3, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_2", "SegmentText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Start": 3, "End": 16, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_3", "SegmentText": "", "Start": 16, "End": 16, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "145", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_4", "SegmentText": ", the court considered whether the plaintiff’s response to the circumstances in which a ", "Start": 16, "End": 104, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_5", "SegmentText": "small barking dog ran around and attempt", "Start": 104, "End": 144, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_6", "SegmentText": "ed", "Start": 144, "End": 146, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "146", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_7", "SegmentText": " to jump up on her was appropriate ", "Start": 146, "End": 181, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_8", "SegmentText": "", "Start": 181, "End": 181, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "147", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_9", "SegmentText": "a", "Start": 181, "End": 182, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "148", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_10", "SegmentText": "s a matter going to contributory negligence. It was found the plaintiff did not fall because the dog had knocked her over but rather the fall was caused by the plaintiff stepping backwards and failed to look where she was going. ", "Start": 182, "End": 411, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_11", "SegmentText": "", "Start": 411, "End": 411, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "149", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_12", "SegmentText": "", "Start": 411, "End": 411, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "150", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_13", "SegmentText": "A modest allowance of 15% ", "Start": 411, "End": 437, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "151", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_14", "SegmentText": "for ", "Start": 437, "End": 441, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "152", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_15", "SegmentText": "contributory negligence ", "Start": 441, "End": 465, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_16", "SegmentText": "", "Start": 465, "End": 465, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "153", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_17", "SegmentText": "", "Start": 465, "End": 465, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "154", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_18", "SegmentText": "was ", "Start": 465, "End": 469, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "155", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_19", "SegmentText": "allowed", "Start": 469, "End": 476, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "156", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_20", "SegmentText": " as ", "Start": 476, "End": 480, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_21", "SegmentText": "", "Start": 480, "End": 480, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "157", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_22", "SegmentText": "the ", "Start": 480, "End": 484, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "158", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_23", "SegmentText": "plaintiff ", "Start": 484, "End": 494, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "159", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_24", "SegmentText": "was aware of the dogs on her street", "Start": 494, "End": 529, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_25", "SegmentText": ",", "Start": 529, "End": 530, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "160", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_26", "SegmentText": " and the event of a small dog barking around her should not ", "Start": 530, "End": 590, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_27", "SegmentText": "have ", "Start": 590, "End": 595, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "161", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_28", "SegmentText": "prevent", "Start": 595, "End": 602, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_29", "SegmentText": "ed", "Start": 602, "End": 604, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "162", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_30", "SegmentText": " her ", "Start": 604, "End": 609, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_31", "SegmentText": "", "Start": 609, "End": 609, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "163", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_32", "SegmentText": "look", "Start": 609, "End": 613, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_33", "SegmentText": "ing", "Start": 613, "End": 616, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "164", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_34", "SegmentText": " at where she ", "Start": 616, "End": 630, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_35", "SegmentText": "wa", "Start": 630, "End": 632, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "165", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_36", "SegmentText": "", "Start": 632, "End": 632, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "166", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "2E5A5307_37", "SegmentText": "s stepping.", "Start": 632, "End": 643, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "5A300969", "PlainText": "References: <PERSON><PERSON><PERSON><PERSON><PERSON> v Cheum [2014] NSWDC 26; BC201441061", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "5A300969_0", "SegmentText": "References: ", "Start": 0, "End": 12, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5A300969_1", "SegmentText": "Meimaropoulos v Cheum ", "Start": 12, "End": 34, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5A300969_2", "SegmentText": "", "Start": 34, "End": 34, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId48", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5A300969_3", "SegmentText": "; ", "Start": 34, "End": 36, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "5A300969_4", "SegmentText": "", "Start": 36, "End": 36, "Properties": {"Color": "0077CC", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "true", "Underline": "true"}, "FieldId": "", "HyberlinkId": "rId49", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "7FE5E120", "PlainText": "There’s an old saying that “you can’t get blood out of a stone”. Unfortunately, this saying applies here. Iif the dog owner is not insured and does not own sufficient assets or have sufficient finances to respond to the claim, for compensation may not be recoverable. This will not prohibit the owner from being criminally liable for the incident. Further to this, it is possible that the dog will be seized and/or destroyed, and the owner may be prevented from owning another dog in the future. ", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "7FE5E120_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "167", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_2", "SegmentText": "Unfortunately, ", "Start": 0, "End": 15, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_3", "SegmentText": "", "Start": 15, "End": 15, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "168", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_4", "SegmentText": "i", "Start": 15, "End": 16, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "169", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_5", "SegmentText": "f the dog owner is not insured and does not own sufficient assets or have sufficient finances to respond to the claim", "Start": 16, "End": 133, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_6", "SegmentText": ",", "Start": 133, "End": 134, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "170", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_7", "SegmentText": " ", "Start": 134, "End": 135, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_8", "SegmentText": "", "Start": 135, "End": 135, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "171", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "7FE5E120_9", "SegmentText": "compensation may not be recoverable. This will not prohibit the owner from being criminally liable for the incident. Further to this, it is possible that the dog will be seized and/or destroyed, and the owner may be prevented from owning another dog in the future. ", "Start": 135, "End": 400, "Properties": {"Color": "000000", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "30FA7C9E", "PlainText": "", "Properties": {"Style": "Normal1"}, "Segments": []}, {"Key": "ParagraphElement", "ElementType": "Paragraph", "ElementId": "148B73C5", "PlainText": "012700000End of Document", "Properties": {"Style": "Normal1"}, "Segments": [{"SegmentId": "148B73C5_0", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "", "Italic": "", "Underline": ""}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "148B73C5_1", "SegmentText": "", "Start": 0, "End": 0, "Properties": {"Color": "", "FontSize": "", "FontFamily": "", "Bold": "false", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}, {"SegmentId": "148B73C5_2", "SegmentText": "End of Document", "Start": 0, "End": 15, "Properties": {"Color": "767676", "FontSize": "16", "FontFamily": "", "Bold": "true", "Italic": "false", "Underline": "false"}, "FieldId": "", "HyberlinkId": "", "ContentRevisionId": "", "PropRevisions": [], "CommentIds": []}]}], "Annotations": {"Comments": {"41": {"CommentId": "41", "Author": "Newling, Ben (LNG-HBE)", "Date": "2025-04-29T14:52:00Z", "Text": "Please link to:https://advance.lexis.com/document/?pdmfid=1201009&crid=640a1f82-bc31-4317-b8e5-341223b7c181&pddocfullpath=%2Fshared%2Fdocument%2Fpractical-guidance-au%2Furn%3AcontentItem%3A5D23-T3X1-DYMS-62DW-00000-00&pdcontentcomponentid=375430&pdteaserkey=sr0&pditab=allpods&pddocpracticeareas=urn%3Akrm%3A252C14DFE10E4F95AED8B391AF563B2F&ecomp=qthhk&earg=sr0&prid=530aec68-df3f-4016-a702-595290d68989", "Targets": ["1567253A_1"]}, "54": {"CommentId": "54", "Author": "Newling, Ben (LNG-HBE)", "Date": "2025-04-29T14:55:00Z", "Text": "Please link to:https://advance.lexis.com/document?crid=26df6d34-3f60-4e5c-b9da-9a4ab993499e&pddocfullpath=%2Fshared%2Fdocument%2Flegislation-au%2Furn%3AcontentItem%3A58X6-XG01-F873-B0S0-00000-00&pdsourcegroupingtype=&pdcontentcomponentid=267877&pdmfid=1201009&pdisurlapi=true", "Targets": ["1726855B_3", "1726855B_4", "1726855B_5"]}, "88": {"CommentId": "88", "Author": "Newling, Ben (LNG-HBE)", "Date": "2025-04-29T15:00:00Z", "Text": "Please link to:https://advance.lexis.com/LnpgBrowseV2?pdmfid=1201009&config=0154JAAzMDJmNjJhMi00M2EyLTQ4MzQtYWRlOS04NTFlYzc5OTdiOTYKAFBvZENhdGFsb2fiAW4jHfvp2DnSyXWlcdEY&pdcurrmodid=1013016&pdcurrtopid=2641&pdcurrsubtid=2680&pdactivecontenttype=Snapshot&earg=2680&prid=072b0410-a2cd-40c9-999d-621cdb684249&crid=0b4a884e-9809-44eb-b613-d9d4a2691898#/subtopic-browse-page?pdmfid=1201009&config=0154JAAzMDJmNjJhMi00M2EyLTQ4MzQtYWRlOS04NTFlYzc5OTdiOTYKAFBvZENhdGFsb2fiAW4jHfvp2DnSyXWlcdEY&pdcurrmodid=1013016&pdcurrtopid=2641&pdcurrsubtid=2680&pdactivecontenttype=Snapshot&earg=2680&prid=072b0410-a2cd-40c9-999d-621cdb684249&crid=0b4a884e-9809-44eb-b613-d9d4a2691898", "Targets": ["12825CC1_1"]}, "100": {"CommentId": "100", "Author": "Newling, Ben (LNG-HBE)", "Date": "2025-05-14T11:04:00Z", "Text": "Please link to:https://advance.lexis.com/document/lpadocument/?pdmfid=1201009&crid=3d46c5fe-bbed-4197-a1f4-1192514e1f79&pddocfullpath=%2Fshared%2Fdocument%2Fpractical-guidance-au%2Furn%3AcontentItem%3A5D23-T3X1-DYMS-62DC-00000-00&pdcomponentid=375430&pdpinpoint=CITEID_17940&pdcontentcomponentid=375430&pdcurrmodid=1013016&prid=c16c2f33-3ff2-4f34-bf18-96d2b417b3bd", "Targets": ["7E7461FB_1"]}}, "Hyperlinks": {"rId8": {"HyperlinkId": "rId8", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId9": {"HyperlinkId": "rId9", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId22": {"HyperlinkId": "rId22", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId23": {"HyperlinkId": "rId23", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId24": {"HyperlinkId": "rId24", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId25": {"HyperlinkId": "rId25", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId26": {"HyperlinkId": "rId26", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId27": {"HyperlinkId": "rId27", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId28": {"HyperlinkId": "rId28", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId29": {"HyperlinkId": "rId29", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId30": {"HyperlinkId": "rId30", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId31": {"HyperlinkId": "rId31", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId32": {"HyperlinkId": "rId32", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId37": {"HyperlinkId": "rId37", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId38": {"HyperlinkId": "rId38", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId39": {"HyperlinkId": "rId39", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId40": {"HyperlinkId": "rId40", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId41": {"HyperlinkId": "rId41", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId42": {"HyperlinkId": "rId42", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId43": {"HyperlinkId": "rId43", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId44": {"HyperlinkId": "rId44", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId45": {"HyperlinkId": "rId45", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId46": {"HyperlinkId": "rId46", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId47": {"HyperlinkId": "rId47", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId48": {"HyperlinkId": "rId48", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}, "rId49": {"HyperlinkId": "rId49", "Uri": "http://schemas.openxmlformats.org/wordprocessingml/2006/main"}}}, "NativeRevisions": {"1": {"Key": "NativeRevision", "RevId": "1", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:55:00"}, "3": {"Key": "NativeRevision", "RevId": "3", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:57:00"}, "4": {"Key": "NativeRevision", "RevId": "4", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "7": {"Key": "NativeRevision", "RevId": "7", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "9": {"Key": "NativeRevision", "RevId": "9", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "11": {"Key": "NativeRevision", "RevId": "11", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "13": {"Key": "NativeRevision", "RevId": "13", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "15": {"Key": "NativeRevision", "RevId": "15", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "17": {"Key": "NativeRevision", "RevId": "17", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "18": {"Key": "NativeRevision", "RevId": "18", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:59:00"}, "19": {"Key": "NativeRevision", "RevId": "19", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:59:00"}, "20": {"Key": "NativeRevision", "RevId": "20", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:56:00"}, "21": {"Key": "NativeRevision", "RevId": "21", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:58:00"}, "22": {"Key": "NativeRevision", "RevId": "22", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:59:00"}, "23": {"Key": "NativeRevision", "RevId": "23", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 10:58:00"}, "24": {"Key": "NativeRevision", "RevId": "24", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:45:00"}, "25": {"Key": "NativeRevision", "RevId": "25", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/30/2025 14:28:00"}, "27": {"Key": "NativeRevision", "RevId": "27", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:50:00"}, "29": {"Key": "NativeRevision", "RevId": "29", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:04:00"}, "30": {"Key": "NativeRevision", "RevId": "30", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:05:00"}, "31": {"Key": "NativeRevision", "RevId": "31", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:06:00"}, "32": {"Key": "NativeRevision", "RevId": "32", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:07:00"}, "33": {"Key": "NativeRevision", "RevId": "33", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:08:00"}, "34": {"Key": "NativeRevision", "RevId": "34", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:08:00"}, "35": {"Key": "NativeRevision", "RevId": "35", "RevType": "InsertedRun", "Author": "<PERSON>, <PERSON><PERSON> (LNG-LON)", "Date": "05/19/2025 14:08:00"}, "36": {"Key": "NativeRevision", "RevId": "36", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:00:00"}, "38": {"Key": "NativeRevision", "RevId": "38", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:46:00"}, "40": {"Key": "NativeRevision", "RevId": "40", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:51:00"}, "42": {"Key": "NativeRevision", "RevId": "42", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:52:00"}, "43": {"Key": "NativeRevision", "RevId": "43", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:51:00"}, "46": {"Key": "NativeRevision", "RevId": "46", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "47": {"Key": "NativeRevision", "RevId": "47", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "49": {"Key": "NativeRevision", "RevId": "49", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "51": {"Key": "NativeRevision", "RevId": "51", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "56": {"Key": "NativeRevision", "RevId": "56", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:55:00"}, "60": {"Key": "NativeRevision", "RevId": "60", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:55:00"}, "65": {"Key": "NativeRevision", "RevId": "65", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:56:00"}, "67": {"Key": "NativeRevision", "RevId": "67", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:58:00"}, "69": {"Key": "NativeRevision", "RevId": "69", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:56:00"}, "71": {"Key": "NativeRevision", "RevId": "71", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:56:00"}, "73": {"Key": "NativeRevision", "RevId": "73", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:57:00"}, "76": {"Key": "NativeRevision", "RevId": "76", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:57:00"}, "78": {"Key": "NativeRevision", "RevId": "78", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:57:00"}, "80": {"Key": "NativeRevision", "RevId": "80", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:57:00"}, "82": {"Key": "NativeRevision", "RevId": "82", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:58:00"}, "87": {"Key": "NativeRevision", "RevId": "87", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:59:00"}, "89": {"Key": "NativeRevision", "RevId": "89", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 15:00:00"}, "90": {"Key": "NativeRevision", "RevId": "90", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:59:00"}, "92": {"Key": "NativeRevision", "RevId": "92", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:52:00"}, "93": {"Key": "NativeRevision", "RevId": "93", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:01:00"}, "94": {"Key": "NativeRevision", "RevId": "94", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:01:00"}, "95": {"Key": "NativeRevision", "RevId": "95", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:01:00"}, "96": {"Key": "NativeRevision", "RevId": "96", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:02:00"}, "99": {"Key": "NativeRevision", "RevId": "99", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:03:00"}, "101": {"Key": "NativeRevision", "RevId": "101", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:04:00"}, "102": {"Key": "NativeRevision", "RevId": "102", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:03:00"}, "103": {"Key": "NativeRevision", "RevId": "103", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:04:00"}, "104": {"Key": "NativeRevision", "RevId": "104", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:03:00"}, "105": {"Key": "NativeRevision", "RevId": "105", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "106": {"Key": "NativeRevision", "RevId": "106", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:07:00"}, "107": {"Key": "NativeRevision", "RevId": "107", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:07:00"}, "108": {"Key": "NativeRevision", "RevId": "108", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:07:00"}, "109": {"Key": "NativeRevision", "RevId": "109", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "110": {"Key": "NativeRevision", "RevId": "110", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "111": {"Key": "NativeRevision", "RevId": "111", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "112": {"Key": "NativeRevision", "RevId": "112", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "113": {"Key": "NativeRevision", "RevId": "113", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "114": {"Key": "NativeRevision", "RevId": "114", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "115": {"Key": "NativeRevision", "RevId": "115", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:54:00"}, "116": {"Key": "NativeRevision", "RevId": "116", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "117": {"Key": "NativeRevision", "RevId": "117", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "118": {"Key": "NativeRevision", "RevId": "118", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "119": {"Key": "NativeRevision", "RevId": "119", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "120": {"Key": "NativeRevision", "RevId": "120", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:50:00"}, "121": {"Key": "NativeRevision", "RevId": "121", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "122": {"Key": "NativeRevision", "RevId": "122", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "123": {"Key": "NativeRevision", "RevId": "123", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "124": {"Key": "NativeRevision", "RevId": "124", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "125": {"Key": "NativeRevision", "RevId": "125", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "126": {"Key": "NativeRevision", "RevId": "126", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:50:00"}, "127": {"Key": "NativeRevision", "RevId": "127", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "128": {"Key": "NativeRevision", "RevId": "128", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:09:00"}, "129": {"Key": "NativeRevision", "RevId": "129", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:08:00"}, "130": {"Key": "NativeRevision", "RevId": "130", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:09:00"}, "131": {"Key": "NativeRevision", "RevId": "131", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "132": {"Key": "NativeRevision", "RevId": "132", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "133": {"Key": "NativeRevision", "RevId": "133", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/15/2025 07:49:00"}, "134": {"Key": "NativeRevision", "RevId": "134", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:53:00"}, "135": {"Key": "NativeRevision", "RevId": "135", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:53:00"}, "136": {"Key": "NativeRevision", "RevId": "136", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:53:00"}, "137": {"Key": "NativeRevision", "RevId": "137", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:53:00"}, "138": {"Key": "NativeRevision", "RevId": "138", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "04/29/2025 14:53:00"}, "139": {"Key": "NativeRevision", "RevId": "139", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:09:00"}, "140": {"Key": "NativeRevision", "RevId": "140", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:09:00"}, "141": {"Key": "NativeRevision", "RevId": "141", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:09:00"}, "142": {"Key": "NativeRevision", "RevId": "142", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:09:00"}, "143": {"Key": "NativeRevision", "RevId": "143", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "144": {"Key": "NativeRevision", "RevId": "144", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "145": {"Key": "NativeRevision", "RevId": "145", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "146": {"Key": "NativeRevision", "RevId": "146", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "147": {"Key": "NativeRevision", "RevId": "147", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "148": {"Key": "NativeRevision", "RevId": "148", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:10:00"}, "149": {"Key": "NativeRevision", "RevId": "149", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:14:00"}, "150": {"Key": "NativeRevision", "RevId": "150", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:12:00"}, "151": {"Key": "NativeRevision", "RevId": "151", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:14:00"}, "152": {"Key": "NativeRevision", "RevId": "152", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:12:00"}, "153": {"Key": "NativeRevision", "RevId": "153", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:14:00"}, "154": {"Key": "NativeRevision", "RevId": "154", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:12:00"}, "155": {"Key": "NativeRevision", "RevId": "155", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:12:00"}, "156": {"Key": "NativeRevision", "RevId": "156", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:14:00"}, "157": {"Key": "NativeRevision", "RevId": "157", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:12:00"}, "158": {"Key": "NativeRevision", "RevId": "158", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:12:00"}, "159": {"Key": "NativeRevision", "RevId": "159", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:14:00"}, "160": {"Key": "NativeRevision", "RevId": "160", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:15:00"}, "161": {"Key": "NativeRevision", "RevId": "161", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:11:00"}, "162": {"Key": "NativeRevision", "RevId": "162", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:11:00"}, "163": {"Key": "NativeRevision", "RevId": "163", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:11:00"}, "164": {"Key": "NativeRevision", "RevId": "164", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:11:00"}, "165": {"Key": "NativeRevision", "RevId": "165", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:15:00"}, "166": {"Key": "NativeRevision", "RevId": "166", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/14/2025 11:15:00"}, "167": {"Key": "NativeRevision", "RevId": "167", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/13/2025 16:26:00"}, "168": {"Key": "NativeRevision", "RevId": "168", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/13/2025 16:26:00"}, "169": {"Key": "NativeRevision", "RevId": "169", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/13/2025 16:26:00"}, "170": {"Key": "NativeRevision", "RevId": "170", "RevType": "InsertedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/13/2025 16:26:00"}, "171": {"Key": "NativeRevision", "RevId": "171", "RevType": "DeletedRun", "Author": "Newling, Ben (LNG-HBE)", "Date": "05/13/2025 16:26:00"}}}