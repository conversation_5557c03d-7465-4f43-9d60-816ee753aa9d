using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Common.Model;
using Convert.Model;
using Xunit;

namespace Convert.Tests.Model
{
    public class ParagraphInfoTests
    {
        [Fact]
        public void ParagraphInfo_ShouldInitializeWithRequiredProperties()
        {
            // Arrange & Act
            var paragraphInfo = new ParagraphInfo
            {
                ParaId = "para-001",
                Text = "This is a sample paragraph.",
                StyleProperties = new Dictionary<string, string>
                {
                    ["Style"] = "Heading1",
                    ["FontSize"] = "14pt"
                }
            };

            // Assert
            Assert.Equal("para-001", paragraphInfo.ParaId);
            Assert.Equal("This is a sample paragraph.", paragraphInfo.Text);
            Assert.Empty(paragraphInfo.Runs);
            Assert.Equal(2, paragraphInfo.StyleProperties.Count);
            Assert.Equal("Heading1", paragraphInfo.StyleProperties["Style"]);
            Assert.Equal("14pt", paragraphInfo.StyleProperties["FontSize"]);
        }

        [Fact]
        public void ParagraphInfo_WithNormalRuns_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var paragraphInfo = new ParagraphInfo
            {
                ParaId = "para-002",
                Text = "This text has multiple normal runs.",
                Runs = new List<Run>
                {
                    new NormalRun
                    {
                        Text = "This text "
                    },
                    new NormalRun
                    {
                        Text = "has multiple "
                    },
                    new NormalRun
                    {
                        Text = "normal runs."
                    }
                }
            };

            // Assert
            Assert.Equal("para-002", paragraphInfo.ParaId);
            Assert.Equal(3, paragraphInfo.Runs.Count);
            Assert.All(paragraphInfo.Runs, run => Assert.IsType<NormalRun>(run));
            
            var normalRuns = paragraphInfo.Runs.Cast<NormalRun>().ToArray();
            Assert.Equal("This text ", normalRuns[0].Text);
            Assert.Equal("has multiple ", normalRuns[1].Text);
            Assert.Equal("normal runs.", normalRuns[2].Text);
        }

        [Fact]
        public void ParagraphInfo_WithRevisionRuns_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var paragraphInfo = new ParagraphInfo
            {
                ParaId = "para-003",
                Text = "This text has revision runs.",
                Runs = new List<Run>
                {
                    new NormalRun
                    {
                        Text = "This text "
                    },
                    new RevisionRun
                    {
                        Id = "rev-001",
                        Type = "Insert",
                        Text = new List<string> { "has revision ", "runs." },
                        Author = "John Doe",
                        Date = "2024-06-10T10:30:00Z",
                        Links = { new LinkInfo { Uri = "https://example.com", DisplayText = "Link text" } }
                    }
                }
            };

            // Assert
            Assert.Equal("para-003", paragraphInfo.ParaId);
            Assert.Equal(2, paragraphInfo.Runs.Count);
            Assert.IsType<NormalRun>(paragraphInfo.Runs[0]);
            Assert.IsType<RevisionRun>(paragraphInfo.Runs[1]);
            
            var normalRun = (NormalRun)paragraphInfo.Runs[0];
            Assert.Equal("This text ", normalRun.Text);
            
            var revisionRun = (RevisionRun)paragraphInfo.Runs[1];
            Assert.Equal("rev-001", revisionRun.Id);
            Assert.Equal("Insert", revisionRun.Type);
            Assert.Equal(2, revisionRun.Text.Count);
            Assert.Equal("has revision ", revisionRun.Text[0]);
            Assert.Equal("runs.", revisionRun.Text[1]);
            Assert.Equal("John Doe", revisionRun.Author);
            Assert.Equal("2024-06-10T10:30:00Z", revisionRun.Date);
            Assert.Single(revisionRun.Links);
            Assert.Equal("https://example.com", revisionRun.Links[0].Uri);
            Assert.Equal("Link text", revisionRun.Links[0].DisplayText);
        }

        [Fact]
        public void ParagraphInfo_WithMixedRuns_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var paragraphInfo = new ParagraphInfo
            {
                ParaId = "para-004",
                Text = "Mixed content with normal and revision runs.",
                Runs = new List<Run>
                {
                    new NormalRun { Text = "Mixed content " },
                    new RevisionRun
                    {
                        Id = "ins-001",
                        Type = "Insert",
                        Text = new List<string> { "with normal " },
                        Author = "Editor 1",
                        Date = "2024-06-10T09:00:00Z"
                    },
                    new NormalRun { Text = "and " },
                    new RevisionRun
                    {
                        Id = "del-001",
                        Type = "Delete",
                        Text = new List<string> { "revision " },
                        Author = "Editor 2",
                        Date = "2024-06-10T10:00:00Z"
                    },
                    new NormalRun { Text = "runs." }
                }
            };

            // Assert
            Assert.Equal("para-004", paragraphInfo.ParaId);
            Assert.Equal(5, paragraphInfo.Runs.Count);
            
            // Check alternating pattern
            Assert.IsType<NormalRun>(paragraphInfo.Runs[0]);
            Assert.IsType<RevisionRun>(paragraphInfo.Runs[1]);
            Assert.IsType<NormalRun>(paragraphInfo.Runs[2]);
            Assert.IsType<RevisionRun>(paragraphInfo.Runs[3]);
            Assert.IsType<NormalRun>(paragraphInfo.Runs[4]);

            var insertRevision = (RevisionRun)paragraphInfo.Runs[1];
            Assert.Equal("Insert", insertRevision.Type);
            Assert.Equal("Editor 1", insertRevision.Author);

            var deleteRevision = (RevisionRun)paragraphInfo.Runs[3];
            Assert.Equal("Delete", deleteRevision.Type);
            Assert.Equal("Editor 2", deleteRevision.Author);
        }

        [Fact]
        public void RevisionRun_WithMultipleLinks_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var revisionRun = new RevisionRun
            {
                Id = "rev-links",
                Type = "Insert",
                Text = new List<string> { "Check out ", "these links ", "for more info." },
                Author = "Link Editor",
                Date = "2024-06-10T11:00:00Z"
            };

            // Add links to the revision
            revisionRun.Links.Add(new LinkInfo { Uri = "https://docs.example.com", DisplayText = "Documentation" });
            revisionRun.Links.Add(new LinkInfo { Uri = "https://help.example.com", DisplayText = "Help Center" });

            // Assert
            Assert.Equal("rev-links", revisionRun.Id);
            Assert.Equal("Insert", revisionRun.Type);
            Assert.Equal(3, revisionRun.Text.Count);
            Assert.Equal(2, revisionRun.Links.Count);
            
            Assert.Equal("https://docs.example.com", revisionRun.Links[0].Uri);
            Assert.Equal("Documentation", revisionRun.Links[0].DisplayText);
            
            Assert.Equal("https://help.example.com", revisionRun.Links[1].Uri);
            Assert.Equal("Help Center", revisionRun.Links[1].DisplayText);
        }

        [Fact]
        public void LinkInfo_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var linkInfo = new LinkInfo
            {
                Uri = "https://www.example.com/page",
                DisplayText = "Example Page"
            };

            // Assert
            Assert.Equal("https://www.example.com/page", linkInfo.Uri);
            Assert.Equal("Example Page", linkInfo.DisplayText);
        }

        [Fact]
        public void LinkInfo_WithEmptyValues_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var linkInfo = new LinkInfo();

            // Assert
            Assert.Equal("", linkInfo.Uri);
            Assert.Equal("", linkInfo.DisplayText);
        }

        [Fact]
        public void Run_PolymorphicSerialization_ShouldWorkCorrectly()
        {
            // Arrange
            var runs = new List<Run>
            {
                new NormalRun { Text = "Normal text" },
                new RevisionRun
                {
                    Id = "rev-serialize",
                    Type = "Insert",
                    Text = new List<string> { "Inserted text" },
                    Author = "Test Author",
                    Date = "2024-06-10T12:00:00Z",
                    Links = { new LinkInfo { Uri = "https://test.com", DisplayText = "Test Link" } }
                }
            };

            // Act
            var json = JsonSerializer.Serialize(runs, new JsonSerializerOptions { WriteIndented = true, IncludeFields = true });
            var deserializedRuns = JsonSerializer.Deserialize<List<Run>>(json);

            // Assert
            Assert.NotNull(deserializedRuns);
            Assert.Equal(2, deserializedRuns.Count);
            
            Assert.IsType<NormalRun>(deserializedRuns[0]);
            Assert.IsType<RevisionRun>(deserializedRuns[1]);

            var normalRun = (NormalRun)deserializedRuns[0];
            Assert.Equal("Normal text", normalRun.Text);

            var revisionRun = (RevisionRun)deserializedRuns[1];
            Assert.Equal("rev-serialize", revisionRun.Id);
            Assert.Equal("Insert", revisionRun.Type);
            Assert.Single(revisionRun.Text);
            Assert.Equal("Inserted text", revisionRun.Text[0]);
            Assert.Equal("Test Author", revisionRun.Author);
            Assert.Single(revisionRun.Links);
            Assert.Equal("https://test.com", revisionRun.Links[0].Uri);
        }

        [Fact]
        public void ParagraphInfo_JsonSerialization_WithMixedRuns_ShouldWorkCorrectly()
        {
            // Arrange
            var originalParagraphInfo = new ParagraphInfo
            {
                ParaId = "para-serialize-mixed",
                Text = "Paragraph with mixed runs for serialization test.",
                Runs = new List<Run>
                {
                    new NormalRun { Text = "Paragraph with " },
                    new RevisionRun
                    {
                        Id = "ser-rev-001",
                        Type = "Insert",
                        Text = new List<string> { "mixed runs " },
                        Author = "Serialization Tester",
                        Date = "2024-06-10T13:00:00Z"
                    },
                    new NormalRun { Text = "for serialization test." }
                },
                StyleProperties = new Dictionary<string, string>
                {
                    ["Style"] = "Normal",
                    ["FontFamily"] = "Arial"
                }
            };

            // Act
            var json = JsonSerializer.Serialize(originalParagraphInfo, new JsonSerializerOptions { WriteIndented = true });
            var deserializedParagraphInfo = JsonSerializer.Deserialize<ParagraphInfo>(json);

            // Assert
            Assert.NotNull(deserializedParagraphInfo);
            Assert.Equal(originalParagraphInfo.ParaId, deserializedParagraphInfo.ParaId);
            Assert.Equal(originalParagraphInfo.Text, deserializedParagraphInfo.Text);
            Assert.Equal(3, deserializedParagraphInfo.Runs.Count);
            
            Assert.IsType<NormalRun>(deserializedParagraphInfo.Runs[0]);
            Assert.IsType<RevisionRun>(deserializedParagraphInfo.Runs[1]);
            Assert.IsType<NormalRun>(deserializedParagraphInfo.Runs[2]);

            var deserializedRevision = (RevisionRun)deserializedParagraphInfo.Runs[1];
            Assert.Equal("ser-rev-001", deserializedRevision.Id);
            Assert.Equal("Insert", deserializedRevision.Type);
            Assert.Equal("Serialization Tester", deserializedRevision.Author);
        }

        [Theory]
        [InlineData("Insert")]
        [InlineData("Delete")]
        [InlineData("Move")]
        [InlineData("PropertyChange")]
        public void RevisionRun_WithDifferentTypes_ShouldInitializeCorrectly(string revisionType)
        {
            // Arrange & Act
            var revisionRun = new RevisionRun
            {
                Id = $"rev-{revisionType.ToLower()}",
                Type = revisionType,
                Text = new List<string> { $"Text for {revisionType} revision" },
                Author = $"{revisionType} Author",
                Date = "2024-06-10T14:00:00Z"
            };

            // Assert
            Assert.Equal($"rev-{revisionType.ToLower()}", revisionRun.Id);
            Assert.Equal(revisionType, revisionRun.Type);
            Assert.Single(revisionRun.Text);
            Assert.Equal($"Text for {revisionType} revision", revisionRun.Text[0]);
            Assert.Equal($"{revisionType} Author", revisionRun.Author);
        }

        [Fact]
        public void RevisionRun_WithEmptyTextList_ShouldHandleCorrectly()
        {
            // Arrange & Act
            var revisionRun = new RevisionRun
            {
                Id = "rev-empty-text",
                Type = "Insert",
                Author = "Empty Text Author",
                Date = "2024-06-10T15:00:00Z"
            };

            // Assert
            Assert.Equal("rev-empty-text", revisionRun.Id);
            Assert.Equal("Insert", revisionRun.Type);
            Assert.Empty(revisionRun.Text);
            Assert.Empty(revisionRun.Links);
            Assert.Equal("Empty Text Author", revisionRun.Author);
        }

        [Fact]
        public void ParagraphInfo_WithComplexRevisionStructure_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var paragraphInfo = new ParagraphInfo
            {
                ParaId = "para-complex-revision",
                Text = "Complex paragraph with multiple revision types.",
                Runs = new List<Run>
                {
                    new NormalRun { Text = "Complex paragraph " },
                    new RevisionRun
                    {
                        Id = "complex-ins-001",
                        Type = "Insert",
                        Text = new List<string> { "with ", "multiple " },
                        Author = "Inserter",
                        Date = "2024-06-10T16:00:00Z",
                        Links = { new LinkInfo { Uri = "https://insert.example.com", DisplayText = "Insert Info" } }
                    },
                    new RevisionRun
                    {
                        Id = "complex-del-001",
                        Type = "Delete",
                        Text = new List<string> { "revision " },
                        Author = "Deleter",
                        Date = "2024-06-10T16:30:00Z"
                    },
                    new NormalRun { Text = "types." }
                },
                StyleProperties = new Dictionary<string, string>
                {
                    ["Style"] = "ComplexStyle",
                    ["FontSize"] = "11pt",
                    ["Color"] = "#333333"
                }
            };

            // Assert
            Assert.Equal("para-complex-revision", paragraphInfo.ParaId);
            Assert.Equal(4, paragraphInfo.Runs.Count);
            Assert.Equal(3, paragraphInfo.StyleProperties.Count);

            var insertRevision = (RevisionRun)paragraphInfo.Runs[1];
            Assert.Equal("Insert", insertRevision.Type);
            Assert.Equal(2, insertRevision.Text.Count);
            Assert.Single(insertRevision.Links);

            var deleteRevision = (RevisionRun)paragraphInfo.Runs[2];
            Assert.Equal("Delete", deleteRevision.Type);
            Assert.Single(deleteRevision.Text);
            Assert.Empty(deleteRevision.Links);
        }

        // Keep existing tests for DocumentInfo, RevLink, etc. as they don't depend on Run structure
        [Fact]
        public void DocumentInfo_ShouldInitializeWithDefaultValues()
        {
            // Arrange & Act
            var documentInfo = new DocumentInfo
            {
                Version = "2.0",
                DocumentId = "doc-001"
            };

            // Assert
            Assert.Equal("2.0", documentInfo.Version);
            Assert.Equal("doc-001", documentInfo.DocumentId);
            Assert.Empty(documentInfo.Elements);
            Assert.NotNull(documentInfo.Annotations);
            Assert.Empty(documentInfo.NativeRevisions);
        }

        // ... (keep other DocumentInfo, RevLink, and non-Run related tests as they were)
    }
}