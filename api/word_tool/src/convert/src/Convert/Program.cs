using System.CommandLine;

public class Program
{
    public static async Task Main(string[] args)
    {
        var inputOption = new Option<string>(
            name: "--input",
            description: "Input document path (.docx)")
        { IsRequired = true };

        var outputOption = new Option<string?>(
            name: "--output",
            description: "Output document path (optional)");

        var rootCommand = new RootCommand("Document Conversion Tool")
        {
            inputOption,
            outputOption
        };

        rootCommand.SetHandler(async (inputPath, outputPath) =>
        {
            var executor = new LocalConvertExecutor();
            await executor.ExecuteLocalAsync(inputPath, outputPath);
        }, inputOption, outputOption);

        await rootCommand.InvokeAsync(args);
    }
}