using Amazon.Lambda.Core;
using Common.Model;
using Convert.Model;
using Convert.Executor;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace Convert;

public class ConvertHandler
{
    public static async Task<ResultResponse> Execute(ConvertEvent evt, ILambdaContext context)
    {
        var executor = new ConvertExecutor(evt, context);
        var response = await executor.Execute();
        return response;
    }
}
