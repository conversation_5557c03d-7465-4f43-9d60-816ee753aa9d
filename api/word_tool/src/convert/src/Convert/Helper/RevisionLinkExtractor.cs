using System.Text.RegularExpressions;
using Convert.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Helper
{
    /// <summary>
    /// Extract hyperlinks embedded inside tracked‑change (revision) elements.
    /// Covers both inline <w:hyperlink> (relationship‑based) and field‑based hyperlinks.
    /// 提取嵌入在跟踪更改（修订）元素内的超链接。
    /// 涵盖内联 <w:hyperlink>（基于关系）和基于字段的超链接。
    /// </summary>
    public static class RevisionLinkExtractor
    {
        public static IEnumerable<RevLink> GetRevisionLinks(MainDocumentPart mainPart)
        {
            // using var doc = WordprocessingDocument.Open(filePath, false);
            // var main = doc.MainDocumentPart ?? throw new InvalidOperationException("Missing MainDocumentPart");
            var body = mainPart.Document.Body!;

            // --- 1) Relationship‑backed <w:hyperlink> --------------------------------------------------
            var relMap = mainPart.HyperlinkRelationships.ToDictionary(r => r.Id!, r => r.Uri.ToString());

            foreach (var tc in body.Descendants<RunTrackChangeType>())
            {
                if (tc?.Id?.Value == null)
                    continue;

                string revId = tc.Id.Value;
                foreach (var h in tc.Descendants<Hyperlink>())
                {
                    if (h.Id?.Value == null)
                        continue;
                        
                    relMap.TryGetValue(h.Id.Value, out var uri);
                    var paragraph = h.Ancestors<Paragraph>().FirstOrDefault();
                    if (paragraph == null) continue;

                    // === ③ 计算起止索引 ===
                    var pid = paragraph?
                                .GetAttribute("paraId",
                                            "http://schemas.microsoft.com/office/word/2010/wordml")
                                .Value;
                    yield return new RevLink(pid, revId, h.InnerText, uri);
                }
            }

            // --- 2) Field‑based hyperlinks ------------------------------------------------------------
            // Strategy: locate every <w:instrText> that contains "HYPERLINK", then walk forward to collect
            // display text until the corresponding fldChar type="end" is hit.
            // 策略：定位每个包含 "HYPERLINK" 的 <w:instrText>，然后向前收集显示文本，直到命中相应的 fldChar type="end"。

            foreach (var fc in body.Descendants<FieldCode>())
            {
                if (!fc.Text.TrimStart().StartsWith("HYPERLINK", StringComparison.OrdinalIgnoreCase))
                    continue;

                // URL inside quotes
                // 引号内的 URL
                var m = Regex.Match(fc.Text, "\"([^\"]+)\"");
                var url = m.Success ? m.Groups[1].Value : null;
                if (url == null) continue;

                // Ascend to the revision that owns this field (if any)
                // 上升到拥有此字段的修订（如果有）
                var tc = fc.Ancestors<RunTrackChangeType>().FirstOrDefault();
                if (tc?.Id?.Value == null)
                    continue;

                string revId = tc.Id.Value;

                // Display text: siblings after the run containing <w:instrText>
                // 显示文本：包含 <w:instrText> 的运行之后的兄弟元素
                var run = fc.Parent as Run;
                if (run == null) continue;

                var display = string.Concat(
                    run.ElementsAfter()
                        .TakeWhile(el => !el.Descendants<FieldChar>()
                                            .Any(c => c.FieldCharType?.Value == FieldCharValues.End))
                        .SelectMany(el => el.Descendants<Text>())
                        .Select(t => t.Text)
                );
                // fc =  <w:instrText>  (字段型 HYPERLINK)
                var paragraph = fc.Ancestors<Paragraph>().FirstOrDefault();
                if (paragraph == null) continue;
                var pid = paragraph?
                            .GetAttribute("paraId",
                                        "http://schemas.microsoft.com/office/word/2010/wordml")
                            .Value;

                if (pid != null)
                {
                    yield return new RevLink(pid, revId, display, url);
                }
            }
        }
    }
}
