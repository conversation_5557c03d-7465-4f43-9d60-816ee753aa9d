using System.Text;
using System.Text.RegularExpressions;
using Common.DocItemModel;
using Convert.Exceptions;
using Convert.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Helper
{

    public class Processor
    {
        protected const string WORD2010_NAMESPACE = "http://schemas.microsoft.com/office/word/2010/wordml";
        protected const string WORD2006_NAMESPACE = "http://schemas.openxmlformats.org/wordprocessingml/2006/main";
#pragma warning disable CS8618

        /// <summary>
        /// Process a table row and return a TableRowElement
        /// </summary>
        /// <param name="documentInfo">Document information</param>
        /// <param name="tableRow">The table row to process</param>
        /// <param name="tableId">ID of the table</param>
        /// <param name="rowIndex">Index of the row in the table</param>
        /// <returns>Processed TableRowElement</returns>
        public static ParagraphElement ProcessParagraphUnified(
            DocumentInfo documentInfo,
            Paragraph paragraph,
            string paraId,
            Numbering? numbering,
            bool isTableCell = false,
            Dictionary<string, int>? documentLevelCommentRanges = null)
        {
            ParagraphElement paraItem;

            var listDetector = new WordListDetector();
            var listProcessor = new ListProcessor(listDetector);

            if (listDetector.IsListParagraph(paragraph))
            {
                if (numbering == null)
                {
                    throw new NumberingDefinitionsNotFoundException();
                }
                var _paraItem = listProcessor.ProcessListParagraph(documentInfo, paragraph, paraId, numbering);

                if (_paraItem != null)
                {
                    paraItem = _paraItem;

                }
                else
                {
                    // fallback to normal paragraph processing if list processing fails
                    paraItem = ConverterFactory.CreateParagraphElement(paraId, paragraph);
                    // 统一处理段落属性
                    if (!isTableCell)
                    {
                        ConverterHelper.GetPragraphProperties(documentInfo, paraItem, paragraph);
                    }
                }
            }
            else
            {
                paraItem = ConverterFactory.CreateParagraphElement(paraId, paragraph);
                // 统一处理段落属性
                if (!isTableCell)
                {
                    ConverterHelper.GetPragraphProperties(documentInfo, paraItem, paragraph);
                }
            }

            // 统一处理元素
            ProcessParagraphElementsUnified(documentInfo, paraItem, paragraph, documentLevelCommentRanges);

            return paraItem;
        }

        public static ParagraphElement ProcessParagraphElementsUnified(
            DocumentInfo documentInfo,
            ParagraphElement paraItem,
            Paragraph paragraph,
            Dictionary<string, int>? documentLevelCommentRanges = null
        )
        {
            var activeCommentRanges = new Dictionary<string, int>();
            var position = 0;
            var contentBuilder = new StringBuilder();

            var allElements = paragraph.Elements<OpenXmlElement>().Where(e => e != null).ToList();

            foreach (var element in allElements)
            {
                ProcessSingleElement(documentInfo, paraItem, element, ref position, contentBuilder, activeCommentRanges, documentLevelCommentRanges);
            }
            var paraContent = contentBuilder.ToString();
            var FilteredText = ProcessFieldChar(documentInfo, paraItem, paraContent);
            paraItem.PlainText = FilteredText;
            return paraItem;
        }

        public static void ProcessSingleElement(
            DocumentInfo documentInfo,
            ParagraphElement paraItem,
            OpenXmlElement element,
            ref int position,
            StringBuilder contentBuilder,
            Dictionary<string, int> activeCommentRanges,
            Dictionary<string, int>? documentLevelCommentRanges = null)
        {
            switch (element)
            {
                case ParagraphProperties:
                    break;
                case CommentRangeStart commentRangeStart:
                    ProcessCommentStart(paraItem, commentRangeStart, activeCommentRanges);
                    break;
                case CommentRangeEnd commentRangeEnd:
                    // First check if this is ending a document-level comment range
                    if (documentLevelCommentRanges != null && ProcessDocumentLevelCommentEnd(documentInfo, paraItem, commentRangeEnd, documentLevelCommentRanges))
                    {
                        // Document-level comment range was processed
                        break;
                    }
                    // Otherwise, process as normal paragraph-level comment range
                    ProcessCommentEnd(documentInfo, paraItem, commentRangeEnd, activeCommentRanges);
                    break;
                default:
                    ProcessNestedElement(documentInfo, paraItem, element, ref position, contentBuilder, activeCommentRanges, "");
                    break;
            }
        }

        public static void ProcessNestedElement(
            DocumentInfo documentInfo,
            ParagraphElement paraItem,
            OpenXmlElement element,
            ref int position,
            StringBuilder contentBuilder,
            Dictionary<string, int> activeCommentRanges,
            string contextTags = ""
            )
        {
            var result = element switch
            {
                InsertedRun insertedRun when !ConverterHelper.IsInNestedContext(contextTags, "ins") => ProcessRevisionRun(documentInfo, paraItem, insertedRun, position, activeCommentRanges, contextTags),
                DeletedRun deletedRun when !ConverterHelper.IsInNestedContext(contextTags, "delete") => ProcessRevisionRun(documentInfo, paraItem, deletedRun, position, activeCommentRanges, contextTags),
                MoveToRun moveTo when !ConverterHelper.IsInNestedContext(contextTags, "moveto") => ProcessRevisionRun(documentInfo, paraItem, moveTo, position, activeCommentRanges, contextTags),
                MoveFromRun moveFrom when !ConverterHelper.IsInNestedContext(contextTags, "movefrom") => ProcessRevisionRun(documentInfo, paraItem, moveFrom, position, activeCommentRanges, contextTags),
                Hyperlink hyperlink when !ConverterHelper.IsInNestedContext(contextTags, "hyperlink") => ProcessHyperLink(documentInfo, paraItem, hyperlink, position, contextTags),
                Run run => ProcessNormalRun(documentInfo, paraItem, run, position),
                _ => string.Empty
            };

            position += result.Length;
            contentBuilder.Append(result);
        }

        public static TableRowElement ProcessTableRow(DocumentInfo documentInfo, Numbering numbering, TableRow tableRow, int tableIdx, int rowIdx)
        {

            var rowElement = ConverterFactory.CreateTableRowElement(rowIdx);
            // Process table cells
            var tableCells = tableRow.Elements<TableCell>().Where(c => c != null).ToList();
            var cellIndex = 0;

            foreach (var tableCell in tableCells)
            {
                var cellElement = ProcessTableCell(documentInfo, numbering, tableCell, tableIdx, rowIdx, cellIndex);
                if (cellElement != null)
                {
                    rowElement.Cells.Add(cellElement);
                }
                cellIndex++;
            }

            return rowElement;
        }

        public static TableCellElement ProcessTableCell(DocumentInfo documentInfo, Numbering numbering, TableCell tableCell, int tableIdx, int rowIdx, int cellIdx)
        {
            var cellElement = ConverterFactory.CreateTableCellElement(tableIdx, rowIdx, cellIdx);

            // Process paragraphs within the cell
            var paragraphs = tableCell.Elements<Paragraph>().Where(p => p != null).ToList();
            foreach (var paragraph in paragraphs)
            {
                var paraId = IdGenerator.GenerateParagraphId(documentInfo, GetParagraphId(paragraph));

                var paraItem = ProcessParagraphUnified(documentInfo, paragraph, paraId, numbering, true);

                cellElement.Elements.Add(paraItem);
            }

            return cellElement;
        }

        private static string? GetParagraphId(Paragraph paragraph)
        {
            try
            {
                // Get the paraId attribute from Word 2010+
                var paraIdAttr = paragraph.GetAttribute("paraId", WORD2010_NAMESPACE);
                return paraIdAttr.Value;
            }
            catch
            {
                //
            }
            return "Para" + Guid.NewGuid().ToString();
        }

        private static string ExtractRevisionId<T>(T revisionRun) where T : OpenXmlElement
        {
            return revisionRun switch
            {
                InsertedRun insertedRun => insertedRun.Id?.Value ?? "",
                DeletedRun deletedRun => deletedRun.Id?.Value ?? "",
                MoveTo moveTo => moveTo.Id?.Value ?? "",
                MoveFrom moveFrom => moveFrom.Id?.Value ?? "",
                _ => ""
            };
        }

        public static void ProcessComments(DocumentInfo documentInfo, WordprocessingCommentsPart commentsPart)
        {
            foreach (var comment in commentsPart.Comments.Elements<DocumentFormat.OpenXml.Wordprocessing.Comment>())
            {
                var commentId = comment.Id?.Value ?? "";
                if (string.IsNullOrEmpty(commentId))
                {
                    continue; // Skip comments without an ID
                }

                var docComment = ConverterFactory.CreateDocComment(commentId, comment.Author?.Value ?? "", comment.Date?.ToString() ?? "", comment.InnerText);

                documentInfo.Annotations.Comments[commentId] = docComment;
            }
        }
        public static void ProcessCommentStart(ParagraphElement paraItem, CommentRangeStart commentRangeStart, Dictionary<string, int> activeCommentRanges)
        {
            var commentId = commentRangeStart.Id?.Value ?? "";
            if (!string.IsNullOrEmpty(commentId))
            {
                activeCommentRanges[commentId] = paraItem.Segments.Count;
            }
        }
        public static void ProcessCommentEnd(DocumentInfo documentInfo, ParagraphElement paraItem, CommentRangeEnd commentRangeEnd, Dictionary<string, int> activeCommentRanges)
        {
            var commentId = commentRangeEnd.Id?.Value ?? "";
            if (!string.IsNullOrEmpty(commentId) && activeCommentRanges.ContainsKey(commentId))
            {
                var commentStartIndex = activeCommentRanges[commentId] - 1;

                var startIdex = paraItem.Segments.Count - 1 - commentStartIndex;
                for (int i = startIdex; i < paraItem.Segments.Count; i++)
                {
                    var cur = paraItem.Segments[i];
                    paraItem.Segments[i].CommentIds.Add(commentId);
                    documentInfo.Annotations.Comments[commentId].Targets.Add(cur.SegmentId);
                }

            }
        }

        public static bool ProcessDocumentLevelCommentEnd(DocumentInfo documentInfo, ParagraphElement paraItem, CommentRangeEnd commentRangeEnd, Dictionary<string, int> documentLevelCommentRanges)
        {
            var commentId = commentRangeEnd.Id?.Value ?? "";
            if (!string.IsNullOrEmpty(commentId) && documentLevelCommentRanges.ContainsKey(commentId))
            {
                // This is ending a document-level comment range
                // Apply the comment to all segments in the current paragraph
                foreach (var segment in paraItem.Segments)
                {
                    segment.CommentIds.Add(commentId);
                    if (documentInfo.Annotations.Comments.ContainsKey(commentId))
                    {
                        documentInfo.Annotations.Comments[commentId].Targets.Add(segment.SegmentId);
                    }
                }

                // Remove the processed comment range from document-level tracking
                documentLevelCommentRanges.Remove(commentId);
                return true; // Indicates that this was a document-level comment range
            }
            return false; // Not a document-level comment range
        }

        public static string ProcessNormalRun(DocumentInfo documentInfo, ParagraphElement paraItem, Run run, int startPosition)
        {
            var content = TextExtractor.ExtractFromElement(run);

            var fieldId = ConverterHelper.GetFieldId(run);
            var segment = ConverterFactory.CreateSegment(
                paraItem,
                content,
                startPosition,
                ConverterHelper.GetRunProperties(run),
                ConverterHelper.GetPropertyChange(documentInfo, run),
                "",
                "",
                fieldId
            );

            paraItem.Segments.Add(segment);
            return content;
        }

        public static string ProcessRevisionRun<T>(DocumentInfo documentInfo, ParagraphElement paraItem, T revisionRun, int startPosition, Dictionary<string, int> activeCommentRanges, string contextTags = "")
    where T : OpenXmlElement
        {
            var contentBuilder = new StringBuilder();
            string revisionId = ExtractRevisionId(revisionRun);
            ConverterHelper.InsertRevision(documentInfo, revisionRun);
            var currentPosition = startPosition;
            bool isDeletedRevision = revisionRun is DeletedRun;
            bool isMoveFromRevision = revisionRun is MoveFrom;
            bool isMoveToRevision = revisionRun is MoveTo;
            var revisionType = revisionRun switch
            {
                InsertedRun => "ins",
                DeletedRun => "delete",
                MoveTo => "moveto",
                MoveFrom => "movefrom",
                _ => "revision"
            };
            var newContextTags = ConverterHelper.AddContext(contextTags, revisionType);
            var revChildElements = revisionRun.Elements<OpenXmlElement>().ToList();
            if (revChildElements.Count > 0)
            {
                foreach (var revchild in revChildElements)
                {
                    switch (revchild)
                    {
                        case CommentRangeStart commentRangeStart:
                            ProcessCommentStart(paraItem, commentRangeStart, activeCommentRanges);
                            break;
                        case CommentRangeEnd commentRangeEnd:
                            ProcessCommentEnd(documentInfo, paraItem, commentRangeEnd, activeCommentRanges);
                            break;
                        case Run revInnerRun:
                            string runContent = TextExtractor.ExtractFromElement(revInnerRun);
                            var cur_seg = ConverterFactory.CreateSegment(
                                paraItem,
                                runContent,
                                currentPosition,
                                ConverterHelper.GetRunProperties(revInnerRun),
                                ConverterHelper.GetPropertyChange(documentInfo, revInnerRun),
                                revisionId,
                                "",
                                ConverterHelper.GetFieldId(revInnerRun)
                            );
                            if (!isDeletedRevision && !isMoveFromRevision)
                            {
                                contentBuilder.Append(runContent);
                                currentPosition += runContent.Length;
                                paraItem.Segments.Add(cur_seg);
                            }
                            break;
                        default:
                            ProcessNestedElement(documentInfo, paraItem, revchild, ref currentPosition, contentBuilder, activeCommentRanges, newContextTags);
                            break;
                    }
                }
                return contentBuilder.ToString();
            }

            string unicontent = TextExtractor.ExtractFromElementDescendants(revisionRun);

            var segment = ConverterFactory.CreateSegment(
                paraItem,
                unicontent,
                currentPosition,
                new RunProperty(),
                [],
                revisionId,
                "",
                ""
            );
            paraItem.Segments.Add(segment);
            if (!isDeletedRevision)
            {
                contentBuilder.Append(unicontent);
            }
            return contentBuilder.ToString();
        }

        public static string ProcessHyperLink(DocumentInfo documentInfo, ParagraphElement paraItem, Hyperlink hyperlink, int startPosition, string contextTags)
        {
            var hyperlinkId = hyperlink.Id?.Value ?? "";
            ConverterHelper.InsertHyperlink(documentInfo, hyperlink);

            var contentBuilder = new StringBuilder();
            var currentPosition = startPosition;
            var activeCommentRanges = new Dictionary<string, int>();
            var childElements = hyperlink.Elements<OpenXmlElement>().ToList(); // For recursive processing
            var segmentCountBefore = paraItem.Segments.Count;
            var newContextTags = ConverterHelper.AddContext(contextTags, "hyperlink");
            foreach (var element in childElements)
            {
                switch (element)
                {
                    case ParagraphProperties:
                        break;
                    case CommentRangeStart commentRangeStart:
                        ProcessCommentStart(paraItem, commentRangeStart, activeCommentRanges);
                        break;
                    case CommentRangeEnd commentRangeEnd:
                        ProcessCommentEnd(documentInfo, paraItem, commentRangeEnd, activeCommentRanges);
                        break;
                    default:
                        ProcessNestedElement(documentInfo, paraItem, element, ref currentPosition, contentBuilder, activeCommentRanges, newContextTags);
                        break;
                }
            }
            if (!string.IsNullOrEmpty(hyperlinkId))
            {
                for (int i = segmentCountBefore; i < paraItem.Segments.Count; i++)
                {
                    paraItem.Segments[i].HyperlinkId = hyperlinkId;
                    documentInfo.Annotations.Hyperlinks[hyperlinkId].Targets.Add(paraItem.Segments[i].SegmentId);
                }
            }
            return contentBuilder.ToString();
        }

        public static void ProcessRelHyperlink(DocumentInfo documentInfo, MainDocumentPart? mainDocumentPart)
        {
            foreach (var link in documentInfo.Annotations.Hyperlinks.Values)
            {
                if (mainDocumentPart != null && link.HyperlinkId.StartsWith("rId"))
                {
                    var rel = mainDocumentPart.HyperlinkRelationships.FirstOrDefault(r => r.Id == link.HyperlinkId);
                    if (rel != null)
                    {
                        documentInfo.Annotations.Hyperlinks[link.HyperlinkId].Uri = rel.Uri.ToString();
                    }
                }
            }
        }

        public static string ProcessFieldChar(DocumentInfo documentInfo, ParagraphElement paraItem, string paraContent)
        {
            if (paraItem.Segments.Count == 0)
            {
                return paraContent; // No segments to process
            }
            var beginIndices = new List<int>();
            var endIndices = new List<int>();
            // get index pair
            for (int i = 0; i < paraItem.Segments.Count; i++)
            {
                var segment = paraItem.Segments[i];
                switch (segment.FieldId)
                {
                    case "begin":
                        beginIndices.Add(i);
                        break;
                    case "end":
                        endIndices.Add(i);
                        break;
                    default:
                        break;
                }
            }
            var min_pair_count = Math.Min(beginIndices.Count, endIndices.Count);
            for (int i = 0; i < min_pair_count; i++)
            {
                var beginIndex = beginIndices[i];
                var endIndex = endIndices[i];

                var hyperlink_key = $"{paraItem.ElementId}-{i}";

                var targets = new List<string>();
                var uri = "";
                for (int j = beginIndex; j <= endIndex; j++)
                {
                    if (paraItem.Segments[j].SegmentText.StartsWith("HYPERLINK"))
                    {
                        var match = Regex.Match(paraItem.Segments[j].SegmentText, @"HYPERLINK\s+""([^""]+)""");
                        uri = match.Success ? match.Groups[1].Value : "";
                        if (match.Success && !string.IsNullOrEmpty(uri))
                        {
                            paraContent = paraContent.Replace(paraItem.Segments[j].SegmentText, ""); // Remove from content
                            paraItem.Segments[j].SegmentText = ""; // Clear the text for hyperlink segment
                        }
                    }


                    targets.Add(paraItem.Segments[j].SegmentId);
                    paraItem.Segments[j].HyperlinkId = hyperlink_key;
                }

                // 保存字段分组的目标
                documentInfo.Annotations.Hyperlinks[hyperlink_key] = ConverterFactory.CreateDocHyperlink(
                    hyperlink_key,
                    uri,
                    targets
                );
            }
            // Correct Segment Range start and end
            var globalStart = 0;
            for (int i = 0; i < paraItem.Segments.Count; i++)
            {
                var segment = paraItem.Segments[i];
                var segmentLength = segment.SegmentText.Length;
                segment.Start = globalStart;
                segment.End = segmentLength > 0 ? globalStart + segmentLength - 1 : globalStart; // Adjust end to be inclusive
                globalStart += segmentLength;
            }
            return paraContent;
        }

        public static void ProcessDefaultRun(ParagraphElement paraItem)
        {
            ConverterHelper.InsertCommonSegments(paraItem, "Other");
        }
    }
}