using Common.DocItemModel;
using Convert.Abstract;
using Convert.Model;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Helper
{
    /// <summary>
    /// List processor responsible for handling list paragraphs in Word documents
    /// </summary>
    public class ListProcessor : IListProcessor
    {
        private readonly IListDetector _listDetector;

        /// <summary>
        /// Initializes a new instance of the ListProcessor class.
        /// </summary>
        /// <param name="listDetector">The list detector.</param>
        /// <exception cref="ArgumentNullException">Thrown when listDetector is null.</exception>
        public ListProcessor(IListDetector listDetector)
        {
            _listDetector = listDetector ?? throw new ArgumentNullException(nameof(listDetector));
        }

        /// <summary>
        /// Processes a list paragraph, extracts list properties, and creates a ParagraphElement.
        /// </summary>
        /// <param name="documentInfo">Document information object.</param>
        /// <param name="paragraph">The list paragraph to process.</param>
        /// <param name="paraId">Paragraph ID.</param>
        /// <returns>The processed paragraph element with ElementType set to "List".</returns>
        /// <exception cref="ArgumentNullException">Thrown when a parameter is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the paragraph is not a list paragraph.</exception>
        public ParagraphElement? ProcessListParagraph(DocumentInfo documentInfo, Paragraph paragraph, string paraId, Numbering numbering)
        {
            if (string.IsNullOrWhiteSpace(paraId))
                throw new ArgumentException("Paragraph ID cannot be empty", nameof(paraId));

            // Verify if it is a list paragraph
            if (!_listDetector.IsListParagraph(paragraph))
            {
                // TODO: 需要 logger导入，而不是将 lambda context 传递到这里
                Console.WriteLine($"Paragraph with ID {paraId} is not a list paragraph.");
                return null; // Not a list paragraph, return null
            }

            // Extract list properties
            var listInfo = _listDetector.ExtractListProperties(paragraph, numbering);
            if (listInfo == null)
            {
                Console.WriteLine($"Failed to extract list properties for paragraph with ID {paraId}");
                return null; // Extraction failed, return null
            }

            // Use ConverterFactory to create paragraph element
            var paraElement = ConverterFactory.CreateListParagraphElement(paraId, paragraph, listInfo);

            // Set ElementType = "List" as required by PRD
            paraElement.ElementType = "List";

            return paraElement;
        }
    }
}