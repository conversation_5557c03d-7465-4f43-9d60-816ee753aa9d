﻿using System.Text;
using Amazon.Lambda.Core;
using Common.DocItemModel;
using Common.Helper;
using Common.Model;
using Convert.Abstract;
using Convert.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Run = DocumentFormat.OpenXml.Wordprocessing.Run;


namespace Convert.Helper
{
    /// <summary>
    /// implementation of the class that implements the DOCX conversion logic
    /// </summary>
    public class DocxConverter : DocxHandle, IDocxConverter
    {
        public DocxConverter(ILambdaContext _context)
        {
            context = _context;
        }

        private string? GetParagraphId(Paragraph paragraph)
        {
            try
            {
                // Get the paraId attribute from Word 2010+
                var paraIdAttr = paragraph.GetAttribute("paraId", WORD2010_NAMESPACE);
                return paraIdAttr.Value;
            }
            catch
            {
                context.Logger.LogWarning("No paragraph id found.");
            }
            return string.Empty;
        }

        private Dictionary<string, string> GetParagraphStyleInfo(Paragraph paragraph)
        {
            var styleInfo = new Dictionary<string, string>();
            var pPar = paragraph.Elements<ParagraphProperties>().FirstOrDefault();
            if (pPar == null)
            {
                return styleInfo;
            }
            var pStyle = pPar.Elements<ParagraphStyleId>().FirstOrDefault();
            if (pStyle != null && pStyle.Val != null)
            {
                var styleVal = pStyle.Val?.Value;
                styleInfo["Style"] = styleVal ?? "";
            }
            return styleInfo;
        }

        public List<ParagraphInfo> Convert(string filePath)
        {
            List<ParagraphInfo> paragraphsWithRevisions = new List<ParagraphInfo>();
            using (WordprocessingDocument doc = WordprocessingDocument.Open(filePath, false))
            {
                MainDocumentPart? mainPart = doc.MainDocumentPart;
                if (mainPart?.Document == null)
                {
                    context.Logger.LogError("Can't open the document or MainDocumentPart is missing.");
                    return paragraphsWithRevisions;
                }

                // loop through all paragraphs in the document
                var paragraphs = mainPart.Document?.Body?.Elements<Paragraph>();
                if (paragraphs == null || !paragraphs.Any())
                {
                    context.Logger.LogError("No paragraphs found in the document.");
                    return paragraphsWithRevisions;
                }

                foreach (var paragraph in paragraphs)
                {
                    if (paragraph == null)
                        continue;

                    // Only process paragraphs that contain revision elements
                    var hasRevisionData = false;
                    // check if the paragraph contains any revision elements (deleted or revision)
                    var revisionElements = paragraph.Elements<OpenXmlElement>()
                        .Where(e => e is InsertedRun || e is Run || e is Hyperlink)
                        .ToList();

                    if (revisionElements == null || revisionElements.Count == 0)
                        continue;

                    // skip the paragraph without paraId
                    // TODO: create an id and insert to paragraph ?
                    var paraId = GetParagraphId(paragraph);
                    if (string.IsNullOrEmpty(paraId))
                        continue;

                    ParagraphInfo paraInfo = new ParagraphInfo
                    {
                        ParaId = paraId,
                        Text = paragraph.InnerText,
                        Runs = new List<Common.Model.Run>(),
                        StyleProperties = GetParagraphStyleInfo(paragraph)
                    };

                    var finalTextBuilder = new StringBuilder();
                    foreach (var element in revisionElements)
                    {
                        if (element is Run run)
                        {
                            // normal text: get content from <w:t>                       

                            finalTextBuilder.Append(string.Concat(run.Elements<Text>().Select(t => t.Text)));
                        }
                        else if (element is InsertedRun insertedRun)
                        {
                            // insert revision: get content from <w:t>
                            hasRevisionData = true;
                            var text = string.Concat(insertedRun.Descendants<Text>().Select(t => t.Text));
                            finalTextBuilder.Append(text);
                        }
                        else if (element is Hyperlink hyperLink)
                        {
                            var text = string.Concat(hyperLink.Descendants<Text>().Select(t => t.Text));
                            finalTextBuilder.Append(text);
                        }
                    }

                    var paragraphText = finalTextBuilder.ToString();
                    // Remove the paragraph text is empty, e.g: paragraph is kind of graph
                    if (string.IsNullOrEmpty(paragraphText))
                    {
                        continue;
                    }
                    paraInfo.Text = finalTextBuilder.ToString();

                    // loop through each revision element in the paragraph
                    foreach (var revision in revisionElements)
                    {
                        string type;
                        if (revision is Inserted || revision is InsertedRun)
                            type = "Insert";
                        else if (revision is Run)
                            type = "Normal";
                        else
                            type = "Unknown";

                        // Modified code: Use the auxiliary method to directly extract the text of each revision element to ensure the order
                        var texts = revision.Descendants<Text>().Select(t => t.Text);
                        var deletedTexts = revision.Descendants<DeletedText>().Select(dt => dt.Text);
                        List<string> revisionText = new List<string>();

                        if (revision is Inserted || revision is InsertedRun || revision is Run)
                        {
                            revisionText.AddRange(texts);
                        }
                        else if (revision is Deleted || revision is DeletedRun)
                        {
                            revisionText.AddRange(deletedTexts);
                        }
                        if (revision is Run)
                        {
                            paraInfo.Runs.Add(
                                new NormalRun
                                {
                                    Text = string.Join("", revisionText)
                                }
                            );
                        }
                        else if (revision is Hyperlink)
                        {
                            paraInfo.Runs.Add(
                                new NormalRun
                                {
                                    Text = string.Join("", texts)
                                }
                            );
                        }
                        else
                        {
                            string wNamespace = WORD2006_NAMESPACE;
                            string revisionId = GetElementId(revision) ?? "";
                            string author = revision.GetAttribute("author", wNamespace).Value ?? "";
                            string date = revision.GetAttribute("date", wNamespace).Value ?? "";
                            paraInfo.Runs.Add(
                                new RevisionRun
                                {
                                    Id = revisionId,
                                    Type = type,
                                    Text = revisionText,
                                    Author = author,
                                    Date = date
                                }
                            );
                        }
                    }

                    // Add the paragraph to the list if it contains revision data
                    if (hasRevisionData)
                        paragraphsWithRevisions.Add(paraInfo);
                }
                // ① 提取、分组所有 RevLink
                var allLinks = RevisionLinkExtractor
                            .GetRevisionLinks(mainPart)
                            .ToList();
                var linksByPara = allLinks
                                .GroupBy(l => l.ParaId)
                                .ToDictionary(g => g.Key, g => g.ToList());

                // ② 把链接信息挂到每个 ParagraphInfo.Runs（RevisionRun）上
                foreach (var paraInfo in paragraphsWithRevisions)
                {
                    if (!linksByPara.TryGetValue(paraInfo.ParaId, out var paraLinks))
                        continue;

                    foreach (var run in paraInfo.Runs.OfType<RevisionRun>())
                    {
                        // 精确匹配同一条修订里的所有链接
                        var matched = paraLinks
                                    .Where(l => l.RevisionId == run.Id)
                                    .Select(l => new LinkInfo
                                    {
                                        Uri = l.TargetUri!,
                                        DisplayText = l.DisplayText!
                                    });
                        run.Links.AddRange(matched);
                    }
                }
            }
            return paragraphsWithRevisions;
        }

        static string? GetElementId(OpenXmlElement element)
        {
            if (element is Inserted inserted)
                return inserted.Id?.Value;

            if (element is Deleted deleted)
                return deleted.Id?.Value;

            if (element is InsertedRun insertedRun)
                return insertedRun.Id?.Value;

            if (element is DeletedRun deletedRun)
                return deletedRun.Id?.Value;
            return Guid.NewGuid().ToString();
        }

        public DocumentInfo ConvertToDocumentInfo(string filePath)
        {
            var documentInfo = ConverterFactory.CreateDocumentInfo(filePath);

            using (WordprocessingDocument doc = WordprocessingDocument.Open(filePath, false))
            {
                MainDocumentPart? mainPart = doc.MainDocumentPart;
                if (mainPart?.Document == null)
                {
                    context.Logger.LogError("Can't open the document or MainDocumentPart is missing.");
                    return documentInfo;
                }

                var commentsPart = mainPart.WordprocessingCommentsPart;
                if (commentsPart != null)
                    Processor.ProcessComments(documentInfo, commentsPart);

                // process paragraphs
                var numbering = mainPart.NumberingDefinitionsPart?.Numbering;
                var bodyElements = mainPart.Document?.Body?.Elements<OpenXmlElement>();
                if (bodyElements == null || !bodyElements.Any())
                {
                    context.Logger.LogError("No body elements found in the document.");
                    return documentInfo;
                }
                var documentLevelCommentRanges = new Dictionary<string, int>();

                var tableCount = 0;
                foreach (var bodyElement in bodyElements)
                {
                    if (bodyElement == null)
                        continue;

                    switch (bodyElement)
                    {
                        case Paragraph paragraph:
                            ProcessParagraphElements(documentInfo, paragraph, numbering, documentLevelCommentRanges);
                            break;
                        case Table table:
                            ProcessTableElements(documentInfo, table, tableCount++, numbering);
                            break;
                        case CommentRangeStart commentRangeStart:
                            ProcessDocumentLevelCommentStart(documentInfo, commentRangeStart, documentLevelCommentRanges);
                            break;
                        case CommentRangeEnd commentRangeEnd:
                            ProcessDocumentLevelCommentEnd(documentInfo, commentRangeEnd, documentLevelCommentRanges);
                            break;
                        default:
                            context.Logger.LogWarning($"Unsupported body element type: {bodyElement.GetType().Name}");
                            break;
                    }
                }

                // post-process for hyperlinks
                if (documentInfo.Annotations.Hyperlinks.Count > 0)
                {
                    Processor.ProcessRelHyperlink(documentInfo, mainPart);
                }
            }
            return documentInfo;
        }

        public void ProcessParagraphElements(DocumentInfo documentInfo, Paragraph paragraph, Numbering? numbering, Dictionary<string, int> documentLevelCommentRanges)
        {
            var paraId = IdGenerator.GenerateParagraphId(documentInfo, GetParagraphId(paragraph));
            var paraItem = Processor.ProcessParagraphUnified(documentInfo, paragraph, paraId, numbering, false, documentLevelCommentRanges);
            documentInfo.Elements.Add(paraItem);
        }

        public static void ProcessTableElements(DocumentInfo documentInfo, Table table, int table_count, Numbering? numbering)
        {
            var tableElement = ConverterFactory.CreateTableElement(table_count);

            var tableRows = table.Elements<TableRow>().Where(r => r != null).ToList();
            for (int rowIndex = 0; rowIndex < tableRows.Count; rowIndex++)
            {
                var rowElement = Processor.ProcessTableRow(documentInfo, numbering, tableRows[rowIndex], table_count, rowIndex);
                if (rowElement != null)
                {
                    tableElement.Rows.Add(rowElement);
                }
            }
            documentInfo.Elements.Add(tableElement);
        }

        private void ProcessDocumentLevelCommentStart(DocumentInfo documentInfo, CommentRangeStart commentRangeStart, Dictionary<string, int> documentLevelCommentRanges)
        {
            var commentId = commentRangeStart.Id?.Value ?? "";
            if (!string.IsNullOrEmpty(commentId))
            {
                // Record the current element count as the start position for the comment range
                documentLevelCommentRanges[commentId] = documentInfo.Elements.Count;
                context.Logger.LogInformation($"Document-level comment range started: {commentId} at element index {documentInfo.Elements.Count}");
            }
        }

        private void ProcessDocumentLevelCommentEnd(DocumentInfo documentInfo, CommentRangeEnd commentRangeEnd, Dictionary<string, int> documentLevelCommentRanges)
        {
            var commentId = commentRangeEnd.Id?.Value ?? "";
            if (!string.IsNullOrEmpty(commentId) && documentLevelCommentRanges.ContainsKey(commentId))
            {
                var commentStartIndex = documentLevelCommentRanges[commentId];
                
                // Associate the comment with all elements created since the comment start
                for (int i = commentStartIndex; i < documentInfo.Elements.Count; i++)
                {
                    var element = documentInfo.Elements[i];
                    
                    // Add comment to all segments in paragraph elements
                    if (element is ParagraphElement paragraphElement)
                    {
                        foreach (var segment in paragraphElement.Segments)
                        {
                            segment.CommentIds.Add(commentId);
                            if (documentInfo.Annotations.Comments.ContainsKey(commentId))
                            {
                                documentInfo.Annotations.Comments[commentId].Targets.Add(segment.SegmentId);
                            }
                        }
                    }
                    // Add comment to table elements and their content
                    else if (element is TableElement tableElement)
                    {
                        foreach (var row in tableElement.Rows)
                        {
                            foreach (var cell in row.Cells)
                            {
                                foreach (var cellElement in cell.Elements)
                                {
                                    if (cellElement is ParagraphElement cellParagraph)
                                    {
                                        foreach (var segment in cellParagraph.Segments)
                                        {
                                            segment.CommentIds.Add(commentId);
                                            if (documentInfo.Annotations.Comments.ContainsKey(commentId))
                                            {
                                                documentInfo.Annotations.Comments[commentId].Targets.Add(segment.SegmentId);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Remove the processed comment range
                documentLevelCommentRanges.Remove(commentId);
                context.Logger.LogInformation($"Document-level comment range ended: {commentId}, applied to {documentInfo.Elements.Count - commentStartIndex} elements");
            }
            else if (!string.IsNullOrEmpty(commentId))
            {
                context.Logger.LogWarning($"Comment range end found without matching start: {commentId}");
            }
        }
    }
}

