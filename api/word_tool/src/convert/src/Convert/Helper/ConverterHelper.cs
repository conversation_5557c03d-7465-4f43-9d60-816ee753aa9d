using System.Text;
using Common.DocItemModel;
using Convert.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Helper
{
    public class ConverterHelper
    {
        /// <summary>
        /// Extracts paragraph properties from a Paragraph element and populates the docItem.
        /// </summary>
        /// <param name="documentInfo">The document information to which revisions will be added.</param>
        /// <param name="docItem">The ParagraphElement to populate with properties.</param>
        /// <param name="paragraph">The Paragraph element from which to extract properties.</param>
        /// <returns></returns>
        # region Extract Properties
        public static void GetPragraphProperties(DocumentInfo documentInfo, ParagraphElement docItem, Paragraph paragraph)
        {
            var pPr = paragraph.Elements<ParagraphProperties>().FirstOrDefault();
            if (pPr == null)
            {
                return; // No properties found, return empty StyleProperty
            }
            var pStyle = pPr.Elements<ParagraphStyleId>().FirstOrDefault();
            if (pStyle != null)
            {
                docItem.Properties.Style = pStyle.Val?.Value ?? "Normal";
            }

            var indentLeft = pPr.Indentation?.Left?.Value ?? "0";
            docItem.Properties.IndentLeft = int.TryParse(indentLeft, out var indent) ? indent : 0;

            var rPrChanges = pPr.Elements<ParagraphMarkRunPropertiesChange>().ToList();
            if (rPrChanges.Count > 0)
            {
                foreach (var rPrChange in rPrChanges)
                {
                    var nativeRevision = new NativeRevision
                    {
                        RevId = rPrChange.Id?.Value ?? "",
                        RevType = "ParagraphMarkRunPropertiesChange",
                        Author = rPrChange.Author?.Value ?? "",
                        Date = rPrChange.Date?.Value.ToString() ?? ""
                    };
                    documentInfo.NativeRevisions[nativeRevision.RevId] = nativeRevision;
                }
            }
        }

        public static string GetFieldId(Run run)
        {
            var fieldChar = run.Elements<FieldChar>().FirstOrDefault();
            if (fieldChar?.FieldCharType?.Value == null)
                return "";
            if (fieldChar.FieldCharType == null)
                return "";
            return fieldChar.FieldCharType;
        }

        public static RunProperty GetRunProperties(Run run)
        {
            if (run == null)
            {
                return new RunProperty(); // Return empty RunProperty if run is null
            }
            var styleProperty = new RunProperty();
            var rPr = run.Elements<RunProperties>().FirstOrDefault();
            if (rPr == null)
            {
                return styleProperty;
            }
            return ExtractRunPropertiesFromElement(rPr);
        }

        public static RunProperty GetRunProperties(Hyperlink hyperlink)
        {
            var styleProperty = new RunProperty();

            // Hyperlink 中的 RunProperties 可能在其内部的 Run 元素中
            var runs = hyperlink.Elements<Run>().ToList();
            if (runs.Any())
            {
                // 取第一个 Run 的属性，或者合并所有 Run 的属性
                var firstRun = runs.First();
                var rPr = firstRun.Elements<RunProperties>().FirstOrDefault();
                if (rPr != null)
                {
                    return ExtractRunPropertiesFromElement(rPr);
                }
            }

            return styleProperty;
        }

        private static RunProperty ExtractRunPropertiesFromElement(RunProperties rPr)
        {
            var styleProperty = new RunProperty();

            var rFont = rPr.Elements<RunFonts>().FirstOrDefault();
            if (rFont != null)
            {
                styleProperty.FontFamily = rFont.Ascii?.Value ?? "";
            }
            var rColor = rPr.Elements<Color>().FirstOrDefault();
            if (rColor != null)
            {
                styleProperty.Color = rColor.Val?.Value ?? "";
            }
            var rSize = rPr.Elements<FontSize>().FirstOrDefault();
            if (rSize != null)
            {
                styleProperty.FontSize = rSize.Val?.Value ?? "";
            }
            var rBold = rPr.Elements<Bold>().FirstOrDefault();
            styleProperty.Bold = rBold != null ? "true" : "false";
            var rItalic = rPr.Elements<Italic>().FirstOrDefault();
            styleProperty.Italic = rItalic != null ? "true" : "false";
            var rUnderline = rPr.Elements<Underline>().FirstOrDefault();
            styleProperty.Underline = rUnderline != null ? "true" : "false";

            return styleProperty;
        }

        public static List<PropertyRevision> GetPropertyChange(DocumentInfo documentInfo, Run run)
        {
            var result = new List<PropertyRevision>();
            var rPrChanges = run.Elements<RunPropertiesChange>().ToList();
            if (rPrChanges.Count > 0)
            {
                foreach (var rPrChange in rPrChanges)
                {
                    var nativeRevision = new NativeRevision
                    {
                        RevId = rPrChange.Id?.Value ?? "",
                        RevType = "RunPropertiesChange",
                        Author = rPrChange.Author?.Value ?? "",
                        Date = rPrChange.Date?.Value.ToString() ?? ""
                    };
                    documentInfo.NativeRevisions[nativeRevision.RevId] = nativeRevision;

                    result.Add(new PropertyRevision
                    {
                        RevId = nativeRevision.RevId,
                        RevType = nativeRevision.RevType
                    });
                }
            }
            return result;
        }

        public static List<PropertyRevision> GetPropertyChange(DocumentInfo documentInfo, Hyperlink hyperlink)
        {
            var result = new List<PropertyRevision>();
            var runs = hyperlink.Elements<Run>().ToList();
            foreach (var run in runs)
            {
                var rPrChanges = run.Elements<RunPropertiesChange>().ToList();
                foreach (var rPrChange in rPrChanges)
                {
                    var rPrId = rPrChange.Id?.Value ?? "";
                    var nativeRevision = new NativeRevision
                    {
                        RevId = rPrChange.Id?.Value ?? "",
                        RevType = "HyperlinkRunPropertiesChange",
                        Author = rPrChange.Author?.Value ?? "",
                        Date = rPrChange.Date?.Value.ToString() ?? ""
                    };
                    documentInfo.NativeRevisions[nativeRevision.RevId] = nativeRevision;

                    result.Add(new PropertyRevision
                    {
                        RevId = nativeRevision.RevId,
                        RevType = nativeRevision.RevType
                    });
                }
            }
            return result;
        }

        #endregion

        public static void InsertInsertRun(DocumentInfo documentInfo, InsertedRun insertedRun)
        {

            var revisionId = insertedRun.Id?.Value ?? "";
            documentInfo.NativeRevisions[revisionId] = new NativeRevision
            {
                RevId = revisionId,
                RevType = "InsertRun",
                Author = insertedRun.Author?.Value ?? "",
                Date = insertedRun.Date != null ? insertedRun.Date.Value.ToString() : ""
            };
        }

        public static void InsertDeletedRun(DocumentInfo documentInfo, DeletedRun deletedRun)
        {
            // check if revision exists
            var revisionId = deletedRun.Id?.Value ?? "";
            documentInfo.NativeRevisions[revisionId] = new NativeRevision
            {
                RevId = revisionId,
                RevType = "DeletedRun",
                Author = deletedRun.Author?.Value ?? "",
                Date = deletedRun.Date != null ? deletedRun.Date.Value.ToString() : ""
            };
        }

        public static void InsertRevision<T>(DocumentInfo documentInfo, T revisionElement)
            where T : OpenXmlElement
        {
            var revisionId = "";
            var revisionType = "";
            var author = "";
            var date = "";
            switch (revisionElement)
            {
                case InsertedRun insertedRun:
                    revisionId = insertedRun.Id?.Value ?? "";
                    revisionType = "InsertedRun";
                    author = insertedRun.Author?.Value ?? "";
                    date = insertedRun.Date != null ? insertedRun.Date.Value.ToString() : "";
                    break; // Exit after inserting InsertedRun
                case DeletedRun deletedRun:
                    revisionId = deletedRun.Id?.Value ?? "";
                    revisionType = "DeletedRun";
                    author = deletedRun.Author?.Value ?? "";
                    date = deletedRun.Date != null ? deletedRun.Date.Value.ToString() : "";
                    break; // Exit after inserting DeletedRun
                default:
                    break;
            }
            if (revisionId == "" && revisionType == "")
                return;

            documentInfo.NativeRevisions[revisionId] = ConverterFactory.CreateNativeRevision(
                revisionId, revisionType, author, date);
        }

        public static void InsertHyperlink(DocumentInfo documentInfo, Hyperlink hyperlink)
        {

            var hyperlinkId = hyperlink.Id?.Value ?? "";
            if (documentInfo.Annotations.Hyperlinks.ContainsKey(hyperlinkId))
            {
                return;
            }
            documentInfo.Annotations.Hyperlinks[hyperlinkId] = ConverterFactory.CreateDocHyperlink(
                hyperlinkId, hyperlink.NamespaceUri ?? "", []);

        }

        public static void InsertCommonSegments(ParagraphElement paraItem, string segType)
        {
            var UnknownSegment = new Segment
            {
                SegmentId = segType + "_" + paraItem.Segments.Count.ToString(),
                SegmentText = segType,
                Start = 0,
                End = 0,
                Properties = new RunProperty(),
                FieldId = "",
                HyperlinkId = "",
                ContentRevisionId = ""
            };
            paraItem.Segments.Add(UnknownSegment);
        }

        public static string AddContext(string currentContext, string newTag)
        {
            if (string.IsNullOrEmpty(currentContext))
                return newTag;

            if (string.IsNullOrEmpty(newTag))
                return currentContext;

            // 检查是否已经存在相同标签，避免重复
            var existingTags = currentContext.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                        .Select(tag => tag.Trim())
                                        .ToHashSet(StringComparer.OrdinalIgnoreCase);

            if (!existingTags.Contains(newTag))
            {
                return $"{currentContext},{newTag}";
            }

            return currentContext;
        }
        public static bool IsInNestedContext(string contextTags, string targetTag)
        {
            if (string.IsNullOrEmpty(contextTags) || string.IsNullOrEmpty(targetTag))
            {
                return false;
            }

            // Split contextTags by comma and check if targetTag is present
            var tags = contextTags.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            return tags.Contains(targetTag, StringComparer.OrdinalIgnoreCase);
        }
    }

    public static class TextExtractor
    {
        ///<summary>
        /// Extract text from run
        /// </summary>    
        public static string ExtractFromElement<T>(T element)
        where T : OpenXmlElement
        {
            if (element == null)
            {
                return string.Empty; // Return empty string if element is null
            }
            switch (element)
            {
                case Run run:
                    return ExtractFromRun(run);
                case DeletedRun deletedRun:
                    return string.Concat(deletedRun.Elements<DeletedText>().Select(t => t.Text));
                case InsertedRun insertedRun:
                    return string.Concat(insertedRun.Elements<Text>().Select(t => t.Text));
                case Hyperlink hyperlink:
                    return string.Concat(hyperlink.Elements<Text>().Select(t => t.Text));
                default:
                    return string.Empty; // Return empty string for unsupported types
            }
        }

        public static String ExtractFromRun(Run run)
        {
            var content = new StringBuilder();

            foreach (var element in run.Elements<OpenXmlElement>())
            {
                var text = element switch
                {
                    Text textElement => textElement.Text,
                    // InstrText instrTextElement => instrTextElement.Text,
                    // TabChar => "\t",
                    // CarriageReturn => "\n",
                    // Break => "\n",
                    // FieldChar => string.Empty,
                    // FieldCode => string.Empty,
                    _ => element.InnerText
                };

                content.Append(text);
            }

            return content.ToString();
        }

        /// <summary>
        /// Extract text from revision element
        /// </summary>
        public static string ExtractFromElementDescendants<T>(T element)
        where T : OpenXmlElement
        {
            if (element == null)
            {
                return string.Empty; // Return empty string if element is null
            }
            switch (element)
            {
                case Run run:
                    return string.Concat(run.Descendants<Text>().Select(t => t.Text));
                case DeletedRun deletedRun:
                    return string.Concat(deletedRun.Descendants<DeletedText>().Select(t => t.Text));
                case InsertedRun insertedRun:
                    return string.Concat(insertedRun.Descendants<Text>().Select(t => t.Text));
                case Hyperlink hyperlink:
                    return string.Concat(hyperlink.Descendants<Text>().Select(t => t.Text));
                default:
                    return string.Empty; // Return empty string for unsupported types
            }
        }

        /// <summary>
        /// extract from hyperlink
        /// </summary>
        public static string ExtractFromHyperlink(Hyperlink hyperlink)
        {
            return string.Concat(hyperlink.Descendants<Text>().Select(t => t.Text));
        }
    }

    public static class ConverterFactory
    {
        /// <summary>
        /// Create a segment from a standard
        /// </summary>
        public static Segment CreateSegment(
            ParagraphElement paraItem,
            string content,
            int startPosition,
            RunProperty properties,
            List<PropertyRevision> propRevisions,
            string revisionId = "",
            string hyperlinkId = "",
            string fieldId = "")
        {
            properties ??= new RunProperty();
            propRevisions ??= [];
            return new Segment
            {
                SegmentId = paraItem.ElementId + "-" + paraItem.Segments.Count.ToString(),
                SegmentText = content,
                Start = startPosition,
                End = startPosition + content.Length,
                Properties = properties,
                FieldId = fieldId,
                HyperlinkId = hyperlinkId,
                ContentRevisionId = revisionId,
                PropRevisions = propRevisions
            };
        }

        public static DocumentInfo CreateDocumentInfo(string filePath)
        {
            return new DocumentInfo
            {
                Version = "1.0",
                DocumentId = Path.GetFileName(filePath),
                Elements = [],
                Annotations = CreateAnnotations(),
                NativeRevisions = []
            };
        }

        public static Annotations CreateAnnotations()
        {
            return new Annotations
            {
                Comments = [],
                Hyperlinks = []
            };
        }

        public static ParagraphElement CreateParagraphElement(string paraId, Paragraph paragraph)
        {
            return new ParagraphElement
            {
                ElementType = "Paragraph",
                ElementId = paraId,
                PlainText = "",
                Properties = new ParagraphProperty(),
                Segments = []
            };
        }


        /// <summary>
        /// 创建列表段落元素，并设置列表属性
        /// </summary>
        /// <param name="paraId">段落ID</param>
        /// <param name="paragraph">包含列表信息的段落</param>
        /// <param name="listInfo">列表信息对象,可以为null</param>
        /// <returns>初始化后的段落元素</returns>
        public static ParagraphElement CreateListParagraphElement(string paraId, Paragraph paragraph, ListInfo? listInfo)
        {
            var paraElement = new ParagraphElement
            {
                ElementType = "List",
                ElementId = paraId,
                PlainText = "",
                Properties = new ParagraphProperty
                {
                    ListInfo = listInfo
                },
                Segments = []
            };
            // 保留原有段落内容处理逻辑, 由调用方处理
            // GetPragraphProperties(documentInfo, paraElement, paragraph);
            return paraElement;
        }

        public static TableElement CreateTableElement(int index)
        {
            var tableId = $"tbl-{index}";
            return new TableElement
            {
                ElementType = "Table",
                ElementId = tableId,
                Rows = []
            };
        }
        public static TableRowElement CreateTableRowElement(int rowIndex)
        {
            return new TableRowElement
            {
                RowId = rowIndex,
                Cells = []
            };
        }

        public static TableCellElement CreateTableCellElement(int tableIdx, int rowIdx, int cellIdx)
        {
            return new TableCellElement
            {
                CellId = cellIdx,
                Elements = []
            };
        }

        public static DocComment CreateDocComment(string commentId, string author, string date, string text)
        {
            return new DocComment
            {
                CommentId = commentId,
                Author = author,
                Date = date,
                Text = text
            };
        }
        public static DocHyperlink CreateDocHyperlink(string hyperlinkId, string uri, List<string> targets)
        {
            return new DocHyperlink
            {
                HyperlinkId = hyperlinkId,
                Uri = uri,
                Targets = targets ?? []
            };
        }

        public static NativeRevision CreateNativeRevision(string revId, string revType, string author, string date)
        {
            return new NativeRevision
            {
                RevId = revId,
                RevType = revType,
                Author = author,
                Date = date
            };
        }
    }

    public static class IdGenerator
    {
        public static string GenerateParagraphId(DocumentInfo documentInfo, string? existingId = null)
        {
            return existingId ?? $"None-{documentInfo.Elements.Count}";
        }

        public static string GenerateTableId(int tableCount) => $"Table-{tableCount}";

        public static string GenerateRowId(int rowCounter) => $"Row-{rowCounter}";

        public static string GenerateCellId(string tableId, int rowIndex, int cellIndex)
            => $"{tableId}-Cell-{rowIndex}_{cellIndex}";
    }

}