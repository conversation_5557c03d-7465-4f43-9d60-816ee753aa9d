using System;
using System.Linq;
using Common.DocItemModel;
using Convert.Abstract;
using Convert.Exceptions;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Helper
{
    /// <summary>
    /// Word document list detector, used to identify list paragraphs in Word documents and extract list properties
    /// </summary>
    public class WordListDetector : IListDetector
    {
        /// <summary>
        /// Determines whether a paragraph is a list paragraph.
        /// </summary>
        /// <param name="paragraph">The paragraph to check.</param>
        /// <returns>Returns true if it is a list paragraph, otherwise false.</returns>
        public bool IsListParagraph(Paragraph paragraph)
        {
            return paragraph.Elements<ParagraphProperties>()
                            .Any(pPr => pPr.Elements<NumberingProperties>().Any());
        }

        /// <summary>
        /// Extracts list property information from a paragraph using the Open XML SDK.
        /// </summary>
        /// <param name="paragraph">The paragraph containing list information.</param>
        /// <param name="numbering">The numbering part of the document.</param>
        /// <returns>A ListInfo object with extracted properties, or null if extraction fails.</returns>
        public ListInfo? ExtractListProperties(Paragraph paragraph, Numbering numbering)
        {
            var pPr = paragraph.Elements<ParagraphProperties>().FirstOrDefault();
            var numPr = pPr?.Elements<NumberingProperties>().FirstOrDefault();
            if (numPr == null) return null;

            var numIdValue = numPr.Elements<NumberingId>().FirstOrDefault()?.Val?.Value;
            var ilvlValue = numPr.Elements<NumberingLevelReference>().FirstOrDefault()?.Val?.Value;

            if (numIdValue == null || ilvlValue == null) return null;

            var numId = numIdValue.Value;
            var ilvl = ilvlValue.Value;

            var numberingInstance = numbering.Elements<NumberingInstance>().FirstOrDefault(n => n.NumberID?.Value == numId);
            if (numberingInstance == null) return null;

            var abstractNumId = numberingInstance.AbstractNumId?.Val?.Value;
            if (abstractNumId == null) return null;

            var abstractNum = numbering.Elements<AbstractNum>().FirstOrDefault(an => an.AbstractNumberId?.Value == abstractNumId);
            if (abstractNum == null) return null;

            var level = abstractNum.Elements<Level>().FirstOrDefault(l => l.LevelIndex?.Value == ilvl);
            if (level == null) return null;

            var listType = level.NumberingFormat?.Val?.InnerText;
            var indentLeft = level.PreviousParagraphProperties?.Indentation?.Left?.Value;
            var levelText = level.LevelText?.Val?.Value;

            return new ListInfo
            {
                NumId = numId,
                Level = ilvl,
                ListType = listType ?? "none",
                IndentLeft = indentLeft != null ? int.Parse(indentLeft) : (int?)null,
                LevelText = levelText
            };
        }
    }
}