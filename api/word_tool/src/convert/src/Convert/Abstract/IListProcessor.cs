using Common.DocItemModel;
using Convert.Model;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Abstract
{
    /// <summary>
    /// Processor interface for handling list paragraphs in Word documents
    /// </summary>
    public interface IListProcessor
    {
        /// <summary>
        /// Processes a list paragraph, extracts list properties, and creates a ParagraphElement
        /// </summary>
        /// <param name="documentInfo">Document information object</param>
        /// <param name="paragraph">The list paragraph to process</param>
        /// <param name="paraId">Paragraph ID</param>
        /// <param name="numbering">The numbering part of the document</param>
        /// <returns>The processed paragraph element with ElementType set to "List"</returns>
        /// <exception cref="ArgumentNullException">Thrown when a parameter is null</exception>
        /// <exception cref="InvalidOperationException">Thrown when the paragraph is not a list paragraph</exception>
        ParagraphElement? ProcessListParagraph(DocumentInfo documentInfo, Paragraph paragraph, string paraId, Numbering numbering);
    }
}