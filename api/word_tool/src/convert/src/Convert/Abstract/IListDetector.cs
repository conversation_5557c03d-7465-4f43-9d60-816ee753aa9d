using Common.DocItemModel;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Convert.Abstract
{
    /// <summary>
    /// List detector interface, used to identify and extract list information from Word documents
    /// </summary>
    public interface IListDetector
    {
        /// <summary>
        /// Determines whether a paragraph is a list paragraph
        /// </summary>
        /// <param name="paragraph">The paragraph to check</param>
        /// <returns>Returns true if it is a list paragraph, otherwise false</returns>
        bool IsListParagraph(Paragraph paragraph);

        /// <summary>
        /// Extracts list property information from a paragraph
        /// </summary>
        /// <param name="paragraph">The paragraph containing list information</param>
        /// <param name="numbering">The numbering part of the document</param>
        /// <returns>List information object, or null if not a list paragraph</returns>
        ListInfo? ExtractListProperties(Paragraph paragraph, Numbering numbering);
    }
}