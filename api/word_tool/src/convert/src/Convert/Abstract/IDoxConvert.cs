﻿using Convert.Model;

namespace Convert.Abstract
{
    public interface IDocxConverter
    {
        /// <summary>
        /// Parse the DOCX file and extract paragraph information.
        /// </summary>
        /// <param name="filePath">DOCX path</param>
        /// <returns>Paragraph information list</returns>
        List<ParagraphInfo> Convert(string filePath);

        DocumentInfo ConvertToDocumentInfo(string filePath);
    }
}
