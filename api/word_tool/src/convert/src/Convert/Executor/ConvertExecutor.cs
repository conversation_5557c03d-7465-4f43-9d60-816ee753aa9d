﻿using Amazon.Lambda.Core;
using Amazon.S3.Model;
using Common.Abstract;
using Common.DocItemModel;
using Common.Model;
using Common.Service;
using Convert.Abstract;
using Convert.Helper;
using Convert.Model;


namespace Convert.Executor
{
    public class ConvertExecutor : Common.Abstract.Executor
    {
        private ConvertEvent convertEvt;

        public ConvertExecutor(ConvertEvent evt, ILambdaContext _context)
        {
            convertEvt = evt;
            context = _context;
            s3Service = new S3Service(settings.Region, context);
        }

        public ConvertExecutor(IS3Service _s3Service, ConvertEvent _applyRevisionEvent, ILambdaContext _context)
        {
            convertEvt = _applyRevisionEvent;
            context = _context;
            s3Service = _s3Service;
        }

        private async Task<GetObjectResponse?> GetDocument(string bucket, string key, ILambdaContext context)
        {
            var documentRes = await s3Service.GetObjectAsync(bucket, key);
            return documentRes;
        }

        private DocumentInfo GetParagraphInfos(string filePath, ILambdaContext context)
        {
            IDocxConverter docxConverter = new DocxConverter(context);
            var result = docxConverter.ConvertToDocumentInfo(filePath);
            return result ?? new DocumentInfo
            {
                Version = "1.0",
                DocumentId = "",
                Elements = new List<DocItem>(),
                Annotations = new Annotations(),
                NativeRevisions = new Dictionary<string, DocItem>()
            };
        }

        public async override Task<ResultResponse> Run()
        {
            var errorResponse = ResponseFactory<string>.error(
            data: "",
            message: "Event is empty."
        );
            if (convertEvt == null)
            {
                context.Logger.LogError("Convert event is empty.");
                return errorResponse;
            }

            if (string.IsNullOrEmpty(convertEvt.Bucket) || string.IsNullOrEmpty(convertEvt.Key))
            {
                context.Logger.LogInformation($"Bucket: {convertEvt.Bucket} or Key: {convertEvt.Key} is empty.");
                errorResponse.Message = "The bucket or key is empty.";
                return errorResponse;
            }

            var documentRes = await GetDocument(convertEvt.Bucket, convertEvt.Key, context);
            if (documentRes?.ResponseStream == null)
            {
                errorResponse.Message = "Get Document failed.";
                return errorResponse;
            }

            var tempFilePath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.temp.dox");
            using (FileStream fileStream = new FileStream(tempFilePath, FileMode.Create))
            {
                await documentRes.ResponseStream.CopyToAsync(fileStream);
            }

            try
            {
                var result = GetParagraphInfos(tempFilePath, context);
                var successRes = ResponseFactory<DocumentInfo>.success(result);
                return successRes;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Convert file error occurs, ex: {ex.ToString()}");
            }

            if (File.Exists(tempFilePath))
            {
                File.Delete(tempFilePath);
            }

            errorResponse.Message = "Convert document changes failed.";
            return errorResponse;
        }
        
    }
}
