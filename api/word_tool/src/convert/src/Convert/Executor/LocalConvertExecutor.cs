using Amazon.Lambda.Core;
using Common.DocItemModel;
using Common.Model;
using Convert.Helper;
using Convert.Model;
using Newtonsoft.Json;

public class LocalConvertExecutor
{

    /// <summary>
    /// Mock implementation of ILambdaContext for local development.
    /// Provides console-based logging instead of CloudWatch.
    /// </summary>
    public class LocalLambdaContext : ILambdaContext
    {
        public string AwsRequestId => Guid.NewGuid().ToString();
        public IClientContext ClientContext => null!;
        public string FunctionName => "ApplyRevision-Local";
        public string FunctionVersion => "1.0.0";
        public ICognitoIdentity Identity => null!;
        public string InvokedFunctionArn => "arn:aws:lambda:local:000000000000:function:ApplyRevision-Local";
        public ILambdaLogger Logger { get; }
        public string LogGroupName => "/aws/lambda/ApplyRevision-Local";
        public string LogStreamName => DateTime.Now.ToString("yyyy/MM/dd/HHmmss");
        public int MemoryLimitInMB => 512;
        public TimeSpan RemainingTime => TimeSpan.FromMinutes(5);

        public LocalLambdaContext()
        {
            Logger = new LocalLambdaLogger();
        }
    }

    /// <summary>
    /// Mock implementation of ILambdaLogger for local development.
    /// Outputs log messages to console with appropriate formatting.
    /// </summary>
    public class LocalLambdaLogger : ILambdaLogger
    {
        private string GetCallerInfo()
        {
            var frame = new System.Diagnostics.StackFrame(2, true);
            var fileName = System.IO.Path.GetFileName(frame.GetFileName()) ?? "Unknown";
            var lineNumber = frame.GetFileLineNumber();
            var methodName = frame.GetMethod()?.Name ?? "Unknown";
            return $"{fileName}:{lineNumber} [{methodName}]";
        }

        public void Log(string message)
        {
            Console.WriteLine($"[LOG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
        }

        public void LogLine(string message)
        {
            Console.WriteLine($"[LOG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
        }

        public void LogInformation(string message)
        {
            Console.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
        }

        public void LogWarning(string message)
        {
            Console.WriteLine($"[WARN] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
        }

        public void LogError(string message)
        {
            Console.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
        }

        public void LogCritical(string message)
        {
            Console.WriteLine($"[CRITICAL] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
        }
    }

    public async Task<ResultResponse> ExecuteLocalAsync(string inputPath, string? outputPath = null)
    {
        Console.WriteLine($"📥 Input file: {inputPath}");
        try
        {
            await Task.Delay(100); // Simulate async work

            var output = outputPath ?? Path.ChangeExtension(inputPath, ".json");
            Console.WriteLine($"📤 Output file: {output}");

            var mockContext = new LocalLambdaContext();
            var docxConverter = new DocxConverter(mockContext);
            var result = docxConverter.ConvertToDocumentInfo(inputPath) ?? new DocumentInfo
            {
                Version = "1.0",
                DocumentId = "",
                Elements = [],
                Annotations = new Annotations(),
                NativeRevisions = []
            };

            // 序列化结果到test.json
            var outputDir = Path.GetDirectoryName(output);
            var jsonPath = Path.Combine(outputDir!, "test.json");
            File.WriteAllText(jsonPath, JsonConvert.SerializeObject(result, Formatting.Indented));

            Console.WriteLine($"✅ Conversion successful. Output file: {output}");
            return ResponseFactory<string>.success("Conversion successful.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ An error occurred: {ex.Message}");
            return ResponseFactory<string>.error(ex.Message);
        }
    }
}