﻿using Common.DocItemModel;
using Common.Model;

namespace Convert.Model
{
    /// <summary>
    /// paragraph Information Data Structure
    /// A data structure used to describe paragraphs and their revision status extracted from a DOCX file.
    /// </summary>
    public class ParagraphInfo
    {
        /// <summary>
        /// Paragraph identifier（e.g: paraId）
        /// </summary>
        public required string ParaId { get; set; }

        /// <summary>
        /// Paragraph full text
        /// </summary>
        public string Text { get; set; } = "";

        /// <summary>
        /// a list of runs, including normal text and revision runs
        /// </summary>
        public List<Run> Runs { get; set; } = new List<Run> { };

        /// <summary>
        /// a dict of paragraph style properties
        /// </summary>
        public Dictionary<string, string> StyleProperties { get; set; } = new Dictionary<string, string> { };
    }

    public class DocumentInfo
    {
        /// <summary>
        /// Document identifier
        /// </summary>
        public required string Version { get; set; } = "1.0";

        /// <summary>
        /// Document title
        /// </summary>
        public string DocumentId { get; set; } = "";

        /// <summary>
        /// List of elemens in the document
        /// </summary>
        public List<DocItem> Elements { get; set; } = [];

        /// <summary>
        /// List of annotations in the document
        /// </summary>
        public Annotations Annotations { get; set; } = new Annotations();

        /// <summary>
        /// List of nativeRevisions in the document
        /// </summary>
        public Dictionary<string, DocItem> NativeRevisions { get; set; } = [];
    }

    /// <summary>
    /// Represents a revision-linked hyperlink record.
    /// </summary>
    public record RevLink(
        string ParaId,
        string? RevisionId,
        string DisplayText,
        string? TargetUri
    );
}
