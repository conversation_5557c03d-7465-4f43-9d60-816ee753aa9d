# ApplyRevision Strategy Architecture Analysis
# ApplyRevision 策略架构分析

## 概述 / Overview

本文档分析了现有的 `ApplyRevision.Strategy` 命名空间和 `IOperationStrategy` 接口，为实现新的 `CommentReplyStrategy` 提供架构指导。

This document analyzes the existing `ApplyRevision.Strategy` namespace and `IOperationStrategy` interface to provide architectural guidance for implementing the new `CommentReplyStrategy`.

## IOperationStrategy 接口分析 / Interface Analysis

### 核心方法 / Core Methods

```csharp
public interface IOperationStrategy
{
    // 主要实现方法 - 使用相对定位执行操作
    // Primary implementation method - Execute operation with relative positioning
    int ExecuteWithRelativePosition(WordprocessingDocument doc, Operation operation, 
        Run targetRun, int relativeStart, int relativeEnd);
    
    // 向后兼容方法 - 执行操作
    // Backward compatibility method - Execute operation
    void Execute(WordprocessingDocument doc, Operation operation);
}
```

### 方法职责 / Method Responsibilities

- **ExecuteWithRelativePosition**: 主要实现方法，返回操作引起的实际偏移变化
- **Execute**: 向后兼容方法，内部调用 ExecuteWithRelativePosition

## 现有策略架构 / Existing Strategy Architecture

### BaseOperationStrategy 基类 / Base Class

提供所有策略的通用功能：

**依赖注入 / Dependency Injection:**
- `ILambdaContext context` - 日志记录上下文
- `RunLocator runLocator` - Run元素定位器
- `IElementFactory elementFactory` - 元素工厂
- `IRevisionElementFactory revisionFactory` - 修订元素工厂
- `IIdManager idManager` - ID管理器
- `ICommentManager commentManager` - 注释管理器

**通用方法 / Common Methods:**
- `CreateCommentComponent()` - 创建注释组件
- `CreateInsertedRun()` - 创建插入修订元素
- `CreateDeletedRun()` - 创建删除修订元素
- `AddCommentToDocument()` - 将注释添加到文档
- `LocateTextPosition()` - 定位文本位置
- `ValidateOperationRange()` - 验证操作范围

### 现有策略类 / Existing Strategy Classes

1. **InsertStrategy** - 插入文本操作
   - 支持带注释的插入
   - 创建 InsertedRun 修订元素
   - 处理文本分割和位置计算

2. **DeleteStrategy** - 删除文本操作
   - 支持带注释的删除
   - 创建 DeletedRun 修订元素
   - 处理整个Run删除或部分删除

3. **ReplaceStrategy** - 替换文本操作
   - 支持带注释的替换
   - 结合删除和插入操作
   - 保持格式一致性

4. **FormatStrategy** - 格式化操作
   - 处理文本格式属性变更
   - 创建 RunPropertiesChange 修订元素
   - 支持部分或全部Run格式化

## 操作类型系统 / Operation Type System

### OperationType 枚举 / Enum

```csharp
public enum OperationType
{
    Insert,   // 插入操作
    Delete,   // 删除操作
    Replace,  // 替换操作
    Format    // 格式化操作
    // 需要添加: CommentReply
}
```

### Operation 模型 / Model

```csharp
public class Operation
{
    public OperationType Op { get; set; }           // 操作类型
    public Target Target { get; set; }              // 目标位置
    public Range Range { get; set; }                // 范围
    public string Text { get; set; }                // 文本内容
    public Dictionary<string, object>? Props { get; set; }  // 格式属性
    public Comment? Comment { get; set; }           // 注释信息
    public Revision? Revision { get; set; }         // 修订信息
    // 需要添加: public string? CommentId { get; set; }
}
```

## 注释处理机制 / Comment Handling Mechanism

### CommentManager 服务 / Service

**核心功能 / Core Functions:**
- `CreateComment()` - 创建注释元素
- `CreateCommentComponent()` - 创建完整注释组件
- `AddComment()` - 添加注释到文档
- `CreateCommentReference()` - 创建注释引用

### CommentComponent 组件 / Component

```csharp
public class CommentComponent
{
    public Comment Comment { get; set; }              // 注释元素
    public CommentRangeStart RangeStart { get; set; } // 注释范围开始
    public CommentRangeEnd RangeEnd { get; set; }     // 注释范围结束
    public Run Reference { get; set; }                // 注释引用Run
}
```

## 工厂模式 / Factory Pattern

### OperationStrategyFactory

```csharp
public IOperationStrategy CreateStrategy(OperationType operationType)
{
    return operationType switch
    {
        OperationType.Insert => new InsertStrategy(...),
        OperationType.Delete => new DeleteStrategy(...),
        OperationType.Replace => new ReplaceStrategy(...),
        OperationType.Format => new FormatStrategy(...),
        // 需要添加: OperationType.CommentReply => new CommentReplyStrategy(...)
        _ => throw new ArgumentException($"Unsupported operation type: {operationType}")
    };
}
```

## CommentReplyStrategy 设计指导 / Design Guidance

### 实现要点 / Implementation Points

1. **继承 BaseOperationStrategy** - 复用通用功能
2. **实现 ExecuteWithRelativePosition** - 主要逻辑方法
3. **处理注释查找** - 通过 CommentId 定位目标注释
4. **创建回复结构** - 根据 OpenXML 规范创建回复
5. **集成现有服务** - 使用 CommentManager 等现有服务

### 特殊考虑 / Special Considerations

- CommentReply 操作不需要 Target 和 Range（因为是回复现有注释）
- 需要扩展 Operation 模型添加 CommentId 字段
- 可能需要扩展 CommentManager 添加查找和回复功能
- 返回值应为 0（因为不改变文档文本偏移）

## 总结 / Summary

现有架构为实现 CommentReplyStrategy 提供了良好的基础：
- 清晰的策略模式和工厂模式
- 完善的依赖注入和服务层
- 成熟的注释处理机制
- 统一的错误处理和日志记录

The existing architecture provides a solid foundation for implementing CommentReplyStrategy:
- Clear strategy and factory patterns
- Comprehensive dependency injection and service layer
- Mature comment handling mechanism
- Unified error handling and logging
