```json
{
    "id": "759764f2-fbf9-428e-9b77-ae1d04e0f16b",
    "op": "replace",
    "target": [
        {
            "segId": "6A3102BA-3",
            "range": {
                "start": 245,
                "end": 246
            }
        }
    ],
    "text": "\n\n",
    "revision": {
        "author": "Styling Agent",
        "date": "2025-06-24T04:41:48Z"
    }
}
```

当遇到包含换行符（"\n\n"）的replace操作时，需要实现段落分割功能。具体要求如下：

**触发条件：**
- 操作类型为"replace"
- 替换文本为"\n\n"（双换行符）
- 目标位置通过segId和range精确定位

**实现逻辑（基于Word文档处理机制）：**
1. **创建新段落：** 创建一个空的段落克隆（paragraphClone），继承当前段落的所有属性（样式、格式等）
2. **内容迁移：** 从当前段落中的开头开始逐步移除（pop）元素，按顺序追加到paragraphClone中，直到达到指定的分割位置（range.start位置）
3. **段落插入：** 将paragraphClone插入到当前段落之前，形成两个独立的段落

**技术细节：**
- 我们需要实现一个新的策略类
- 分割位置由target中的segId和range.start确定
- 需要正确处理OpenXML文档结构，确保段落属性正确继承
- 分割后的两个段落应保持原有的格式和样式

**示例场景：**
当在segId "6A3102BA-3"的第245-246位置执行换行操作时，该位置之前的所有内容应移动到新创建的段落中。
