# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a multi-language monorepo containing:

- **C# Services** (located in root directories): Word document processing services
  - `apply_revision/`: Applies changes/revisions to Word documents
  - `convert/`: Converts Word documents to other formats
  - `common/`: Shared C# utilities and models

- **Python APIs** (located in `wordtool/api/`): REST API services
  - `styling_agent/`: AI-powered document styling agent
  - `caas_repository/`: Content as a Service repository API
  - `common/`: Shared Python utilities

- **Frontend Applications** (located in `wordtool/app/` and `wordtool/front-end/`)
- **Backend Services** (located in `wordtool/back-end/`)

## Build Commands

### C# Projects
```bash
# Build all C# projects
dotnet build word_tool.sln

# Run all C# tests
dotnet test word_tool.sln

# Build specific project
dotnet build apply_revision/src/ApplyRevision/ApplyRevision.csproj

# Run tests for specific project
dotnet test apply_revision/test/ApplyRevision.Test/ApplyRevision.Tests.csproj
```

### Python Projects
Each Python project uses Poetry for dependency management:

```bash
# Navigate to specific Python project
cd wordtool/api/styling_agent

# Install dependencies
poetry install --no-root

# Run tests
poetry run pytest

# Run with coverage
poetry run pytest --cov
```

## Testing Framework

- **C#**: Uses xUnit with Moq for mocking
- **Python**: Uses pytest with standard test discovery
- Test files are located in `test/` directories alongside source code

## Architecture Notes

### C# Word Processing Services
- Built as AWS Lambda functions targeting .NET 8.0
- Uses DocumentFormat.OpenXml for Word document manipulation
- Follows strategy pattern for different operation types (Insert, Replace, Delete, Format, Comment operations)
- Implements offset tracking for accurate text positioning during document modifications

### Python API Services
- FastAPI-based REST services
- Uses Poetry for dependency management
- Implements agent-based architecture for document styling
- Integrates with external LLM services via llm-proxy

### Key Components
- **ApplyRevision**: Core service for applying document changes with sophisticated offset calculation
- **Convert**: Document format conversion service
- **StylingAgent**: AI-powered document styling and formatting
- **Common**: Shared utilities for S3, document handling, and validation

## Development Workflow

1. This repo follows a dev -> main branch strategy
2. Create feature branches from dev
3. Use squash merge for pull requests
4. Automatic deployment to dev environment after merge
5. Cherry-pick commits to main for production releases

## Coding Guidelines

- **Documentation and Comments**:
  - Always write code comments and docstrings in English, even if the surrounding context contains Chinese comments