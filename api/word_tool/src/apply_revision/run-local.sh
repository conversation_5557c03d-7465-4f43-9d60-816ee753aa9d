#!/bin/bash

# ApplyRevision Local Runner Script
# This script helps run the ApplyRevision tool locally for development and testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -d, --document PATH     Path to the Word document (.docx)"
    echo "  -r, --revision PATH     Path to the revision file (.json)"
    echo "  -o, --output PATH       Output path (optional)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -d test.docx -r patch.json"
    echo "  $0 --document test.docx --revision patch.json --output modified.docx"
    echo ""
    echo "Environment Variables:"
    echo "  DOTNET_CLI_TELEMETRY_OPTOUT=1  Disable .NET telemetry"
}

# Parse command line arguments
DOCUMENT=""
REVISION=""
OUTPUT=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--document)
            DOCUMENT="$2"
            shift 2
            ;;
        -r|--revision)
            REVISION="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required arguments
if [[ -z "$DOCUMENT" ]]; then
    print_error "Document path is required"
    show_usage
    exit 1
fi

if [[ -z "$REVISION" ]]; then
    print_error "Revision path is required"
    show_usage
    exit 1
fi

# Check if files exist
if [[ ! -f "$DOCUMENT" ]]; then
    print_error "Document file not found: $DOCUMENT"
    exit 1
fi

if [[ ! -f "$REVISION" ]]; then
    print_error "Revision file not found: $REVISION"
    exit 1
fi

# Capture the original working directory before changing directories
ORIGINAL_DIR="$(pwd)"

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR/src/ApplyRevision"

# Check if project exists
if [[ ! -f "$PROJECT_DIR/ApplyRevision.csproj" ]]; then
    print_error "ApplyRevision project not found at: $PROJECT_DIR"
    exit 1
fi

print_info "Building ApplyRevision project..."

# Build the project
cd "$PROJECT_DIR"
if ! dotnet build --configuration Debug --verbosity quiet; then
    print_error "Failed to build the project"
    exit 1
fi

print_success "Project built successfully"

# Function to convert relative path to absolute path
get_absolute_path() {
    local path="$1"
    if [[ "$path" = /* ]]; then
        # Already absolute
        echo "$path"
    else
        # Use Python to get the absolute path from the original directory
        (cd "$ORIGINAL_DIR" && python3 -c "import os; print(os.path.abspath('$path'))" 2>/dev/null) || {
            # Fallback if Python is not available
            local abs_path="$ORIGINAL_DIR/$path"
            # Remove ./ patterns
            abs_path="${abs_path//\/.\//\/}"
            echo "$abs_path"
        }
    fi
}

ABSOLUTE_DOCUMENT="$(get_absolute_path "$DOCUMENT")"
ABSOLUTE_REVISION="$(get_absolute_path "$REVISION")"

print_info "Resolved paths:"
print_info "  Document: $ABSOLUTE_DOCUMENT"
print_info "  Revision: $ABSOLUTE_REVISION"

# Verify files exist before proceeding
if [[ ! -f "$ABSOLUTE_DOCUMENT" ]]; then
    print_error "Document file not found: $ABSOLUTE_DOCUMENT"
    exit 1
fi

if [[ ! -f "$ABSOLUTE_REVISION" ]]; then
    print_error "Revision file not found: $ABSOLUTE_REVISION"
    exit 1
fi

# Prepare arguments for the application
ARGS="--document \"$ABSOLUTE_DOCUMENT\" --revision \"$ABSOLUTE_REVISION\""
if [[ -n "$OUTPUT" ]]; then
    # If output is relative, make it relative to original directory
    if [[ "$OUTPUT" = /* ]]; then
        ABSOLUTE_OUTPUT="$OUTPUT"
    else
        ABSOLUTE_OUTPUT="$ORIGINAL_DIR/$OUTPUT"
    fi
    ARGS="$ARGS --output \"$ABSOLUTE_OUTPUT\""
else
    # Set default output to original directory with modified filename
    DEFAULT_OUTPUT="$ORIGINAL_DIR/$(basename "$DOCUMENT" .docx)_modified.docx"
    ARGS="$ARGS --output \"$DEFAULT_OUTPUT\""
    ABSOLUTE_OUTPUT="$DEFAULT_OUTPUT"
fi

print_info "Running ApplyRevision locally..."
print_info "Document: $ABSOLUTE_DOCUMENT"
print_info "Revision: $ABSOLUTE_REVISION"
print_info "Output: $ABSOLUTE_OUTPUT"

# Disable .NET telemetry for cleaner output
export DOTNET_CLI_TELEMETRY_OPTOUT=1

# Run the application
if eval "dotnet run --configuration Debug --no-build -- $ARGS"; then
    print_success "ApplyRevision completed successfully"
else
    print_error "ApplyRevision failed"
    exit 1
fi
