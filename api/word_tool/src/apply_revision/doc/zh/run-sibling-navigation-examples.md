# OpenXML Run 兄弟节点导航示例

在OpenXML中，`Run`元素继承自`OpenXmlElement`，提供了多种方法来访问其兄弟节点。

## 基本属性和方法

### 1. 直接兄弟节点访问

```csharp
using DocumentFormat.OpenXml.Wordprocessing;

// 假设你有一个Run元素
Run currentRun = GetSomeRun();

// 获取下一个兄弟节点
OpenXmlElement nextSibling = currentRun.NextSibling();

// 获取上一个兄弟节点  
OpenXmlElement previousSibling = currentRun.PreviousSibling();

// 获取父节点
OpenXmlElement parent = currentRun.Parent;
```

### 2. 类型安全的兄弟节点访问

```csharp
// 获取下一个Run类型的兄弟节点
Run nextRun = currentRun.NextSibling<Run>();

// 获取上一个Run类型的兄弟节点
Run previousRun = currentRun.PreviousSibling<Run>();

// 检查是否存在
if (nextRun != null)
{
    Console.WriteLine($"Next run text: {nextRun.InnerText}");
}
```

### 3. 遍历所有兄弟节点

```csharp
// 遍历所有后续兄弟节点
var nextSiblings = new List<OpenXmlElement>();
var sibling = currentRun.NextSibling();
while (sibling != null)
{
    nextSiblings.Add(sibling);
    sibling = sibling.NextSibling();
}

// 遍历所有前面的兄弟节点
var previousSiblings = new List<OpenXmlElement>();
sibling = currentRun.PreviousSibling();
while (sibling != null)
{
    previousSiblings.Add(sibling);
    sibling = sibling.PreviousSibling();
}
```

### 4. 通过父节点访问所有兄弟节点

```csharp
// 通过父节点获取所有子元素（包括当前Run）
if (currentRun.Parent != null)
{
    var allSiblings = currentRun.Parent.ChildElements.ToList();
    
    // 找到当前Run的索引
    int currentIndex = allSiblings.IndexOf(currentRun);
    
    // 获取前面的兄弟节点
    var previousSiblings = allSiblings.Take(currentIndex).ToList();
    
    // 获取后面的兄弟节点
    var nextSiblings = allSiblings.Skip(currentIndex + 1).ToList();
}
```

### 5. 查找特定类型的兄弟节点

```csharp
// 查找下一个Run兄弟节点
public static Run FindNextRun(Run currentRun)
{
    var sibling = currentRun.NextSibling();
    while (sibling != null)
    {
        if (sibling is Run run)
            return run;
        sibling = sibling.NextSibling();
    }
    return null;
}

// 查找上一个Run兄弟节点
public static Run FindPreviousRun(Run currentRun)
{
    var sibling = currentRun.PreviousSibling();
    while (sibling != null)
    {
        if (sibling is Run run)
            return run;
        sibling = sibling.PreviousSibling();
    }
    return null;
}
```

### 6. 实际应用示例

```csharp
/// <summary>
/// 在指定Run之后插入新的Run
/// </summary>
public static void InsertRunAfter(Run targetRun, Run newRun)
{
    if (targetRun.Parent != null)
    {
        targetRun.Parent.InsertAfter(newRun, targetRun);
    }
}

/// <summary>
/// 在指定Run之前插入新的Run
/// </summary>
public static void InsertRunBefore(Run targetRun, Run newRun)
{
    if (targetRun.Parent != null)
    {
        targetRun.Parent.InsertBefore(newRun, targetRun);
    }
}

/// <summary>
/// 获取Run在段落中的位置索引
/// </summary>
public static int GetRunIndex(Run targetRun)
{
    if (targetRun.Parent is Paragraph paragraph)
    {
        var runs = paragraph.Elements<Run>().ToList();
        return runs.IndexOf(targetRun);
    }
    return -1;
}

/// <summary>
/// 获取段落中的所有Run兄弟节点
/// </summary>
public static List<Run> GetAllRunSiblings(Run targetRun)
{
    var siblings = new List<Run>();
    
    if (targetRun.Parent is Paragraph paragraph)
    {
        siblings = paragraph.Elements<Run>().Where(r => r != targetRun).ToList();
    }
    
    return siblings;
}
```

### 7. 处理复杂文档结构

```csharp
/// <summary>
/// 查找Run的所有相邻文本内容（包括其他元素中的文本）
/// </summary>
public static string GetSurroundingText(Run targetRun, int contextLength = 50)
{
    var beforeText = "";
    var afterText = "";
    
    // 获取前面的文本
    var prevSibling = targetRun.PreviousSibling();
    while (prevSibling != null && beforeText.Length < contextLength)
    {
        if (prevSibling is Run prevRun)
        {
            beforeText = prevRun.InnerText + beforeText;
        }
        else if (prevSibling.InnerText.Length > 0)
        {
            beforeText = prevSibling.InnerText + beforeText;
        }
        prevSibling = prevSibling.PreviousSibling();
    }
    
    // 获取后面的文本
    var nextSibling = targetRun.NextSibling();
    while (nextSibling != null && afterText.Length < contextLength)
    {
        if (nextSibling is Run nextRun)
        {
            afterText += nextRun.InnerText;
        }
        else if (nextSibling.InnerText.Length > 0)
        {
            afterText += nextSibling.InnerText;
        }
        nextSibling = nextSibling.NextSibling();
    }
    
    return $"{beforeText}[{targetRun.InnerText}]{afterText}";
}
```

## 注意事项

1. **空值检查**: 始终检查兄弟节点是否为null，特别是在文档边界处
2. **类型转换**: 使用`is`操作符或泛型方法进行安全的类型转换
3. **父节点验证**: 在操作兄弟节点之前确保父节点存在
4. **性能考虑**: 对于大型文档，避免频繁遍历兄弟节点
5. **文档结构**: 理解Word文档的层次结构（Document > Body > Paragraph > Run）

## 常见用例

- **文本操作**: 在特定位置插入或删除文本
- **格式应用**: 将格式应用到相邻的Run元素
- **内容分析**: 分析文本上下文
- **评论处理**: 在Run周围插入评论标记
- **修订跟踪**: 处理文档修订时的元素关系
