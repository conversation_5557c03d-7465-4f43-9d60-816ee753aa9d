# Word 评论功能开发指南 / Word Comment Development Guide

## 快速开始 / Quick Start

### 环境要求 / Prerequisites
- .NET 8.0 或更高版本
- DocumentFormat.OpenXml NuGet 包
- Amazon.Lambda.Core (用于日志记录)

### 基本设置 / Basic Setup

```csharp
// 1. 创建必要的服务实例
var context = lambdaContext; // 或 null 用于测试
var elementFactory = new ElementFactory(context);
var idManager = new IdManager(context);

// 2. 创建服务层组件
var commentXmlService = new CommentXmlService(context, elementFactory, idManager);
var documentManipulationService = new DocumentManipulationService(context, elementFactory);
var commentReplyExtensionService = new CommentReplyExtensionService(context);
var validationService = new ValidationService(context);

// 3. 创建编排器
var orchestrator = new CommentOrchestrator(
    context,
    commentXmlService,
    documentManipulationService,
    commentReplyExtensionService,
    validationService,
    idManager,
    elementFactory);

// 4. 初始化
using var document = WordprocessingDocument.Open(filePath, true);
orchestrator.Initialize(document.MainDocumentPart);
```

## 核心功能实现 / Core Feature Implementation

### 1. 创建基础评论 / Creating Basic Comments

```csharp
public async Task<bool> AddCommentToDocument(string filePath, string author, 
    string message, string paraId, int position)
{
    try
    {
        using var document = WordprocessingDocument.Open(filePath, true);
        
        // 初始化编排器
        orchestrator.Initialize(document.MainDocumentPart);
        
        // 创建评论组件
        var component = orchestrator.CreateCommentComponent(
            author, 
            DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"), 
            message);
        
        // 插入到文档
        bool success = orchestrator.InsertCommentIntoDocument(
            component, paraId, position);
        
        if (success)
        {
            orchestrator.Save();
            return true;
        }
        
        return false;
    }
    catch (Exception ex)
    {
        // 错误处理
        Console.WriteLine($"Error adding comment: {ex.Message}");
        return false;
    }
}
```

### 2. 创建评论回复 / Creating Comment Replies

```csharp
public async Task<bool> AddCommentReply(string filePath, string parentCommentId, 
    string author, string replyText)
{
    try
    {
        using var document = WordprocessingDocument.Open(filePath, true);
        
        // 初始化编排器
        orchestrator.Initialize(document.MainDocumentPart);
        
        // 创建回复
        bool success = orchestrator.CreateCommentReply(
            parentCommentId,
            author,
            DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"),
            replyText);
        
        if (success)
        {
            orchestrator.Save();
            return true;
        }
        
        return false;
    }
    catch (CommentNotFoundException ex)
    {
        Console.WriteLine($"Parent comment not found: {ex.Message}");
        return false;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error adding reply: {ex.Message}");
        return false;
    }
}
```

### 3. 查找和管理评论 / Finding and Managing Comments

```csharp
// 查找评论
var comment = orchestrator.FindCommentById("comment-id");
if (comment != null)
{
    Console.WriteLine($"Found comment by {comment.Author}: {comment.InnerText}");
}

// 获取评论数量
int count = orchestrator.GetCommentCount();
Console.WriteLine($"Total comments: {count}");

// 检查编排器状态
bool isInitialized = orchestrator.IsInitialized;
```

## 高级功能 / Advanced Features

### 1. 嵌套评论支持 / Nested Comment Support

DocumentManipulationService 支持嵌套评论的智能插入：

```csharp
// 系统会自动处理嵌套评论的正确顺序
// commentRangeStart (外层)
// commentRangeStart (内层)
// 文本内容
// commentRangeEnd (内层)
// commentReference (内层)
// commentRangeEnd (外层)
// commentReference (外层)
```

### 2. 自定义验证 / Custom Validation

```csharp
// 扩展 ValidationService
public class CustomValidationService : ValidationService
{
    public CustomValidationService(ILambdaContext? context) : base(context) { }
    
    public void ValidateCommentContent(string content)
    {
        if (content.Length > 1000)
        {
            throw new ArgumentException("Comment content too long");
        }
        
        if (content.Contains("禁用词"))
        {
            throw new ArgumentException("Comment contains prohibited content");
        }
    }
}
```

### 3. 批量操作 / Batch Operations

```csharp
public async Task<bool> AddMultipleComments(string filePath, 
    List<CommentData> comments)
{
    using var document = WordprocessingDocument.Open(filePath, true);
    orchestrator.Initialize(document.MainDocumentPart);
    
    bool allSuccess = true;
    
    foreach (var commentData in comments)
    {
        var component = orchestrator.CreateCommentComponent(
            commentData.Author, commentData.Date, commentData.Message);
        
        bool success = orchestrator.InsertCommentIntoDocument(
            component, commentData.ParaId, commentData.Position);
        
        if (!success)
        {
            allSuccess = false;
            // 记录失败但继续处理其他评论
        }
    }
    
    if (allSuccess)
    {
        orchestrator.Save();
    }
    
    return allSuccess;
}
```

## 错误处理最佳实践 / Error Handling Best Practices

### 1. 异常类型 / Exception Types

```csharp
try
{
    // 评论操作
}
catch (CommentNotFoundException ex)
{
    // 评论不存在
    logger.LogWarning($"Comment not found: {ex.CommentId}");
}
catch (ArgumentException ex)
{
    // 参数验证失败
    logger.LogError($"Invalid argument: {ex.Message}");
}
catch (InvalidOperationException ex)
{
    // 操作状态错误
    logger.LogError($"Invalid operation: {ex.Message}");
}
catch (Exception ex)
{
    // 其他未预期错误
    logger.LogError($"Unexpected error: {ex.Message}");
}
```

### 2. 日志记录 / Logging

```csharp
// 使用结构化日志
context?.Logger.LogInformation("Creating comment for author: {Author}", author);
context?.Logger.LogDebug("Comment ID generated: {CommentId}", commentId);
context?.Logger.LogWarning("Comment validation failed: {Reason}", reason);
context?.Logger.LogError("Failed to save document: {Error}", ex.Message);
```

## 测试指南 / Testing Guide

### 1. 单元测试示例 / Unit Test Example

```csharp
[Test]
public void CreateCommentComponent_ValidInput_ReturnsComponent()
{
    // Arrange
    var mockContext = new Mock<ILambdaContext>();
    var elementFactory = new ElementFactory(mockContext.Object);
    var idManager = new IdManager(mockContext.Object);
    var xmlService = new CommentXmlService(mockContext.Object, elementFactory, idManager);
    
    // Act
    var component = orchestrator.CreateCommentComponent("Author", "2024-01-01", "Message");
    
    // Assert
    Assert.IsNotNull(component);
    Assert.IsNotNull(component.Comment);
    Assert.AreEqual("Author", component.Comment.Author.Value);
}
```

### 2. 集成测试示例 / Integration Test Example

```csharp
[Test]
public void AddCommentReply_ValidParent_CreatesReply()
{
    // Arrange
    var testFile = "test-document.docx";
    File.Copy("template.docx", testFile, true);
    
    // Act
    using (var document = WordprocessingDocument.Open(testFile, true))
    {
        orchestrator.Initialize(document.MainDocumentPart);
        
        // 先创建父评论
        var parentComponent = orchestrator.CreateCommentComponent("Parent", "2024-01-01", "Parent comment");
        orchestrator.InsertCommentIntoDocument(parentComponent, "para1", 0);
        
        // 创建回复
        bool success = orchestrator.CreateCommentReply(
            parentComponent.Comment.Id.Value, "Child", "2024-01-02", "Reply comment");
        
        orchestrator.Save();
    }
    
    // Assert
    // 验证文件中的评论结构
    using (var document = WordprocessingDocument.Open(testFile, false))
    {
        var comments = document.MainDocumentPart.WordprocessingCommentsPart.Comments;
        Assert.AreEqual(2, comments.Elements<Comment>().Count());
    }
}
```

## 性能优化建议 / Performance Optimization

1. **批量操作**: 尽量批量处理多个评论，减少文档保存次数
2. **延迟初始化**: 只在需要时初始化服务组件
3. **内存管理**: 及时释放大型文档资源
4. **缓存策略**: 对频繁访问的评论数据进行缓存

## 常见问题 / Common Issues

### 1. durableId 格式问题
**问题**: 评论回复不显示
**解决**: 确保 durableId 以 0-7 开头，IdManager 已处理此问题

### 2. XML 命名空间错误
**问题**: XML 解析失败
**解决**: 使用正确的 OpenXML 命名空间前缀

### 3. 文档保存失败
**问题**: 更改未保存到文档
**解决**: 确保调用 orchestrator.Save() 并处理异常

## 扩展开发 / Extension Development

### 添加新功能的步骤：
1. 在相应的服务层添加方法
2. 更新编排器协调逻辑
3. 添加必要的验证
4. 编写测试用例
5. 更新文档
