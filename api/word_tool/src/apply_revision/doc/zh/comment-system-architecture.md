# Word 评论系统架构文档 / Word Comment System Architecture

## 概述 / Overview

本文档描述了 Word 文档评论功能的分层架构设计，该架构支持基础评论和评论回复功能，采用职责分离和依赖注入的设计原则。

## 架构概览 / Architecture Overview

### 整体架构图 / Overall Architecture Diagram

```mermaid
graph TB
    subgraph "Strategy Layer 策略层"
        BOS[BaseOperationStrategy<br/>基础操作策略]
        CRS[CommentReplyStrategy<br/>评论回复策略]
        BOS --> CRS
    end

    subgraph "Orchestration Layer 编排层"
        CO[CommentOrchestrator<br/>评论编排器<br/>🎯 协调中心]
    end

    subgraph "Service Layer 服务层"
        subgraph "Core Services 核心服务"
            CXS[CommentXmlService<br/>评论XML服务<br/>📝 XML操作]
            DMS[DocumentManipulationService<br/>文档操作服务<br/>📄 文档修改]
        end

        subgraph "Extension Services 扩展服务"
            CRES[CommentReplyExtensionService<br/>评论回复扩展服务<br/>🔗 回复关系]
        end

        subgraph "Utility Services 工具服务"
            VS[ValidationService<br/>验证服务<br/>✅ 参数验证]
        end
    end

    subgraph "Factory & Component Layer 工厂和组件层"
        subgraph "Factories 工厂"
            EF[ElementFactory<br/>元素工厂<br/>🏭 XML元素创建]
            IM[IdManager<br/>ID管理器<br/>🔢 唯一ID生成]
        end

        subgraph "Components 组件"
            CC[CommentComponent<br/>评论组件<br/>💬 基础评论]
            CRC[CommentReplyComponent<br/>评论回复组件<br/>↩️ 回复评论]
        end
    end

    subgraph "Document Operations 文档操作"
        XML1[comments.xml<br/>基础评论文件]
        XML2[commentsExtended.xml<br/>扩展评论文件]
        XML3[commentsExtensible.xml<br/>可扩展评论文件]
        XML4[commentsIds.xml<br/>评论ID映射文件]
        DOC[document.xml<br/>主文档文件]
    end

    %% Dependencies 依赖关系
    CRS -->|委托给| CO

    CO -->|协调| CXS
    CO -->|协调| DMS
    CO -->|协调| CRES
    CO -->|验证| VS
    CO -->|使用| IM
    CO -->|使用| EF

    CXS -->|操作| XML1
    CRES -->|操作| XML2
    CRES -->|操作| XML3
    CRES -->|操作| XML4
    DMS -->|操作| DOC

    CXS -->|使用| IM
    CXS -->|使用| EF
    DMS -->|使用| EF
    CRES -->|使用| IM

    CO -->|创建| CC
    CO -->|创建| CRC

    %% Styling
    classDef strategyClass fill:#e6ccff,stroke:#9900cc,stroke-width:2px
    classDef orchestrationClass fill:#ccffcc,stroke:#00aa00,stroke-width:3px
    classDef serviceClass fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef factoryClass fill:#ffffcc,stroke:#aaaa00,stroke-width:2px
    classDef componentClass fill:#ffeecc,stroke:#cc6600,stroke-width:2px
    classDef xmlClass fill:#ffcccc,stroke:#cc0000,stroke-width:2px

    class BOS,CRS strategyClass
    class CO orchestrationClass
    class CXS,DMS,CRES,VS serviceClass
    class EF,IM factoryClass
    class CC,CRC componentClass
    class XML1,XML2,XML3,XML4,DOC xmlClass
```

### 分层架构说明 / Layer Architecture Description

```mermaid
graph TD
    A[Strategy Layer<br/>策略层<br/>🎯 业务逻辑入口] --> B[Orchestration Layer<br/>编排层<br/>🎼 协调各服务]
    B --> C[Service Layer<br/>服务层<br/>⚙️ 具体功能实现]
    C --> D[Factory & Component Layer<br/>工厂和组件层<br/>🏭 对象创建和封装]
    D --> E[Document Operations<br/>文档操作<br/>📄 XML文件操作]

    classDef layerBox fill:#f9f9f9,stroke:#333,stroke-width:2px,color:#000
    class A,B,C,D,E layerBox
```

## 核心组件 / Core Components

### 1. 策略层 / Strategy Layer

```mermaid
classDiagram
    class BaseOperationStrategy {
        <<abstract>>
        +ILambdaContext context
        +IElementFactory elementFactory
        +IRevisionElementFactory revisionFactory
        +IIdManager idManager
        +ICommentManager commentManager
        +Initialize(MainDocumentPart) void
        +Execute(Operation) bool
    }

    class CommentReplyStrategy {
        -CommentOrchestrator orchestrator
        +CommentReplyStrategy(dependencies)
        +ProcessCommentReply(CommentModel) bool
        +ValidateReplyParameters(params) bool
    }

    BaseOperationStrategy <|-- CommentReplyStrategy
    CommentReplyStrategy --> CommentOrchestrator : uses

    note for BaseOperationStrategy "所有操作策略的基类\n提供通用的文档操作接口"
    note for CommentReplyStrategy "处理评论回复操作的具体策略\n委托具体操作给编排器"
```

#### BaseOperationStrategy
- **职责**: 所有操作策略的基类
- **功能**: 提供通用的文档操作接口和基础功能

#### CommentReplyStrategy
- **职责**: 处理评论回复操作的具体策略
- **功能**:
  - 继承自 BaseOperationStrategy
  - 委托具体操作给 CommentOrchestrator
  - 提供评论回复的业务逻辑入口

### 2. 编排层 / Orchestration Layer

```mermaid
classDiagram
    class CommentOrchestrator {
        -ILambdaContext context
        -CommentXmlService commentXmlService
        -DocumentManipulationService documentManipulationService
        -ICommentReplyExtensionService commentReplyExtensionService
        -ValidationService validationService
        -IIdManager idManager
        -IElementFactory elementFactory
        +Initialize(MainDocumentPart) void
        +CreateCommentComponent(author, date, message) CommentComponent
        +CreateCommentReply(parentId, author, date, text) bool
        +InsertCommentIntoDocument(component, paraId, position) bool
        +FindCommentById(commentId) Comment
        +GetCommentCount() int
        +Save() bool
        +IsInitialized bool
    }

    CommentOrchestrator --> CommentXmlService : coordinates
    CommentOrchestrator --> DocumentManipulationService : coordinates
    CommentOrchestrator --> CommentReplyExtensionService : coordinates
    CommentOrchestrator --> ValidationService : uses
    CommentOrchestrator --> IdManager : uses
    CommentOrchestrator --> ElementFactory : uses

    note for CommentOrchestrator "评论操作的协调中心\n管理所有服务组件\n提供统一的操作接口"
```

#### CommentOrchestrator
- **职责**: 协调各个服务完成评论相关操作
- **核心功能**:
  - 初始化和管理各个服务组件
  - 协调评论创建、回复、插入等操作
  - 统一的错误处理和日志记录
  - 事务性操作管理

**主要方法**:
```csharp
// 初始化编排器
void Initialize(MainDocumentPart mainPart)

// 创建评论组件
CommentComponent CreateCommentComponent(string author, string date, string message)

// 创建评论回复
bool CreateCommentReply(string parentCommentId, string author, string date, string text)

// 插入评论到文档
bool InsertCommentIntoDocument(CommentComponent component, string paraId, int position)

// 保存所有更改
bool Save()
```

### 3. 服务层 / Service Layer

```mermaid
classDiagram
    class CommentXmlService {
        -ILambdaContext context
        -IElementFactory elementFactory
        -IIdManager idManager
        -WordprocessingCommentsPart commentsPart
        +Initialize(MainDocumentPart) void
        +CreateComment(author, date, message) Comment
        +AddComment(Comment) void
        +FindCommentById(commentId) Comment
        +GetCommentCount() int
        +Save() void
    }

    class DocumentManipulationService {
        -ILambdaContext context
        -IElementFactory elementFactory
        -MainDocumentPart mainPart
        +Initialize(MainDocumentPart) void
        +InsertCommentReference(rangeStart, rangeEnd, reference, paraId, position) bool
        +FindParagraphByParaId(paraId) Paragraph
        +Save() void
    }

    class CommentReplyExtensionService {
        -ILambdaContext context
        -MainDocumentPart mainPart
        +Initialize(MainDocumentPart) void
        +CreateReplyExtensions(parentComment, replyComment, replyDurableId, parentDurableId, utcDateString) bool
        -CreateCommentsExtendedPart(paraIdParent, paraId) bool
        -CreateCommentsExtensiblePart(replyDurableId, parentDurableId, utcDateString) bool
        -CreateCommentsIdsPart(replyParaId, parentParaId, replyDurableId, parentDurableId) bool
    }

    class ValidationService {
        -ILambdaContext context
        +ValidateCommentExists(comment, commentId) void
        +ValidateCommentReplyParameters(parentId, author, date, text) void
        +ValidateNotNullOrEmpty(value, parameterName) void
        +ValidateCommentIdFormat(commentId) void
    }

    CommentXmlService --> ElementFactory : uses
    CommentXmlService --> IdManager : uses
    DocumentManipulationService --> ElementFactory : uses
    CommentReplyExtensionService --> IdManager : uses

    note for CommentXmlService "管理评论相关的XML操作\n操作comments.xml文件"
    note for DocumentManipulationService "处理文档内容的直接操作\n支持嵌套评论插入"
    note for CommentReplyExtensionService "处理评论回复的扩展XML文件\n管理父子评论关系"
    note for ValidationService "提供各种验证功能\n确保参数和数据有效性"
```

#### 服务层职责分工 / Service Layer Responsibilities

```mermaid
graph LR
    subgraph "XML Operations XML操作"
        CXS[CommentXmlService<br/>评论XML服务]
        CRES[CommentReplyExtensionService<br/>回复扩展服务]
    end

    subgraph "Document Operations 文档操作"
        DMS[DocumentManipulationService<br/>文档操作服务]
    end

    subgraph "Validation 验证"
        VS[ValidationService<br/>验证服务]
    end

    CXS -->|操作| XML1[comments.xml]
    CRES -->|操作| XML2[commentsExtended.xml]
    CRES -->|操作| XML3[commentsExtensible.xml]
    CRES -->|操作| XML4[commentsIds.xml]
    DMS -->|操作| DOC[document.xml]

    VS -->|验证| CXS
    VS -->|验证| CRES
    VS -->|验证| DMS

    classDef serviceClass fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef xmlClass fill:#ffcccc,stroke:#cc0000,stroke-width:2px

    class CXS,CRES,DMS,VS serviceClass
    class XML1,XML2,XML3,XML4,DOC xmlClass
```

#### CommentXmlService
- **职责**: 管理评论相关的 XML 操作
- **功能**:
  - 创建和管理 Comment 元素
  - 操作 comments.xml 文件
  - 查找和验证评论

#### DocumentManipulationService
- **职责**: 处理文档内容的直接操作
- **功能**:
  - 插入评论标记到文档
  - 支持嵌套评论的插入逻辑
  - 管理 document.xml 的修改

#### CommentReplyExtensionService
- **职责**: 处理评论回复的扩展 XML 文件
- **功能**:
  - 创建 commentsExtended.xml
  - 创建 commentsExtensible.xml
  - 创建 commentsIds.xml
  - 管理评论间的父子关系

#### ValidationService
- **职责**: 提供各种验证功能
- **功能**:
  - 验证评论存在性
  - 验证参数有效性
  - 验证评论ID格式

### 4. 工厂和组件层 / Factory & Component Layer

```mermaid
classDiagram
    class ElementFactory {
        -ILambdaContext context
        +CreateCommentElement(commentId, author, date, message, paraId) Comment
        +CreateCommentRangeStart(commentId) CommentRangeStart
        +CreateCommentRangeEnd(commentId) CommentRangeEnd
        +CreateCommentReference(commentId) Run
        +CreateTextRun(text, preserveSpace) Run
        +CreateParagraph(text) Paragraph
        +GetCommentInitials(author) StringValue
    }

    class IdManager {
        -ILambdaContext context
        -int maxCommentId
        -int maxRevisionId
        +Initialize(commentsPart, documentBody) void
        +GetNextCommentId() int
        +GetNextRevisionId() int
        +GenerateParaId() string
        +GenerateDurableId() string
    }

    class CommentComponent {
        +Comment Comment
        +CommentRangeStart RangeStart
        +CommentRangeEnd RangeEnd
        +Run Reference
        +GetCommentId() string
    }

    class CommentReplyComponent {
        +Comment ParentComment
        +Comment ReplyComment
        +string ReplyDurableId
        +string ParentDurableId
        +string UtcDateString
        +GetParentCommentId() string
        +GetReplyCommentId() string
    }

    class ICommentContainer {
        <<interface>>
        +GetCommentId() string
    }

    CommentComponent ..|> ICommentContainer
    CommentReplyComponent ..|> ICommentContainer

    note for ElementFactory "创建OpenXML元素的工厂\n提供统一的元素创建接口"
    note for IdManager "管理唯一ID生成\n确保ID格式正确性"
    note for CommentComponent "封装单个评论的所有相关元素"
    note for CommentReplyComponent "封装评论回复的所有相关元素"
```

#### 组件关系图 / Component Relationship Diagram

```mermaid
graph TD
    subgraph "Comment Structure 评论结构"
        CC[CommentComponent<br/>评论组件]
        C[Comment<br/>评论对象]
        CRS[CommentRangeStart<br/>范围开始]
        CRE[CommentRangeEnd<br/>范围结束]
        CR[CommentReference<br/>评论引用]
    end

    subgraph "Reply Structure 回复结构"
        CRC[CommentReplyComponent<br/>回复组件]
        PC[ParentComment<br/>父评论]
        RC[ReplyComment<br/>回复评论]
        RDI[ReplyDurableId<br/>回复持久ID]
        PDI[ParentDurableId<br/>父持久ID]
    end

    CC --> C
    CC --> CRS
    CC --> CRE
    CC --> CR

    CRC --> PC
    CRC --> RC
    CRC --> RDI
    CRC --> PDI

    PC -.->|关联| C
    RC -.->|关联| C

    classDef componentClass fill:#ffeecc,stroke:#cc6600,stroke-width:2px
    classDef elementClass fill:#e6f3ff,stroke:#0066cc,stroke-width:1px

    class CC,CRC componentClass
    class C,CRS,CRE,CR,PC,RC,RDI,PDI elementClass
```

#### ElementFactory
- **职责**: 创建 OpenXML 元素
- **功能**:
  - 创建评论相关的 XML 元素
  - 提供统一的元素创建接口

#### IdManager
- **职责**: 管理唯一ID生成
- **功能**:
  - 生成评论ID
  - 生成持久ID (durableId)
  - 确保ID的唯一性和格式正确性

#### CommentComponent
- **职责**: 封装单个评论的所有相关元素
- **属性**:
  - Comment: 评论对象
  - RangeStart: 评论范围开始标记
  - RangeEnd: 评论范围结束标记
  - Reference: 评论引用

#### CommentReplyComponent
- **职责**: 封装评论回复的所有相关元素
- **属性**:
  - ParentComment: 父评论
  - ReplyComment: 回复评论
  - ReplyDurableId: 回复持久ID
  - ParentDurableId: 父评论持久ID
  - UtcDateString: UTC时间字符串

## 数据流程图 / Data Flow Diagrams

### 基础评论创建流程 / Basic Comment Creation Flow

```mermaid
sequenceDiagram
    participant Client as 客户端<br/>Client
    participant CO as CommentOrchestrator<br/>编排器
    participant CXS as CommentXmlService<br/>XML服务
    participant DMS as DocumentManipulationService<br/>文档服务
    participant EF as ElementFactory<br/>元素工厂
    participant IM as IdManager<br/>ID管理器

    Client->>CO: CreateCommentComponent(author, date, message)
    CO->>CXS: CreateComment(author, date, message)
    CXS->>IM: GetNextCommentId()
    IM-->>CXS: commentId
    CXS->>IM: GenerateParaId()
    IM-->>CXS: paraId
    CXS->>EF: CreateCommentElement(commentId, author, date, message, paraId)
    EF-->>CXS: Comment
    CXS-->>CO: Comment
    CO->>EF: CreateCommentRangeStart(commentId)
    EF-->>CO: CommentRangeStart
    CO->>EF: CreateCommentRangeEnd(commentId)
    EF-->>CO: CommentRangeEnd
    CO->>EF: CreateCommentReference(commentId)
    EF-->>CO: CommentReference
    CO-->>Client: CommentComponent

    Client->>CO: InsertCommentIntoDocument(component, paraId, position)
    CO->>DMS: InsertCommentReference(rangeStart, rangeEnd, reference, paraId, position)
    DMS-->>CO: success
    CO-->>Client: success

    Client->>CO: Save()
    CO->>CXS: Save()
    CO->>DMS: Save()
    CO-->>Client: success
```

### 评论回复创建流程 / Comment Reply Creation Flow

```mermaid
sequenceDiagram
    participant Client as 客户端<br/>Client
    participant CO as CommentOrchestrator<br/>编排器
    participant CXS as CommentXmlService<br/>XML服务
    participant CRES as CommentReplyExtensionService<br/>回复扩展服务
    participant VS as ValidationService<br/>验证服务
    participant IM as IdManager<br/>ID管理器

    Client->>CO: CreateCommentReply(parentId, author, date, text)
    CO->>VS: ValidateCommentReplyParameters(parentId, author, date, text)
    VS-->>CO: validation passed
    CO->>CXS: FindCommentById(parentId)
    CXS-->>CO: parentComment
    CO->>VS: ValidateCommentExists(parentComment, parentId)
    VS-->>CO: validation passed
    CO->>CXS: CreateComment(author, date, text)
    CXS-->>CO: replyComment
    CO->>IM: GenerateParaId() [x2]
    IM-->>CO: replyDurableId, parentDurableId
    CO->>CXS: AddComment(replyComment)
    CO->>CRES: CreateReplyExtensions(parentComment, replyComment, replyDurableId, parentDurableId, utcDateString)
    CRES-->>CO: success
    CO-->>Client: success
```

## 使用示例 / Usage Examples

### 创建基础评论 / Creating Basic Comment

```mermaid
graph LR
    A[初始化编排器<br/>Initialize Orchestrator] --> B[创建评论组件<br/>Create Comment Component]
    B --> C[插入到文档<br/>Insert to Document]
    C --> D[保存更改<br/>Save Changes]

    classDef stepClass fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    class A,B,C,D stepClass
```

```csharp
// 初始化编排器
var orchestrator = new CommentOrchestrator(context, xmlService, docService,
    extensionService, validationService, idManager, elementFactory);
orchestrator.Initialize(mainDocumentPart);

// 创建评论组件
var commentComponent = orchestrator.CreateCommentComponent(
    "John Doe", "2024-01-01", "This is a comment");

// 插入到文档
bool success = orchestrator.InsertCommentIntoDocument(
    commentComponent, "paragraph-id", 0);

// 保存更改
orchestrator.Save();
```

### 创建评论回复 / Creating Comment Reply

```mermaid
graph LR
    A[验证父评论<br/>Validate Parent] --> B[创建回复评论<br/>Create Reply Comment]
    B --> C[生成持久ID<br/>Generate Durable IDs]
    C --> D[创建扩展文件<br/>Create Extension Files]
    D --> E[保存更改<br/>Save Changes]

    classDef stepClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    class A,B,C,D,E stepClass
```

```csharp
// 创建回复
bool success = orchestrator.CreateCommentReply(
    "parent-comment-id", "Jane Smith", "2024-01-02", "This is a reply");

// 保存更改
orchestrator.Save();
```

## 文件结构 / File Structure

```mermaid
graph TD
    subgraph "ApplyRevision Project"
        subgraph "Strategy 策略层"
            S1[BaseOperationStrategy.cs<br/>基础操作策略]
            S2[CommentReplyStrategy.cs<br/>评论回复策略]
        end

        subgraph "Service 服务层"
            SV1[CommentOrchestrator.cs<br/>评论编排器]
            SV2[CommentXmlService.cs<br/>评论XML服务]
            SV3[DocumentManipulationService.cs<br/>文档操作服务]
            SV4[CommentReplyExtensionService.cs<br/>回复扩展服务]
            SV5[ValidationService.cs<br/>验证服务]
        end

        subgraph "Factory 工厂层"
            F1[ElementFactory.cs<br/>元素工厂]
            F2[IdManager.cs<br/>ID管理器]
        end

        subgraph "Component 组件层"
            C1[CommentComponent.cs<br/>评论组件]
            C2[CommentReplyComponent.cs<br/>回复组件]
        end

        subgraph "Exceptions 异常"
            E1[CommentNotFoundException.cs<br/>评论未找到异常]
        end
    end

    classDef strategyClass fill:#e6ccff,stroke:#9900cc,stroke-width:2px
    classDef serviceClass fill:#cceeff,stroke:#0066cc,stroke-width:2px
    classDef factoryClass fill:#ffffcc,stroke:#aaaa00,stroke-width:2px
    classDef componentClass fill:#ffeecc,stroke:#cc6600,stroke-width:2px
    classDef exceptionClass fill:#ffcccc,stroke:#cc0000,stroke-width:2px

    class S1,S2 strategyClass
    class SV1,SV2,SV3,SV4,SV5 serviceClass
    class F1,F2 factoryClass
    class C1,C2 componentClass
    class E1 exceptionClass
```

## 设计原则 / Design Principles

```mermaid
mindmap
  root((设计原则<br/>Design Principles))
    单一职责原则
      每个类只负责一个功能领域
      职责明确分离
      便于维护和理解
    依赖注入
      构造函数注入依赖
      便于测试和维护
      降低耦合度
    分层架构
      清晰的分层结构
      便于理解和扩展
      职责分离明确
    错误处理
      统一的异常处理
      详细的日志记录
      优雅的错误恢复
    可测试性
      接口驱动设计
      便于单元测试
      模拟依赖简单
```

### 设计原则详解 / Design Principles Details

1. **单一职责原则 (Single Responsibility Principle)**
   - 每个类只负责一个特定的功能领域
   - 例如：CommentXmlService 只处理 XML 操作，DocumentManipulationService 只处理文档操作

2. **依赖注入 (Dependency Injection)**
   - 通过构造函数注入依赖，便于测试和维护
   - 所有服务都通过接口定义，支持模拟测试

3. **分层架构 (Layered Architecture)**
   - 清晰的分层结构，便于理解和扩展
   - 每层只依赖下层，不跨层调用

4. **错误处理 (Error Handling)**
   - 统一的异常处理和日志记录
   - 使用特定的异常类型，提供详细的错误信息

5. **可测试性 (Testability)**
   - 接口驱动设计，便于单元测试
   - 依赖注入使得模拟测试变得简单

## 扩展指南 / Extension Guide

### 添加新功能的流程 / Adding New Features Flow

```mermaid
flowchart TD
    A[确定需求<br/>Define Requirements] --> B{选择扩展层级<br/>Choose Extension Layer}

    B -->|服务层扩展| C[添加服务方法<br/>Add Service Method]
    B -->|组件层扩展| D[创建新组件<br/>Create New Component]
    B -->|工厂层扩展| E[扩展工厂方法<br/>Extend Factory Method]

    C --> F[更新编排器<br/>Update Orchestrator]
    D --> F
    E --> F

    F --> G[添加验证逻辑<br/>Add Validation Logic]
    G --> H[编写单元测试<br/>Write Unit Tests]
    H --> I[集成测试<br/>Integration Tests]
    I --> J[更新文档<br/>Update Documentation]

    classDef processClass fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef decisionClass fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef testClass fill:#e3f2fd,stroke:#2196f3,stroke-width:2px

    class A,C,D,E,F,G,J processClass
    class B decisionClass
    class H,I testClass
```

### 扩展示例：添加评论标签功能 / Extension Example: Adding Comment Tags

```mermaid
sequenceDiagram
    participant Dev as 开发者<br/>Developer
    participant EF as ElementFactory<br/>元素工厂
    participant CXS as CommentXmlService<br/>XML服务
    participant CO as CommentOrchestrator<br/>编排器
    participant VS as ValidationService<br/>验证服务

    Note over Dev: 1. 扩展ElementFactory
    Dev->>EF: 添加CreateCommentWithTags方法

    Note over Dev: 2. 扩展CommentXmlService
    Dev->>CXS: 添加AddTagsToComment方法

    Note over Dev: 3. 扩展CommentOrchestrator
    Dev->>CO: 添加CreateTaggedComment方法

    Note over Dev: 4. 扩展ValidationService
    Dev->>VS: 添加ValidateCommentTags方法

    Note over Dev: 5. 测试新功能
    Dev->>Dev: 编写单元测试和集成测试
```

### 添加新的评论功能 / Adding New Comment Features
1. **在相应的服务层添加新方法**
   - 确定功能属于哪个服务的职责范围
   - 遵循现有的命名约定和错误处理模式

2. **在编排器中协调新功能**
   - 在 CommentOrchestrator 中添加协调方法
   - 确保正确的服务调用顺序

3. **更新相关的组件类**
   - 如需要，扩展现有组件或创建新组件
   - 保持组件的封装性和一致性

4. **添加相应的测试用例**
   - 编写单元测试验证功能正确性
   - 添加集成测试确保端到端功能

### 修改XML结构 / Modifying XML Structure
1. **更新 ElementFactory 中的创建方法**
   - 修改或添加 XML 元素创建方法
   - 确保生成的 XML 符合 OpenXML 规范

2. **修改相应的服务类处理逻辑**
   - 更新服务类以处理新的 XML 结构
   - 保持向后兼容性

3. **更新验证规则**
   - 在 ValidationService 中添加新的验证逻辑
   - 确保数据完整性和格式正确性

4. **测试兼容性**
   - 测试新旧格式的兼容性
   - 验证 Word 应用程序的正确显示

## 注意事项 / Important Notes

### 重要约束说明 / Important Constraints

1. **durableId 格式**: Word 要求 durableId 必须以 0-7 开头
   - IdManager 已自动处理此约束
   - 自定义 ID 生成时需要注意

2. **XML 命名空间**: 确保使用正确的 OpenXML 命名空间
   - 使用预定义的命名空间常量
   - 避免硬编码命名空间字符串

3. **文档保存**: 所有更改必须通过 Save() 方法保存
   - 编排器提供统一的保存接口
   - 确保事务性操作的完整性

4. **错误处理**: 使用适当的异常类型和日志级别
   - 使用特定的异常类型（如 CommentNotFoundException）
   - 提供详细的错误信息和上下文
