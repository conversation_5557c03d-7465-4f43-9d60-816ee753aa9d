# ApplyRevision 本地开发指南

## 概述

ApplyRevision 项目原本设计为在 AWS Lambda 环境中运行，依赖于 `ILambdaContext` 和 S3 服务。为了支持本地开发和测试，我们添加了本地运行功能，允许直接处理本地文件而无需 AWS 环境。

## 架构变更

### 新增组件

1. **Program.cs** - 本地运行的入口点，提供命令行界面
2. **LocalApplyRevisionExecutor** - 本地执行器，处理本地文件而非 S3
3. **LocalS3Service** - S3 服务的本地模拟实现
4. **LocalLambdaContext** - Lambda 上下文的本地模拟实现
5. **LocalLambdaLogger** - Lambda 日志记录器的本地实现

### 项目配置变更

- 在 Debug 模式下启用控制台应用程序输出类型 (`<OutputType>Exe</OutputType>`)
- 添加 `System.CommandLine` 包依赖用于命令行参数解析

## 使用方法

### 方法 1: 使用便捷脚本（推荐）

#### Linux/macOS

```bash
# 基本用法
./run-local.sh -d document.docx -r revision.json

# 指定输出文件
./run-local.sh -d document.docx -r revision.json -o output.docx

# 查看帮助
./run-local.sh --help
```

#### Windows

```cmd
REM 基本用法
run-local.bat -d document.docx -r revision.json

REM 指定输出文件
run-local.bat -d document.docx -r revision.json -o output.docx

REM 查看帮助
run-local.bat --help
```

### 方法 2: 直接使用 dotnet run

```bash
cd src/ApplyRevision

# 构建项目
dotnet build

# 运行应用
dotnet run -- --document "path/to/document.docx" --revision "path/to/revision.json"

# 指定输出路径
dotnet run -- --document "path/to/document.docx" --revision "path/to/revision.json" --output "path/to/output.docx"
```

### 方法 3: 构建并运行可执行文件

```bash
cd src/ApplyRevision

# 发布应用
dotnet publish -c Release -o publish

# 运行可执行文件
./publish/ApplyRevision --document "document.docx" --revision "revision.json"
```

## 命令行参数

| 参数         | 简写 | 描述                     | 必需 |
| ------------ | ---- | ------------------------ | ---- |
| `--document` | `-d` | Word 文档路径 (.docx)    | 是   |
| `--revision` | `-r` | 修订补丁文件路径 (.json) | 是   |
| `--output`   | `-o` | 输出文件路径 (可选)      | 否   |
| `--help`     | `-h` | 显示帮助信息             | 否   |

## 文件格式要求

### Word 文档

- 必须是 `.docx` 格式
- 文件必须存在且可读

### 修订文件

- 必须是 `.json` 格式
- 必须符合 `WordTextPatch` 数据结构
- 示例格式：

```json
{
  "operations": [
    {
      "type": "insert",
      "target": "segId123",
      "text": "插入的文本",
      "offset": 0
    },
    {
      "type": "delete",
      "target": "segId456",
      "length": 5,
      "offset": 10
    }
  ]
}
```

## 输出行为

- 如果未指定输出路径，将在原文档同目录下创建带 `_modified` 后缀的文件
- 例如：`document.docx` → `document_modified.docx`
- 输出目录会自动创建（如果不存在）

## 日志记录

本地运行时，所有日志都会输出到控制台，包括：

- `[INFO]` - 信息性消息
- `[WARN]` - 警告消息
- `[ERROR]` - 错误消息
- `[CRITICAL]` - 严重错误消息

## 错误处理

常见错误及解决方案：

1. **文件不存在**

   - 检查文件路径是否正确
   - 确保文件具有读取权限

2. **JSON 解析失败**

   - 验证修订文件的 JSON 格式
   - 检查数据结构是否符合 `WordTextPatch` 规范

3. **构建失败**

   - 确保安装了 .NET 8.0 SDK
   - 检查项目依赖是否完整

4. **权限问题**
   - 确保对输出目录有写入权限
   - 在 Linux/macOS 上确保脚本有执行权限

## 开发和调试

### 在 IDE 中调试

1. 在 Visual Studio 或 VS Code 中打开项目
2. 设置启动参数：
   ```
   --document "test.docx" --revision "test.json"
   ```
3. 设置断点并开始调试

### 单元测试

现有的单元测试仍然有效，因为它们使用模拟的 Lambda 上下文。本地运行功能不会影响测试。

### 添加新功能

如果需要添加新的本地功能：

1. 在 `LocalApplyRevisionExecutor` 中添加方法
2. 在 `Program.cs` 中添加相应的命令行选项
3. 更新便捷脚本以支持新选项

## 与 Lambda 环境的兼容性

- 本地运行功能不会影响 Lambda 部署
- `Handler.cs` 保持不变，仍然是 Lambda 的入口点
- 在 Release 模式下构建时，不会包含控制台应用程序功能

## 性能考虑

- 本地运行时没有 Lambda 的冷启动延迟
- 文件 I/O 性能取决于本地存储性能
- 内存使用与 Lambda 环境类似

## 故障排除

### 常见问题

1. **"找不到 dotnet 命令"**

   - 安装 .NET 8.0 SDK
   - 确保 PATH 环境变量包含 dotnet

2. **"项目构建失败"**

   - 运行 `dotnet restore` 恢复包依赖
   - 检查 NuGet 包源配置

3. **"权限被拒绝"**
   - Linux/macOS: `chmod +x run-local.sh`
   - Windows: 以管理员身份运行

### 获取详细日志

设置环境变量以获取更详细的输出：

```bash
export DOTNET_CLI_TELEMETRY_OPTOUT=1
export DOTNET_NOLOGO=1
```

## 示例用法

```bash
# 处理测试文档
./run-local.sh -d "TestFiles/sample.docx" -r "TestFiles/patch.json"

# 批量处理（使用脚本循环）
for doc in *.docx; do
    ./run-local.sh -d "$doc" -r "common-patch.json" -o "processed_$doc"
done
```
