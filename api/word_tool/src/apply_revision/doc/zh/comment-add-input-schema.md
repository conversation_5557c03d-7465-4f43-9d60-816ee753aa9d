# CommentAdd 操作输入模式

## 概述

本文档定义了 `commentAdd` 操作的 JSON 输入结构，用于向 Word 文档的指定段落添加评论。

## JSON 模式定义

### 基本结构

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "your-document-id",
  "operations": [
    {
      "op": "commentAdd",
      "target": [
        "1-0",
        "1-1"
      ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "Your comment text here"
      },
      "revision": {
        "author": "Author Name",
        "date": "2024-01-01T12:00:00Z"
      }
    }
  ]
}
```

### 字段说明

#### 必需字段

| 字段 | 类型 | 描述 |
|-------------|-------------|-------------------|
| `op` | string | 操作类型，必须为 "commentAdd" |
| `target` | List\<string\> | 目标段落ID，格式为 "段落ID-run索引"，如 "1-0" |
| `comment.text` | string | 评论文本内容，不能为空，最大长度10000字符 |

#### 可选字段

| 字段 | 类型 | 默认值 | 描述 |
|-------------|-------------|-----------------|-------------------|
| `range.start` | integer | 0 | 范围起始位置（CommentAdd操作中可选） |
| `range.end` | integer | 0 | 范围结束位置（CommentAdd操作中可选） |
| `text` | string | "" | 文本内容（CommentAdd操作中可选，评论文本应在comment.text中） |
| `revision.author` | string | "Styling Agent" | 修订作者 |
| `revision.date` | string | 当前时间 | 修订日期，ISO 8601格式 |

#### 禁止字段

以下字段在 CommentAdd 操作中不应使用：

- `comment.commentId` - 评论ID由系统自动生成
- `comment.parentCommentId` - 用于回复操作，CommentAdd中应为空
- `commentId` - 用于CommentReply操作标识父评论

## 示例

### 基本示例

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "sample-document",
  "operations": [
    {
      "op": "commentAdd",
      "target": [ "1-0" ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "This is a simple comment on the first paragraph."
      },
      "revision": {
        "author": "John Doe",
        "date": "2024-01-15T10:30:00Z"
      }
    }
  ]
}
```

### 多评论示例

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "multi-comment-document",
  "operations": [
    {
      "op": "commentAdd",
      "target": [ "1-0", "1-1" ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "First comment on paragraph 1"
      },
      "revision": {
        "author": "Reviewer 1",
        "date": "2024-01-15T09:00:00Z"
      }
    }
  ]
}
```

### 带详细修订信息的示例

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "detailed-revision-document",
  "defaultRevision": {
    "author": "Default Author",
    "date": "2024-01-15T08:00:00Z"
  },
  "operations": [
    {
      "op": "commentAdd",
      "target": [ "3-1" ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "This comment uses specific revision information that overrides the default."
      },
      "revision": {
        "author": "Specific Reviewer",
        "date": "2024-01-15T11:45:00Z"
      }
    }
  ]
}
```

## 验证规则

### 输入验证

1. **操作类型验证**
   - `op` 字段必须为 "commentAdd"

2. **目标验证**
   - `target` 不能为空
   - 格式必须为 "数字-数字"（如 "1-0", "123-2"）
   - 段落ID必须为正整数（≥1）
   - Run索引必须为非负整数（≥0）

3. **评论验证**
   - `comment.text` 不能为空或仅包含空白字符
   - 文本长度不能超过10000字符
   - `comment.parentCommentId` 必须为空（用于区分CommentAdd和CommentReply）

4. **约束验证**
   - `commentId` 字段必须为空（CommentAdd不使用此字段）
   - 如果提供了 `range`，起始位置不能为负数，结束位置不能小于起始位置

## 错误处理

### 常见错误

1. **ArgumentNullException**
   - 当 `operation`、`target` 或 `comment` 为 null 时抛出

2. **ArgumentException**
   - 无效的操作类型
   - 无效的 SegId 格式
   - 空的评论文本
   - 评论文本过长
   - 存在不应该有的字段（如 parentCommentId）

### 错误消息示例

```
ArgumentException: Expected CommentAdd operation, but got CommentReply
ArgumentException: Target SegId cannot be null or empty
ArgumentException: Invalid SegId format: abc-def. Expected format: 'paragraphId-runIndex' (e.g., '1-0')
ArgumentException: Comment text cannot be null or empty
ArgumentException: Comment text is too long (15000 characters). Maximum allowed: 10000 characters
ArgumentException: ParentCommentId should be null or empty for CommentAdd operation. Use CommentReply for reply operations.
```

## 最佳实践

1. **使用有意义的评论文本**
   - 提供清晰、具体的评论内容

2. **正确的段落定位**
   - 确保 SegId 指向存在的段落
   - 使用正确的格式 "paragraphId-runIndex"

3. **适当的修订信息**
   - 提供准确的作者和时间信息
   - 使用 ISO 8601 日期格式

4. **批量操作优化**
   - 将多个评论操作组合在一个请求中
   - 按段落顺序排列操作以提高性能
