# CommentAdd Operation Input Schema

## Overview

This document defines the JSON input structure for the `commentAdd` operation, used to add comments to specified paragraphs in Word documents.

## JSON Schema Definition

### Basic Structure

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "your-document-id",
  "operations": [
    {
      "op": "commentAdd",
      "target": [
        "1-0",
        "1-1"
      ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "Your comment text here"
      },
      "revision": {
        "author": "Author Name",
        "date": "2024-01-01T12:00:00Z"
      }
    }
  ]
}
```

### Field Descriptions

#### Required Fields

| Field | Type | Description |
|-------------|-------------|-------------------|
| `op` | string | Operation type, must be "commentAdd" |
| `target` | List\<string\> | Target paragraph ID, format "paragraphId-runIndex", e.g., "1-0" |
| `comment.text` | string | Comment text content, cannot be empty, max 10000 characters |

#### Optional Fields

| Field | Type | Default | Description |
|-------------|-------------|-----------------|-------------------|
| `range.start` | integer | 0 | Range start position (optional in CommentAdd) |
| `range.end` | integer | 0 | Range end position (optional in CommentAdd) |
| `text` | string | "" | Text content (optional in CommentAdd, comment text should be in comment.text) |
| `revision.author` | string | "Styling Agent" | Revision author |
| `revision.date` | string | Current time | Revision date in ISO 8601 format |

#### Prohibited Fields

The following fields should not be used in CommentAdd operations:

- `comment.commentId` - Comment ID is auto-generated by system
- `comment.parentCommentId` - Used for reply operations, should be empty in CommentAdd
- `commentId` - Used in CommentReply operations to identify parent comment

## Examples

### Basic Example

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "sample-document",
  "operations": [
    {
      "op": "commentAdd",
      "target": [ "1-0" ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "This is a simple comment on the first paragraph."
      },
      "revision": {
        "author": "John Doe",
        "date": "2024-01-15T10:30:00Z"
      }
    }
  ]
}
```

### Multiple Comments Example

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "multi-comment-document",
  "operations": [
    {
      "op": "commentAdd",
      "target": [ "1-0", "1-1" ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "First comment on paragraph 1"
      },
      "revision": {
        "author": "Reviewer 1",
        "date": "2024-01-15T09:00:00Z"
      }
    }
  ]
}
```

### Example with Detailed Revision Information

```json
{
  "schema": "word-text-patch",
  "version": "2.0",
  "documentId": "detailed-revision-document",
  "defaultRevision": {
    "author": "Default Author",
    "date": "2024-01-15T08:00:00Z"
  },
  "operations": [
    {
      "op": "commentAdd",
      "target": [ "3-1" ],
      "range": {
        "start": 0,
        "end": 0
      },
      "text": "",
      "comment": {
        "text": "This comment uses specific revision information that overrides the default."
      },
      "revision": {
        "author": "Specific Reviewer",
        "date": "2024-01-15T11:45:00Z"
      }
    }
  ]
}
```

## Validation Rules

### Input Validation

1. **Operation Type Validation**
   - `op` field must be "commentAdd"

2. **Target Validation**
   - `target` cannot be empty
   - Format must be "number-number" (e.g., "1-0", "123-2")
   - Paragraph ID must be positive integer (≥1)
   - Run index must be non-negative integer (≥0)

3. **Comment Validation**
   - `comment.text` cannot be empty or contain only whitespace
   - Text length cannot exceed 10000 characters
   - `comment.parentCommentId` must be empty (to distinguish from CommentReply)

4. **Constraint Validation**
   - `commentId` field must be empty (not used in CommentAdd)
   - If `range` is provided, start position cannot be negative, end position cannot be less than start position

## Error Handling

### Common Errors

1. **ArgumentNullException**
   - Thrown when `operation`, `target`, or `comment` is null

2. **ArgumentException**
   - Invalid operation type
   - Invalid SegId format
   - Empty comment text
   - Comment text too long
   - Presence of fields that shouldn't exist (like parentCommentId)

### Error Message Examples

```
ArgumentException: Expected CommentAdd operation, but got CommentReply
ArgumentException: Target SegId cannot be null or empty
ArgumentException: Invalid SegId format: abc-def. Expected format: 'paragraphId-runIndex' (e.g., '1-0')
ArgumentException: Comment text cannot be null or empty
ArgumentException: Comment text is too long (15000 characters). Maximum allowed: 10000 characters
ArgumentException: ParentCommentId should be null or empty for CommentAdd operation. Use CommentReply for reply operations.
```

## Best Practices

1. **Use Meaningful Comment Text**
   - Provide clear and specific comment content

2. **Correct Paragraph Targeting**
   - Ensure SegId points to existing paragraphs
   - Use correct format "paragraphId-runIndex"

3. **Appropriate Revision Information**
   - Provide accurate author and timestamp information
   - Use ISO 8601 date format

4. **Batch Operation Optimization**
   - Combine multiple comment operations in a single request
   - Order operations by paragraph sequence for better performance
