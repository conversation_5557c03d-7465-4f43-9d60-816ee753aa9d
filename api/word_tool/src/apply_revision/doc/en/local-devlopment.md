# ApplyRevision Local Development Guide

## Overview

The ApplyRevision project was originally designed to run in the AWS Lambda environment, relying on `ILambdaContext` and S3 services. To support local development and testing, we have added local execution capabilities that allow direct processing of local files without requiring an AWS environment.

## Architecture Changes

### New Components

1. **Program.cs** - Local execution entry point providing command-line interface
2. **LocalApplyRevisionExecutor** - Local executor that handles local files instead of S3
3. **LocalS3Service** - Local mock implementation of S3 service
4. **LocalLambdaContext** - Local mock implementation of Lambda context
5. **LocalLambdaLogger** - Local implementation of Lambda logger

### Project Configuration Changes

- Enable console application output type in Debug mode (`<OutputType>Exe</OutputType>`)
- Add `System.CommandLine` package dependency for command-line argument parsing

## Usage

### Method 1: Using Convenience Scripts (Recommended)

#### Linux/macOS

```bash
# Basic usage
./run-local.sh -d document.docx -r revision.json

# Specify output file
./run-local.sh -d document.docx -r revision.json -o output.docx

# View help
./run-local.sh --help
```

#### Windows

```cmd
REM Basic usage
run-local.bat -d document.docx -r revision.json

REM Specify output file
run-local.bat -d document.docx -r revision.json -o output.docx

REM View help
run-local.bat --help
```

### Method 2: Direct dotnet run

```bash
cd src/ApplyRevision

# Build project
dotnet build

# Run application
dotnet run -- --document "path/to/document.docx" --revision "path/to/revision.json"

# Specify output path
dotnet run -- --document "path/to/document.docx" --revision "path/to/revision.json" --output "path/to/output.docx"
```

### Method 3: Build and Run Executable

```bash
cd src/ApplyRevision

# Publish application
dotnet publish -c Release -o publish

# Run executable
./publish/ApplyRevision --document "document.docx" --revision "revision.json"
```

## Command Line Arguments

| Parameter    | Short | Description                    | Required |
| ------------ | ----- | ------------------------------ | -------- |
| `--document` | `-d`  | Word document path (.docx)     | Yes      |
| `--revision` | `-r`  | Revision patch file path (.json) | Yes      |
| `--output`   | `-o`  | Output file path (optional)    | No       |
| `--help`     | `-h`  | Show help information          | No       |

## File Format Requirements

### Word Document

- Must be in `.docx` format
- File must exist and be readable

### Revision File

- Must be in `.json` format
- Must conform to `WordTextPatch` data structure
- Example format:

```json
{
  "operations": [
    {
      "type": "insert",
      "target": "segId123",
      "text": "Text to insert",
      "offset": 0
    },
    {
      "type": "delete",
      "target": "segId456",
      "length": 5,
      "offset": 10
    }
  ]
}
```

## Output Behavior

- If no output path is specified, a file with `_modified` suffix will be created in the same directory as the original document
- Example: `document.docx` → `document_modified.docx`
- Output directory will be created automatically if it doesn't exist

## Logging

During local execution, all logs are output to the console, including:

- `[INFO]` - Informational messages
- `[WARN]` - Warning messages
- `[ERROR]` - Error messages
- `[CRITICAL]` - Critical error messages

## Error Handling

Common errors and solutions:

1. **File not found**
   - Check if the file path is correct
   - Ensure the file has read permissions

2. **JSON parsing failed**
   - Validate the JSON format of the revision file
   - Check if the data structure conforms to `WordTextPatch` specification

3. **Build failed**
   - Ensure .NET 8.0 SDK is installed
   - Check if project dependencies are complete

4. **Permission issues**
   - Ensure write permissions to the output directory
   - On Linux/macOS, ensure scripts have execute permissions

## Development and Debugging

### Debugging in IDE

1. Open the project in Visual Studio or VS Code
2. Set launch arguments:
   ```
   --document "test.docx" --revision "test.json"
   ```
3. Set breakpoints and start debugging

### Unit Testing

Existing unit tests remain valid as they use mocked Lambda context. Local execution functionality does not affect testing.

### Adding New Features

If you need to add new local functionality:

1. Add methods to `LocalApplyRevisionExecutor`
2. Add corresponding command-line options in `Program.cs`
3. Update convenience scripts to support new options

## Lambda Environment Compatibility

- Local execution functionality does not affect Lambda deployment
- `Handler.cs` remains unchanged and is still the Lambda entry point
- Console application functionality is not included when building in Release mode

## Performance Considerations

- No Lambda cold start delay during local execution
- File I/O performance depends on local storage performance
- Memory usage is similar to Lambda environment

## Troubleshooting

### Common Issues

1. **"dotnet command not found"**
   - Install .NET 8.0 SDK
   - Ensure PATH environment variable includes dotnet

2. **"Project build failed"**
   - Run `dotnet restore` to restore package dependencies
   - Check NuGet package source configuration

3. **"Permission denied"**
   - Linux/macOS: `chmod +x run-local.sh`
   - Windows: Run as administrator

### Getting Detailed Logs

Set environment variables for more detailed output:

```bash
export DOTNET_CLI_TELEMETRY_OPTOUT=1
export DOTNET_NOLOGO=1
```

## Example Usage

```bash
# Process test document
./run-local.sh -d "TestFiles/sample.docx" -r "TestFiles/patch.json"

# Batch processing (using script loop)
for doc in *.docx; do
    ./run-local.sh -d "$doc" -r "common-patch.json" -o "processed_$doc"
done
```