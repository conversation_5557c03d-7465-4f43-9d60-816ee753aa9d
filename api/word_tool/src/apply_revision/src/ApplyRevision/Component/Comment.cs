using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Abstract;

namespace ApplyRevision.Component
{
    /// <summary>
    /// Represents a single comment component with all related elements
    /// </summary>
    /// <remarks>
    /// Example XML structure:
    /// <w:commentRangeStart w:id="1"/> <!-- Start of comment range -->
    /// <w:r> <!-- Run containing commented text -->
    ///   <w:t>Text being commented on</w:t>
    /// </w:r>
    /// <w:commentRangeEnd w:id="1"/> <!-- End of comment range -->
    /// <w:r> <!-- Run containing comment reference -->
    ///   <w:rPr>
    ///     <w:rStyle w:val="CommentReference"/> <!-- Style for comment reference -->
    ///   </w:rPr>
    ///   <w:commentReference w:id="1"/> <!-- Reference to comment -->
    /// </w:r>
    /// </remarks>
    public class CommentComponent : ICommentContainer
    {
        /// <summary>
        /// The comment to be added to the document
        /// </summary>
        public required Comment Comment { get; set; }

        /// <summary>
        /// The comment range start marker
        /// </summary>
        public required CommentRangeStart RangeStart { get; set; }

        /// <summary>
        /// The comment range end marker
        /// </summary>
        public required CommentRangeEnd RangeEnd { get; set; }

        /// <summary>
        /// The comment reference element
        /// </summary>
        public required Run Reference { get; set; }
    }

    /// <summary>
    /// Build a comment component with all related elements
    /// </summary>
    /// <remarks>
    /// 1. Comment: the comment element
    /// 2. RangeStart: the start of the comment range
    /// 3. RangeEnd: the end of the comment range
    /// 4. CommentReferenceRun: the reference run of the comment
    /// </remarks>
    public class CommentComponentBuilder
    {
        public Comment? Comment;
        public CommentRangeStart? RangeStart;
        public CommentRangeEnd? RangeEnd;
        public Run? CommentReferenceRun;

        public CommentComponentBuilder(Comment comment)
        {
            Comment = comment ?? throw new ArgumentNullException(nameof(comment));

            // Ensure comment has an ID
            if (comment.Id == null)
            {
                throw new ArgumentException("Comment must have a valid ID", nameof(comment));
            }

            RangeStart = new CommentRangeStart() { Id = comment.Id };
            RangeEnd = new CommentRangeEnd() { Id = comment.Id };
            CommentReferenceRun = new Run(new CommentReference() { Id = comment.Id })
            {
                RunProperties = new RunProperties(
                    new RunStyle { Val = "CommentReference" }
                )
            };
        }

        // TODO: use this constructor to build comment insertion
        // public CommentInsertionBuilder(StringValue commentId, string author, string date, string commentText)
        // {
        //     var _date = DateHepler.SafeParseDate(date);
        //     Comment comment = new
        //     {
        //         Id = commentId.ToString(),
        //         Author = author,
        //         Initials = GetCommentInitials(author),
        //         Date = new DateTimeValue(_date)
        //     };
        //     // comment element always ins text
        //     var run = GetRunElement(message, RevisionOperatorType.ins);
        //     var paragraph = new Paragraph(run);
        //     comment.Append(paragraph);
        //     return comment;
        // }

        public void SetStartId(StringValue commentId)
        {
            RangeStart = new CommentRangeStart() { Id = commentId };
        }

        public void SetEndId(StringValue commentId)
        {
            RangeEnd = new CommentRangeEnd() { Id = commentId };
        }

        public void SetReference(StringValue commentId)
        {
            var reference = new CommentReference() { Id = commentId };
            var run = new Run(reference)
            {
                RunProperties = new RunProperties(
                    new RunStyle { Val = "CommentReference" }
                )
            };
            CommentReferenceRun = run;
        }

        public CommentComponent Build()
        {
            if (Comment == null || RangeStart == null || RangeEnd == null || CommentReferenceRun == null)
            {
                throw new Exception("Comment, RangeStart, RangeEnd, and Reference must be set");
            }
            return new CommentComponent()
            {
                Comment = Comment,
                RangeStart = RangeStart,
                RangeEnd = RangeEnd,
                Reference = CommentReferenceRun
            };
        }
    }
}