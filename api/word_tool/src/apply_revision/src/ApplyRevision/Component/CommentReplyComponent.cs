using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Abstract;

namespace ApplyRevision.Component
{
    /// <summary>
    /// 评论回复组件，包含回复评论的所有相关元素
    /// Comment reply component containing all related elements for a reply comment
    /// </summary>
    public class CommentReplyComponent : ICommentContainer
    {
        /// <summary>
        /// 父评论对象
        /// Parent comment object
        /// </summary>
        public Comment ParentComment { get; set; }

        /// <summary>
        /// 回复评论对象
        /// Reply comment object
        /// </summary>
        public Comment ReplyComment { get; set; }

        /// <summary>
        /// 回复评论的持久ID
        /// Reply comment durable ID
        /// </summary>
        public string ReplyDurableId { get; set; }

        /// <summary>
        /// 父评论的持久ID
        /// Parent comment durable ID
        /// </summary>
        public string ParentDurableId { get; set; }

        /// <summary>
        /// 创建时间的UTC字符串
        /// UTC date string for creation time
        /// </summary>
        public string UtcDateString { get; set; }

        /// <summary>
        /// 实现ICommentContainer接口，返回回复评论作为主要评论
        /// Implement ICommentContainer interface, return reply comment as primary comment
        /// </summary>
        public Comment Comment => ReplyComment;

        /// <summary>
        /// 初始化评论回复组件
        /// Initialize comment reply component
        /// </summary>
        /// <param name="parentComment">父评论 / Parent comment</param>
        /// <param name="replyComment">回复评论 / Reply comment</param>
        /// <param name="replyDurableId">回复持久ID / Reply durable ID</param>
        /// <param name="parentDurableId">父评论持久ID / Parent durable ID</param>
        /// <param name="utcDateString">UTC日期字符串 / UTC date string</param>
        public CommentReplyComponent(
            Comment parentComment,
            Comment replyComment,
            string replyDurableId,
            string parentDurableId,
            string utcDateString)
        {
            ParentComment = parentComment ?? throw new ArgumentNullException(nameof(parentComment));
            ReplyComment = replyComment ?? throw new ArgumentNullException(nameof(replyComment));
            ReplyDurableId = replyDurableId ?? throw new ArgumentNullException(nameof(replyDurableId));
            ParentDurableId = parentDurableId ?? throw new ArgumentNullException(nameof(parentDurableId));
            UtcDateString = utcDateString ?? throw new ArgumentNullException(nameof(utcDateString));
        }

        /// <summary>
        /// 获取父评论ID
        /// Get parent comment ID
        /// </summary>
        /// <returns>父评论ID / Parent comment ID</returns>
        public string GetParentCommentId()
        {
            return ParentComment.Id?.Value ?? string.Empty;
        }

        /// <summary>
        /// 获取回复评论ID
        /// Get reply comment ID
        /// </summary>
        /// <returns>回复评论ID / Reply comment ID</returns>
        public string GetReplyCommentId()
        {
            return ReplyComment.Id?.Value ?? string.Empty;
        }

        /// <summary>
        /// 获取父评论作者
        /// Get parent comment author
        /// </summary>
        /// <returns>父评论作者 / Parent comment author</returns>
        public string GetParentCommentAuthor()
        {
            return ParentComment.Author?.Value ?? string.Empty;
        }

        /// <summary>
        /// 获取回复评论作者
        /// Get reply comment author
        /// </summary>
        /// <returns>回复评论作者 / Reply comment author</returns>
        public string GetReplyCommentAuthor()
        {
            return ReplyComment.Author?.Value ?? string.Empty;
        }

        /// <summary>
        /// 获取父评论内容
        /// Get parent comment content
        /// </summary>
        /// <returns>父评论内容 / Parent comment content</returns>
        public string GetParentCommentText()
        {
            return ExtractCommentText(ParentComment);
        }

        /// <summary>
        /// 获取回复评论内容
        /// Get reply comment content
        /// </summary>
        /// <returns>回复评论内容 / Reply comment content</returns>
        public string GetReplyCommentText()
        {
            return ExtractCommentText(ReplyComment);
        }

        /// <summary>
        /// 从评论对象中提取文本内容
        /// Extract text content from comment object
        /// </summary>
        /// <param name="comment">评论对象 / Comment object</param>
        /// <returns>评论文本内容 / Comment text content</returns>
        private static string ExtractCommentText(Comment comment)
        {
            if (comment == null)
                return string.Empty;

            var textElements = comment.Descendants<Text>();
            return string.Join("", textElements.Select(t => t.Text));
        }

        /// <summary>
        /// 验证组件的完整性
        /// Validate component integrity
        /// </summary>
        /// <returns>是否有效 / Whether valid</returns>
        public bool IsValid()
        {
            return ParentComment != null &&
                   ReplyComment != null &&
                   !string.IsNullOrWhiteSpace(ReplyDurableId) &&
                   !string.IsNullOrWhiteSpace(ParentDurableId) &&
                   !string.IsNullOrWhiteSpace(UtcDateString) &&
                   !string.IsNullOrWhiteSpace(GetParentCommentId()) &&
                   !string.IsNullOrWhiteSpace(GetReplyCommentId());
        }

        /// <summary>
        /// 获取组件的字符串表示
        /// Get string representation of component
        /// </summary>
        /// <returns>字符串表示 / String representation</returns>
        public override string ToString()
        {
            return $"CommentReplyComponent: Reply {GetReplyCommentId()} -> Parent {GetParentCommentId()}";
        }
    }
}
