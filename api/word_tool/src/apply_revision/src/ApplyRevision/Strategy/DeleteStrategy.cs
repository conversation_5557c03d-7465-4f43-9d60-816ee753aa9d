using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Component;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Helper;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for handling delete operations
    /// 处理删除操作的策略
    /// </summary>
    public class DeleteStrategy : BaseOperationStrategy
    {
        public DeleteStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager
        ) : base(context, elementFactory, revisionFactory, idManager, commentManager)
        {
        }

        /// <summary>
        /// Execute delete operation with relative positioning
        /// 使用相对定位执行删除操作，如果删除的长度等于文本的长度，则将删除targetRun
        /// </summary>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            // 通过这个方法，我们可以操作实际需要操作的 run 以及相对该 run 的偏移量
            var targetRun = runLocator.FindRunWithTextForOperation(doc, operation, out int relativeStart, out int relativeEnd);
            if (targetRun == null)
            {
                context.Logger.LogError($"Cannot find Run with text for SegId: {operation.Target.FirstOrDefault()}");
                return 0;
            }

            context.Logger.LogInformation($"Located target Run with text for SegId: {operation.Target.FirstOrDefault()}, relative position [{relativeStart}, {relativeEnd}]");

            context.Logger.LogInformation($"Applying delete from relative position {relativeStart} to {relativeEnd}");

            try
            {
                // Debug: Log target run content
                var runText = string.Join("", targetRun.Descendants<Text>().Select(t => t.Text));
                context.Logger.LogInformation($"Target Run text: '{runText}' (length: {runText.Length})");

                // Calculate the delete length
                int deleteLength = relativeEnd - relativeStart;

                // Check if delete length equals total text length - if so, delete the entire run
                // 检查删除长度是否等于总文本长度 - 如果是，则删除整个run
                if (deleteLength == runText.Length && relativeStart == 0)
                {
                    context.Logger.LogInformation($"Delete length ({deleteLength}) equals text length ({runText.Length}), deleting entire Run");
                    return DeleteEntireRun(operation, targetRun, runText);
                }

                // Locate position within the Run for delete operation using relative position
                var (text, offset) = LocateTextPosition(targetRun, relativeStart);
                if (text == null)
                {
                    context.Logger.LogError($"Cannot locate text position {relativeStart} within Run for delete operation");
                    return 0;
                }

                context.Logger.LogInformation($"Located text element: '{text.Text}' at offset {offset}");

                CommentComponent? commentComponent = null;

                // Create comment component if needed, if not, use standard delete
                if (operation.Comment != null)
                {
                    commentComponent = CreateCommentComponent(operation.Comment);
                }
                // Execute the delete operation
                if (commentComponent != null)
                {
                    ApplyDeleteWithComment(operation, targetRun, text, offset, relativeStart, relativeEnd, commentComponent);
                }
                else
                {
                    ApplyStandardDelete(operation, targetRun, text, offset, relativeStart, relativeEnd);
                }

                var deletedLength = relativeEnd - relativeStart;
                context.Logger.LogInformation($"Delete operation completed, text offset: -{deletedLength}");
                return -deletedLength;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply delete: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Deletes the entire target run and replaces it with a DeletedRun
        /// 删除整个目标run并用DeletedRun替换它
        /// </summary>
        private int DeleteEntireRun(Operation operation, Run targetRun, string runText)
        {
            context.Logger.LogInformation($"Deleting entire Run with text: '{runText}'");

            try
            {
                // Create DeletedRun for the entire text
                var deletedRun = CreateDeletedRun(runText, operation.Revision ?? throw new InvalidOperationException("Revision is required"), targetRun);

                CommentComponent? commentComponent = null;

                // Create comment component if needed
                if (operation.Comment != null)
                {
                    commentComponent = CreateCommentComponent(operation.Comment);
                }

                if (commentComponent != null)
                {
                    // Insert comment range start before the target run
                    targetRun.InsertBeforeSelf(commentComponent.RangeStart);

                    // Insert the deleted run after range start
                    commentComponent.RangeStart.InsertAfterSelf(deletedRun);

                    // Insert comment range end after the deleted run
                    deletedRun.InsertAfterSelf(commentComponent.RangeEnd);

                    // Insert comment reference after the range end
                    commentComponent.RangeEnd.InsertAfterSelf(commentComponent.Reference);

                    // Add comment to document
                    AddCommentToDocument(commentComponent);
                }
                else
                {
                    // Insert the deleted run before the target run
                    targetRun.InsertBeforeSelf(deletedRun);
                }

                // Remove the original target run
                targetRun.Remove();

                // If operation includes a comment, we also add a comment reference Run
                // which affects subsequent Run positioning
                // 如果操作包含评论，我们还会添加一个评论引用Run，这会影响后续Run的定位
                var commentOffset = commentComponent != null ? 1 : 0;
                var totalOffset = -runText.Length + commentOffset;

                context.Logger.LogInformation($"Successfully deleted entire Run, text offset: -{runText.Length}, comment offset: +{commentOffset}, total offset: {totalOffset:+#;-#;0}");
                return totalOffset;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to delete entire Run: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies a delete operation with comment integration
        /// 应用带注释集成的删除操作
        /// </summary>
        private void ApplyDeleteWithComment(Operation operation, Run targetRun, Text text, int offset,
            int relativeStart, int relativeEnd, CommentComponent commentComponent)
        {
            context.Logger.LogInformation($"Applying delete with comment from position {relativeStart} to {relativeEnd}");

            try
            {
                // Calculate the range to delete
                int deleteLength = relativeEnd - relativeStart;
                string originalText = text.Text;

                if (offset + deleteLength > originalText.Length)
                {
                    context.Logger.LogError($"Delete range exceeds text length. Text: '{originalText}', Offset: {offset}, Length: {deleteLength}");
                    return;
                }

                // Extract the text to be deleted
                string beforeText = originalText.Substring(0, offset);
                string deletedText = originalText.Substring(offset, deleteLength);
                string afterText = originalText.Substring(offset + deleteLength);

                // Create DeletedRun for the deleted text
                var deletedRun = CreateDeletedRun(deletedText, operation.Revision ?? throw new InvalidOperationException("Revision is required"), targetRun);

                // Update original text to before portion
                text.Text = beforeText;

                // Insert comment range start after the current run
                targetRun.InsertAfterSelf(commentComponent.RangeStart);

                // Insert the deleted run after range start
                commentComponent.RangeStart.InsertAfterSelf(deletedRun);

                // Insert comment range end after the deleted run
                deletedRun.InsertAfterSelf(commentComponent.RangeEnd);

                // Insert comment reference after the range end
                commentComponent.RangeEnd.InsertAfterSelf(commentComponent.Reference);

                // Create after run if there's remaining text
                if (!string.IsNullOrEmpty(afterText))
                {
                    var afterRun = elementFactory.CloneElement(targetRun, deepClone: false);
                    afterRun.RemoveAllChildren<Text>();

                    var afterTextElement = new Text(afterText);
                    // Only set xml:space="preserve" if the text actually needs it
                    if (RequiresSpacePreservation(afterText))
                    {
                        afterTextElement.Space = SpaceProcessingModeValues.Preserve;
                    }
                    afterRun.Append(afterTextElement);

                    commentComponent.Reference.InsertAfterSelf(afterRun);
                }

                // Add comment to document
                AddCommentToDocument(commentComponent);

                context.Logger.LogInformation("Successfully applied delete operation with comment");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply delete with comment: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies a standard delete operation without comments
        /// 应用标准删除操作（不带注释）
        /// </summary>
        private void ApplyStandardDelete(Operation operation, Run targetRun, Text text, int offset,
            int relativeStart, int relativeEnd)
        {
            context.Logger.LogInformation($"Applying standard delete from position {relativeStart} to {relativeEnd}");

            try
            {
                // Calculate the range to delete
                int deleteLength = relativeEnd - relativeStart;
                string originalText = text.Text;

                if (offset + deleteLength > originalText.Length)
                {
                    context.Logger.LogError($"Delete range exceeds text length. Text: '{originalText}', Offset: {offset}, Length: {deleteLength}");
                    return;
                }

                // Extract the text to be deleted
                string beforeText = originalText.Substring(0, offset);
                string deletedText = originalText.Substring(offset, deleteLength);
                string afterText = originalText.Substring(offset + deleteLength);

                // Create DeletedRun for the deleted text
                var deletedRun = CreateDeletedRun(deletedText, operation.Revision ?? throw new InvalidOperationException("Revision is required"), targetRun);

                // Update original text to before portion
                text.Text = beforeText;

                // Insert the deleted run after the current run
                targetRun.InsertAfterSelf(deletedRun);

                // Set xml:space="preserve" for targetRun's text element if needed
                if (RequiresSpacePreservation(beforeText))
                {
                    var targetTextElement = targetRun.GetFirstChild<Text>();
                    if (targetTextElement != null)
                    {
                        targetTextElement.Space = SpaceProcessingModeValues.Preserve;
                    }
                }

                // Create after run if there's remaining text
                if (!string.IsNullOrEmpty(afterText))
                {
                    var afterRun = elementFactory.CloneElement(targetRun, deepClone: false);
                    afterRun.RemoveAllChildren<Text>();
                    afterRun.Append(new Text(afterText) { Space = SpaceProcessingModeValues.Preserve });

                    deletedRun.InsertAfterSelf(afterRun);
                }

                context.Logger.LogInformation("Successfully applied standard delete operation");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply standard delete: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Check if text requires xml:space="preserve" attribute
        /// 检查文本是否需要 xml:space="preserve" 属性
        /// </summary>
        /// <param name="text">Text to check</param>
        /// <returns>True if space preservation is needed</returns>
        private static bool RequiresSpacePreservation(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // Check for leading spaces, trailing spaces, multiple consecutive spaces, or tabs
            return text.StartsWith(" ") ||
                   text.EndsWith(" ") ||
                   text.Contains("  ") ||  // Multiple consecutive spaces
                   text.Contains("\t");    // Tab characters
        }

    }
}