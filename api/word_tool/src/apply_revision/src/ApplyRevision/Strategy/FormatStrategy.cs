using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Component;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for handling format operations
    /// 处理格式操作的策略
    /// 
    /// Overview:
    /// This strategy handles text formatting operations in Word documents while maintaining
    /// proper revision tracking. It applies formatting properties to specified text ranges
    /// and creates appropriate revision tracking elements.
    /// 
    /// Key Features:
    /// - Applies formatting properties (bold, italic, underline, color, font size, etc.)
    /// - Maintains proper revision tracking with RunPropertiesChange elements
    /// - Handles text segmentation for partial formatting within runs
    /// - Preserves existing formatting while applying new properties
    /// - Supports comment integration for formatting changes
    /// 
    /// Formatting Process:
    /// 1. Locate the target text range within the run
    /// 2. Extract formatting properties from operation.Props
    /// 3. Split the run if formatting applies to partial text
    /// 4. Apply formatting properties to the target segment
    /// 5. Create RunPropertiesChange revision element for tracking
    /// 6. Maintain document structure and revision history
    /// 
    /// Supported Formatting Properties:
    /// - bold: true/false
    /// - italic: true/false  
    /// - underline: true/false or underline type
    /// - color: hex color value (e.g., "FF0000" for red)
    /// - fontSize: font size in points
    /// - fontFamily: font family name
    /// - highlight: highlight color
    /// - strikethrough: true/false
    /// 
    /// Usage Example:
    /// var operation = new Operation {
    ///     Op = OperationType.Format,
    ///     Target = new("segment1"),
    ///     Range = new Range { Start = 5, End = 15 },
    ///     Props = new Dictionary<string, object> {
    ///         { "bold", true },
    ///         { "color", "FF0000" },
    ///         { "fontSize", 14 }
    ///     },
    ///     Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
    /// };
    /// </summary>
    public class FormatStrategy : BaseOperationStrategy
    {
        public FormatStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager
        ) : base(context, elementFactory, revisionFactory, idManager, commentManager)
        {
        }

        /// <summary>
        /// Execute format operation with relative positioning
        /// 使用相对定位执行格式操作
        /// </summary>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            var targetRun = runLocator.FindRunWithTextForOperation(doc, operation, out int relativeStart, out int relativeEnd);
            if (targetRun == null)
            {
                context.Logger.LogError($"Cannot find Run with text for SegId: {operation.Target.FirstOrDefault()}");
                return 0;
            }

            context.Logger.LogInformation($"Located target Run with text for SegId: {operation.Target.FirstOrDefault()}, relative position [{relativeStart}, {relativeEnd}]");
            context.Logger.LogInformation($"Applying format operation from position {relativeStart} to {relativeEnd}");

            try
            {
                // Validate operation parameters
                if (operation.Props == null || operation.Props.Count == 0)
                {
                    context.Logger.LogWarning("No formatting properties provided for format operation");
                    return 0;
                }

                // Validate relative positions
                if (relativeStart < 0)
                {
                    context.Logger.LogError($"Invalid offset: {relativeStart}");
                    return 0;
                }

                if (relativeEnd <= relativeStart)
                {
                    context.Logger.LogError($"Invalid format length: {relativeEnd - relativeStart}");
                    return 0;
                }

                // Debug: Log current run content before locating position
                var currentRunText = string.Join("", targetRun.Descendants<Text>().Select(t => t.Text));
                context.Logger.LogInformation($"Current Run text before locate: '{currentRunText}' (length: {currentRunText.Length})");
                context.Logger.LogInformation($"Trying to locate relative position {relativeStart} in Run");

                // Check if the relative positions are valid for the current run
                // 检查相对位置对于当前run是否有效
                if (relativeStart >= currentRunText.Length || relativeEnd > currentRunText.Length)
                {
                    context.Logger.LogWarning($"Relative position [{relativeStart}, {relativeEnd}] exceeds or spans beyond current run length {currentRunText.Length}. This may be due to previous operations modifying the run structure.");

                    // Handle cross-run formatting by splitting the operation
                    // 通过分割操作来处理跨Run的格式化
                    var paragraph = targetRun.Ancestors<Paragraph>().FirstOrDefault();
                    if (paragraph != null)
                    {
                        var allRuns = paragraph.Descendants<Run>().ToList();
                        int cumulativePosition = 0;
                        int totalFormatted = 0;

                        foreach (var run in allRuns)
                        {
                            var runText = string.Join("", run.Descendants<Text>().Select(t => t.Text));
                            var delRunText = string.Join("", run.Descendants<DeletedText>().Select(t => t.Text));
                            var runLength = runText.Length + delRunText.Length;

                            // Check if this run overlaps with our target range
                            // 检查这个run是否与我们的目标范围重叠
                            var runStart = cumulativePosition;
                            var runEnd = cumulativePosition + runLength;

                            if (relativeStart < runEnd && relativeEnd > runStart)
                            {
                                // Calculate the overlap
                                // 计算重叠部分
                                var overlapStart = Math.Max(relativeStart, runStart) - runStart;
                                var overlapEnd = Math.Min(relativeEnd, runEnd) - runStart;

                                if (overlapEnd > overlapStart)
                                {
                                    context.Logger.LogInformation($"Applying format to run with text: '{runText}', position [{overlapStart}, {overlapEnd}]");

                                    // Apply formatting to this run segment
                                    // 对这个run段应用格式化
                                    var segmentResult = ApplyFormattingToRunSegment(operation, run, overlapStart, overlapEnd);
                                    if (segmentResult > 0)
                                    {
                                        totalFormatted += segmentResult;
                                    }
                                }
                            }

                            cumulativePosition += runLength;

                            // If we've processed beyond our target range, we're done
                            // 如果我们已经处理超出了目标范围，就完成了
                            if (cumulativePosition >= relativeEnd)
                            {
                                break;
                            }
                        }

                        context.Logger.LogInformation($"Cross-run formatting completed, total segments formatted: {totalFormatted}");
                        return 0; // Format operations don't change text length
                    }
                    else
                    {
                        context.Logger.LogError($"Cannot find paragraph containing the run");
                        return 0;
                    }
                }

                // Locate position within the Run using relative position
                var (text, offset) = LocateTextPosition(targetRun, relativeStart);
                if (text == null)
                {
                    context.Logger.LogError($"Cannot locate text position within Run. Run has {targetRun.Descendants<Text>().Count()} Text elements");
                    return 0;
                }

                context.Logger.LogInformation($"Located text element: '{text.Text}' at offset {offset}");

                // Validate the range
                int formatLength = relativeEnd - relativeStart;
                if (!ValidateFormatRange(text.Text, offset, formatLength))
                {
                    return 0;
                }

                CommentComponent? commentComponent = null;
                if (operation.Comment != null)
                {
                    // Create comment component if needed
                    commentComponent = CreateCommentComponent(operation.Comment);
                }

                // Execute the format operation
                if (commentComponent != null)
                {
                    ApplyFormatWithComment(operation, targetRun, text, offset, formatLength, commentComponent);
                }
                else
                {
                    ApplyStandardFormat(operation, targetRun, text, offset, formatLength);
                }

                context.Logger.LogInformation("Format operation completed successfully");
                return 0; // Format operations don't change text length
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply format: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Validates the format range parameters
        /// 验证格式范围参数
        /// </summary>
        private bool ValidateFormatRange(string text, int offset, int formatLength)
        {
            if (offset < 0 || offset > text.Length)
            {
                context.Logger.LogError($"Invalid offset: {offset}, text length: {text.Length}");
                return false;
            }

            if (formatLength <= 0)
            {
                context.Logger.LogError($"Invalid format length: {formatLength}");
                return false;
            }

            if (offset + formatLength > text.Length)
            {
                context.Logger.LogError($"Format range exceeds text length. Text: '{text}', Offset: {offset}, Length: {formatLength}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Applies format operation with comment integration
        /// 应用带注释集成的格式操作
        /// </summary>
        private void ApplyFormatWithComment(Operation operation, Run targetRun, Text text, int offset, int formatLength, CommentComponent commentComponent)
        {
            context.Logger.LogInformation("Applying format with comment");

            try
            {
                // Apply the formatting and get the formatted run
                var formattedRun = ApplyFormattingToTextRange(operation, targetRun, text, offset, formatLength);

                // Wrap the formatted run with comment markers
                if (formattedRun != null)
                {
                    // Insert comment range start before the formatted run
                    formattedRun.InsertBeforeSelf(commentComponent.RangeStart);

                    // Insert comment range end after the formatted run
                    formattedRun.InsertAfterSelf(commentComponent.RangeEnd);
                }

                // Add comment to document
                AddCommentToDocument(commentComponent);

                context.Logger.LogInformation("Successfully applied format operation with comment");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply format with comment: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies a standard format operation without comments
        /// 应用标准格式操作（不带注释）
        /// </summary>
        private void ApplyStandardFormat(Operation operation, Run targetRun, Text text, int offset, int formatLength)
        {
            context.Logger.LogInformation("Applying standard format");

            try
            {
                ApplyFormattingToTextRange(operation, targetRun, text, offset, formatLength);
                context.Logger.LogInformation("Successfully applied standard format operation");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply standard format: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies formatting to a specific segment of a run (used for cross-run operations)
        /// 对run的特定段应用格式化（用于跨Run操作）
        /// </summary>
        private int ApplyFormattingToRunSegment(Operation operation, Run targetRun, int segmentStart, int segmentEnd)
        {
            try
            {
                var (text, offset) = LocateTextPosition(targetRun, segmentStart);
                if (text == null)
                {
                    context.Logger.LogError($"Cannot locate text position {segmentStart} within run segment");
                    return 0;
                }

                int formatLength = segmentEnd - segmentStart;
                if (!ValidateFormatRange(text.Text, offset, formatLength))
                {
                    return 0;
                }

                ApplyFormattingToTextRange(operation, targetRun, text, offset, formatLength);
                return 1; // Return 1 to indicate successful formatting
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply formatting to run segment: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Applies formatting to a specific text range within a run
        /// 对run内的特定文本范围应用格式
        /// </summary>
        private Run? ApplyFormattingToTextRange(Operation operation, Run targetRun, Text text, int offset, int formatLength)
        {
            string originalText = text.Text;
            string beforeText = originalText.Substring(0, offset);
            string formattedText = originalText.Substring(offset, formatLength);
            string afterText = originalText.Substring(offset + formatLength);

            context.Logger.LogInformation($"Formatting text segment: '{formattedText}' (length: {formatLength})");

            if (operation.Props == null || operation.Props.Count == 0)
            {
                context.Logger.LogWarning("No formatting properties provided for format operation, will not apply formatting");
                return targetRun;
            }

            // Create RunPropertiesChange for revision tracking
            var revisionId = idManager.GetNextRevisionId();
            var runPropertiesChange = revisionFactory.CreateRunPropertiesChange(
                revisionId.ToString(),
                operation.Revision?.Author ?? "Styling Agent",
                operation.Revision?.Date.ToString("yyyy-MM-ddTHH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss"),
                targetRun.RunProperties?.CloneNode(true) as RunProperties
            );

            Run formattedRun;
            if (beforeText.Length == 0 && afterText.Length == 0)
            {
                // Format the entire run
                formattedRun = FormatEntireRun(targetRun, operation.Props, runPropertiesChange);
            }
            else if (beforeText.Length == 0)
            {
                // Format from beginning
                formattedRun = FormatFromBeginning(targetRun, text, formattedText, afterText, operation.Props, runPropertiesChange);
            }
            else if (afterText.Length == 0)
            {
                // Format to end
                formattedRun = FormatToEnd(targetRun, text, beforeText, formattedText, operation.Props, runPropertiesChange);
            }
            else
            {
                // Format middle section
                formattedRun = FormatMiddleSection(targetRun, text, beforeText, formattedText, afterText, operation.Props, runPropertiesChange);
            }

            return formattedRun;
        }

        /// <summary>
        /// Formats the entire run
        /// 格式化整个run
        /// </summary>
        private Run FormatEntireRun(Run targetRun, Dictionary<string, object> props, RunPropertiesChange runPropertiesChange)
        {
            // Apply formatting properties to the existing run
            ApplyFormattingProperties(targetRun, props);

            // Insert the RunPropertiesChange element before the run
            targetRun.InsertBeforeSelf(runPropertiesChange);

            return targetRun;
        }

        /// <summary>
        /// Formats text from the beginning of the run
        /// 从run开头格式化文本
        /// </summary>
        private Run FormatFromBeginning(Run targetRun, Text text, string formattedText, string afterText, Dictionary<string, object> props, RunPropertiesChange runPropertiesChange)
        {
            // Update original text to the formatted portion
            text.Text = formattedText;

            // Apply formatting to the current run
            ApplyFormattingProperties(targetRun, props);

            // Insert RunPropertiesChange before the formatted run
            targetRun.InsertBeforeSelf(runPropertiesChange);

            // Create after run for remaining text
            if (!string.IsNullOrEmpty(afterText))
            {
                var afterRun = elementFactory.CloneElement(targetRun, deepClone: false);
                // Remove formatting from the cloned run to preserve original formatting
                afterRun.RunProperties = targetRun.RunProperties?.CloneNode(true) as RunProperties;
                afterRun.RemoveAllChildren<Text>();
                afterRun.Append(new Text(afterText) { Space = SpaceProcessingModeValues.Preserve });

                targetRun.InsertAfterSelf(afterRun);
            }

            return targetRun;
        }

        /// <summary>
        /// Formats text to the end of the run
        /// 格式化文本到run结尾
        /// </summary>
        private Run FormatToEnd(Run targetRun, Text text, string beforeText, string formattedText, Dictionary<string, object> props, RunPropertiesChange runPropertiesChange)
        {
            // Update original text to before portion
            text.Text = beforeText;

            // Create formatted run for the formatted text
            var formattedRun = elementFactory.CloneElement(targetRun, deepClone: false);
            formattedRun.RemoveAllChildren<Text>();
            formattedRun.Append(new Text(formattedText) { Space = SpaceProcessingModeValues.Preserve });

            // Apply formatting properties to the formatted run
            ApplyFormattingProperties(formattedRun, props);

            // Insert RunPropertiesChange before the formatted run
            targetRun.InsertAfterSelf(runPropertiesChange);
            targetRun.InsertAfterSelf(formattedRun);

            return formattedRun;
        }

        /// <summary>
        /// Formats text in the middle of the run
        /// 格式化run中间的文本
        /// </summary>
        private Run FormatMiddleSection(Run targetRun, Text text, string beforeText, string formattedText, string afterText, Dictionary<string, object> props, RunPropertiesChange runPropertiesChange)
        {
            // Update original text to before portion
            text.Text = beforeText;

            // Create formatted run for the formatted text
            var formattedRun = elementFactory.CloneElement(targetRun, deepClone: false);
            formattedRun.RemoveAllChildren<Text>();
            formattedRun.Append(new Text(formattedText) { Space = SpaceProcessingModeValues.Preserve });

            // Apply formatting properties to the formatted run
            ApplyFormattingProperties(formattedRun, props);

            // Create after run for remaining text
            var afterRun = elementFactory.CloneElement(targetRun, deepClone: false);
            afterRun.RemoveAllChildren<Text>();
            afterRun.Append(new Text(afterText) { Space = SpaceProcessingModeValues.Preserve });

            // Insert elements in order: original (before), RunPropertiesChange, formatted, after
            targetRun.InsertAfterSelf(runPropertiesChange);
            runPropertiesChange.InsertAfterSelf(formattedRun);
            formattedRun.InsertAfterSelf(afterRun);

            return formattedRun;
        }

        /// <summary>
        /// Applies formatting properties to a run
        /// 对run应用格式属性
        /// </summary>
        private void ApplyFormattingProperties(Run run, Dictionary<string, object> props)
        {
            // Ensure the run has RunProperties
            if (run.RunProperties == null)
            {
                run.RunProperties = new RunProperties();
            }

            var runProperties = run.RunProperties;

            foreach (var prop in props)
            {
                ApplyFormattingProperty(runProperties, prop.Key, prop.Value);
            }
        }

        /// <summary>
        /// Applies a single formatting property to RunProperties
        /// 对RunProperties应用单个格式属性
        /// </summary>
        private void ApplyFormattingProperty(RunProperties runProperties, string propertyName, object propertyValue)
        {
            switch (propertyName.ToLowerInvariant())
            {
                case "bold":
                    ApplyBoldProperty(runProperties, propertyValue);
                    break;

                case "italic":
                    ApplyItalicProperty(runProperties, propertyValue);
                    break;

                case "underline":
                    ApplyUnderlineProperty(runProperties, propertyValue);
                    break;

                case "color":
                    ApplyColorProperty(runProperties, propertyValue);
                    break;

                case "fontsize":
                case "font-size":
                    ApplyFontSizeProperty(runProperties, propertyValue);
                    break;

                case "fontfamily":
                case "font-family":
                    ApplyFontFamilyProperty(runProperties, propertyValue);
                    break;

                case "highlight":
                    ApplyHighlightProperty(runProperties, propertyValue);
                    break;

                case "strikethrough":
                case "strike":
                    ApplyStrikethroughProperty(runProperties, propertyValue);
                    break;

                default:
                    context.Logger.LogWarning($"Unsupported formatting property: {propertyName}");
                    break;
            }
        }

        /// <summary>
        /// Applies bold formatting property
        /// 应用粗体格式属性
        /// </summary>
        private void ApplyBoldProperty(RunProperties runProperties, object propertyValue)
        {
            if (ConvertToBool(propertyValue))
            {
                if (runProperties.GetFirstChild<Bold>() == null)
                {
                    runProperties.Append(new Bold());
                }
            }
            else
            {
                runProperties.RemoveAllChildren<Bold>();
            }
        }

        /// <summary>
        /// Applies italic formatting property
        /// 应用斜体格式属性
        /// </summary>
        private void ApplyItalicProperty(RunProperties runProperties, object propertyValue)
        {
            if (ConvertToBool(propertyValue))
            {
                if (runProperties.GetFirstChild<Italic>() == null)
                {
                    runProperties.Append(new Italic());
                }
            }
            else
            {
                runProperties.RemoveAllChildren<Italic>();
            }
        }

        /// <summary>
        /// Applies underline formatting property
        /// 应用下划线格式属性
        /// </summary>
        private void ApplyUnderlineProperty(RunProperties runProperties, object propertyValue)
        {
            // Remove existing underline first
            runProperties.RemoveAllChildren<Underline>();

            // Handle different underline value types
            if (propertyValue is string stringValue && !string.IsNullOrEmpty(stringValue))
            {
                // Parse underline type from string
                if (TryParseUnderlineValue(stringValue, out UnderlineValues underlineType))
                {
                    runProperties.Append(new Underline { Val = underlineType });
                }
                else
                {
                    context.Logger.LogWarning($"Invalid underline value: {stringValue}");
                }
            }
            else if (ConvertToBool(propertyValue))
            {
                // Default to single underline for boolean true
                runProperties.Append(new Underline { Val = UnderlineValues.Single });
            }
        }

        /// <summary>
        /// Applies color formatting property
        /// 应用颜色格式属性
        /// </summary>
        private void ApplyColorProperty(RunProperties runProperties, object propertyValue)
        {
            if (propertyValue is string colorValue && !string.IsNullOrEmpty(colorValue))
            {
                // Remove existing color
                runProperties.RemoveAllChildren<Color>();

                // Add new color (ensure it's a valid hex color)
                if (IsValidHexColor(colorValue))
                {
                    runProperties.Append(new Color { Val = colorValue });
                }
                else
                {
                    context.Logger.LogWarning($"Invalid color value: {colorValue}");
                }
            }
            else if (propertyValue is bool boolValue && !boolValue)
            {
                // Remove color when explicitly set to false
                runProperties.RemoveAllChildren<Color>();
            }
            else if (!ConvertToBool(propertyValue))
            {
                // Remove color for any falsy value
                runProperties.RemoveAllChildren<Color>();
            }
        }

        /// <summary>
        /// Applies font size formatting property
        /// 应用字体大小格式属性
        /// </summary>
        private void ApplyFontSizeProperty(RunProperties runProperties, object propertyValue)
        {
            if (TryConvertToInt(propertyValue, out int fontSize) && fontSize > 0)
            {
                // Remove existing font size
                runProperties.RemoveAllChildren<FontSize>();

                // Add new font size (Word uses half-points, so multiply by 2)
                runProperties.Append(new FontSize { Val = (fontSize * 2).ToString() });
            }
            else if (!ConvertToBool(propertyValue))
            {
                // Remove font size for falsy values
                runProperties.RemoveAllChildren<FontSize>();
            }
            else
            {
                context.Logger.LogWarning($"Invalid font size value: {propertyValue}");
            }
        }

        /// <summary>
        /// Applies font family formatting property
        /// 应用字体族格式属性
        /// </summary>
        private void ApplyFontFamilyProperty(RunProperties runProperties, object propertyValue)
        {
            if (propertyValue is string fontFamily && !string.IsNullOrEmpty(fontFamily))
            {
                // Remove existing font family
                runProperties.RemoveAllChildren<RunFonts>();

                // Add new font family
                runProperties.Append(new RunFonts { Ascii = fontFamily, HighAnsi = fontFamily });
            }
            else if (!ConvertToBool(propertyValue))
            {
                // Remove font family for falsy values
                runProperties.RemoveAllChildren<RunFonts>();
            }
        }

        /// <summary>
        /// Applies highlight formatting property
        /// 应用高亮格式属性
        /// </summary>
        private void ApplyHighlightProperty(RunProperties runProperties, object propertyValue)
        {
            if (propertyValue is string highlightValue && !string.IsNullOrEmpty(highlightValue))
            {
                // Remove existing highlight
                runProperties.RemoveAllChildren<Highlight>();

                // Add new highlight using string constructor for HighlightColorValues
                try
                {
                    var highlightColor = new HighlightColorValues(highlightValue);
                    runProperties.Append(new Highlight { Val = highlightColor });
                }
                catch (ArgumentException)
                {
                    context.Logger.LogWarning($"Invalid highlight color: {highlightValue}");
                }
            }
            else if (!ConvertToBool(propertyValue))
            {
                // Remove highlight for falsy values
                runProperties.RemoveAllChildren<Highlight>();
            }
        }

        /// <summary>
        /// Applies strikethrough formatting property
        /// 应用删除线格式属性
        /// </summary>
        private void ApplyStrikethroughProperty(RunProperties runProperties, object propertyValue)
        {
            if (ConvertToBool(propertyValue))
            {
                if (runProperties.GetFirstChild<Strike>() == null)
                {
                    runProperties.Append(new Strike());
                }
            }
            else
            {
                runProperties.RemoveAllChildren<Strike>();
            }
        }

        /// <summary>
        /// Converts a value to boolean
        /// 将值转换为布尔值
        /// </summary>
        private bool ConvertToBool(object value)
        {
            if (value is bool boolValue)
                return boolValue;

            if (value is string stringValue)
            {
                return string.Equals(stringValue, "true", StringComparison.OrdinalIgnoreCase) ||
                       string.Equals(stringValue, "1", StringComparison.OrdinalIgnoreCase);
            }

            if (value is int intValue)
                return intValue != 0;

            return false;
        }

        /// <summary>
        /// Tries to convert a value to integer
        /// 尝试将值转换为整数
        /// </summary>
        private bool TryConvertToInt(object value, out int result)
        {
            result = 0;

            if (value is int intValue)
            {
                result = intValue;
                return true;
            }

            if (value is string stringValue)
            {
                return int.TryParse(stringValue, out result);
            }

            if (value is double doubleValue)
            {
                result = (int)Math.Round(doubleValue);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Validates if a string is a valid hex color
        /// 验证字符串是否为有效的十六进制颜色
        /// </summary>
        private bool IsValidHexColor(string color)
        {
            if (string.IsNullOrEmpty(color))
                return false;

            // Remove # if present
            if (color.StartsWith("#"))
                color = color.Substring(1);

            // Check if it's 6 characters and all hex digits
            return color.Length == 6 && color.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'));
        }

        /// <summary>
        /// Tries to parse an underline value
        /// 尝试解析下划线值
        /// </summary>
        private bool TryParseUnderlineValue(string value, out UnderlineValues result)
        {
            result = UnderlineValues.None;

            if (string.IsNullOrEmpty(value))
                return false;

            // Handle common string values and map them to UnderlineValues
            switch (value.ToLowerInvariant())
            {
                case "single":
                    result = UnderlineValues.Single;
                    return true;
                case "double":
                    result = UnderlineValues.Double;
                    return true;
                case "none":
                    result = UnderlineValues.None;
                    return true;
                case "words":
                    result = UnderlineValues.Words;
                    return true;
                case "thick":
                    result = UnderlineValues.Thick;
                    return true;
                case "dotted":
                    result = UnderlineValues.Dotted;
                    return true;
                case "dash":
                    result = UnderlineValues.Dash;
                    return true;
                case "wave":
                    result = UnderlineValues.Wave;
                    return true;
                default:
                    // Try to parse directly as UnderlineValues enum
                    if (Enum.TryParse(value, true, out result))
                    {
                        return true;
                    }
                    break;
            }

            return false;
        }

        /// <summary>
        /// Check if text requires xml:space="preserve" attribute
        /// 检查文本是否需要 xml:space="preserve" 属性
        /// </summary>
        /// <param name="text">Text to check</param>
        /// <returns>True if space preservation is needed</returns>
        private static bool RequiresSpacePreservation(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // Check for leading spaces, trailing spaces, multiple consecutive spaces, or tabs
            return text.StartsWith(' ') ||
                   text.EndsWith(' ') ||
                   text.Contains("  ") ||  // Multiple consecutive spaces
                   text.Contains('\t');    // Tab characters
        }
    }
}