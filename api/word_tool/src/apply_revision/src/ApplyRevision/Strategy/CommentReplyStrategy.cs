using Amazon.Lambda.Core;
using DocumentFormat.OpenXml.Packaging;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Exceptions;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for handling comment reply operations
    /// 处理评论回复操作的策略
    /// 
    /// Overview:
    /// This strategy handles comment reply operations in Word documents. Unlike other
    /// text operations, comment replies don't modify the document text directly but
    /// add reply content to existing comments in the comments.xml part.
    /// 
    /// 概述：
    /// 此策略处理Word文档中的评论回复操作。与其他文本操作不同，评论回复不直接修改
    /// 文档文本，而是在comments.xml部分向现有评论添加回复内容。
    /// 
    /// Key Features:
    /// - Locates target comments by ID
    /// - Creates reply comments with proper metadata
    /// - Maintains comment hierarchy and relationships
    /// - Supports author and timestamp tracking
    /// - Returns zero offset change (no text modification)
    /// 
    /// 主要功能：
    /// - 通过ID定位目标评论
    /// - 创建具有适当元数据的回复评论
    /// - 维护评论层次结构和关系
    /// - 支持作者和时间戳跟踪
    /// - 返回零偏移变化（无文本修改）
    /// 
    /// Usage Example:
    /// var operation = new Operation {
    ///     Op = OperationType.CommentReply,
    ///     CommentId = "5",
    ///     Text = "This is a reply to the comment",
    ///     Revision = new Revision { 
    ///         Author = "AI Assistant", 
    ///         Date = DateTime.Now 
    ///     }
    /// };
    /// 
    /// Note: CommentReply operations do not require Target or Range properties
    /// since they operate on existing comments rather than document text positions.
    /// 
    /// 注意：CommentReply操作不需要Target或Range属性，因为它们操作现有评论
    /// 而不是文档文本位置。
    /// </summary>
    public class CommentReplyStrategy : BaseOperationStrategy
    {
        private readonly ICommentOrchestrator _commentOrchestrator;

        /// <summary>
        /// Initialize the CommentReplyStrategy with required dependencies
        /// 使用所需依赖项初始化CommentReplyStrategy
        /// </summary>
        /// <param name="context">Lambda execution context / Lambda执行上下文</param>
        /// <param name="elementFactory">Factory for creating OpenXML elements / 用于创建OpenXML元素的工厂</param>
        /// <param name="revisionFactory">Factory for creating revision elements / 用于创建修订元素的工厂</param>
        /// <param name="idManager">Manager for generating unique IDs / 用于生成唯一ID的管理器</param>
        /// <param name="commentManager">Manager for comment operations / 用于评论操作的管理器</param>
        /// <param name="commentOrchestrator">Comment orchestrator for coordinating services / 用于协调服务的评论编排器</param>
        public CommentReplyStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager,
            ICommentOrchestrator commentOrchestrator
        ) : base(context, elementFactory, revisionFactory, idManager, commentManager)
        {
            _commentOrchestrator = commentOrchestrator ?? throw new ArgumentNullException(nameof(commentOrchestrator));
        }

        /// <summary>
        /// Initialize the CommentReplyStrategy with required dependencies (legacy constructor)
        /// 使用所需依赖项初始化CommentReplyStrategy（遗留构造函数）
        /// </summary>
        /// <param name="context">Lambda execution context / Lambda执行上下文</param>
        /// <param name="elementFactory">Factory for creating OpenXML elements / 用于创建OpenXML元素的工厂</param>
        /// <param name="revisionFactory">Factory for creating revision elements / 用于创建修订元素的工厂</param>
        /// <param name="idManager">Manager for generating unique IDs / 用于生成唯一ID的管理器</param>
        /// <param name="commentManager">Manager for comment operations / 用于评论操作的管理器</param>
        [Obsolete("Use constructor with CommentOrchestrator parameter for new architecture")]
        public CommentReplyStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager
        ) : base(context, elementFactory, revisionFactory, idManager, commentManager)
        {
            // Create orchestrator with required services for backward compatibility
            // 为向后兼容性创建具有所需服务的编排器
            var commentXmlService = new CommentXmlService(context, elementFactory, idManager);
            var documentManipulationService = new DocumentManipulationService(context, elementFactory);
            var commentReplyExtensionService = new CommentReplyExtensionService(context);
            var validationService = new ValidationService(context);

            _commentOrchestrator = new CommentOrchestrator(
                context,
                commentXmlService,
                documentManipulationService,
                commentReplyExtensionService,
                validationService,
                idManager,
                elementFactory);
        }

        /// <summary>
        /// Execute comment reply operation with relative positioning using new orchestrator architecture
        /// 使用新编排器架构执行相对定位的评论回复操作
        ///
        /// Note: For comment reply operations, the targetRun, relativeStart, and relativeEnd
        /// parameters are not used since we're operating on comments rather than document text.
        ///
        /// 注意：对于评论回复操作，不使用targetRun、relativeStart和relativeEnd参数，
        /// 因为我们操作的是评论而不是文档文本。
        /// </summary>
        /// <param name="doc">Word document / Word文档</param>
        /// <param name="operation">Operation details containing CommentId and reply text / 包含CommentId和回复文本的操作详细信息</param>
        /// <param name="targetRun">Target run (not used for comment replies) / 目标run（评论回复不使用）</param>
        /// <param name="relativeStart">Relative start position (not used for comment replies) / 相对起始位置（评论回复不使用）</param>
        /// <param name="relativeEnd">Relative end position (not used for comment replies) / 相对结束位置（评论回复不使用）</param>
        /// <returns>Returns 1 since comment replies add a comment reference Run, affecting subsequent Run positioning / 返回1，因为评论回复会添加一个评论引用Run，影响后续Run的定位</returns>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            // Validate operation parameters
            if (operation.Comment == null)
            {
                context.Logger.LogError("Comment is required for comment reply operations");
                return 0;
            }

            if (operation.Comment.ParentCommentId == null)
            {
                context.Logger.LogError("ParentCommentId is required for comment reply operations");
                return 0;
            }

            string parentCommentId = operation.Comment.ParentCommentId;
            context.Logger.LogInformation($"Applying comment reply operation for comment ID: {parentCommentId}");

            try
            {
                // Extract operation parameters
                // 提取操作参数
                string text = operation.Comment.Text ?? string.Empty;
                if (string.IsNullOrWhiteSpace(text))
                {
                    context.Logger.LogError("Reply text is required for comment reply operations");
                    return 0;
                }

                string author = operation.Revision?.Author ?? "Styling Agent";
                string date = operation.Revision?.Date.ToString("yyyy-MM-ddTHH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");

                // Initialize orchestrator with document parts
                // 使用文档部分初始化编排器
                _commentOrchestrator.Initialize(doc.MainDocumentPart!);

                // Use orchestrator to process comment reply
                // 使用编排器处理评论回复
                bool success = _commentOrchestrator.ProcessCommentReply(parentCommentId, author, date, text, true);

                if (success)
                {
                    context.Logger.LogInformation($"Successfully processed comment reply for parent ID {parentCommentId}");
                }
                else
                {
                    context.Logger.LogError($"Failed to process comment reply for parent ID {parentCommentId}");
                    throw new InvalidOperationException($"Failed to process comment reply for parent ID {parentCommentId}");
                }
                return 0;
            }
            catch (CommentNotFoundException)
            {
                // Re-throw CommentNotFoundException as-is
                // 原样重新抛出CommentNotFoundException
                throw;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply comment reply: {ex.Message}");
                throw;
            }
        }
    }
}
