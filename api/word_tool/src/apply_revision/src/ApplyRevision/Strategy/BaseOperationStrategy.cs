using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Component;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Base class for operation strategies providing common functionality
    /// 操作策略的基类，提供通用功能
    /// </summary>
    public abstract class BaseOperationStrategy : IOperationStrategy
    {
        protected readonly ILambdaContext context;
        protected readonly RunLocator runLocator;
        protected readonly IElementFactory elementFactory;
        protected readonly IRevisionElementFactory revisionFactory;
        protected readonly IIdManager idManager;
        protected readonly ICommentManager commentManager;

        protected BaseOperationStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager)
        {
            this.context = context ?? throw new ArgumentNullException(nameof(context));
            this.elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
            this.revisionFactory = revisionFactory ?? throw new ArgumentNullException(nameof(revisionFactory));
            this.idManager = idManager ?? throw new ArgumentNullException(nameof(idManager));
            this.commentManager = commentManager ?? throw new ArgumentNullException(nameof(commentManager));
            this.runLocator = RunLocator.GetInstance(context);
        }
        /// <summary>
        /// Execute the operation (backwards compatibility implementation)
        /// 执行操作（向后兼容实现）
        /// </summary>
        public virtual int Execute(WordprocessingDocument doc, Operation operation)
        {
            throw new NotImplementedException("Execute method must be implemented in derived classes.");
        }

        /// <summary>
        /// Creates a comment component from comment information
        /// 从注释信息创建注释组件
        /// </summary>
        protected CommentComponent? CreateCommentComponent(ApplyRevision.Model.Comment commentInfo)
        {
            if (commentInfo == null)
                return null;

            return commentManager.CreateCommentComponent(
                "Styling Agent", // Default author since Comment model doesn't have Author
                DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss"),
                commentInfo.Text
            );
        }

        /// <summary>
        /// Creates an InsertedRun element with proper revision attributes
        /// 创建具有适当修订属性的InsertedRun元素
        /// </summary>
        protected InsertedRun CreateInsertedRun(string text, Revision revision, Run originalRun)
        {
            var revisionId = idManager.GetNextRevisionId();

            var insertedRun = revisionFactory.CreateInsertedRunElement(
                text,
                revisionId.ToString(),
                revision.Author ?? "Styling Agent",
                revision.Date.ToString("yyyy-MM-ddTHH:mm:ss"),
                originalRun);

            context.Logger.LogDebug($"Created InsertedRun with ID: {revisionId}, Author: {insertedRun.Author}, Text: '{text}'");

            return insertedRun;
        }

        /// <summary>
        /// Creates a DeletedRun element with proper revision attributes
        /// 创建具有适当修订属性的DeletedRun元素
        /// </summary>
        protected DeletedRun CreateDeletedRun(string text, Revision revision, Run originalRun)
        {
            var revisionId = idManager.GetNextRevisionId();

            var deletedRun = revisionFactory.CreateDeletedRunElement(
                text,
                revisionId.ToString(),
                revision.Author ?? "Styling Agent",
                revision.Date.ToString("yyyy-MM-ddTHH:mm:ss"));

            context.Logger.LogDebug($"Created DeletedRun with ID: {revisionId}, Author: {deletedRun.Author}, Text: '{text}'");

            return deletedRun;
        }

        /// <summary>
        /// Adds comment to document if commentComponent is provided
        /// 如果提供了commentComponent，则将注释添加到文档中
        /// </summary>
        protected void AddCommentToDocument(CommentComponent? commentComponent)
        {
            if (commentComponent != null)
            {
                commentManager.AddComment(commentComponent.Comment);
                context.Logger.LogDebug($"Comment added to document with ID: {commentComponent.Comment.Id}");
            }
        }

        /// <summary>
        /// Gets the text content from a Text element, handling null cases
        /// 从Text元素获取文本内容，处理null情况
        /// </summary>
        protected string GetTextContent(Text? text)
        {
            return text?.Text ?? string.Empty;
        }

        /// <summary>
        /// Validates that the operation parameters are correct for the text range
        /// 验证操作参数对于文本范围是否正确
        /// </summary>
        protected bool ValidateOperationRange(Operation operation, string text, int relativeStart, int relativeEnd)
        {
            if (relativeStart < 0 || relativeEnd < relativeStart || relativeEnd > text.Length)
            {
                context.Logger.LogError($"Invalid operation range. Text length: {text.Length}, Range: [{relativeStart}, {relativeEnd}]");
                return false;
            }
            return true;
        }

        /// <summary>
        /// Locates the text position within a run using relative positioning
        /// 使用相对定位在run内定位文本位置
        /// </summary>
        protected (Text? text, int offset) LocateTextPosition(Run targetRun, int relativePosition)
        {
            return runLocator.LocatePosition(targetRun, relativePosition);
        }
    }
}