using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Operation strategy interface for different text operation types
    /// 操作策略接口，用于不同的文本操作类型
    /// </summary>
    public interface IOperationStrategy
    {
        int Execute(WordprocessingDocument doc, Operation operation);
    }
}