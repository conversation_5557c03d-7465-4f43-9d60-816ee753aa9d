using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for adding comments to Word documents
    ///
    /// This strategy handles the CommentAdd operation type, which creates new comments
    /// and attaches them to specific text ranges in the document. Unlike CommentReply,
    /// CommentAdd operations create standalone comments that are not replies to existing comments.
    ///
    /// Key Features:
    /// - Creates new standalone comments with proper XML structure
    /// - Inserts comment range markers and references in the document
    /// - Supports author and timestamp tracking
    /// - Integrates with CommentOrchestrator for consistent comment management
    /// - Returns zero offset change (no text modification)
    ///
    /// Usage Example:
    /// var operation = new Operation {
    ///     Op = OperationType.CommentAdd,
    ///     Target = new("segment1", 0, 1),
    ///     Text = "This is a new comment",
    ///     Revision = new Revision {
    ///         Author = "AI Assistant",
    ///         Date = DateTime.Now
    ///     }
    /// };
    ///
    /// Note: CommentAdd operations require Target and Range properties to specify
    /// the text location where the comment should be attached.
    /// </summary>
    public class CommentAddStrategy : BaseOperationStrategy
    {
        private readonly ICommentOrchestrator _commentOrchestrator;

        /// <summary>
        /// Initializes a new instance of CommentAddStrategy
        /// </summary>
        /// <param name="context">Lambda context for logging</param>
        /// <param name="elementFactory">Factory for creating OpenXML elements</param>
        /// <param name="revisionFactory">Factory for creating revision elements</param>
        /// <param name="idManager">Manager for generating unique IDs</param>
        /// <param name="commentManager">Manager for comment operations</param>
        public CommentAddStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager
        ) : base(context, elementFactory, revisionFactory, idManager, commentManager)
        {
            // Create orchestrator with required services for comment management
            var commentXmlService = new CommentXmlService(context, elementFactory, idManager);
            var documentManipulationService = new DocumentManipulationService(context, elementFactory);
            var commentReplyExtensionService = new CommentReplyExtensionService(context);
            var validationService = new ValidationService(context);

            _commentOrchestrator = new CommentOrchestrator(
                context,
                commentXmlService,
                documentManipulationService,
                commentReplyExtensionService,
                validationService,
                idManager,
                elementFactory);
        }

        /// <summary>
        /// Execute comment add operation with relative position
        /// </summary>
        /// <param name="doc">Word document</param>
        /// <param name="operation">Operation details</param>
        /// <param name="targetRun">Target run containing the text</param>
        /// <param name="relativeStart">Relative start position within the run</param>
        /// <param name="relativeEnd">Relative end position within the run</param>
        /// <returns>Actual offset change (always 0 for comment operations)</returns>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            context.Logger.LogInformation($"CommentAdd operation has {operation.Target.Count} SegIds provided");

            RangeRunResult? rangeRunResult = null;
            if (operation.Target.Count == 1)
            {
                var segment = operation.Target.FirstOrDefault();
                // Use the new method to get precise start and end run information
                rangeRunResult = runLocator.GetRangeRunBySegment(doc, segment!);

                if (rangeRunResult == null)
                {
                    context.Logger.LogWarning($"Could not analyze range for SegId: {segment!.SegId}");
                }

                context.Logger.LogInformation($"Single SegId provided: {operation.Target.FirstOrDefault()}");
            }
            else
            {
                rangeRunResult = runLocator.GetRangeRunByOperation(doc, operation);
            }

            if (rangeRunResult == null)
            {
                context.Logger.LogError("Failed to determine range for CommentAdd operation");
                return 0; // No change if range cannot be determined
            }

            try
            {
                // Validate operation parameters
                if (string.IsNullOrEmpty(operation.Comment?.Text))
                {
                    throw new ArgumentException("Comment text cannot be null or empty", nameof(operation));
                }

                if (operation.Target.Count == 0)
                {
                    throw new ArgumentException("At least one SegId must be provided", nameof(operation));
                }

                string commentText = operation.Comment.Text;
                string author = operation.Revision?.Author ?? "Styling Agent";
                string date = operation.Revision?.Date.ToString("yyyy-MM-ddTHH:mm:ss") ?? DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss");

                // Initialize orchestrator with document parts
                _commentOrchestrator.Initialize(doc.MainDocumentPart!);

                // Use unified range processing for both single and multiple SegIds
                // For single SegId, firstRun == lastRun, so the logic works the same
                bool success = _commentOrchestrator.ProcessCommentAdd(rangeRunResult, author, date, commentText);

                if (!success)
                {
                    context.Logger.LogWarning($"Failed to process comment add for SegIds");
                    return 0;
                }

                // Save changes through orchestrator
                bool saveSuccess = _commentOrchestrator.Save();

                if (saveSuccess)
                {
                    context.Logger.LogInformation($"Successfully added comment to {operation.Target.Count} SegIds");
                }
                else
                {
                    context.Logger.LogWarning($"Comment created but save operation failed");
                }
                return 0;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to execute CommentAdd operation: {ex.Message}");
                throw;
            }
        }
    }
}
