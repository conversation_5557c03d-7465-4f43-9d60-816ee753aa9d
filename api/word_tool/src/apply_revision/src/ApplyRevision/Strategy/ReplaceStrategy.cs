using Amazon.Lambda.Core;
using ApplyRevision.Component;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for handling replace operations
    /// 处理替换操作的策略
    /// </summary>
    public class ReplaceStrategy(
        ILambdaContext context,
        IElementFactory elementFactory,
        IRevisionElementFactory revisionFactory,
        IIdManager idManager,
        ICommentManager commentManager
        ) : BaseOperationStrategy(context, elementFactory, revisionFactory, idManager, commentManager)
    {

        /// <summary>
        /// Execute replace operation with relative positioning
        /// 使用相对定位执行替换操作
        /// </summary>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            // 通过这个方法，我们可以操作实际需要操作的 run 以及相对该 run 的偏移量
            var targetRun = runLocator.FindRunWithTextForOperation(doc, operation, out int relativeStart, out int relativeEnd);
            if (targetRun == null)
            {
                context.Logger.LogError($"Cannot find Run with text for SegId: {operation.Target.FirstOrDefault()}");
                return 0;
            }

            context.Logger.LogInformation($"Located target Run with text for SegId: {operation.Target.FirstOrDefault()}, relative position [{relativeStart}, {relativeEnd}]");
            context.Logger.LogInformation($"Applying replace from relative position {relativeStart} to {relativeEnd} with '{operation.Text}'");

            try
            {
                // Locate position within the Run for replace operation using relative position
                var (text, offset) = LocateTextPosition(targetRun, relativeStart);
                if (text == null)
                {
                    context.Logger.LogError("Cannot locate text position within Run for replace operation");
                    return 0;
                }

                CommentComponent? commentComponent = null;
                if (operation.Comment != null)
                {
                    commentComponent = CreateCommentComponent(operation.Comment);
                }

                // Execute the replace operation
                if (commentComponent != null)
                {
                    ApplyReplaceWithComment(operation, targetRun, text, offset, relativeStart, relativeEnd, commentComponent);
                }
                else
                {
                    ApplyReplace(operation, targetRun, text, offset, relativeStart, relativeEnd);
                }

                var replacedLength = relativeEnd - relativeStart;
                var newLength = operation.Text?.Length ?? 0;
                var textOffset = newLength - replacedLength;

                // If operation includes a comment, we also add a comment reference Run
                // which affects subsequent Run positioning
                // 如果操作包含评论，我们还会添加一个评论引用Run，这会影响后续Run的定位
                var commentOffset = commentComponent != null ? 1 : 0;
                var totalOffset = textOffset + commentOffset;

                context.Logger.LogInformation($"Replace operation completed, text offset: {textOffset:+#;-#;0}, comment offset: +{commentOffset}, total offset: {totalOffset:+#;-#;0}");
                return totalOffset;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply replace: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Core replace logic shared between standard and comment-based replacements
        /// 标准替换和带注释替换共享的核心逻辑
        /// </summary>
        private (InsertedRun insertedRun, DeletedRun deletedRun, OpenXmlElement lastInsertedElement) ExecuteReplaceCore(
            Operation operation, Run targetRun, Text text, int offset,
            int relativeStart, int relativeEnd, Action<OpenXmlElement, InsertedRun, DeletedRun>? insertElementsHandler = null)
        {
            context.Logger.LogInformation($"Executing core replace logic from position {relativeStart} to {relativeEnd} with '{operation.Text}'");

            try
            {
                // Calculate the range to replace
                int replaceLength = relativeEnd - relativeStart;
                string originalText = text.Text;

                if (offset + replaceLength > originalText.Length)
                {
                    context.Logger.LogError($"Replace range exceeds text length. Text: '{originalText}', Offset: {offset}, Length: {replaceLength}");
                    throw new InvalidOperationException($"Replace range exceeds text length. Text: '{originalText}', Offset: {offset}, Length: {replaceLength}");
                }

                // Extract the text parts
                string beforeText = originalText[..offset];
                string replacedText = originalText.Substring(offset, replaceLength);
                string afterText = originalText[(offset + replaceLength)..];

                // Create DeletedRun for the replaced text and InsertedRun for the new text
                // Use the same revision ID and exact timestamp to help Word associate them as a replace operation
                var revision = operation.Revision ?? throw new InvalidOperationException("Revision is required");
                var revisionId = idManager.GetNextRevisionId();
                var timestamp = revision.Date.ToString("yyyy-MM-ddTHH:mm:ss");
                var author = revision.Author ?? "Styling Agent";

                // Create DeletedRun with specific revision ID and inherited properties
                var deletedRun = revisionFactory.CreateDeletedRunElement(
                    replacedText,
                    revisionId.ToString(),
                    author,
                    timestamp,
                    targetRun);

                // Create InsertedRun with the SAME revision ID, timestamp, and properties
                var insertedRun = revisionFactory.CreateInsertedRunElement(
                    operation.Text,
                    revisionId.ToString(),
                    author,
                    timestamp,
                    targetRun);

                // Update original text to before portion
                text.Text = beforeText;
                text.Space = SpaceProcessingModeValues.Preserve;

                // Check if targetRun's parent is insRun or delRun
                // If parent is insRun or delRun, elements need to be inserted after the parent node
                // Otherwise, insert directly after targetRun
                OpenXmlElement insertionPoint = targetRun;
                var parent = targetRun.Parent;

                // Check if the parent is an insertion or deletion run
                if (parent is InsertedRun || parent is DeletedRun)
                {
                    insertionPoint = parent;
                    context.Logger.LogInformation($"Parent is revision element ({parent.GetType().Name}), inserting after parent");

                    // Handle multiple runs within the parent revision element
                    // If parent contains multiple runs, extract all runs except the first one
                    // and wrap each in its own parent element to avoid replace operation ordering issues
                    SeparateMultipleRunsInRevisionParent(parent, targetRun);

                    if (parent is InsertedRun parentInsertedRun)
                    {
                        WrapDeletedRunWithParentInsertedRun(deletedRun, parentInsertedRun);
                    }
                }
                else
                {
                    context.Logger.LogInformation("Parent is not revision element, inserting after targetRun");
                }

                // Use custom handler for element insertion if provided, otherwise use default logic
                OpenXmlElement lastInsertedElement;
                if (insertElementsHandler != null)
                {
                    insertElementsHandler(insertionPoint, insertedRun, deletedRun);
                    // For custom handlers, we need to find the actual last element by scanning from insertion point
                    lastInsertedElement = FindLastInsertedElement(insertionPoint);
                }
                else
                {
                    // Default insertion: insertedRun then deletedRun
                    insertionPoint.InsertAfterSelf(insertedRun);
                    insertedRun.InsertAfterSelf(deletedRun);
                    lastInsertedElement = deletedRun;
                }

                // Handle after text creation and return required elements
                if (!string.IsNullOrEmpty(afterText))
                {
                    CreateAfterRun(targetRun, afterText, lastInsertedElement, parent);
                }

                context.Logger.LogInformation($"Successfully executed core replace logic with revision ID {revisionId}");
                return (insertedRun, deletedRun, lastInsertedElement);
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to execute core replace logic: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Finds the last element inserted after the insertion point by scanning siblings
        /// 通过扫描兄弟节点找到插入点后的最后一个插入元素
        /// </summary>
        private OpenXmlElement FindLastInsertedElement(OpenXmlElement insertionPoint)
        {
            var current = insertionPoint;
            var knownCommentElements = new[] { "commentRangeStart", "commentRangeEnd", "commentReference" };

            // Keep moving to the next sibling until we find one that's not part of our insertion
            while (current.NextSibling() != null)
            {
                var next = current.NextSibling();
                // Continue if it's a revision element or comment element
                if (next is InsertedRun || next is DeletedRun ||
                    knownCommentElements.Contains(next.LocalName))
                {
                    current = next;
                }
                else
                {
                    break;
                }
            }

            return current;
        }

        /// <summary>
        /// Creates and inserts the after run element for remaining text
        /// 为剩余文本创建并插入后续运行元素
        /// </summary>
        private void CreateAfterRun(Run targetRun, string afterText, OpenXmlElement lastElement, OpenXmlElement? parent)
        {
            var afterRun = elementFactory.CloneElement(targetRun, deepClone: true);
            afterRun.RemoveAllChildren<Text>();
            afterRun.Append(new Text(afterText) { Space = SpaceProcessingModeValues.Preserve });

            if (parent is InsertedRun || parent is DeletedRun)
            {
                var parentClone = elementFactory.CloneElement(parent, deepClone: true);
                parentClone.RemoveAllChildren();
                parentClone.Append(afterRun);
                lastElement.InsertAfterSelf(parentClone);
            }
            else
            {
                lastElement.InsertAfterSelf(afterRun);
            }
        }

        /// <summary>
        /// Applies a replace operation with comment integration
        /// 应用带注释集成的替换操作
        /// </summary>
        private void ApplyReplaceWithComment(Operation operation, Run targetRun, Text text, int offset,
            int relativeStart, int relativeEnd, CommentComponent commentComponent)
        {
            context.Logger.LogInformation($"Applying replace with comment from position {relativeStart} to {relativeEnd} with '{operation.Text}'");

            try
            {
                // Use core replace logic with custom comment insertion handler
                var (insertedRun, deletedRun, lastElement) = ExecuteReplaceCore(operation, targetRun, text, offset, relativeStart, relativeEnd,
                    (insertionPoint, insertedRun, deletedRun) =>
                    {
                        // Insert comment range start after the insertion point
                        insertionPoint.InsertAfterSelf(commentComponent.RangeStart);

                        // Insert the inserted run after range start (new order: ins then del)
                        commentComponent.RangeStart.InsertAfterSelf(insertedRun);

                        // Insert the deleted run after inserted run
                        insertedRun.InsertAfterSelf(deletedRun);

                        // Insert comment range end after the deleted run
                        deletedRun.InsertAfterSelf(commentComponent.RangeEnd);

                        // Insert comment reference after the range end
                        commentComponent.RangeEnd.InsertAfterSelf(commentComponent.Reference);
                    });

                // Add comment to document
                AddCommentToDocument(commentComponent);

                context.Logger.LogInformation($"Successfully applied replace operation with comment");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply replace with comment: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies a standard replace operation without comments
        /// </summary>
        /// <exception cref="Exception">Throws if the replace operation fails</exception>
        /// <remarks>
        /// This method uses the core replace logic without any custom comment handling.
        /// It is intended for standard text replacements where no comments are involved.
        /// </remarks>
        /// <param name="operation">The replace operation details</param>
        /// <param name="targetRun">The target Run element where the replace occurs</param>
        /// <param name="text">The Text element containing the text to be replaced</param>
        /// <param name="offset">The offset within the Text element where the replace starts</param>
        /// <param name="relativeStart">The relative start position within the Run for the replace</param>
        /// <param name="relativeEnd">The relative end position within the Run for the replace</param>
        private void ApplyReplace(Operation operation, Run targetRun, Text text, int offset,
            int relativeStart, int relativeEnd)
        {
            context.Logger.LogInformation($"Applying standard replace from position {relativeStart} to {relativeEnd} with '{operation.Text}'");

            try
            {
                // Use core replace logic with default insertion behavior (no custom handler)
                var (insertedRun, deletedRun, lastElement) = ExecuteReplaceCore(operation, targetRun, text, offset, relativeStart, relativeEnd);

                context.Logger.LogInformation($"Successfully applied standard replace operation");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply standard replace: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Separates elements after the target run within a revision parent to avoid replace operation ordering issues.
        /// </summary>
        /// <remarks>
        /// When a revision element (InsertedRun or DeletedRun) contains multiple child elements including runs,
        /// this method splits the revision parent at the target run boundary, moving all elements AFTER the target run
        /// to a new revision parent. This ensures that replace operations are inserted in the correct position.
        ///
        /// Problem Context:
        /// When performing replace operations on runs within revision elements that contain multiple elements,
        /// the order of operations can cause unexpected results. For example:
        ///
        /// Original structure:
        /// <w:ins>
        ///   <w:r><w:t> (the</w:t></w:r>
        ///   <w:commentRangeStart w:id="40" />
        ///   <w:r><w:t>Bill</w:t></w:r>  <- target run for replacement
        ///   <w:commentRangeEnd w:id="40" />
        ///   <w:r><w:t> more text</w:t></w:r>
        /// </w:ins>
        ///
        /// After replacement without separation, the new elements are inserted after the entire <w:ins>,
        /// which can cause the replacement to appear after "more text" instead of after "Bill".
        ///
        /// Solution:
        /// This method transforms the structure by splitting AFTER the target run:
        /// <w:ins>
        ///   <w:r><w:t> (the</w:t></w:r>
        ///   <w:commentRangeStart w:id="40" />
        ///   <w:r><w:t>Bill</w:t></w:r>   <- target run remains in original parent
        /// </w:ins>
        /// <!-- Replace operations will be inserted here -->
        /// <w:ins>
        ///   <w:commentRangeEnd w:id="40" />
        ///   <w:r><w:t> more text</w:t></w:r>  <- elements after target run moved to new parent
        /// </w:ins>
        ///
        /// This ensures replace operations are inserted immediately after the target run's revision parent,
        /// maintaining proper document flow and element relationships.
        /// </remarks>
        /// <param name="revisionParent">The revision parent element (InsertedRun or DeletedRun)</param>
        /// <param name="targetRun">The specific run being targeted for replacement</param>
        private void SeparateMultipleRunsInRevisionParent(OpenXmlElement revisionParent, Run targetRun)
        {
            if (revisionParent == null || targetRun == null)
            {
                context.Logger.LogWarning("Cannot separate runs: revision parent or target run is null");
                return;
            }

            // Get all child elements from the revision parent
            var allChildren = revisionParent.Elements().ToList();
            var allRuns = revisionParent.Elements<Run>().ToList();

            if (allRuns.Count <= 1)
            {
                context.Logger.LogDebug("Revision parent contains only one or no runs, no separation needed");
                return;
            }

            context.Logger.LogInformation($"Separating revision parent with {allChildren.Count} total elements and {allRuns.Count} runs");

            // Find the index of the target run in the children list
            int targetRunIndex = allChildren.IndexOf(targetRun);
            if (targetRunIndex == -1)
            {
                context.Logger.LogWarning("Target run not found in revision parent children, skipping separation");
                return;
            }

            // Split the children: elements AFTER target run go to new parent
            // targetRunIndex + 1 ensures target run stays in original parent
            var elementsAfterTargetRun = allChildren.Skip(targetRunIndex + 1).ToList();

            if (elementsAfterTargetRun.Count == 0)
            {
                context.Logger.LogDebug("No elements to separate after target run");
                return;
            }

            // Create new revision parent and move elements after target run
            SeparateElementsIntoNewRevisionParent(revisionParent, elementsAfterTargetRun);

            context.Logger.LogInformation($"Successfully separated {elementsAfterTargetRun.Count} elements after target run");
        }

        /// <summary>
        /// Separates a group of elements from their revision parent into a new revision parent element.
        /// Creates a new revision parent with the same attributes and properties as the original,
        /// then moves the specified elements into the new parent while preserving their order and relationships.
        ///
        /// This method ensures that all related elements (runs, comments, bookmarks, etc.) are moved together
        /// to maintain the integrity of the document structure and element relationships.
        /// </summary>
        /// <param name="originalParent">The original revision parent element</param>
        /// <param name="elementsToSeparate">The list of elements to be moved to a new parent</param>
        private void SeparateElementsIntoNewRevisionParent(OpenXmlElement originalParent, List<OpenXmlElement> elementsToSeparate)
        {
            try
            {
                // Create a new revision parent with the same type and attributes
                OpenXmlElement newParent = CreateRevisionParentClone(originalParent);

                // Remove elements from the original parent and add them to the new parent
                // Process in order to maintain element relationships
                foreach (var element in elementsToSeparate)
                {
                    element.Remove();
                    newParent.AppendChild(element);
                }

                // Insert the new parent after the original parent
                originalParent.InsertAfterSelf(newParent);

                context.Logger.LogDebug($"Successfully separated {elementsToSeparate.Count} elements into new {newParent.GetType().Name} parent");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to separate elements into new revision parent: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Creates a clone of a revision parent element (InsertedRun or DeletedRun) with the same attributes
        /// but without any child elements. This is used to create new parent containers for separated runs.
        /// </summary>
        /// <param name="originalParent">The original revision parent to clone</param>
        /// <returns>A new revision parent element with the same attributes</returns>
        private OpenXmlElement CreateRevisionParentClone(OpenXmlElement originalParent)
        {
            return originalParent switch
            {
                InsertedRun insertedRun => new InsertedRun
                {
                    Id = insertedRun.Id,
                    Author = insertedRun.Author,
                    Date = insertedRun.Date
                },
                DeletedRun deletedRun => new DeletedRun
                {
                    Id = deletedRun.Id,
                    Author = deletedRun.Author,
                    Date = deletedRun.Date
                },
                _ => throw new InvalidOperationException($"Unsupported revision parent type: {originalParent.GetType().Name}")
            };
        }

        /// <summary>
        /// Reorganizes the structure when parent is DeletedRun by wrapping child elements
        /// of deletedRun within an insertedRun structure inside the parent DeletedRun.
        /// This transforms:
        /// <w:del>
        ///   <w:r><w:delText>content</w:delText></w:r>
        /// </w:del>
        /// Into:
        /// <w:del>
        ///   <w:ins>
        ///     <w:r><w:delText>content</w:delText></w:r>
        ///   </w:ins>
        /// </w:del>
        /// </summary>
        /// <param name="parentInsertedRun">The parent InsertedRun element</param>
        /// <param name="run">The DeletedRun element to be reorganized</param>
        private void WrapDeletedRunWithParentInsertedRun(OpenXmlElement run, InsertedRun parentInsertedRun)
        {
            try
            {
                context.Logger.LogInformation("Reorganizing DeletedRun structure with InsertedRun wrapper");

                var originalInsertedRun = elementFactory.CloneElement(parentInsertedRun, deepClone: true);
                originalInsertedRun.RemoveAllChildren();

                // Get all child elements from the current deletedRun that need to be wrapped
                var childElementsToWrap = run.Elements().ToList();

                if (childElementsToWrap.Count == 0)
                {
                    context.Logger.LogWarning("No child elements found in deletedRun to reorganize");
                    return;
                }

                // Clear the deletedRun's children since we'll be reorganizing them
                run.RemoveAllChildren();

                // Move all child elements from deletedRun into insertedRun
                foreach (var childElement in childElementsToWrap)
                {
                    // Clone the element to avoid issues with moving between parents
                    var clonedElement = childElement.CloneNode(true);
                    originalInsertedRun.AppendChild(clonedElement);
                }

                // Now wrap the original inserted run within the deleted run
                run.AppendChild(originalInsertedRun);

                context.Logger.LogInformation("Successfully reorganized DeletedRun structure with InsertedRun wrapper");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to reorganize DeletedRun structure: {ex.Message}");
                throw;
            }
        }
    }
}

