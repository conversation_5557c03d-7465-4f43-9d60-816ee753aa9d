using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Component;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Helper;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for handling insert operations
    /// 
    /// Overview:
    /// This strategy handles text insertion operations in Word documents while maintaining
    /// proper revision tracking. It intelligently determines the best insertion method
    /// based on the target position within existing text runs.
    /// 
    /// Key Features:
    /// - Preserves original text formatting (bold, italic, font properties, etc.)
    /// - Maintains proper revision tracking with author and timestamp
    /// - Handles three insertion scenarios: beginning, middle, and end of text
    /// - Creates proper OpenXML InsertedRun elements for revision workflow
    /// - Supports comment integration
    /// 
    /// Insertion Strategies Demonstration:
    /// 
    /// 1. Beginning Insertion (offset = 0):
    ///    ┌─────────────────┐         ┌─────────────┐  ┌─────────────────┐
    ///    │ Original:       │   →     │ Inserted:   │  │ Original:       │
    ///    │ "Hello World"   │         │ "New "      │  │ "Hello World"   │
    ///    └─────────────────┘         └─────────────┘  └─────────────────┘
    ///    Result: "New Hello World"
    ///    
    ///    XML: <w:r><w:t>Hello World</w:t></w:r>
    ///     →   <w:ins><w:r><w:t>New </w:t></w:r></w:ins><w:r><w:t>Hello World</w:t></w:r>
    /// 
    /// 2. End Insertion (offset = text.Length):
    ///    ┌─────────────────┐         ┌─────────────────┐  ┌─────────────┐
    ///    │ Original:       │   →     │ Original:       │  │ Inserted:   │
    ///    │ "Hello World"   │         │ "Hello World"   │  │ "!"         │
    ///    └─────────────────┘         └─────────────────┘  └─────────────┘
    ///    Result: "Hello World!"
    ///    
    ///    XML: <w:r><w:t>Hello World</w:t></w:r>
    ///     →   <w:r><w:t>Hello World</w:t></w:r><w:ins><w:r><w:t>!</w:t></w:r></w:ins>
    /// 
    /// 3. Middle Insertion (0 < offset < text.Length):
    ///    ┌─────────────────┐         ┌─────────┐  ┌─────────────┐  ┌─────────┐
    ///    │ Original:       │   →     │ Before: │  │ Inserted:   │  │ After:  │
    ///    │ "Hello World"   │         │ "Hello "│  │ "Beautiful "│  │ "World" │
    ///    └─────────────────┘         └─────────┘  └─────────────┘  └─────────┘
    ///    Result: "Hello Beautiful World"
    ///    
    ///    XML: <w:r><w:t>Hello World</w:t></w:r>
    ///     →   <w:r><w:t>Hello </w:t></w:r><w:ins><w:r><w:t>Beautiful </w:t></w:r></w:ins><w:r><w:t>World</w:t></w:r>
    /// 
    /// Revision Tracking XML Structure:
    /// All inserted text is wrapped in InsertedRun elements with metadata:
    /// 
    /// <w:ins w:id="uniqueId" w:author="Author Name" w:date="2024-01-01T10:00:00">
    ///   <w:r>
    ///     <w:rPr>
    ///       <!-- Inherited formatting properties -->
    ///       <w:b/>  <!-- Bold if original was bold -->
    ///       <w:i/>  <!-- Italic if original was italic -->
    ///       <w:sz w:val="24"/>  <!-- Font size inherited -->
    ///       <w:color w:val="FF0000"/>  <!-- Color inherited -->
    ///     </w:rPr>
    ///     <w:t xml:space="preserve">Inserted Text</w:t>
    ///   </w:r>
    /// </w:ins>
    /// 
    /// Key XML Benefits:
    /// - Preserves original formatting (bold, italic, font size, etc.)
    /// - Tracks who made the change and when
    /// - Allows Word to show/hide revisions
    /// - Enables accept/reject revision workflow
    /// - Maintains document structure integrity
    /// - Supports complex formatting inheritance
    /// 
    /// This enables Word's revision tracking features:
    /// - Show/hide revisions
    /// - Accept/reject changes
    /// - Track multiple authors and edit sessions
    /// - Maintain document history
    /// 
    /// Usage Example:
    /// var strategy = new InsertStrategy(context, elementFactory, revisionFactory, idManager);
    /// var operation = new Operation {
    ///     Target = new("segment1"),
    ///     Range = new Range { Start = 6 },
    ///     Text = "Beautiful ",
    ///     Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
    /// };
    /// strategy.Execute(document, operation);
    /// </summary>
    public class InsertStrategy : BaseOperationStrategy
    {
        public InsertStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager
        ) : base(context, elementFactory, revisionFactory, idManager, commentManager)
        {
        }

        /// <summary>
        /// Execute insert operation with relative positioning
        /// 使用相对定位执行插入操作
        /// </summary>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            // 通过这个方法，我们可以操作实际需要操作的 run 以及相对该 run 的偏移量
            var targetRun = runLocator.FindRunWithTextForOperation(doc, operation, out int relativeStart, out int relativeEnd);
            if (targetRun == null)
            {
                context.Logger.LogError($"Cannot find Run with text for SegId: {operation.Target.FirstOrDefault()}");
                return 0;
            }

            context.Logger.LogInformation($"Located target Run with text for SegId: {operation.Target.FirstOrDefault()}, relative position [{relativeStart}, {relativeEnd}]");

            // 使用策略模式执行操作
            context.Logger.LogInformation($"Applying insert: '{operation.Text}' at relative position {relativeStart}");

            try
            {
                // Locate position within the Run using relative position
                var (text, offset) = LocateTextPosition(targetRun, relativeStart);
                if (text == null)
                {
                    context.Logger.LogError("Cannot locate text position within Run");
                    return 0;
                }

                CommentComponent? commentComponent = null;

                // Create comment component if needed
                if (operation.Comment != null)
                {
                    commentComponent = CreateCommentComponent(operation.Comment);
                }

                // Execute the insert operation
                if (commentComponent != null)
                {
                    ApplyInsertWithComment(operation, targetRun, text, offset, commentComponent);
                }
                else
                {
                    ApplyStandardInsert(targetRun, text, offset, operation);
                }

                var insertedLength = operation.Text?.Length ?? 0;
                context.Logger.LogInformation($"Insert operation completed, text offset: +{insertedLength}");
                return insertedLength;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply insert: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies an insert operation with comment integration
        /// 应用带注释集成的插入操作
        /// </summary>
        private void ApplyInsertWithComment(Operation operation, Run targetRun, Text text, int offset, CommentComponent commentComponent)
        {
            context.Logger.LogInformation($"Applying insert with comment: '{operation.Text}'");

            try
            {
                // Create InsertedRun for the new text
                var insertedRun = CreateInsertedRun(operation.Text, operation.Revision ?? throw new InvalidOperationException("Revision is required"), targetRun);
                context.Logger.LogDebug($"Created InsertedRun with revision ID: {insertedRun.Id}");

                // Split the text and insert the revision with comment structure
                SplitTextAndInsertWithComment(targetRun, text, offset, insertedRun, commentComponent);

                // Add comment to document
                AddCommentToDocument(commentComponent);

                context.Logger.LogInformation("Successfully applied insert operation with comment");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Failed to apply insert with comment: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies a standard insert operation without comments
        /// 应用标准插入操作（不带注释）
        /// </summary>
        private void ApplyStandardInsert(Run targetRun, Text text, int offset, Operation operation)
        {
            context.Logger.LogInformation("Applying standard insert");

            // Create InsertedRun for the new text
            var insertedRun = CreateInsertedRun(operation.Text, operation.Revision ?? throw new InvalidOperationException("Revision is required"), targetRun);

            // Split the text and insert the revision
            SplitTextAndInsert(targetRun, text, offset, insertedRun);
        }

        /// <summary>
        /// Splits text and inserts revision with comment markers
        /// 分割文本并插入带注释标记的修订
        /// </summary>
        private void SplitTextAndInsertWithComment(Run targetRun, Text text, int offset,
            InsertedRun insertedRun, CommentComponent commentComponent)
        {
            context.Logger.LogDebug($"Splitting text and inserting with comment at offset {offset}");

            // Save original text
            string originalText = text.Text;

            // Split text
            string beforeText = originalText.Substring(0, offset);
            string afterText = originalText.Substring(offset);

            // Update original text to before portion
            text.Text = beforeText;

            // Insert comment range start after the current run
            targetRun.InsertAfterSelf(commentComponent.RangeStart);

            // Insert the revision (containing the new text) after range start
            commentComponent.RangeStart.InsertAfterSelf(insertedRun);

            // Insert comment range end after the revision
            insertedRun.InsertAfterSelf(commentComponent.RangeEnd);

            // Insert comment reference after the range end
            commentComponent.RangeEnd.InsertAfterSelf(commentComponent.Reference);

            // Create after run if there's remaining text
            if (!string.IsNullOrEmpty(afterText))
            {
                var afterRun = elementFactory.CloneElement(targetRun, deepClone: true);
                afterRun.RemoveAllChildren<Text>();

                var afterTextElement = new Text(afterText);
                // Only set xml:space="preserve" if the text actually needs it
                if (RequiresSpacePreservation(afterText))
                {
                    afterTextElement.Space = SpaceProcessingModeValues.Preserve;
                }
                afterRun.Append(afterTextElement);

                // Insert after run at the end
                commentComponent.Reference.InsertAfterSelf(afterRun);
            }

            context.Logger.LogDebug($"Successfully inserted revision with comment structure");
        }

        /// <summary>
        /// Splits text and inserts revision without comments
        /// 分割文本并插入修订（不带注释）
        /// </summary>
        private void SplitTextAndInsert(Run targetRun, Text text, int offset, InsertedRun insertedRun)
        {
            // Save original text
            string originalText = text.Text;

            // Split text
            string beforeText = originalText[..offset];
            string afterText = originalText[offset..];

            // Update original text
            text.Text = beforeText;

            // Insert the revision
            targetRun.InsertAfterSelf(insertedRun);

            // Set xml:space="preserve" for targetRun's text element if needed
            if (RequiresSpacePreservation(beforeText))
            {
                var targetTextElement = targetRun.GetFirstChild<Text>();
                if (targetTextElement != null)
                {
                    targetTextElement.Space = SpaceProcessingModeValues.Preserve;
                }
            }

            // Create after run if needed
            if (!string.IsNullOrEmpty(afterText))
            {
                var afterRun = elementFactory.CloneElement(targetRun, deepClone: true);
                afterRun.RemoveAllChildren<Text>();
                afterRun.Append(new Text(afterText) { Space = SpaceProcessingModeValues.Preserve });

                insertedRun.InsertAfterSelf(afterRun);
            }
        }

        /// <summary>
        /// Check if text requires xml:space="preserve" attribute
        /// 检查文本是否需要 xml:space="preserve" 属性
        /// </summary>
        /// <param name="text">Text to check</param>
        /// <returns>True if space preservation is needed</returns>
        private static bool RequiresSpacePreservation(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // Check for leading spaces, trailing spaces, multiple consecutive spaces, or tabs
            return text.StartsWith(' ') ||
                   text.EndsWith(' ') ||
                   text.Contains("  ") ||  // Multiple consecutive spaces
                   text.Contains('\t');    // Tab characters
        }
    }
}