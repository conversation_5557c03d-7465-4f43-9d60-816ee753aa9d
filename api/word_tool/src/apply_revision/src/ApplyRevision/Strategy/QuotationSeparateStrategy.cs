using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;

namespace ApplyRevision.Strategy
{
    /// <summary>
    /// Strategy for separating quotation text from the main paragraph.
    /// Handles the complex logic of extracting quotation text and formatting it
    /// as a separate paragraph while preserving formatting, styles, and document structure integrity.
    /// Based on copyeditor requirements for quotation handling and text formatting.
    /// </summary>
    public class QuotationSeparateStrategy(
        ILambdaContext context,
        IElementFactory elementFactory,
        IRevisionElementFactory revisionFactory,
        IIdManager idManager,
        ICommentManager commentManager
        ) : BaseOperationStrategy(context, elementFactory, revisionFactory, idManager, commentManager)
    {


        /// <summary>
        /// Executes the quotation separation operation.
        /// Separates quotation text from the main paragraph, creating a new paragraph for the quotation
        /// while preserving formatting and document structure integrity.
        /// </summary>
        /// <param name="doc">The Word document to modify</param>
        /// <param name="operation">The operation containing quotation separation parameters</param>
        /// <returns>Number of elements processed (typically 2 for the two resulting paragraphs)</returns>
        public override int Execute(WordprocessingDocument doc, Operation operation)
        {
            context.Logger.LogInformation($"Starting quotation separation operation for operation ID: {operation.Id}");

            // Validate operation parameters
            if (operation.Target?.FirstOrDefault == null)
            {
                throw new ArgumentException("Target segment cannot be null for paragraph split operation", nameof(operation));
            }

            var segment = operation.Target.FirstOrDefault;
            if (segment?.Range == null)
            {
                throw new ArgumentException("Range must be specified for paragraph split operation", nameof(operation));
            }

            if (string.IsNullOrWhiteSpace(segment.SegId))
            {
                throw new ArgumentException("SegId cannot be null or empty for paragraph split operation", nameof(operation));
            }

            var segmentRelativePosition = segment.Range.Start;
            context.Logger.LogInformation($"Splitting paragraph at SegId: {segment.SegId}, Segment Relative Position: {segmentRelativePosition}");

            try
            {
                // Locate the target paragraph
                var targetParagraph = FindTargetParagraph(doc, segment.SegId);
                if (targetParagraph == null)
                {
                    context.Logger.LogWarning($"Target paragraph not found for SegId: {segment.SegId}");
                    return 0;
                }

                // Convert segment-relative position to paragraph-absolute position
                var paragraphAbsolutePosition = ConvertToParagraphAbsolutePosition(doc, segment.SegId, segmentRelativePosition);
                context.Logger.LogInformation($"Converted to paragraph absolute position: {paragraphAbsolutePosition}");

                // Execute the split operation
                var result = SplitParagraphAtPosition(targetParagraph, paragraphAbsolutePosition);

                context.Logger.LogInformation($"Paragraph split completed successfully. Created {result} paragraphs.");
                return result;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error during paragraph split operation: {ex.Message}");
                throw new InvalidOperationException($"Failed to split paragraph: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Finds the target paragraph based on the segment ID.
        ///
        /// ASCII Diagram - SegId Structure and Paragraph Location:
        /// ┌─────────────────────────────────────────────────────────────┐
        /// │                    Word Document                            │
        /// │  ┌─────────────────────────────────────────────────────┐    │
        /// │  │                    Body                             │    │
        /// │  │  ┌─────────────────────────────────────────────┐    │    │
        /// │  │  │  Paragraph (paraId="P001")                  │    │    │
        /// │  │  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐           │    │    │
        /// │  │  │  │Run-0│ │Run-1│ │Run-2│ │Run-3│           │    │    │
        /// │  │  │  └─────┘ └─────┘ └─────┘ └─────┘           │    │    │
        /// │  │  │  SegId: "P001-2" points to Run-2 ────────┘│    │    │
        /// │  │  └─────────────────────────────────────────────┘    │    │
        /// │  │  ┌─────────────────────────────────────────────┐    │    │
        /// │  │  │  Paragraph (paraId="P002")                  │    │    │
        /// │  │  │  ┌─────┐ ┌─────┐                           │    │    │
        /// │  │  │  │Run-0│ │Run-1│                           │    │    │
        /// │  │  │  └─────┘ └─────┘                           │    │    │
        /// │  │  └─────────────────────────────────────────────┘    │    │
        /// │  └─────────────────────────────────────────────────────┘    │
        /// └─────────────────────────────────────────────────────────────┘
        ///
        /// Process:
        /// 1. Parse segId "P001-2" → paragraphId="P001", runIndex="2"
        /// 2. Search document.Body.Descendants<Paragraph>()
        /// 3. Find paragraph where ParagraphId == "P001"
        /// 4. Return the matching paragraph
        /// </summary>
        /// <param name="doc">The Word document</param>
        /// <param name="segId">The segment ID in format "paragraphId-runIndex"</param>
        /// <returns>The target paragraph or null if not found</returns>
        private Paragraph? FindTargetParagraph(WordprocessingDocument doc, string segId)
        {
            // Parse the segId to extract paragraph ID
            var parts = segId.Split('-');
            if (parts.Length < 1)
            {
                context.Logger.LogWarning($"Invalid SegId format: {segId}. Expected format: 'paragraphId-runIndex'");
                return null;
            }

            string paragraphId = parts[0];

            // Find the paragraph by ID
            var mainDoc = doc.MainDocumentPart?.Document;
            if (mainDoc?.Body == null)
            {
                context.Logger.LogWarning("Document body is null");
                return null;
            }

            // Search for paragraph with the specified ID
            return mainDoc.Body.Descendants<Paragraph>().Where(p => p.ParagraphId == paragraphId).FirstOrDefault();
        }

        /// <summary>
        /// Converts a segment-relative position to a paragraph-absolute position.
        /// The segId format is "paragraphId-runIndex", and the position is relative to that specific segment.
        /// This method uses RunLocator to handle cases where the original run may have been split.
        ///
        /// ASCII Diagram - Position Conversion Process:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                           Paragraph Text                                   │
        /// │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐             │
        /// │  │  Run-0  │ │  Run-1  │ │  Run-2  │ │  Run-3  │ │  Run-4  │             │
        /// │  │"Hello " │ │"world " │ │"this "  │ │"is "    │ │"text"   │             │
        /// │  │(len=6)  │ │(len=6)  │ │(len=5)  │ │(len=3)  │ │(len=4)  │             │
        /// │  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘             │
        /// │  0         6           12          17         20          24              │
        /// │  │         │           │           │          │           │              │
        /// │  └─────────┼───────────┼───────────┼──────────┼───────────┘              │
        /// │            │           │           │          │                          │
        /// │            │           │           │          │                          │
        /// │  SegId: "P001-2" (Run-2)          │          │                          │
        /// │  Segment Relative Position: 3 ────┘          │                          │
        /// │  Paragraph Absolute Position: 12 + 3 = 15 ───┘                          │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Calculation Steps:
        /// 1. Find primary run using RunLocator.FindRun(segId)
        /// 2. Get all paragraph runs using GetRunsByConvertRules()
        /// 3. Calculate cumulative length of runs before primary run
        /// 4. Add segment relative position to get absolute position
        ///
        /// Example:
        /// - SegId: "P001-2" → Primary Run is Run-2 ("this ")
        /// - Runs before Run-2: Run-0 (6) + Run-1 (6) = 12
        /// - Segment relative position: 3
        /// - Absolute position: 12 + 3 = 15
        /// </summary>
        /// <param name="doc">The Word document</param>
        /// <param name="segId">The segment ID in format "paragraphId-runIndex"</param>
        /// <param name="segmentRelativePosition">The position relative to the segment</param>
        /// <returns>The absolute position within the paragraph</returns>
        private int ConvertToParagraphAbsolutePosition(WordprocessingDocument doc, string segId, int segmentRelativePosition)
        {
            context.Logger.LogInformation($"Converting segment relative position {segmentRelativePosition} for segId: {segId}");

            try
            {
                // Get RunLocator instance
                var runLocator = RunLocator.GetInstance(context);

                // Find the primary run to get the paragraph
                var primaryRun = runLocator.FindRun(doc, segId);
                if (primaryRun == null)
                {
                    context.Logger.LogWarning($"Primary run not found for segId: {segId}");
                    return segmentRelativePosition;
                }

                var paragraph = primaryRun.Ancestors<Paragraph>().FirstOrDefault();
                if (paragraph == null)
                {
                    context.Logger.LogError("Cannot find paragraph containing the primary run");
                    return segmentRelativePosition;
                }

                // Get all runs in the paragraph using convert rules
                var allParagraphRuns = runLocator.GetRunsByConvertRules(paragraph);

                // Find the index of the primary run in the paragraph
                int primaryRunIndex = allParagraphRuns.IndexOf(primaryRun);
                if (primaryRunIndex == -1)
                {
                    context.Logger.LogWarning($"Primary run not found in paragraph runs for segId: {segId}");
                    return segmentRelativePosition;
                }

                // Calculate absolute position by summing text lengths of all runs before the primary run
                int absolutePosition = 0;
                for (int i = 0; i < primaryRunIndex; i++)
                {
                    var runText = GetRunText(allParagraphRuns[i]);
                    absolutePosition += runText.Length;
                    context.Logger.LogInformation($"Pre-primary run {i}: '{runText}' (length: {runText.Length}), cumulative: {absolutePosition}");
                }

                // Add the segment relative position to get the final absolute position
                absolutePosition += segmentRelativePosition;

                context.Logger.LogInformation($"Primary run index: {primaryRunIndex}, segment relative position: {segmentRelativePosition}, final absolute position: {absolutePosition}");
                return absolutePosition;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error converting segment position to paragraph position: {ex.Message}");
                return segmentRelativePosition; // Fallback to original position
            }
        }

        /// <summary>
        /// Splits a paragraph at the specified character position.
        ///
        /// ASCII Diagram - Paragraph Split Process:
        ///
        /// BEFORE SPLIT:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                        Original Paragraph                                  │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Style, Formatting, etc.                                │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                       │
        /// │  │Run-0│ │Run-1│ │Run-2│ │Run-3│ │Run-4│ │Run-5│                       │
        /// │  │"The "│ │"quick"│ │" brown"│ │" fox"│ │" jumps"│ │" over"│             │
        /// │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘                       │
        /// │  0     4      9       16      21       28       34                      │
        /// │                              ↑                                         │
        /// │                    Split Position: 21                                  │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// AFTER SPLIT:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                        First Paragraph                                     │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Style, Formatting, etc. (preserved)                    │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                                       │
        /// │  │Run-0│ │Run-1│ │Run-2│ │Run-3│                                       │
        /// │  │"The "│ │"quick"│ │" brown"│ │" fox"│                                 │
        /// │  └─────┘ └─────┘ └─────┘ └─────┘                                       │
        /// │  0     4      9       16      21                                       │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                       Second Paragraph                                     │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Style, Formatting, etc. (cloned)                       │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────┐ ┌─────┐                                                       │
        /// │  │Run-4│ │Run-5│                                                       │
        /// │  │" jumps"│ │" over"│                                                   │
        /// │  └─────┘ └─────┘                                                       │
        /// │  0       7      13                                                     │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Process Steps:
        /// 1. Clone original paragraph to create second paragraph
        /// 2. Generate new paraId for second paragraph
        /// 3. Validate split position within paragraph text length
        /// 4. Call SplitParagraphContent() to redistribute elements
        /// 5. Insert second paragraph after first paragraph
        /// </summary>
        /// <remarks>
        /// This method handles the logic of splitting a paragraph into two paragraphs at a specified character position,
        /// preserving the original formatting and styles, and ensuring that the document structure remains intact.
        /// It also manages the creation of a new paragraph for the quotation text,
        /// ensuring that the split occurs at the correct character position within the paragraph text.
        /// </remarks>
        /// <param name="targetParagraph">The paragraph to split</param>
        /// <param name="splitPosition">The character position where to split</param>
        /// <returns>Number of paragraphs created (should be 0)</returns>
        /// <exception cref="ArgumentOutOfRangeException">Thrown if split position exceeds paragraph length</exception>
        /// <exception cref="InvalidOperationException">Thrown if the split operation fails</exception>
        private int SplitParagraphAtPosition(Paragraph targetParagraph, int splitPosition)
        {
            context.Logger.LogInformation($"Splitting paragraph at position: {splitPosition}");

            // Clone the original paragraph to create the second paragraph
            var secondParagraph = (Paragraph)targetParagraph.CloneNode(true);
            secondParagraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", idManager.GenerateParaId()));
            
            // Calculate the actual split position within the paragraph text
            var paragraphText = GetParagraphText(targetParagraph);
            if (splitPosition > paragraphText.Length)
            {
                context.Logger.LogError($"Split position {splitPosition} exceeds paragraph length {paragraphText.Length}");
                throw new ArgumentOutOfRangeException(nameof(splitPosition), "Split position exceeds paragraph length");
            }

            context.Logger.LogInformation($"Paragraph text length: {paragraphText.Length}, Split position: {splitPosition}");

            // Split the content
            SplitParagraphContent(targetParagraph, secondParagraph, splitPosition);

            // Insert the second paragraph after the first one
            targetParagraph.InsertAfterSelf(secondParagraph);

            context.Logger.LogInformation("Paragraph split operation completed successfully");
            return 0;
        }

        /// <summary>
        /// Gets the complete text content of a paragraph.
        /// </summary>
        /// <param name="paragraph">The paragraph to extract text from</param>
        /// <returns>The complete text content</returns>
        private string GetParagraphText(Paragraph paragraph)
        {
            var textElements = paragraph.Descendants<Text>();
            return string.Concat(textElements.Select(t => t.Text));
        }

        /// <summary>
        /// Redistributes elements between two paragraphs based on the specified position.
        /// The first paragraph will contain content up to and including the split position,
        /// while the second paragraph will contain content after the split position.
        /// Preserves all paragraph properties and styles during the redistribution.
        /// Handles all types of paragraph elements including Run, CommentRangeStart, CommentRangeEnd,
        /// BookmarkStart, BookmarkEnd, and other OpenXML elements.
        ///
        /// ASCII Diagram - Element Redistribution Process:
        ///
        /// INITIAL STATE (Both paragraphs have identical content):
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                        First Paragraph                                     │
        /// │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                       │
        /// │  │Run-0│ │Run-1│ │Run-2│ │Run-3│ │Run-4│ │Run-5│                       │
        /// │  │"The "│ │"quick"│ │" brown"│ │" fox"│ │" jumps"│ │" over"│             │
        /// │  │(4)  │ │(5)  │ │(6)   │ │(4)  │ │(6)   │ │(5)  │                   │
        /// │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘                       │
        /// │  0     4      9       15      19       25       30                      │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                       Second Paragraph                                     │
        /// │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                       │
        /// │  │Run-0│ │Run-1│ │Run-2│ │Run-3│ │Run-4│ │Run-5│                       │
        /// │  │"The "│ │"quick"│ │" brown"│ │" fox"│ │" jumps"│ │" over"│             │
        /// │  │(4)  │ │(5)  │ │(6)   │ │(4)  │ │(6)   │ │(5)  │                   │
        /// │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘                       │
        /// │  0     4      9       15      19       25       30                      │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///                                    ↑
        ///                          Split Position: 19
        ///
        /// REDISTRIBUTION LOGIC:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                    Element Processing Logic                                │
        /// │                                                                             │
        /// │  For each element:                                                          │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ currentPos + elementLength <= splitPos                             │   │
        /// │  │ ├─ YES: Add to First Paragraph                                     │   │
        /// │  │ └─ NO: Check if currentPos >= splitPos                             │   │
        /// │  │    ├─ YES: Add to Second Paragraph                                 │   │
        /// │  │    └─ NO: Element spans split → HandleElementSpanningSplit()       │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// FINAL STATE (After redistribution):
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                        First Paragraph                                     │
        /// │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                                       │
        /// │  │Run-0│ │Run-1│ │Run-2│ │Run-3│                                       │
        /// │  │"The "│ │"quick"│ │" brown"│ │" fox"│                                 │
        /// │  │(4)  │ │(5)  │ │(6)   │ │(4)  │                                   │
        /// │  └─────┘ └─────┘ └─────┘ └─────┘                                       │
        /// │  0     4      9       15      19                                       │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                       Second Paragraph                                     │
        /// │  ┌─────┐ ┌─────┐                                                       │
        /// │  │Run-4│ │Run-5│                                                       │
        /// │  │" jumps"│ │" over"│                                                   │
        /// │  │(6)   │ │(5)  │                                                     │
        /// │  └─────┘ └─────┘                                                       │
        /// │  0       6      11                                                     │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Special Elements Handling:
        /// • CommentRangeStart/End: Length = 0, positioned based on majority rule
        /// • BookmarkStart/End: Length = 0, positioned based on majority rule
        /// • Run elements: Can be split if they span the split position
        /// </summary>
        /// <param name="firstParagraph">The original paragraph (will contain content before split)</param>
        /// <param name="secondParagraph">The cloned paragraph (will contain content after split)</param>
        /// <param name="splitPosition">The position where to split the content</param>
        private void SplitParagraphContent(Paragraph firstParagraph, Paragraph secondParagraph, int splitPosition)
        {
            context.Logger.LogInformation($"Starting paragraph content split at position: {splitPosition}");

            // Get all elements from the original paragraph (excluding paragraph properties)
            var allElements = firstParagraph.Elements().Where(e => e is not ParagraphProperties).ToList();
            var secondElements = secondParagraph.Elements().Where(e => e is not ParagraphProperties).ToList();

            // Remove all content elements from both paragraphs, keeping only paragraph properties
            foreach (var element in allElements)
            {
                element.Remove();
            }
            foreach (var element in secondElements)
            {
                element.Remove();
            }

            context.Logger.LogInformation($"Processing {allElements.Count} paragraph elements for redistribution");

            // Redistribute elements based on split position
            int currentPosition = 0;

            foreach (var originalElement in allElements)
            {
                var elementTextLength = GetElementTextLength(originalElement);

                context.Logger.LogInformation($"Processing element {originalElement.GetType().Name} with text length: {elementTextLength}, current position: {currentPosition}");

                if (currentPosition + elementTextLength <= splitPosition)
                {
                    // Entire element goes to first paragraph
                    firstParagraph.AppendChild(originalElement.CloneNode(true));
                    context.Logger.LogInformation($"Element {originalElement.GetType().Name} assigned to first paragraph");
                }
                else if (currentPosition >= splitPosition)
                {
                    // Entire element goes to second paragraph
                    secondParagraph.AppendChild(originalElement.CloneNode(true));
                    context.Logger.LogInformation($"Element {originalElement.GetType().Name} assigned to second paragraph");
                }
                else
                {
                    // Element spans the split position
                    HandleElementSpanningSplit(originalElement, firstParagraph, secondParagraph, splitPosition - currentPosition);
                }

                currentPosition += elementTextLength;
            }

            context.Logger.LogInformation("Paragraph content split completed successfully");
        }

        /// <summary>
        /// Gets the text length of any paragraph element.
        /// Different element types contribute different amounts to the text length.
        /// </summary>
        /// <param name="element">The element to measure</param>
        /// <returns>The text length contributed by this element</returns>
        private int GetElementTextLength(OpenXmlElement element)
        {
            return element switch
            {
                Run run => GetRunText(run).Length,
                // Comment range markers don't contribute to text length
                CommentRangeStart => 0,
                CommentRangeEnd => 0,
                // Bookmark markers don't contribute to text length
                BookmarkStart => 0,
                BookmarkEnd => 0,
                // Other elements that might contain text
                _ => GetElementText(element).Length
            };
        }

        /// <summary>
        /// Gets the text content of any OpenXML element.
        /// </summary>
        /// <param name="element">The element to extract text from</param>
        /// <returns>The text content of the element</returns>
        private string GetElementText(OpenXmlElement element)
        {
            var textElements = element.Descendants<Text>();
            return string.Concat(textElements.Select(t => t.Text));
        }

        /// <summary>
        /// Gets the text content of a run element.
        /// </summary>
        /// <param name="run">The run to extract text from</param>
        /// <returns>The text content of the run</returns>
        private string GetRunText(Run run)
        {
            var textElements = run.Descendants<Text>();
            return string.Concat(textElements.Select(t => t.Text));
        }

        /// <summary>
        /// Handles elements that span across the split position.
        /// Only Run elements can be split; other elements are assigned as complete units.
        ///
        /// ASCII Diagram - Element Spanning Split Position:
        ///
        /// SCENARIO 1: Run Element Spanning Split
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                    Element Spanning Split                                  │
        /// │                                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │                    Run Element                                      │   │
        /// │  │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                         │   │
        /// │  │  │ "H" │ │ "e" │ │ "l" │ │ "l" │ │ "o" │                         │   │
        /// │  │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘                         │   │
        /// │  │  0     1     2     3     4     5                                 │   │
        /// │  │              ↑                                                   │   │
        /// │  │    Split Position: 3                                             │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                             │
        /// │  RESULT: Split into two runs                                                │
        /// │  ┌─────────────────────────────┐ ┌─────────────────────────────────┐     │
        /// │  │        First Run            │ │        Second Run               │     │
        /// │  │  ┌─────┐ ┌─────┐ ┌─────┐   │ │  ┌─────┐ ┌─────┐               │     │
        /// │  │  │ "H" │ │ "e" │ │ "l" │   │ │  │ "l" │ │ "o" │               │     │
        /// │  │  └─────┘ └─────┘ └─────┘   │ │  └─────┘ └─────┘               │     │
        /// │  │  0     1     2     3       │ │  0     1     2                 │     │
        /// │  └─────────────────────────────┘ └─────────────────────────────────┘     │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// SCENARIO 2: Non-Run Element Spanning Split (CommentRangeStart, BookmarkStart, etc.)
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                Non-Run Element Assignment                                   │
        /// │                                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │              CommentRangeStart Element                              │   │
        /// │  │              (Length = 0, Position-based)                           │   │
        /// │  │                        ↑                                           │   │
        /// │  │              Split Position in Element: 0                          │   │
        /// │  │              Element Text Length: 0                                │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                             │
        /// │  DECISION LOGIC:                                                            │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ if (splitPositionInElement <= elementTextLength / 2)               │   │
        /// │  │ ├─ Assign to Second Paragraph (majority falls after)               │   │
        /// │  │ else                                                                │   │
        /// │  │ └─ Assign to First Paragraph (majority falls before)               │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Element Types Handled:
        /// • Run: Can be split using SplitRun() method
        /// • CommentRangeStart/End: Assigned as complete unit (length = 0)
        /// • BookmarkStart/End: Assigned as complete unit (length = 0)
        /// • Other elements: Assigned based on majority rule
        /// </summary>
        /// <param name="element">The element that spans the split</param>
        /// <param name="firstParagraph">The first paragraph</param>
        /// <param name="secondParagraph">The second paragraph</param>
        /// <param name="splitPositionInElement">The split position within this element</param>
        private void HandleElementSpanningSplit(OpenXmlElement element, Paragraph firstParagraph,
            Paragraph secondParagraph, int splitPositionInElement)
        {
            context.Logger.LogInformation($"Handling element {element.GetType().Name} spanning split at position {splitPositionInElement}");

            if (element is Run run)
            {
                // Only Run elements can be split
                var (firstPartRun, secondPartRun) = SplitRun(run, splitPositionInElement);

                if (firstPartRun != null)
                {
                    firstParagraph.AppendChild(firstPartRun);
                    context.Logger.LogInformation("Added first part of split run to first paragraph");
                }
                if (secondPartRun != null)
                {
                    secondParagraph.AppendChild(secondPartRun);
                    context.Logger.LogInformation("Added second part of split run to second paragraph");
                }
            }
            else
            {
                // For non-Run elements, assign the entire element to the appropriate paragraph
                // based on where the majority of its content falls
                var elementTextLength = GetElementTextLength(element);
                if (splitPositionInElement <= elementTextLength / 2)
                {
                    // Majority falls in second paragraph
                    secondParagraph.AppendChild(element.CloneNode(true));
                    context.Logger.LogInformation($"Assigned entire {element.GetType().Name} to second paragraph");
                }
                else
                {
                    // Majority falls in first paragraph
                    firstParagraph.AppendChild(element.CloneNode(true));
                    context.Logger.LogInformation($"Assigned entire {element.GetType().Name} to first paragraph");
                }
            }
        }

        /// <summary>
        /// Splits a run at the specified character position.
        ///
        /// ASCII Diagram - Run Split Process:
        ///
        /// ORIGINAL RUN:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                           Original Run                                     │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Font, Style, Color, etc.                               │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐             │
        /// │  │ "H" │ │ "e" │ │ "l" │ │ "l" │ │ "o" │ │ " " │ │ "!" │             │
        /// │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘             │
        /// │  0     1     2     3     4     5     6     7                         │
        /// │                    ↑                                                 │
        /// │          Split Position: 3                                           │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// SPLIT CASES:
        ///
        /// Case 1: splitPosition <= 0
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │ Result: (null, originalRun.Clone())                                        │
        /// │ ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │ │                    Second Part Only                                 │   │
        /// │ │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐         │   │
        /// │ │  │ "H" │ │ "e" │ │ "l" │ │ "l" │ │ "o" │ │ " " │ │ "!" │         │   │
        /// │ │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘         │   │
        /// │ └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Case 2: splitPosition >= runText.Length
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │ Result: (originalRun.Clone(), null)                                        │
        /// │ ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │ │                     First Part Only                                 │   │
        /// │ │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐         │   │
        /// │ │  │ "H" │ │ "e" │ │ "l" │ │ "l" │ │ "o" │ │ " " │ │ "!" │         │   │
        /// │ │  └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘ └─────┘         │   │
        /// │ └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Case 3: Normal Split (0 < splitPosition < runText.Length)
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │ Result: (firstPart, secondPart)                                            │
        /// │ ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │ │                      First Part                                     │   │
        /// │ │ Properties: Font, Style, Color, etc. (cloned)                      │   │
        /// │ │  ┌─────┐ ┌─────┐ ┌─────┐                                           │   │
        /// │ │  │ "H" │ │ "e" │ │ "l" │                                           │   │
        /// │ │  └─────┘ └─────┘ └─────┘                                           │   │
        /// │ │  0     1     2     3                                               │   │
        /// │ └─────────────────────────────────────────────────────────────────────┘   │
        /// │ ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │ │                     Second Part                                     │   │
        /// │ │ Properties: Font, Style, Color, etc. (cloned)                      │   │
        /// │ │  ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                                 │   │
        /// │ │  │ "l" │ │ "o" │ │ " " │ │ "!" │                                 │   │
        /// │ │  └─────┘ └─────┘ └─────┘ └─────┘                                 │   │
        /// │ │  0     1     2     3     4                                         │   │
        /// │ └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Text Slicing:
        /// • First Part: runText[..splitPosition] → "Hel"
        /// • Second Part: runText[splitPosition..] → "lo !"
        ///
        /// Properties Preservation:
        /// • Both parts inherit all formatting properties from original run
        /// • Font, style, color, size, etc. are preserved through CloneNode(true)
        /// </summary>
        /// <param name="originalRun">The run to split</param>
        /// <param name="splitPosition">The position within the run to split</param>
        /// <returns>A tuple containing the first and second part runs</returns>
        private (Run? firstPart, Run? secondPart) SplitRun(Run originalRun, int splitPosition)
        {
            var runText = GetRunText(originalRun);
            
            if (splitPosition <= 0)
            {
                return (null, (Run)originalRun.CloneNode(true));
            }
            
            if (splitPosition >= runText.Length)
            {
                return ((Run)originalRun.CloneNode(true), null);
            }

            // Create two new runs with split content
            var firstPart = (Run)originalRun.CloneNode(true);
            var secondPart = (Run)originalRun.CloneNode(true);

            // Update text content
            UpdateRunText(firstPart, runText[..splitPosition]);
            UpdateRunText(secondPart, runText[splitPosition..]);

            return (firstPart, secondPart);
        }

        /// <summary>
        /// Updates the text content of a run element.
        ///
        /// ASCII Diagram - Run Text Update Process:
        ///
        /// BEFORE UPDATE:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                           Run Element                                       │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Font, Style, Color, etc. (preserved)                   │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │                    Existing Text Elements                          │   │
        /// │  │  ┌─────────┐ ┌─────────┐ ┌─────────┐                             │   │
        /// │  │  │ Text-1  │ │ Text-2  │ │ Text-3  │                             │   │
        /// │  │  │"Hello"  │ │" "      │ │"World"  │                             │   │
        /// │  │  └─────────┘ └─────────┘ └─────────┘                             │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// STEP 1: Remove Existing Text Elements
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                           Run Element                                       │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Font, Style, Color, etc. (preserved)                   │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │                    [EMPTY]                                          │   │
        /// │  │  All existing Text elements removed                                 │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// STEP 2: Add New Text Element (if newText is not empty)
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                           Run Element                                       │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Properties: Font, Style, Color, etc. (preserved)                   │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// │  ┌─────────────────────────────────────────────────────────────────────┐   │
        /// │  │                    New Text Element                                 │   │
        /// │  │  ┌─────────────────────────────────────────────────────────────┐   │   │
        /// │  │  │                    Text                                     │   │   │
        /// │  │  │  Content: "New text content"                               │   │   │
        /// │  │  │  Space: "preserve" (if starts/ends with space)             │   │   │
        /// │  │  └─────────────────────────────────────────────────────────────┘   │   │
        /// │  └─────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// SPACE PRESERVATION LOGIC:
        /// ┌─────────────────────────────────────────────────────────────────────────────┐
        /// │                    Space Processing Rules                                   │
        /// │                                                                             │
        /// │  if (newText.StartsWith(' ') || newText.EndsWith(' '))                      │
        /// │  {                                                                          │
        /// │      textElement.Space = SpaceProcessingModeValues.Preserve;                │
        /// │  }                                                                          │
        /// │                                                                             │
        /// │  Examples:                                                                  │
        /// │  • " Hello"     → Space="preserve" (leading space)                         │
        /// │  • "Hello "     → Space="preserve" (trailing space)                        │
        /// │  • " Hello "    → Space="preserve" (both)                                  │
        /// │  • "Hello"      → No Space attribute (no leading/trailing spaces)         │
        /// └─────────────────────────────────────────────────────────────────────────────┘
        ///
        /// Special Cases:
        /// • Empty/null newText: No new Text element is added (run becomes empty)
        /// • Whitespace-only text: Space="preserve" is applied
        /// • All formatting properties are preserved from original run
        /// </summary>
        /// <param name="run">The run to update</param>
        /// <param name="newText">The new text content</param>
        private void UpdateRunText(Run run, string newText)
        {
            // Remove existing text elements
            var textElements = run.Elements<Text>().ToList();
            foreach (var textElement in textElements)
            {
                textElement.Remove();
            }

            // Add new text element if text is not empty
            if (!string.IsNullOrEmpty(newText))
            {
                var newTextElement = new Text(newText);
                
                // Preserve space if needed
                if (newText.StartsWith(' ') || newText.EndsWith(' '))
                {
                    newTextElement.Space = SpaceProcessingModeValues.Preserve;
                }
                
                run.AppendChild(newTextElement);
            }
        }


    }
}