﻿using System.Text;
using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Abstract;
using ApplyRevision.Model;
using Common.Helper;
using Run = DocumentFormat.OpenXml.Wordprocessing.Run;
using System.Text.RegularExpressions;
using ApplyRevision.Component;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;


namespace ApplyRevision.Helper
{
    public class DocxChangeApplier : DocxHandle, IDocxChangeApplier
    {
        private int maxRevisionId;
        private int maxCommentId;
        private WordprocessingCommentsPart? commentParts;

        public DocxChangeApplier(ILambdaContext _context)
        {
            context = _context;
        }

        /// <summary>
        /// Get the next comment ID from the comments part of the document.
        /// </summary>
        /// <param name="commentsPart"></param>
        /// <returns></returns>
        private int GetNextCommentId(WordprocessingCommentsPart? commentsPart)
        {
            var commentElements = commentsPart?.Comments.Elements<Comment>();
            if (commentElements != null)
            {
                var maxCommentId = commentElements.Max(item => item.Id);
                if (maxCommentId != null)
                {
                    string? value = maxCommentId.Value;
                    if (int.TryParse(value, out int id))
                    {
                        return id + 1;
                    }
                }
            }
            return 1;
        }

        /// <summary>
        /// Get the next revision ID from the document body.
        /// </summary>
        /// <param name="commentsPart"></param>
        /// <returns></returns>
        private int GetNextRevisonId(Body commentsPart)
        {
            int nextId = 0;
            IEnumerable<OpenXmlElement> revisions = commentsPart.Descendants<Run>();
            foreach (OpenXmlElement revision in revisions)
            {
                var revisionId = GetElementId(revision);
                if (string.IsNullOrEmpty(revisionId))
                    continue;

                int.TryParse(revisionId, out int id);
                if (id > nextId)
                    nextId = id;
            }
            return nextId + 1;
        }

        private string? GetParagarphId(Paragraph paragraph)
        {
            try
            {
                // Check if the paragraph has a w14:paraId attribute
                var paraIdAttr = paragraph.GetAttribute("paraId", WORD2010_NAMESPACE);
                var paraId = paraIdAttr.Value;
                return paraId;
            }
            catch
            {
                context.Logger.LogWarning("No paragraph id found.");
            }
            return string.Empty;
        }

        /// <summary>
        /// Get the mapping of paragraphs with their corresponding paraId attributes.
        /// </summary>
        /// <param name="paragraphs"></param>
        /// <returns></returns>
        private Dictionary<string, Paragraph> GetParagraphMapping(IEnumerable<Paragraph> paragraphs)
        {
            var paragraphRevisions = new Dictionary<string, Paragraph>();
            foreach (var paragraph in paragraphs)
            {
                var paraId = GetParagarphId(paragraph);
                if (string.IsNullOrEmpty(paraId))
                    continue;

                paragraphRevisions.Add(paraId, paragraph);
            }
            return paragraphRevisions;
        }

        /// <summary>
        /// Get the run element based on the revision operator type.
        /// </summary>
        /// <param name="text"></param>
        /// <param name="peratorType"></param>
        /// <returns></returns>
        private Run GetRunElement(string text, RevisionOperatorType opType)
        {
            var normalized = Regex.Replace(text, @"\n{2,}", "\n");
            var lines = normalized.Split('\n');
            var run = new Run();

            for (int i = 0; i < lines.Length; i++)
            {
                var segment = lines[i];
                if (!string.IsNullOrEmpty(segment))
                {
                    if (opType == RevisionOperatorType.del)
                        run.AppendChild(new DeletedText(segment) { Space = SpaceProcessingModeValues.Preserve });
                    else
                        run.AppendChild(new Text(segment) { Space = SpaceProcessingModeValues.Preserve });
                }
                if (i < lines.Length - 1)
                    run.AppendChild(new Break());
            }
            return run;
        }

        /// <summary>
        /// Get the revision element based on the revision operator type.
        /// </summary>
        /// <param name="revisionOperator"></param>
        /// <returns></returns>
        private OpenXmlElement? GetRevisionElement(RevisionOperator revisionOperator)
        {
            maxRevisionId++;
            var date = DateHepler.SafeParseDate(revisionOperator.Date);
            if (revisionOperator.Op == RevisionOperatorType.ins)
            {
                var insertedRun = new InsertedRun()
                {
                    Id = maxRevisionId.ToString(),
                    Author = revisionOperator.Author,
                    Date = new DateTimeValue(date)
                };
                var runObj = GetRunElement(revisionOperator.Text, revisionOperator.Op);
                insertedRun.Append(runObj);
                return insertedRun;
            }

            if (revisionOperator.Op == RevisionOperatorType.del)
            {
                var deleteRun = new DeletedRun()
                {
                    Id = maxRevisionId.ToString(),
                    Author = revisionOperator.Author,
                    Date = new DateTimeValue(date)
                };
                var runObj = GetRunElement(revisionOperator.Text, revisionOperator.Op);
                deleteRun.Append(runObj);
                return deleteRun;
            }
            return null;
        }

        /// <summary>
        /// Get the comment element based on the revision operator.
        /// </summary>
        /// <param name="revisionOperator"></param>
        /// <returns></returns>
        private Comment GetCommentElement(int commentId, string author, string date, string message)
        {
            var _date = DateHepler.SafeParseDate(date);
            Comment comment = new Comment()
            {
                Id = commentId.ToString(),
                Author = author,
                Initials = GetCommentInitials(author),
                Date = new DateTimeValue(_date)
            };
            // comment element always ins text
            var run = GetRunElement(message, RevisionOperatorType.ins);
            var paragraph = new Paragraph(run);
            comment.Append(paragraph);
            return comment;
        }


        /// <summary>
        /// Get the comment reference element based on the previous comment start ID.
        /// </summary>
        /// <param name="commnetId"></param>
        /// <returns></returns>
        private Run GetCommentReference(StringValue commnetId)
        {
            var reference = new CommentReference() { Id = commnetId };
            var run = new Run(reference)
            {
                RunProperties = new RunProperties(
                    new RunStyle { Val = "CommentReference" }
                )
            };
            return run;
        }

        /// <summary>
        /// Get comment content based on the revision operator.
        /// </summary>
        /// <param name="revisionOperator"></param>
        /// <returns></returns>
        private string GetCommentContent(RevisionOperator revisionOperator)
        {
            if (!string.IsNullOrEmpty(revisionOperator.Comment))
                return revisionOperator.Comment;

            string action = "";
            if (revisionOperator.Op == RevisionOperatorType.ins)
                action = "Insert";
            else if (revisionOperator.Op == RevisionOperatorType.del)
                action = "Delete";

            return $"{action}: {revisionOperator.Text}";
        }

        /// <summary>
        /// Get the inserted elements and comments based on the revision operators.
        /// </summary>
        /// <param name="revisionOperators"></param>
        /// <returns></returns>
        private (List<OpenXmlElement> revisons, List<Comment> comments) GetInsertElements(List<RevisionOperator> revisionOperators, EditType editType)
        {
            var revisionElements = new List<OpenXmlElement>();
            var commentElements = new List<Comment>();
            if (revisionOperators == null || revisionOperators.Count == 0)
                return (revisionElements, commentElements);

            StringValue? prevCommentStartId = null;
            foreach (RevisionOperator revisionOp in revisionOperators)
            {
                // skip the author is not styling_agent(ai) and (editType is prefix or suffix)
                if ((editType == EditType.prefix || editType == EditType.suffix) && !revisionOp.Author.StartsWith("styling_agent"))
                    continue;

                if (revisionOp.CommentStart)
                {
                    maxCommentId++;
                    string commentContent = GetCommentContent(revisionOp);
                    var commentElement = GetCommentElement(maxCommentId, revisionOp.Author, revisionOp.Date, commentContent);
                    commentElements.Add(commentElement);
                    // add comment start
                    var commentStart = new CommentRangeStart() { Id = commentElement.Id };
                    revisionElements.Add(commentStart);
                    prevCommentStartId = commentElement.Id;
                }

                var insertedRun = GetRevisionElement(revisionOp);
                if (insertedRun != null)
                    revisionElements.Add(insertedRun);

                if (revisionOp.CommentEnd)
                {
                    // skip If the first element is not a CommentRangeStart
                    if (string.IsNullOrEmpty(prevCommentStartId))
                        continue;

                    var commentEnd = new CommentRangeEnd() { Id = prevCommentStartId };
                    revisionElements.Add(commentEnd);
                    // add comment reference
                    var commentReference = GetCommentReference(prevCommentStartId);
                    revisionElements.Add(commentReference);
                    prevCommentStartId = null;
                }
            }

            // WHICH MEANS THE COMMENT END IS MISSING
            if (prevCommentStartId != null)
            {
                var filterRevisions = new List<OpenXmlElement>();
                foreach (var item in revisionElements)
                {
                    if (item is CommentRangeEnd end && (end.Id != null && end.Id.Equals(prevCommentStartId)))
                        continue;
                    filterRevisions.Add(item);
                }

                // TODO: remove the latest comment reference
                var filterComments = commentElements.Where(item => item.Id != null && item.Id.Equals(prevCommentStartId)).ToList();
                context.Logger.LogWarning($"Comment end is missing for comment id: {prevCommentStartId}");
                return (filterRevisions, filterComments);
            }
            return (revisons: revisionElements, comments: commentElements);
        }

        /// <summary>
        /// Create comment elements for each link
        /// </summary>
        /// <remarks>
        /// 1. Parse revisionLinks to get revisionLink comments
        /// 2. Create commentInsertion based on revisionLink comments and add to revisionLinkInsert
        /// 3. Return revisionLinkInsert
        /// </remarks>
        /// <param name="revisionLinks"></param>
        /// <returns></returns>
        private RevisionLinkInsert GetRevisionLinkComments(List<RevisionLink> revisionLinks)
        {
            RevisionLinkInsert revisionLinkInsert = new();
            if (revisionLinks.Count == 0)
                return revisionLinkInsert;

            var reverseLinks = revisionLinks.AsEnumerable().Reverse().ToList();
            foreach (var revisionLink in reverseLinks)
            {
                maxCommentId++;
                var linkComment = GetCommentElement(
                    maxCommentId,
                    revisionLink.Author,
                    revisionLink.Date,
                    revisionLink.Comment
                );

                var commentInsertion = new CommentComponentBuilder(linkComment).Build();
                revisionLinkInsert.CommentComponents.Add(commentInsertion);
            }
            return revisionLinkInsert;
        }

        #region Hyperlink Helpers
        /// <summary> generate begin‒instrText‒separate </summary>
        private IEnumerable<OpenXmlElement> BuildFieldHeader(string url)
        {
            yield return new FieldChar() { FieldCharType = FieldCharValues.Begin };
            yield return new Run(new FieldCode($" HYPERLINK \"{url}\" ")
            { Space = SpaceProcessingModeValues.Preserve });
            yield return new FieldChar() { FieldCharType = FieldCharValues.Separate };
        }

        /// <summary> insert field header and end </summary>
        private void InsertFieldAroundRuns(List<Run> runs, RevisionLink link)
        {
            if (runs == null || runs.Count == 0) return;

            var first = runs.First();
            var last = runs.Last();

            foreach (var node in BuildFieldHeader(link.Uri))
                first.Parent?.InsertBeforeSelf(node);

            last.Parent?.InsertAfterSelf(new FieldChar() { FieldCharType = FieldCharValues.End });
        }

        /// <summary>滑窗匹配连续 run，返回匹配组</summary>
        private (List<Run> group, bool found) FindRunsForDisplay(
                List<Run> runs, string display)
        {
            string Norm(string s) =>
                s.Replace('\u00A0', ' ')
                .Replace("\r", "").Replace("\n", "")
                .Trim().ToLowerInvariant();

            var tgt = Norm(display);
            for (int start = 0; start < runs.Count; start++)
            {
                var sb = new StringBuilder();
                var grp = new List<Run>();
                for (int idx = start; idx < runs.Count; idx++)
                {
                    sb.Append(runs[idx].InnerText);
                    grp.Add(runs[idx]);
                    if (Norm(sb.ToString()) == tgt) return (grp, true);
                    if (sb.Length > display.Length + 2) break;
                }
            }
            // 找不到：退回第一个 run
            return (runs.Count > 0 ? new List<Run> { runs[0] } : new List<Run>(), false);
        }
        #endregion

        /// <summary>
        /// Get the ID of the element based on its type.
        /// </summary>
        /// <param name="element"></param>
        /// <returns></returns>
        private string? GetElementId(OpenXmlElement element)
        {
            if (element is Inserted inserted)
                return inserted.Id?.Value;

            if (element is Deleted deleted)
                return deleted.Id?.Value;

            if (element is InsertedRun insertedRun)
                return insertedRun.Id?.Value;

            if (element is DeletedRun deletedRun)
                return deletedRun.Id?.Value;
            return string.Empty;
        }

        /// <summary>
        /// Get the mapping of paragraph revisions based on the paragraph element.
        /// </summary>
        /// <param name="paragraph"></param>
        /// <returns></returns>
        private Dictionary<string, OpenXmlElement> GetParagraphRevisionMapping(Paragraph paragraph)
        {
            Dictionary<string, OpenXmlElement> result = new Dictionary<string, OpenXmlElement>();
            if (paragraph == null)
            {
                return result;
            }

            List<OpenXmlElement> revisionElements = paragraph.Elements<OpenXmlElement>()
                        .Where(e => e is Run || e is InsertedRun || e is DeletedRun)
                        .ToList();

            foreach (OpenXmlElement revisionElement in revisionElements)
            {
                if (revisionElement == null)
                    continue;

                var elementId = GetElementId(revisionElement);
                if (string.IsNullOrEmpty(elementId))
                    continue;

                result.Add(elementId, revisionElement);
            }
            return result;
        }

        /// <summary>
        /// Get the mapping of changed documents based on the document changes.
        /// </summary>
        /// <param name="documentChanges"></param>
        /// <returns>
        /// {
        ///     "ParaId": {
        ///         "RevId": List<RevisionInfo>
        ///     }
        /// }
        /// </returns>
        private Dictionary<string, Dictionary<string, List<RevisionInfo>>> GetChangedDocumentMapping(List<DocumentChange> documentChanges)
        {
            var result = new Dictionary<string, Dictionary<string, List<RevisionInfo>>>();
            if (documentChanges == null || documentChanges.Count == 0)
                return result;

            foreach (DocumentChange item in documentChanges)
            {
                if (string.IsNullOrEmpty(item.ParaId) || item.ChangeText == null || item.ChangeText.Count == 0)
                    continue;

                if (!result.ContainsKey(item.ParaId))
                    result[item.ParaId] = new Dictionary<string, List<RevisionInfo>>();

                foreach (RevisionInfo changed in item.ChangeText)
                {
                    if (string.IsNullOrEmpty(changed.RevId))
                        continue;

                    // without ops and links
                    if ((changed.Ops == null || changed.Ops.Count == 0) && (changed.Link == null || changed.Link.Count == 0))
                        continue;

                    if (!result[item.ParaId].ContainsKey(changed.RevId))
                        result[item.ParaId][changed.RevId] = new List<RevisionInfo>();

                    result[item.ParaId][changed.RevId].Add(changed);
                }
            }
            return result;
        }

        /// <summary>
        /// Add comments to the comment part.
        /// </summary>
        /// <param name="comments"></param>
        private void AddCommentParts(List<Comment> comments)
        {
            if (!comments.Any()) return;
            // Add comments
            foreach (var comment in comments)
            {
                if (commentParts == null)
                    continue;
                commentParts.Comments.Append(comment);
            }
        }

        /// <summary>
        /// Insert comment links into the header and footer of the given revision.
        /// </summary>
        /// <param name="header">The comment start element insert position</param>
        /// <param name="footer">The comment end & comment reference element insert position</param>
        /// <param name="revisionLinkInsert"></param>
        private void InsertCommentForLinks(OpenXmlElement header, OpenXmlElement footer, RevisionLinkInsert revisionLinkInsert)
        {
            if (header == null || footer == null)
                return;

            foreach (var commentInsertion in revisionLinkInsert.CommentComponents)
            {
                if (commentParts == null)
                    continue;
                commentParts.Comments.Append(commentInsertion.Comment);
                header.InsertBeforeSelf(commentInsertion.RangeStart);
                footer.InsertAfterSelf(commentInsertion.RangeEnd);
                commentInsertion.RangeEnd.InsertAfterSelf(commentInsertion.Reference);
                header = commentInsertion.RangeStart;
                footer = commentInsertion.Reference;
            }
        }


        private bool isSuffixInsert(EditType editType)
        {
            return editType == EditType.inplace || editType == EditType.suffix || editType == EditType.wrap;
        }


        /// <summary>
        /// Update the paragraph with the revision information.
        /// TODO: wrap type should more robust
        /// </summary>
        /// <param name="paragraph"></param>
        /// <param name="revisionInfos"></param>
        private void UpdateParagraphWithRevisionInfos(Paragraph paragraph, Dictionary<string, List<RevisionInfo>> revisionInfos)
        {
            var revisionElementMapping = GetParagraphRevisionMapping(paragraph);
            if (revisionElementMapping == null || revisionElementMapping.Count == 0)
                return;

            var removedElements = new List<OpenXmlElement>();
            foreach (var item in revisionInfos)
            {
                string revisionId = item.Key;
                var revisions = item.Value;

                if (string.IsNullOrEmpty(revisionId) || revisions == null || revisions.Count == 0)
                {
                    context.Logger.LogWarning($"No revision changed ops found for {revisionId}");
                    continue;
                }

                if (!revisionElementMapping.ContainsKey(revisionId))
                {
                    context.Logger.LogWarning($"No revision element found for {revisionId}");
                    continue;
                }

                OpenXmlElement revisionElement = revisionElementMapping[revisionId];
                foreach (RevisionInfo revision in revisions)
                {
                    if (revision == null)
                        continue;

                    var revisionLinksWithComment = GetRevisionLinkComments(revision.Link);
                    // null means just insert link
                    if (revision.EditType == null)
                    {
                        Run? runWithLinkStart = null;
                        Run? runWithLinkEnd = null;
                        foreach (var link in revision.Link)
                        {
                            var (runsForLink, found) =
                                FindRunsForDisplay([.. revisionElement.ChildElements.ToList().OfType<Run>()], link.DisplayText);
                            if (found)
                            {
                                runWithLinkStart = runsForLink.FirstOrDefault();
                                runWithLinkEnd = runsForLink.LastOrDefault();
                                break;
                            }
                        }
                        if (runWithLinkStart == null || runWithLinkEnd == null)
                        {
                            context.Logger.LogWarning($"[{revision.RevId}] not found link displaytext during insert comment.");
                            continue;
                        }
                        InsertCommentForLinks(runWithLinkStart, runWithLinkEnd, revisionLinksWithComment);
                        continue;
                    }

                    var prevItem = revisionElement;
                    var editType = (EditType)revision.EditType;
                    var result = GetInsertElements(revision.Ops, editType);
                    if (isSuffixInsert(editType))
                    {

                        foreach (var newRevision in result.revisons)
                        {
                            if (prevItem == null)
                                continue;

                            prevItem.InsertAfterSelf(newRevision);
                            prevItem = newRevision;
                        }
                        // patch new revision link by revision.Link
                        if (editType == EditType.inplace &&
                            revision.Link is { Count: > 0 })
                        {
                            var candidateRuns = result.revisons
                                .SelectMany(rv => rv.Descendants<Run>())
                                .Where(r => r.GetFirstChild<Text>() != null || r.GetFirstChild<DeletedText>() != null)
                                .Where(r =>
                                {
                                    var tc = r.Ancestors<RunTrackChangeType>().FirstOrDefault();
                                    if (tc == null) return true;

                                    bool isStyling = tc.Author?.Value
                                            .StartsWith("styling_agent", StringComparison.OrdinalIgnoreCase) ?? false;
                                    bool isIns = tc is InsertedRun;
                                    return !(isStyling && isIns);     // filter sty-ins
                                })
                                .ToList();
                            if (candidateRuns.Count == 0)
                            {
                                context.Logger.LogWarning(
                                    $"[{revision.RevId}] not found runs for display text");
                                continue;
                            }
                            foreach (var link in revision.Link)
                            {
                                var (runsForLink, found) =
                                    FindRunsForDisplay(candidateRuns, link.DisplayText);

                                if (runsForLink.Count == 0)
                                {
                                    context.Logger.LogWarning(
                                        $"[{revision.RevId}] not found displaytext \"{link.DisplayText}\"");
                                    continue;
                                }

                                if (found)
                                {
                                    InsertFieldAroundRuns(runsForLink, link);
                                }
                                else
                                {
                                    context.Logger.LogWarning(
                                        $"[{revision.RevId}] not found displaytext \"{link.DisplayText}\"");
                                    continue;
                                }

                                foreach (var r in runsForLink) candidateRuns.Remove(r);
                            }
                        }

                        AddCommentParts(result.comments);
#pragma warning disable CS8604 // Possible null reference argument.
                        InsertCommentForLinks(revisionElement, prevItem, revisionLinksWithComment);
#pragma warning restore CS8604 // Possible null reference argument.

                        if (revision.EditType == EditType.inplace || revision.EditType == EditType.wrap)
                            paragraph.RemoveChild(revisionElement);
                    }
                    else if (revision.EditType == EditType.prefix)
                    {
                        var reversedRevisions = result.revisons.AsEnumerable().Reverse().ToList();
                        foreach (var newRevision in reversedRevisions)
                        {
                            if (prevItem == null)
                                continue;

                            prevItem.InsertBeforeSelf(newRevision);
                            prevItem = newRevision;
                        }

                        AddCommentParts(result.comments);
#pragma warning disable CS8604 // Possible null reference argument.
                        InsertCommentForLinks(prevItem, revisionElement, revisionLinksWithComment);
#pragma warning restore CS8604 // Possible null reference argument.
                    }
                    else
                    {
                        context.Logger.LogWarning($"Unknown EditType: {revision.EditType}");
                    }
                }
            }
        }

        /// <summary>
        /// Initialize the comment part of the document.
        /// </summary>
        /// <param name="mainPart"></param>
        private void initCommentPart(MainDocumentPart mainPart)
        {
            commentParts = mainPart.WordprocessingCommentsPart ?? mainPart.AddNewPart<WordprocessingCommentsPart>();
            if (commentParts.Comments == null)
                commentParts.Comments = new Comments();
        }

        /// <summary>
        /// Apply the changes to the DOCX file.
        /// </summary>
        /// <param name="docxPath"></param>
        /// <param name="docChanges"></param>
        /// <returns></returns>
        public bool ApplyChanges(string docxPath, List<DocumentChange> docChanges)
        {
            if (!File.Exists(docxPath))
            {
                context.Logger.LogError($"DOCX file not found: {docxPath}");
                return false;
            }

            using (var document = WordprocessingDocument.Open(docxPath, true))
            {
                var mainPart = document.MainDocumentPart;
                if (mainPart?.Document == null || mainPart.Document?.Body == null)
                {
                    context.Logger.LogError("Could not open document or MainDocumentPart is missing.");
                    return false;
                }

                var paragraphs = mainPart.Document.Body.Elements<Paragraph>();
                if (paragraphs == null || !paragraphs.Any())
                {
                    context.Logger.LogInformation("No paragraphs found in the document.");
                    return true;
                }

                var paragraphMapping = GetParagraphMapping(paragraphs);
                if (paragraphMapping == null || paragraphMapping.Count == 0)
                {
                    context.Logger.LogInformation("No paragraphs found in the document.");
                    return true;
                }

                var documentChangedMapping = GetChangedDocumentMapping(docChanges);
                if (documentChangedMapping == null || documentChangedMapping.Count == 0)
                {
                    context.Logger.LogInformation("No document changes found.");
                    return true;
                }

                maxCommentId = GetNextCommentId(mainPart.WordprocessingCommentsPart);
                maxRevisionId = GetNextRevisonId(mainPart.Document.Body);
                initCommentPart(mainPart);

                foreach (var item in paragraphMapping)
                {
                    string paragraphId = item.Key;
                    // the changed revisions belongs the paragraph
                    var revisionInfos = documentChangedMapping.GetValueOrDefault(paragraphId);
                    if (revisionInfos == null || revisionInfos.Count == 0)
                    {
                        context.Logger.LogDebug($"No revision changes found for paragraph with ParaId={paragraphId}.");
                        continue;
                    }
                    // Process each group as needed
                    context.Logger.LogInformation($"Processing paragraph with Id={paragraphId}, containing {revisionInfos.Count} items.");
                    UpdateParagraphWithRevisionInfos(item.Value, revisionInfos);
                }

                commentParts?.Comments.Save();
                mainPart.Document.Save();
                return true;
            }
            ;
        }

        /// <summary>
        /// Get the initials of the comment author.
        /// </summary>
        /// <param name="author"></param>
        /// <returns>
        /// e.g: Jhon Lee => JL
        /// e.g: "" => AI
        /// </returns>
        private StringValue GetCommentInitials(string author)
        {
            if (string.IsNullOrWhiteSpace(author))
                return "AI";

            var parts = author.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries);
            return new string(parts.Select(p => char.ToUpperInvariant(p[0])).ToArray());
        }
    }
}
