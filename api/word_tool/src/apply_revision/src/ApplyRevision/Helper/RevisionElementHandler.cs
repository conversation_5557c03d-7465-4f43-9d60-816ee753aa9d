using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using ApplyRevision.Factory;
using ApplyRevision.Service;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// 处理Word文档中的修订元素
    /// </summary>
    public class RevisionElementHandler
    {
        private readonly ILambdaContext context;
        private readonly IElementFactory elementFactory;
        private readonly IRevisionElementFactory revisionFactory;
        private readonly IIdManager idManager;

        public RevisionElementHandler(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager)
        {
            this.context = context ?? throw new ArgumentNullException(nameof(context));
            this.elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
            this.revisionFactory = revisionFactory ?? throw new ArgumentNullException(nameof(revisionFactory));
            this.idManager = idManager ?? throw new ArgumentNullException(nameof(idManager));
        }

        /// <summary>
        /// 查找包含指定Run的最外层修订元素
        /// </summary>
        /// <param name="run">要检查的Run元素</param>
        /// <returns>最外层的修订元素，如果不在修订元素内则返回null</returns>
        public OpenXmlElement? FindOutermostRevisionElement(Run run)
        {
            context.Logger.LogInformation($"查找包含Run的最外层修订元素: Run.InnerText='{run.InnerText}'");

            // 获取所有祖先元素
            var ancestors = run.Ancestors().ToList();

            // 记录所有修订元素祖先
            var revisionAncestors = new List<OpenXmlElement>();

            // 查找修订元素（DeletedRun、InsertedRun等）
            foreach (var ancestor in ancestors)
            {
                if (ancestor is DeletedRun || ancestor is InsertedRun ||
                    ancestor is MoveFromRun || ancestor is MoveToRun)
                {
                    context.Logger.LogInformation($"找到修订元素祖先: 类型={ancestor.GetType().Name}");
                    revisionAncestors.Add(ancestor);
                }
            }

            if (revisionAncestors.Count == 0)
            {
                context.Logger.LogInformation("未找到修订元素祖先");
                return null;
            }

            // 返回最外层的修订元素（列表中的第一个元素）
            var outermostRevision = revisionAncestors.First();
            context.Logger.LogInformation($"最外层修订元素: 类型={outermostRevision.GetType().Name}");

            return outermostRevision;
        }

        /// <summary>
        /// 处理在修订元素内插入文本
        /// </summary>
        /// <param name="revisionElement">修订元素</param>
        /// <param name="run">当前Run</param>
        /// <param name="text">当前Text</param>
        /// <param name="offset">插入位置偏移量</param>
        /// <param name="newText">要插入的文本</param>
        /// <param name="revision">修订信息</param>
        public void HandleInsertWithinRevisionElement(OpenXmlElement revisionElement, Run run, Text text, int offset, string newText, Revision revision)
        {
            context.Logger.LogInformation($"处理修订元素内的插入: 修订元素类型={revisionElement.GetType().Name}, 偏移量={offset}");

            // 创建新的InsertedRun
            var insertedRun = CreateInsertedRun(newText, revision, run);
            context.Logger.LogInformation($"创建新的InsertedRun: Text='{newText}'");

            // 获取修订元素的所有Run子元素
            var runs = revisionElement.Descendants<Run>().ToList();
            context.Logger.LogInformation($"修订元素包含{runs.Count}个Run元素");

            // 检查是否是DeletedRun
            if (revisionElement is DeletedRun deletedRun)
            {
                // 检查是否是DeletedRun内的InsertedRun嵌套情况
                var isNestedInsertInDelete = run.Ancestors<InsertedRun>().Any();

                if (isNestedInsertInDelete)
                {
                    context.Logger.LogInformation("检测到DeletedRun内的InsertedRun嵌套情况，特殊处理");
                    HandleNestedInsertInDelete(revisionElement, run, insertedRun);
                    return;
                }
                else
                {
                    // 处理简单的DeletedRun情况，在DeletedRun前插入
                    context.Logger.LogInformation("检测到简单的DeletedRun情况，在DeletedRun前插入");
                    deletedRun.Parent?.InsertBefore(insertedRun, deletedRun);
                    return;
                }
            }

            // 确定插入位置
            bool isFirstRun = run == runs.FirstOrDefault();
            bool isLastRun = run == runs.LastOrDefault();

            if (offset == 0 && isFirstRun)
            {
                // 在修订元素的第一个Run的开始处插入，应该在修订元素前插入
                context.Logger.LogInformation("在修订元素前插入");
                revisionElement.Parent?.InsertBefore(insertedRun, revisionElement);
            }
            else if (offset == text.Text.Length && isLastRun)
            {
                // 在修订元素的最后一个Run的结尾处插入，应该在修订元素后插入
                context.Logger.LogInformation("在修订元素后插入");
                revisionElement.Parent?.InsertAfter(insertedRun, revisionElement);
            }
            else
            {
                // 在修订元素内部插入，需要拆分修订元素
                context.Logger.LogInformation("需要拆分修订元素");
                SplitRevisionElementAndInsert(revisionElement, run, text, offset, insertedRun);
            }
        }

        /// <summary>
        /// 处理DeletedRun内的InsertedRun嵌套情况
        /// </summary>
        public void HandleNestedInsertInDelete(OpenXmlElement deletedRun, Run run, InsertedRun newInsertedRun)
        {
            context.Logger.LogInformation("处理DeletedRun内的InsertedRun嵌套情况");

            // 找到包含run的InsertedRun
            var existingInsertedRun = run.Ancestors<InsertedRun>().First();
            context.Logger.LogInformation($"找到现有的InsertedRun: InnerText='{existingInsertedRun.InnerText}'");

            // 在DeletedRun前插入新的InsertedRun
            deletedRun.Parent?.InsertBefore(newInsertedRun, deletedRun);
            context.Logger.LogInformation("在DeletedRun前插入新的InsertedRun");
        }

        /// <summary>
        /// 拆分修订元素并插入新文本
        /// </summary>
        /// <param name="revisionElement">要拆分的修订元素</param>
        /// <param name="run">当前Run</param>
        /// <param name="text">当前Text</param>
        /// <param name="offset">插入位置偏移量</param>
        /// <param name="insertedRun">要插入的InsertedRun</param>
        public void SplitRevisionElementAndInsert(OpenXmlElement revisionElement, Run run, Text text, int offset, InsertedRun insertedRun)
        {
            context.Logger.LogInformation($"拆分修订元素: 类型={revisionElement.GetType().Name}");

            // 保存原始修订信息
            var revisionId = RevisionPropertyHelper.GetRevisionId(revisionElement);
            var revisionAuthor = RevisionPropertyHelper.GetRevisionAuthor(revisionElement);
            var revisionDate = RevisionPropertyHelper.GetRevisionDate(revisionElement);

            context.Logger.LogInformation($"原始修订信息: ID={revisionId}, 作者={revisionAuthor}, 日期={(revisionDate != null ? revisionDate.Value : "null")}");

            // 克隆修订元素，用于创建前半部分和后半部分
            var beforeRevision = revisionElement.CloneNode(true);
            var afterRevision = revisionElement.CloneNode(true);

            // 确保克隆的元素保留原始修订信息
            RevisionPropertyHelper.SetRevisionProperties(beforeRevision, revisionId, revisionAuthor, revisionDate);
            RevisionPropertyHelper.SetRevisionProperties(afterRevision, revisionId, revisionAuthor, revisionDate);

            // 处理前半部分修订元素
            ProcessBeforeRevisionPart(beforeRevision, run, text, offset);

            // 处理后半部分修订元素
            ProcessAfterRevisionPart(afterRevision, run, text, offset);

            // 在原修订元素的位置插入：前半部分 + 新InsertedRun + 后半部分
            var parent = revisionElement.Parent;
            if (parent != null)
            {
                // 只有当前后部分都有内容时才插入
                if (OpenXmlElementComparer.HasContent(beforeRevision, context))
                {
                    context.Logger.LogInformation("插入前半部分修订元素");
                    parent.InsertBefore(beforeRevision, revisionElement);
                }
                else
                {
                    context.Logger.LogInformation("前半部分修订元素没有内容，不插入");
                }

                context.Logger.LogInformation("插入新的InsertedRun");
                parent.InsertBefore(insertedRun, revisionElement);

                if (OpenXmlElementComparer.HasContent(afterRevision, context))
                {
                    context.Logger.LogInformation("插入后半部分修订元素");
                    parent.InsertBefore(afterRevision, revisionElement);
                }
                else
                {
                    context.Logger.LogInformation("后半部分修订元素没有内容，不插入");
                }

                // 移除原修订元素
                context.Logger.LogInformation("移除原修订元素");
                revisionElement.Remove();
            }
        }

        /// <summary>
        /// 处理修订元素的前半部分
        /// </summary>
        public void ProcessBeforeRevisionPart(OpenXmlElement revisionElement, Run targetRun, Text targetText, int offset)
        {
            context.Logger.LogInformation("处理修订元素的前半部分");

            // 查找目标Run在修订元素中的位置
            var runs = revisionElement.Descendants<Run>().ToList();
            int targetIndex = -1;

            for (int i = 0; i < runs.Count; i++)
            {
                if (OpenXmlElementComparer.RunsAreEquivalent(runs[i], targetRun, context))
                {
                    targetIndex = i;
                    break;
                }
            }

            if (targetIndex == -1)
            {
                context.Logger.LogWarning("在修订元素中未找到目标Run");
                return;
            }

            context.Logger.LogInformation($"目标Run在修订元素中的索引: {targetIndex}");

            // 移除目标Run之后的所有Run
            for (int i = runs.Count - 1; i > targetIndex; i--)
            {
                context.Logger.LogInformation($"移除索引为{i}的Run");
                runs[i].Remove();
            }

            // 处理目标Run
            var targetRunInRevision = runs[targetIndex];
            var texts = targetRunInRevision.Elements<Text>().ToList();

            // 查找目标Text
            Text? targetTextInRevision = null;
            foreach (var t in texts)
            {
                if (OpenXmlElementComparer.TextsAreEquivalent(t, targetText))
                {
                    targetTextInRevision = t;
                    break;
                }
            }

            if (targetTextInRevision != null)
            {
                // 截取前半部分文本
                string originalText = targetTextInRevision.Text;
                string beforeText = originalText.Substring(0, offset);

                context.Logger.LogInformation($"截取前半部分文本: 原文='{originalText}', 截取='{beforeText}'");

                targetTextInRevision.Text = beforeText;
            }
            else
            {
                context.Logger.LogWarning("在目标Run中未找到目标Text");
            }
        }

        /// <summary>
        /// 处理修订元素的后半部分
        /// </summary>
        public void ProcessAfterRevisionPart(OpenXmlElement revisionElement, Run targetRun, Text targetText, int offset)
        {
            context.Logger.LogInformation("处理修订元素的后半部分");

            // 查找目标Run在修订元素中的位置
            var runs = revisionElement.Descendants<Run>().ToList();
            int targetIndex = -1;

            for (int i = 0; i < runs.Count; i++)
            {
                if (OpenXmlElementComparer.RunsAreEquivalent(runs[i], targetRun, context))
                {
                    targetIndex = i;
                    break;
                }
            }

            if (targetIndex == -1)
            {
                context.Logger.LogWarning("在修订元素中未找到目标Run");
                return;
            }

            context.Logger.LogInformation($"目标Run在修订元素中的索引: {targetIndex}");

            // 移除目标Run之前的所有Run
            for (int i = 0; i < targetIndex; i++)
            {
                context.Logger.LogInformation($"移除索引为{i}的Run");
                runs[i].Remove();
            }

            // 处理目标Run
            var targetRunInRevision = runs[targetIndex];
            var texts = targetRunInRevision.Elements<Text>().ToList();

            // 查找目标Text
            Text? targetTextInRevision = null;
            foreach (var t in texts)
            {
                if (OpenXmlElementComparer.TextsAreEquivalent(t, targetText))
                {
                    targetTextInRevision = t;
                    break;
                }
            }

            if (targetTextInRevision != null)
            {
                // 截取后半部分文本
                string originalText = targetTextInRevision.Text;
                string afterText = originalText.Substring(offset);

                context.Logger.LogInformation($"截取后半部分文本: 原文='{originalText}', 截取='{afterText}'");

                targetTextInRevision.Text = afterText;
            }
            else
            {
                context.Logger.LogWarning("在目标Run中未找到目标Text");
            }
        }

        /// <summary>
        /// 创建插入的修订元素
        /// </summary>
        /// <param name="text">要插入的文本</param>
        /// <param name="revision">修订信息</param>
        /// <param name="originalRun">原始Run（可选）</param>
        /// <returns>InsertedRun元素</returns>
        public InsertedRun CreateInsertedRun(string text, Revision revision, Run? originalRun = null)
        {
            // 获取下一个修订ID
            var revisionId = idManager.GetNextRevisionId();

            // 尝试使用工厂创建InsertedRun
            InsertedRun insertedRun;

            if (originalRun != null)
            {
                insertedRun = revisionFactory.CreateInsertedRunElement(
                    text,
                    revisionId.ToString(),
                    revision.Author,
                    revision.Date.ToString("yyyy-MM-ddTHH:mm:ss"),
                    originalRun);
            }
            else
            {
                insertedRun = revisionFactory.CreateInsertedRunElement(
                    text,
                    revisionId.ToString(),
                    revision.Author,
                    revision.Date.ToString("yyyy-MM-ddTHH:mm:ss"));
            }

            // 如果工厂返回null（例如在测试中），手动创建
            if (insertedRun == null)
            {
                // 创建InsertedRun手动
                insertedRun = new InsertedRun();

                // 设置修订属性
                insertedRun.Id = revisionId.ToString();
                insertedRun.Author = revision.Author;
                insertedRun.Date = new DateTimeValue(revision.Date);

                // 创建带有继承属性的文本运行
                Run textRun;
                if (originalRun?.RunProperties != null)
                {
                    // 克隆原始运行以保留属性
                    textRun = elementFactory?.CloneElement(originalRun, deepClone: false) ?? new Run();

                    // 添加新的文本内容
                    var textElement = new Text(text) { Space = SpaceProcessingModeValues.Preserve };
                    textRun.Append(textElement);
                }
                else
                {
                    // 没有要继承的属性，使用基本文本运行
                    textRun = elementFactory?.CreateTextRun(text) ?? new Run(new Text(text));
                }

                insertedRun.AppendChild(textRun);
            }

            return insertedRun;
        }
    }
}