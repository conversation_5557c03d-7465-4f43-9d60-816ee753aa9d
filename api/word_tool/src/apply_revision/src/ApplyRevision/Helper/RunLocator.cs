// ═══════════════════════════════════════════════════════════════════════════════════════
// RunLocator.cs - Advanced Word Document Run Navigation and Text Positioning System
// ═══════════════════════════════════════════════════════════════════════════════════════
//
// This file implements a sophisticated Run location system for Microsoft Word OpenXML documents.
// It provides precise text positioning, segment-based run extraction, and intelligent caching
// to support complex document manipulation operations.
//
// ┌─────────────────────────────────────────────────────────────────────────────────────┐
// │                              SYSTEM ARCHITECTURE                                   │
// ├─────────────────────────────────────────────────────────────────────────────────────┤
// │                                                                                     │
// │  Core Components:                                                                   │
// │  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐                 │
// │  │   Singleton     │    │   Caching       │    │   Run Location  │                 │
// │  │   Management    │    │   System        │    │   Engine        │                 │
// │  └─────────────────┘    └─────────────────┘    └─────────────────┘                 │
// │           │                       │                       │                        │
// │           └───────────────────────┼───────────────────────┘                        │
// │                                   │                                                │
// │  ┌─────────────────┐    ┌─────────▼─────────┐    ┌─────────────────┐                 │
// │  │   Position      │    │   RunLocator      │    │   Segment       │                 │
// │  │   Calculation   │    │   (Main Class)    │    │   Analysis      │                 │
// │  └─────────────────┘    └───────────────────┘    └─────────────────┘                 │
// │                                                                                     │
// │  Key Features:                                                                      │
// │  • SegId-based Run location ("paragraphId-runIndex" format)                        │
// │  • Dual-strategy approach (Enhanced + Original for compatibility)                  │
// │  • Intelligent caching for performance optimization                                │
// │  • Revision-aware run extraction (handles InsertedRun, DeletedRun, etc.)          │
// │  • Segment-level text positioning for complex operations                           │
// │  • Convert rules compliance for document transformation                            │
// │                                                                                     │
// │  Performance Characteristics:                                                      │
// │  • O(1) cached lookups for repeated access                                         │
// │  • O(n) initial paragraph/run discovery                                            │
// │  • Early termination optimization for position-based searches                      │
// │  • Memory-efficient caching with manual cleanup support                            │
// └─────────────────────────────────────────────────────────────────────────────────────┘
//
// ═══════════════════════════════════════════════════════════════════════════════════════

using Amazon.Lambda.Core;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// Represents the result of locating start and end runs for a text range operation.
    /// Contains the runs where the range starts and ends, along with their relative positions.
    /// </summary>
    public class RangeRunResult
    {
        /// <summary>
        /// The run containing the start position of the range
        /// </summary>
        public Run StartRun { get; set; } = null!;

        /// <summary>
        /// The run containing the end position of the range
        /// </summary>
        public Run EndRun { get; set; } = null!;

        /// <summary>
        /// The relative position within the start run where the range begins
        /// </summary>
        public int RelativeStartPosition { get; set; }

        /// <summary>
        /// The relative position within the end run where the range ends
        /// </summary>
        public int RelativeEndPosition { get; set; }

        /// <summary>
        /// Indicates whether the range spans across multiple runs
        /// </summary>
        public bool SpansMultipleRuns => !ReferenceEquals(StartRun, EndRun);

        public static RangeRunResult FromSingleRun(Run run)
        {
            return new RangeRunResult
            {
                StartRun = run,
                EndRun = run,
                RelativeStartPosition = 0,
                RelativeEndPosition = run.Elements<Text>().FirstOrDefault()?.Text?.Length ?? 0
            };
        }
    }

    /// <summary>
    /// Provides functionality to locate and navigate Run elements within Microsoft Word documents.
    /// This class facilitates the identification of specific text runs based on segment IDs and
    /// character positions, enabling precise text manipulation operations in OpenXML documents.
    /// Implemented as a singleton pattern to ensure consistent caching across the application.
    ///
    /// ┌─────────────────────────────────────────────────────────────────────────────────┐
    /// │                           RunLocator Architecture                               │
    /// ├─────────────────────────────────────────────────────────────────────────────────┤
    /// │  Singleton Pattern:                                                             │
    /// │  ┌─────────────────┐    GetInstance()    ┌─────────────────┐                   │
    /// │  │   Application   │ ──────────────────► │   RunLocator    │                   │
    /// │  │    Context      │                     │   (Singleton)   │                   │
    /// │  └─────────────────┘                     └─────────────────┘                   │
    /// │                                                   │                             │
    /// │  Caching System:                                  │                             │
    /// │  ┌─────────────────┐                             │                             │
    /// │  │ paragraphCache  │ ◄───────────────────────────┤                             │
    /// │  │ runCache        │                             │                             │
    /// │  └─────────────────┘                             │                             │
    /// │                                                   │                             │
    /// │  Core Operations:                                 │                             │
    /// │  ┌─────────────────┐                             │                             │
    /// │  │ FindRun()       │ ◄───────────────────────────┤                             │
    /// │  │ LocatePosition()│                             │                             │
    /// │  │ ExtractRuns()   │                             │                             │
    /// │  └─────────────────┘                             │                             │
    /// └─────────────────────────────────────────────────────────────────────────────────┘
    /// </summary>
    /// <remarks>
    /// The RunLocator uses a caching mechanism to improve performance when repeatedly accessing
    /// the same paragraphs. Segment IDs follow the format "paragraphId-runIndex" (e.g., "1-0"
    /// represents the first run in paragraph with ID "1").
    /// This class follows the singleton pattern to maintain a single instance with shared cache.
    ///
    /// SegId Format Structure:
    /// ┌─────────────────────────────────────────────────────────────────────────────────┐
    /// │                              SegId: "5-2"                                       │
    /// ├─────────────────────────────────────────────────────────────────────────────────┤
    /// │  ┌─────┐   ┌─────┐                                                              │
    /// │  │  5  │ - │  2  │                                                              │
    /// │  └─────┘   └─────┘                                                              │
    /// │     │         │                                                                │
    /// │     │         └─── Run Index (0-based): 3rd run in paragraph                  │
    /// │     └──────────── Paragraph ID: Paragraph with paraId="5"                     │
    /// │                                                                                │
    /// │  Document Structure:                                                           │
    /// │  <w:p w14:paraId="5">                                                          │
    /// │    <w:r>Run 0</w:r>  ◄── SegId "5-0"                                          │
    /// │    <w:r>Run 1</w:r>  ◄── SegId "5-1"                                          │
    /// │    <w:r>Run 2</w:r>  ◄── SegId "5-2" (Target)                                 │
    /// │    <w:r>Run 3</w:r>  ◄── SegId "5-3"                                          │
    /// │  </w:p>                                                                        │
    /// └─────────────────────────────────────────────────────────────────────────────────┘
    /// </remarks>
    /// <example>
    /// <code>
    /// var locator = RunLocator.GetInstance(lambdaContext);
    /// var run = locator.FindRun(document, "1-0"); // Find first run in paragraph ID "1"
    /// if (run != null)
    /// {
    ///     var (text, offset) = locator.LocatePosition(run, 5);
    /// }
    /// </code>
    /// </example>
    public sealed class RunLocator
    {
        /// <summary>
        /// The single instance of RunLocator (singleton pattern).
        /// </summary>
        private static RunLocator? _instance;

        /// <summary>
        /// Lock object for thread-safe singleton initialization.
        /// </summary>
        private static readonly object _lock = new();

        /// <summary>
        /// The Lambda context used for logging operations and error reporting.
        /// </summary>
        private ILambdaContext context;

        /// <summary>
        /// Cache for storing previously located paragraphs to improve performance
        /// when accessing the same paragraphs multiple times.
        /// Key: paragraph ID, Value: Paragraph element
        /// </summary>
        private readonly Dictionary<string, Paragraph> paragraphCache = [];

        private readonly Dictionary<string, Run> runCache = [];

        /// <summary>
        /// Microsoft Word 2010 XML namespace used for accessing Word-specific attributes.
        /// This namespace is required to access paraId attributes in Word documents.
        /// </summary>
        private static readonly string WORD2010_NAMESPACE = "http://schemas.microsoft.com/office/word/2010/wordml";

        /// <summary>
        /// Private constructor to prevent direct instantiation (singleton pattern).
        /// </summary>
        /// <param name="context">The Lambda context for logging and error reporting operations.</param>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="context"/> is null.</exception>
        private RunLocator(ILambdaContext context)
        {
            this.context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <summary>
        /// Gets the singleton instance of RunLocator.
        /// </summary>
        /// <param name="context">The Lambda context for logging and error reporting operations.</param>
        /// <returns>The singleton instance of RunLocator.</returns>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="context"/> is null.</exception>
        /// <remarks>
        /// This method implements thread-safe lazy initialization using double-checked locking pattern.
        /// If an instance already exists, the context parameter is used to update the internal context.
        /// </remarks>
        public static RunLocator GetInstance(ILambdaContext context)
        {
            ArgumentNullException.ThrowIfNull(context);

            if (_instance == null)
            {
                lock (_lock)
                {
                    _instance ??= new RunLocator(context);
                }
            }
            else
            {
                // Update context if instance already exists
                _instance.context = context;
            }

            return _instance;
        }

        /// <summary>
        /// Updates the Lambda context for the singleton instance.
        /// </summary>
        /// <param name="newContext">The new Lambda context to use.</param>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="newContext"/> is null.</exception>
        /// <remarks>
        /// This method allows updating the context without recreating the singleton instance,
        /// preserving the cached data while allowing different contexts to be used.
        /// </remarks>
        public void UpdateContext(ILambdaContext newContext)
        {
            this.context = newContext ?? throw new ArgumentNullException(nameof(newContext));
        }

        /// <summary>
        /// Locates a specific Run element within a Word document based on the provided segment ID.
        /// This is the main entry point for Run location with dual-strategy approach.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                           FindRun() Strategy Flow                               │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Input: segId "5-2"                                                             │
        /// │         ┌─────────────┐                                                         │
        /// │         │ Check Cache │                                                         │
        /// │         └─────┬───────┘                                                         │
        /// │               │                                                                 │
        /// │         ┌─────▼───────┐     Cache Hit    ┌─────────────┐                       │
        /// │         │ runCache    │ ─────────────────► Return Run  │                       │
        /// │         │ [segId]     │                  └─────────────┘                       │
        /// │         └─────┬───────┘                                                         │
        /// │               │ Cache Miss                                                      │
        /// │         ┌─────▼───────┐                                                         │
        /// │         │FindRunEnhanced│ ◄── Primary Strategy (Handles revision elements)     │
        /// │         └─────┬───────┘                                                         │
        /// │               │                                                                 │
        /// │         ┌─────▼───────┐     Success      ┌─────────────┐                       │
        /// │         │   Result    │ ─────────────────► Cache & Return                      │
        /// │         │   Found?    │                  └─────────────┘                       │
        /// │         └─────┬───────┘                                                         │
        /// │               │ Failure                                                         │
        /// │         ┌─────▼───────┐                                                         │
        /// │         │FindRunOriginal│ ◄── Fallback Strategy (Backward compatibility)       │
        /// │         └─────┬───────┘                                                         │
        /// │               │                                                                 │
        /// │         ┌─────▼───────┐                                                         │
        /// │         │Cache & Return│                                                        │
        /// │         └─────────────┘                                                         │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within.</param>
        /// <param name="segId">
        /// The segment identifier in the format "paragraphId-runIndex".
        /// For example, "1-0" represents the first run (index 0) in the paragraph with ID "1".
        /// </param>
        /// <returns>
        /// The located <see cref="Run"/> element if found; otherwise, <c>null</c>.
        /// </returns>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="doc"/> is null.</exception>
        /// <exception cref="ArgumentException">Thrown when <paramref name="segId"/> is null, empty, or has invalid format.</exception>
        /// <remarks>
        /// This method uses a two-step process:
        /// 1. Parse the segment ID to extract paragraph ID and run index
        /// 2. Locate the paragraph by ID, then find the run by index within that paragraph
        ///
        /// The method will log warnings for missing paragraphs or invalid run indices,
        /// and errors for malformed segment IDs or null document bodies.
        ///
        /// Performance Features:
        /// • Caching: Results are cached to avoid repeated lookups
        /// • Dual Strategy: Enhanced method handles complex scenarios, original for compatibility
        /// • Error Handling: Comprehensive logging for debugging
        /// </remarks>
        /// <example>
        /// <code>
        /// var run = locator.FindRun(document, "5-2"); // Find 3rd run in paragraph ID "5"
        /// if (run != null)
        /// {
        ///     // Process the located run
        ///     var text = run.Elements&lt;Text&gt;().FirstOrDefault()?.Text;
        /// }
        /// </code>
        /// </example>
        public Run? FindRun(WordprocessingDocument doc, string segId)
        {
            // 尝试从缓存中查找Run
            if (runCache.TryGetValue(segId, out Run? cachedRun))
            {
                return cachedRun;
            }

            // First try the enhanced method
            // 首先尝试使用增强版方法
            var enhancedResult = FindRunEnhanced(doc, segId);
            if (enhancedResult != null)
            {
                runCache[segId] = enhancedResult;
                return enhancedResult;
            }

            // Fall back to original implementation for backward compatibility
            // 为了向后兼容，回退到原始实现
            var originalResult = FindRunOriginal(doc, segId);
            if (originalResult != null)
            {
                runCache[segId] = originalResult;
                return originalResult;
            }
            return originalResult;
        }

        /// <summary>
        /// Enhanced Run locator that can find Runs within revision elements (InsertedRun, DeletedRun, etc.)
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within</param>
        /// <param name="segId">The segment identifier in format "paragraphId-runIndex"</param>
        /// <returns>The located Run element, including those within revision elements</returns>
        public Run? FindRunEnhanced(WordprocessingDocument doc, string segId)
        {
            if (string.IsNullOrEmpty(segId))
                throw new ArgumentException("Segment ID cannot be null or empty.", nameof(segId));

            if (doc.MainDocumentPart?.Document?.Body == null)
            {
                context.Logger.LogError("Document body is null.");
                return null;
            }

            // Parse segId
            var parts = segId.Split('-');
            if (parts.Length != 2)
            {
                context.Logger.LogError($"Invalid segId format: {segId}. Expected format: 'paragraphId-runIndex'");
                return null;
            }

            string paragraphId = parts[0];
            if (!int.TryParse(parts[1], out int runIndex))
            {
                context.Logger.LogError($"Invalid run index in segId: {segId}");
                return null;
            }

            // Find paragraph by paragraphId
            var paragraph = FindParagraphById(doc, paragraphId);
            if (paragraph == null)
            {
                context.Logger.LogWarning($"Paragraph with ID '{paragraphId}' not found.");
                return null;
            }

            var runList = GetRunsByConvertRules(paragraph).ToList();

            context.Logger.LogInformation($"Found {runList.Count} runs in paragraph (excluding comment references)");

            if (runIndex < 0 || runIndex >= runList.Count)
            {
                context.Logger.LogWarning($"Run index {runIndex} is out of range for paragraph with ID '{paragraphId}'. Total runs: {runList.Count}");
                return null;
            }

            var targetRun = runList[runIndex];
            context.Logger.LogInformation($"Located Run at index {runIndex}: '{GetRunText(targetRun)}'");

            return targetRun;
        }

        /// <summary>
        /// Original Run locator implementation (for backward compatibility)
        /// 原始Run定位器实现（用于向后兼容）
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within</param>
        /// <param name="segId">The segment identifier in format "paragraphId-runIndex"</param>
        /// <returns>The located Run element from direct paragraph children only</returns>
        [Obsolete("Use FindRunEnhanced instead.")]
        private Run? FindRunOriginal(WordprocessingDocument doc, string segId)
        {
            if (string.IsNullOrEmpty(segId))
                throw new ArgumentException("Segment ID cannot be null or empty.", nameof(segId));

            if (doc.MainDocumentPart?.Document?.Body == null)
            {
                context.Logger.LogError("Document body is null.");
                return null;
            }

            // Parse segId
            var parts = segId.Split('-');
            if (parts.Length != 2)
            {
                context.Logger.LogError($"Invalid segId format: {segId}. Expected format: 'paragraphId-runIndex'");
                return null;
            }

            string paragraphId = parts[0];
            if (!int.TryParse(parts[1], out int runIndex))
            {
                context.Logger.LogError($"Invalid run index in segId: {segId}");
                return null;
            }

            // Find paragraph by paragraphId
            var paragraph = FindParagraphById(doc, paragraphId);
            if (paragraph == null)
            {
                context.Logger.LogWarning($"Paragraph with ID '{paragraphId}' not found.");
                return null;
            }

            var runList = GetRunsByConvertRules(paragraph).ToList();
            if (runIndex < 0 || runIndex >= runList.Count)
            {
                context.Logger.LogWarning($"Run index {runIndex} is out of range for paragraph with ID '{paragraphId}'. Found {runList.Count} runs.");
                return null;
            }

            var targetRun = runList[runIndex];
            context.Logger.LogDebug($"Found run at index {runIndex} in paragraph. Selecting run: '{GetRunText(targetRun)}'");
            return targetRun;
        }

        /// <summary>
        /// Gets Run elements from paragraph elements based on convert extraction rules.
        /// This method implements the core logic for determining which runs should be included
        /// when converting Word documents, following specific rules for revision tracking elements.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                      Convert Rules Processing Flow                              │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Input: Paragraph with mixed content                                            │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ <w:p>                                                                   │   │
        /// │  │   <w:r>Direct Run</w:r>                    ✓ INCLUDE                   │   │
        /// │  │   <w:ins>                                                               │   │
        /// │  │     <w:r>Inserted Run</w:r>                ✓ INCLUDE                   │   │
        /// │  │   </w:ins>                                                              │   │
        /// │  │   <w:del>                                                               │   │
        /// │  │     <w:r>Deleted Run</w:r>                 ✗ EXCLUDE                   │   │
        /// │  │   </w:del>                                                              │   │
        /// │  │   <w:moveFrom>                                                          │   │
        /// │  │     <w:r>Moved From Run</w:r>              ✗ EXCLUDE                   │   │
        /// │  │   </w:moveFrom>                                                         │   │
        /// │  │   <w:moveTo>                                                            │   │
        /// │  │     <w:r>Moved To Run</w:r>                ✓ INCLUDE                   │   │
        /// │  │   </w:moveTo>                                                           │   │
        /// │  │   <w:customXml>                                                         │   │
        /// │  │     <w:r>Custom XML Run</w:r>              ✓ INCLUDE (via default)    │   │
        /// │  │   </w:customXml>                                                        │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Processing Strategy:                                                           │
        /// │  1. Direct Run elements → Include immediately                                  │
        /// │  2. InsertedRun → Include all descendant runs                                  │
        /// │  3. DeletedRun → Skip (continue to next element)                               │
        /// │  4. MoveFromRun → Skip (continue to next element)                              │
        /// │  5. MoveToRun → Include all descendant runs                                    │
        /// │  6. Other elements → Include all descendant runs (default case)               │
        /// │                                                                                 │
        /// │  Output: Ordered sequence of Run elements for conversion                       │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="paragraph">The paragraph to search</param>
        /// <returns>List of Run elements in document order, following convert rules (excludes deleted/moved-from runs)</returns>
        /// <remarks>
        /// This method is critical for maintaining consistency between document structure and
        /// text operations. It ensures that:
        ///
        /// • Deleted content is excluded from conversion operations
        /// • Moved content appears only at destination (moveTo), not source (moveFrom)
        /// • Inserted content is included in conversion
        /// • Comment references are preserved for offset calculation consistency
        ///
        /// The rules align with how Word displays content to users during document conversion.
        /// </remarks>
        public List<Run> GetRunsByConvertRules(Paragraph paragraph)
        {
            var runList = paragraph.Descendants<Run>().ToList();
            List<Run> resultList = [];
            foreach (var run in runList)
            {
                if (run.Parent is DeletedRun)
                {
                    // Skip runs that are children of DeletedRun elements
                    context.Logger.LogDebug($"Skipping run '{GetRunText(run)}' as it is part of a DeletedRun.");
                    continue;
                }
                resultList.Add(run);
            }
            return resultList;
        }

        /// <summary>
        /// Gets the text content of a Run element for logging purposes
        /// 获取Run元素的文本内容用于日志记录
        /// </summary>
        /// <param name="run">The Run element</param>
        /// <returns>The concatenated text content</returns>
        public static string GetRunText(Run run)
        {
            if (run == null) return string.Empty;

            // Only include Text elements, for position calculation consistency
            // 只包含Text元素，以保持位置计算的一致性
            return string.Join("", run.Descendants<Text>().Select(t => t.Text ?? ""));
        }

        /// <summary>
        /// Locates a paragraph within the document by its unique identifier.
        /// This method implements caching to improve performance for repeated paragraph lookups.
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within.</param>
        /// <param name="paragraphId">The unique identifier of the paragraph to locate.</param>
        /// <returns>
        /// The <see cref="Paragraph"/> element with the specified ID if found; otherwise, <c>null</c>.
        /// </returns>
        /// <remarks>
        /// This method searches for paragraphs using the Word 2010 namespace paraId attribute.
        /// Located paragraphs are cached to improve performance on subsequent lookups.
        /// The cache is stored in memory for the lifetime of the RunLocator instance.
        /// </remarks>
        private Paragraph? FindParagraphById(WordprocessingDocument doc, string paragraphId)
        {
            // Check cache first
            if (paragraphCache.TryGetValue(paragraphId, out Paragraph? cachedParagraph))
            {
                return cachedParagraph;
            }

            foreach (var paragraph in doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>() ?? [])
            {
                // Get the paragraph's ParaId attribute
                var paraIdAttr = paragraph.GetAttributes()
                    .FirstOrDefault(a => a.LocalName == "paraId" && a.NamespaceUri == WORD2010_NAMESPACE);

                // Check if attribute exists and has the matching value
                // OpenXmlAttribute is a struct, so we check if Value is not null/empty to determine if attribute was found
                if (!string.IsNullOrEmpty(paraIdAttr.Value) && paraIdAttr.Value == paragraphId)
                {
                    // Add to cache
                    paragraphCache[paragraphId] = paragraph;
                    return paragraph;
                }
            }

            return null;
        }

        /// <summary>
        /// Locates a specific character position within a Run element and returns the containing
        /// Text element along with the relative offset within that text.
        /// </summary>
        /// <param name="run">The Run element to search within.</param>
        /// <param name="position">
        /// The zero-based character position within the run to locate.
        /// Must be non-negative.
        /// </param>
        /// <returns>
        /// A tuple containing:
        /// - <c>text</c>: The <see cref="Text"/> element containing the specified position,
        ///   or <c>null</c> if the run contains no text elements.
        /// - <c>offset</c>: The zero-based character offset within the returned text element.
        ///   If the position exceeds the run's length, returns the length of the last text element.
        /// </returns>
        /// <exception cref="ArgumentNullException">Thrown when <paramref name="run"/> is null.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when <paramref name="position"/> is negative.</exception>
        /// <remarks>
        /// This method iterates through all Text elements within the run, calculating cumulative
        /// character positions until it finds the text element containing the target position.
        /// If the position is beyond the run's total length, it returns the last text element
        /// and its length as the offset.
        /// </remarks>
        /// <example>
        /// <code>
        /// var (text, offset) = locator.LocatePosition(run, 15);
        /// if (text != null)
        /// {
        ///     // Insert text at the specific position
        ///     string beforeText = text.Text.Substring(0, offset);
        ///     string afterText = text.Text.Substring(offset);
        /// }
        /// </code>
        /// </example>
        public (Text? text, int offset) LocatePosition(Run run, int position)
        {
            ArgumentNullException.ThrowIfNull(run);

            if (position < 0)
                throw new ArgumentOutOfRangeException(nameof(position), "Position must be non-negative.");

            int currentPosition = 0;

            foreach (var text in run.Elements<Text>())
            {
                int textLength = text.Text?.Length ?? 0;

                if (currentPosition + textLength > position)
                {
                    return (text, position - currentPosition);
                }

                currentPosition += textLength;
            }

            // If position exceeds Run length, return the last Text and its length
            var lastText = run.Elements<Text>().LastOrDefault();
            return (lastText, lastText?.Text?.Length ?? 0);
        }

        /// <summary>
        /// Locates a specific Run element for a given position within a segment.
        /// This method supports finding a Run for a single position within a segment.
        /// For range operations spanning multiple runs, use LocateStartAndEndRunsBySegment instead.
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within</param>
        /// <param name="segment">The segment containing position information</param>
        /// <param name="relativeStart">Output: relative start position within the found Run</param>
        /// <param name="relativeEnd">Output: relative end position within the found Run</param>
        /// <returns>The Run containing the position, or null if not found</returns>
        public Run? LocateRunBySegment(WordprocessingDocument doc, Segment segment, out int relativeStart, out int relativeEnd)
        {
            relativeStart = 0;
            relativeEnd = 0;

            try
            {
                var targetSegId = segment.SegId;
                if (string.IsNullOrEmpty(targetSegId))
                {
                    context.Logger.LogError("Target SegId is null or empty");
                    return null;
                }

                var remainingStart = segment.Range?.Start;
                var remainingEnd = segment.Range?.End;
                if (remainingStart == null || remainingEnd == null)
                {
                    context.Logger.LogError("Range must not be null");
                    return null;
                }

                context.Logger.LogInformation($"Starting position calculation with remaining range: [{remainingStart}, {remainingEnd}]");

                var segmentRuns = GetRunsFromPrimaryToPosition(doc, targetSegId, remainingStart.Value);
                if (segmentRuns == null || segmentRuns.Count == 0)
                {
                    context.Logger.LogError($"No Runs found for SegId: {targetSegId}");
                    return null;
                }

                context.Logger.LogInformation($"Found {segmentRuns.Count} runs for SegId: {targetSegId}");
                foreach (var run in segmentRuns)
                {
                    var runText = GetRunText(run);
                    var runLength = runText.Length;

                    context.Logger.LogInformation($"Processing Run: '{runText}' (length: {runLength}), remaining range: [{remainingStart}, {remainingEnd}]");

                    if (runLength == 0)
                    {
                        context.Logger.LogDebug($"Skipping empty text Run: '{runText}'");
                        continue;
                    }

                    if (remainingStart < runLength)
                    {
                        relativeStart = remainingStart.Value;
                        relativeEnd = Math.Min(remainingEnd.Value, runLength);

                        context.Logger.LogInformation($"Found target Run containing position, relative position [{relativeStart}, {relativeEnd}]: '{runText}'");
                        LogRunTypeInfo(run);
                        return run;
                    }
                    else if (remainingStart == runLength)
                    {
                        relativeStart = runLength;
                        relativeEnd = runLength;

                        context.Logger.LogInformation($"Found target Run for insert at end position {runLength}, relative position {runLength}: '{runText}'");
                        LogRunTypeInfo(run);
                        return run;
                    }

                    remainingStart -= runLength;
                    remainingEnd -= runLength;

                    context.Logger.LogDebug($"After processing Run '{runText}', remaining range: [{remainingStart}, {remainingEnd}]");
                }
                context.Logger.LogError($"No suitable Run found for SegId: {targetSegId}");
                return null;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error finding Run with text for SegId operation: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Locates both start and end Run elements for a segment range that may span multiple runs.
        /// This improved version properly handles cases where the start and end positions are in different runs
        /// within the same logical segment.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                   Enhanced Start/End Run Location Strategy                      │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Scenario: SegId "3-1" with Range [5, 15] spans multiple runs                  │
        /// │                                                                                 │
        /// │  Segment Structure:                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Run[1]: "Hello " (pos 0-5)    ◄── Primary run (SegId: 3-1)             │   │
        /// │  │ Run[2]: "World " (pos 6-11)   ◄── Part of logical segment               │   │
        /// │  │ Run[3]: "Great!" (pos 12-17)  ◄── Part of logical segment               │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Start Position Location (pos 5):                                              │
        /// │  ├─ Run[1]: "Hello " (0-5) → pos 5 == runLength → startRun = Run[1]           │
        /// │  └─ relativeStartPos = 5 (at end of run)                                       │
        /// │                                                                                 │
        /// │  End Position Location (pos 15):                                               │
        /// │  ├─ Continue from Run[1] through Run[2] to Run[3]                              │
        /// │  ├─ Run[3]: "Great!" (12-17) → pos 15 in range → endRun = Run[3]              │
        /// │  └─ relativeEndPos = 3 (within "Great!")                                       │
        /// │                                                                                 │
        /// │  Result: startRun ≠ endRun, properly handles multi-run ranges                 │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within</param>
        /// <param name="segment">The segment containing range information</param>
        /// <param name="startRun">Output: The Run containing the start position</param>
        /// <param name="endRun">Output: The Run containing the end position</param>
        /// <param name="relativeStartPos">Output: Relative start position within startRun</param>
        /// <param name="relativeEndPos">Output: Relative end position within endRun</param>
        /// <returns>True if both runs were successfully located; false otherwise</returns>
        /// <remarks>
        /// This method solves the limitation identified in the TODO comment by:
        /// 1. Finding all runs that belong to the same logical segment
        /// 2. Separately locating the start position's run and relative position
        /// 3. Separately locating the end position's run and relative position
        /// 4. Supporting cases where start and end positions are in different runs
        /// 
        /// This enables proper handling of operations that span multiple runs within the same segment.
        /// </remarks>
        public bool LocateStartAndEndRunsBySegment(WordprocessingDocument doc, Segment segment,
            out Run? startRun, out Run? endRun, out int relativeStartPos, out int relativeEndPos)
        {
            startRun = null;
            endRun = null;
            relativeStartPos = 0;
            relativeEndPos = 0;

            try
            {
                var targetSegId = segment.SegId;
                if (string.IsNullOrEmpty(targetSegId))
                {
                    context.Logger.LogError("Target SegId is null or empty");
                    return false;
                }

                var startPosition = segment.Range?.Start;
                var endPosition = segment.Range?.End;
                if (startPosition == null || endPosition == null)
                {
                    context.Logger.LogError("Range must not be null");
                    return false;
                }

                context.Logger.LogInformation($"Locating start and end runs for SegId: {targetSegId}, Range: [{startPosition}, {endPosition}]");

                // Get all runs for the segment up to and beyond the end position
                var segmentRuns = GetRunsInRange(doc, targetSegId, startPosition.Value, endPosition.Value);
                if (segmentRuns == null || segmentRuns.Count == 0)
                {
                    context.Logger.LogError($"No Runs found for SegId: {targetSegId}");
                    return false;
                }

                context.Logger.LogInformation($"Found {segmentRuns.Count} runs for range analysis");

                int cumulativePosition = 0;
                bool startFound = false;
                bool endFound = false;

                // First, we need to get the actual starting position within the segment
                // by finding where the primary run begins
                var primaryRun = FindRun(doc, targetSegId);
                if (primaryRun == null)
                {
                    context.Logger.LogError($"Primary run not found for SegId: {targetSegId}");
                    return false;
                }

                // Get all runs for the segment to determine cumulative positions
                var allSegmentRuns = GetRunsFromPrimaryToPosition(doc, targetSegId, Math.Max(startPosition.Value, endPosition.Value));
                if (allSegmentRuns == null)
                {
                    context.Logger.LogError($"Could not get segment runs for SegId: {targetSegId}");
                    return false;
                }

                // Calculate positions and locate start and end runs
                foreach (var run in allSegmentRuns)
                {
                    var runText = GetRunText(run);
                    var runLength = runText.Length;
                    int runStart = cumulativePosition;
                    int runEnd = cumulativePosition + runLength;

                    context.Logger.LogDebug($"Analyzing Run: '{runText}' (length: {runLength}), position: [{runStart}, {runEnd})");

                    // Check for start position
                    if (!startFound && startPosition >= runStart && startPosition <= runEnd)
                    {
                        startRun = run;
                        relativeStartPos = startPosition.Value - runStart;
                        startFound = true;
                        context.Logger.LogInformation($"Found start run: '{runText}' at relative position {relativeStartPos}");
                        LogRunTypeInfo(run);
                    }

                    // Check for end position
                    if (!endFound && endPosition > runStart && endPosition <= runEnd)
                    {
                        endRun = run;
                        relativeEndPos = endPosition.Value - runStart;
                        endFound = true;
                        context.Logger.LogInformation($"Found end run: '{runText}' at relative position {relativeEndPos}");
                        LogRunTypeInfo(run);
                    }

                    // If both found, we can break early
                    if (startFound && endFound)
                    {
                        break;
                    }

                    cumulativePosition += runLength;
                }

                // Validate results
                if (!startFound)
                {
                    context.Logger.LogWarning($"Could not locate start position {startPosition} for SegId: {targetSegId}");
                    return false;
                }

                if (!endFound)
                {
                    context.Logger.LogWarning($"Could not locate end position {endPosition} for SegId: {targetSegId}");
                    return false;
                }

                context.Logger.LogInformation($"Successfully located both start and end runs. SpansMultipleRuns: {!ReferenceEquals(startRun, endRun)}");
                return true;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error locating start and end runs for SegId operation: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Finds the Run that contains actual text for executing an operation and calculates relative positions
        /// within the same SegId segment scope. This is a critical method for precise text manipulation operations.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                    FindRunWithTextForOperation Flow                             │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Input: Operation with SegId "5-0", Range [15, 20]                             │
        /// │                                                                                 │
        /// │  Step 1: Get Segment Runs                                                       │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ GetRunsForSameSegId("5-0", 15)                                         │   │
        /// │  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐                       │   │
        /// │  │ │ Run[0]  │ │ Run[1]  │ │ Run[2]  │ │ Run[3]  │                       │   │
        /// │  │ │"Hello " │ │"World"  │ │" is "   │ │"great!" │                       │   │
        /// │  │ │ 0-5     │ │ 6-10    │ │ 11-15   │ │ 16-21   │                       │   │
        /// │  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘                       │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Step 2: Position Calculation (Target: position 15)                            │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ remainingStart = 15, remainingEnd = 20                                 │   │
        /// │  │                                                                         │   │
        /// │  │ Process Run[0] "Hello " (length: 6)                                    │   │
        /// │  │ ├─ remainingStart (15) >= length (6) → Continue                        │   │
        /// │  │ ├─ remainingStart -= 6 → remainingStart = 9                            │   │
        /// │  │ └─ remainingEnd -= 6 → remainingEnd = 14                               │   │
        /// │  │                                                                         │   │
        /// │  │ Process Run[1] "World" (length: 5)                                     │   │
        /// │  │ ├─ remainingStart (9) >= length (5) → Continue                         │   │
        /// │  │ ├─ remainingStart -= 5 → remainingStart = 4                            │   │
        /// │  │ └─ remainingEnd -= 5 → remainingEnd = 9                                │   │
        /// │  │                                                                         │   │
        /// │  │ Process Run[2] " is " (length: 4)                                      │   │
        /// │  │ ├─ remainingStart (4) >= length (4) → Continue                         │   │
        /// │  │ ├─ remainingStart -= 4 → remainingStart = 0                            │   │
        /// │  │ └─ remainingEnd -= 4 → remainingEnd = 5                                │   │
        /// │  │                                                                         │   │
        /// │  │ Process Run[3] "great!" (length: 6)                                    │   │
        /// │  │ ├─ remainingStart (0) < length (6) → FOUND TARGET RUN!                │   │
        /// │  │ ├─ relativeStart = 0                                                   │   │
        /// │  │ └─ relativeEnd = min(5, 6) = 5                                         │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Result: Run[3] with relative positions [0, 5] → "great"                      │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="doc">The WordprocessingDocument</param>
        /// <param name="operation">The operation to be applied</param>
        /// <param name="relativeStart">Output: relative start position within the found Run</param>
        /// <param name="relativeEnd">Output: relative end position within the found Run</param>
        /// <returns>The Run containing the text at the operation position, or null if not found</returns>
        /// <remarks>
        /// This method performs segment-level Run location by:
        /// 1. Finding all Runs that belong to the same SegId segment (logically related runs)
        /// 2. Calculating cumulative text positions across these segment-related Runs
        /// 3. Finding the Run that contains the target position within the segment scope
        /// 4. Calculating relative positions within that specific Run
        ///
        /// The operation Range is expected to be absolute positions within the specific SegId segment,
        /// not paragraph-level positions.
        ///
        /// Special Cases:
        /// • Insert operations: If position equals run length, selects that run for end insertion
        /// • Fallback strategy: Uses last available run if exact position not found
        /// • Error handling: Comprehensive logging for debugging position calculation issues
        /// </remarks>
        public Run? FindRunWithTextForOperation(WordprocessingDocument doc, ApplyRevision.Model.Operation operation, out int relativeStart, out int relativeEnd)
        {
            relativeStart = 0;
            relativeEnd = 0;

            try
            {
                var targetSegId = operation.Target.FirstOrDefault()?.SegId;
                if (string.IsNullOrEmpty(targetSegId))
                {
                    context.Logger.LogError("Target SegId is null or empty");
                    return null;
                }

                var targetRange = operation.Target.FirstOrDefault()?.Range;
                if (targetRange == null)
                {
                    context.Logger.LogError("Target range is null");
                    return null;
                }

                context.Logger.LogInformation($"Finding Run for operation with SegId: {targetSegId}, Range: [{targetRange.Start}, {targetRange.End}]");
                // 基于相对位置进行逐步计算，找到目标Run中的相对位置
                // Calculate step by step based on relative positions to find relative position in target Run
                int remainingStart = targetRange.Start;
                int remainingEnd = targetRange.End;
                context.Logger.LogInformation($"Starting position calculation with remaining range: [{remainingStart}, {remainingEnd}]");

                // 获取相同SegId的Run，从primary到开始位置
                // Get Runs that belong to the same SegId segment from primary to start position
                var segmentRuns = GetRunsFromPrimaryToPosition(doc, targetSegId, remainingStart);
                if (segmentRuns == null || segmentRuns.Count == 0)
                {
                    context.Logger.LogError($"No Runs found for SegId: {targetSegId}");
                    return null;
                }

                context.Logger.LogInformation($"Found {segmentRuns.Count} runs for SegId: {targetSegId}");
                foreach (var run in segmentRuns)
                {
                    var runText = GetRunText(run);
                    var runLength = runText.Length;

                    context.Logger.LogInformation($"Processing Run: '{runText}' (length: {runLength}), remaining range: [{remainingStart}, {remainingEnd}]");

                    // 如果剩余的起始位置在当前Run的范围内
                    // If remaining start position is within current Run's range
                    if (remainingStart < runLength)
                    {
                        // 计算在当前Run中的相对位置
                        // Calculate relative position within current Run
                        relativeStart = remainingStart;
                        relativeEnd = Math.Min(remainingEnd, runLength);

                        context.Logger.LogInformation($"Found target Run containing position, relative position [{relativeStart}, {relativeEnd}]: '{runText}'");

                        // 记录找到的Run的类型信息用于调试
                        // Log the type of Run found for debugging
                        LogRunTypeInfo(run);

                        return run;
                    }
                    else if (remainingStart == runLength)
                    {
                        // For boundary position, if it's a range operation and end position exceeds current run, adjust relative position
                        if (remainingEnd > runLength)
                        {
                            relativeStart = runLength - 1;
                            relativeEnd = runLength;
                        }
                        else
                        {
                            relativeStart = runLength;
                            relativeEnd = runLength;
                        }

                        context.Logger.LogInformation($"Found target Run for boundary position, relative position [{relativeStart}, {relativeEnd}]: '{runText}'");

                        LogRunTypeInfo(run);
                        return run;
                    }

                    // 减去当前Run的长度，继续查找下一个Run
                    // Subtract current Run's length and continue to next Run
                    remainingStart -= runLength;
                    remainingEnd -= runLength;

                    context.Logger.LogDebug($"After processing Run '{runText}', remaining range: [{remainingStart}, {remainingEnd}]");
                }

                // 如果没有找到合适的Run，使用最后一个Run并调整位置
                // If no suitable Run found, use the last Run and adjust positions
                var lastRun = segmentRuns.LastOrDefault();
                if (lastRun != null)
                {
                    var fallbackText = GetRunText(lastRun);
                    var lastRunLength = fallbackText.Length;

                    // 将位置调整到最后一个Run的末尾
                    // Adjust position to the end of last Run
                    relativeStart = Math.Min(remainingStart, lastRunLength);
                    relativeEnd = Math.Min(remainingEnd, lastRunLength);

                    context.Logger.LogWarning($"Using last Run as fallback with text: '{fallbackText}', adjusted relative positions [{relativeStart}, {relativeEnd}]");

                    LogRunTypeInfo(lastRun);
                    return lastRun;
                }

                context.Logger.LogError($"No suitable Run found for SegId: {targetSegId}");
                return null;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error finding Run with text for SegId operation: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets Runs that belong to the same SegId segment from primary run to the target position,
        /// including runs that were split due to operations like deletions, insertions, or formatting changes.
        /// This method handles the complex scenario where a logical text segment spans multiple Run elements.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                      Segment Run Collection Strategy                            │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Scenario: SegId "3-1" originally pointed to "Hello World"                     │
        /// │  After operations, text may be split across multiple runs:                     │
        /// │                                                                                 │
        /// │  Original Structure:                                                            │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ <w:p w14:paraId="3">                                                    │   │
        /// │  │   <w:r>Run 0: "Start"</w:r>                                             │   │
        /// │  │   <w:r>Run 1: "Hello World"</w:r>  ◄── SegId "3-1"                     │   │
        /// │  │   <w:r>Run 2: "End"</w:r>                                               │   │
        /// │  │ </w:p>                                                                  │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  After Text Operations (e.g., formatting, insertions):                         │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ <w:p w14:paraId="3">                                                    │   │
        /// │  │   <w:r>Run 0: "Start"</w:r>                                             │   │
        /// │  │   <w:r>Run 1: "Hello"</w:r>      ◄── Original SegId "3-1" now split   │   │
        /// │  │   <w:r>Run 1.1: " "</w:r>        ◄── Part of logical segment           │   │
        /// │  │   <w:r>Run 1.2: "World"</w:r>    ◄── Part of logical segment           │   │
        /// │  │   <w:r>Run 2: "End"</w:r>                                               │   │
        /// │  │ </w:p>                                                                  │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Collection Process (Target Position: 7):                                      │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ 1. Find Primary Run: "Hello" (Run 1)                                   │   │
        /// │  │ 2. Get All Paragraph Runs: [Run 0, Run 1, Run 1.1, Run 1.2, Run 2]    │   │
        /// │  │ 3. Start from Primary Run index (1)                                    │   │
        /// │  │ 4. Build Segment:                                                       │   │
        /// │  │    ├─ Add Run 1: "Hello" (pos 0-4, cumulative: 0-4)                   │   │
        /// │  │    ├─ Add Run 1.1: " " (pos 5-5, cumulative: 5-5)                     │   │
        /// │  │    ├─ Add Run 1.2: "World" (pos 6-10, cumulative: 6-10)               │   │
        /// │  │    └─ Target pos 7 found in Run 1.2 → STOP                            │   │
        /// │  │ 5. Return: [Run 1, Run 1.1, Run 1.2] up to target position            │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="doc">The WordprocessingDocument</param>
        /// <param name="segId">The segment ID to find runs for</param>
        /// <param name="targetPosition">The target position within the segment to find</param>
        /// <returns>List of Runs belonging to the same logical segment from primary run to target position, ordered by document position</returns>
        /// <remarks>
        /// This method finds runs that logically belong to the same segment from primary run to target position by:
        /// 1. Finding the primary run identified by the SegId
        /// 2. Getting runs in the same paragraph that follow the convert extraction rules
        /// 3. Building consecutive runs until the target position is found
        ///
        /// The method stops as soon as it finds the run containing the target position,
        /// making it efficient for finding the context leading up to a specific position.
        ///
        /// Key Features:
        /// • Handles run splitting due to formatting or editing operations
        /// • Uses ShouldIncludeRunInSegment heuristics to determine segment boundaries
        /// • Optimized early termination when target position is found
        /// • Maintains document order for consistent text position calculations
        /// • Provides context from primary run to target position
        /// </remarks>
        private List<Run>? GetRunsFromPrimaryToPosition(WordprocessingDocument doc, string segId, int targetPosition)
        {
            try
            {
                // 首先找到SegId对应的主要Run
                // First find the primary Run corresponding to the SegId
                var primaryRun = FindRun(doc, segId);
                if (primaryRun == null)
                {
                    context.Logger.LogWarning($"No primary Run found for SegId: {segId}");
                    return null;
                }

                // 获取包含此Run的段落
                // Get the paragraph containing this Run
                var paragraph = primaryRun.Ancestors<Paragraph>().FirstOrDefault();
                if (paragraph == null)
                {
                    context.Logger.LogError("Cannot find paragraph containing the primary Run");
                    return null;
                }

                // 获取段落中按convert规则提取的所有Run
                // Get all Runs in the paragraph following convert extraction rules
                var allParagraphRuns = GetRunsByConvertRules(paragraph).ToList();

                // 找到主要Run在段落中的位置
                // Find the position of primary Run in the paragraph
                int primaryRunIndex = allParagraphRuns.IndexOf(primaryRun);
                if (primaryRunIndex == -1)
                {
                    context.Logger.LogWarning($"Primary Run not found in paragraph runs for SegId: {segId}");
                    return [primaryRun]; // 回退到单个Run
                }

                // 构建逻辑片段的Run列表，直到找到包含目标位置的Run
                // Build the list of Runs for the logical segment until target position is found
                var segmentRuns = new List<Run>();
                int cumulativePosition = 0;

                // 从主要Run开始，向后查找直到找到目标位置
                // Starting from primary Run, search forward until target position is found
                for (int i = primaryRunIndex; i < allParagraphRuns.Count; i++)
                {
                    var currentRun = allParagraphRuns[i];

                    // 检查当前Run是否应该包含在片段中
                    // Check if current Run should be included in the segment
                    if (ShouldIncludeRunInSegment(currentRun, segmentRuns))
                    {
                        segmentRuns.Add(currentRun);

                        var runText = GetRunText(currentRun);
                        var runLength = runText.Length;

                        context.Logger.LogDebug($"Added Run to segment: '{runText}' (length: {runLength}), cumulative position: [{cumulativePosition}, {cumulativePosition + runLength})");

                        // 检查目标位置是否在当前Run中
                        // Check if target position is within current Run
                        if (targetPosition >= cumulativePosition && targetPosition < cumulativePosition + runLength)
                        {
                            context.Logger.LogInformation($"Found target position {targetPosition} in Run: '{runText}' at segment position [{cumulativePosition}, {cumulativePosition + runLength})");
                            break; // 找到目标Run，停止搜索
                        }
                        cumulativePosition += runLength;
                    }
                    else
                    {
                        // 不包含在片段中的Run（如评论引用），跳过但继续查找
                        // Skip runs not included in segment (like comment references) but continue searching
                        context.Logger.LogDebug($"Skipping Run not included in segment: '{GetRunText(currentRun)}'");
                    }
                }

                context.Logger.LogInformation($"Found {segmentRuns.Count} runs for logical segment of SegId: {segId} up to position {targetPosition}");
                return segmentRuns.Count > 0 ? segmentRuns : [primaryRun];
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error getting runs for SegId {segId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the start and end runs for a specified range within a segment, along with their relative positions.
        /// This method provides precise information about where a range begins and ends within the document structure.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                    Range Start/End Run Location Strategy                        │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Scenario 1: Range within single run                                           │
        /// │  SegId "3-1": [Run1: "Hello World Example"]                                    │
        /// │  Range [6, 11] -> StartRun=Run1(pos:6), EndRun=Run1(pos:11)                   │
        /// │                                                                                 │
        /// │  Scenario 2: Range spans multiple runs                                         │
        /// │  SegId "3-1": [Run1: "Hello "][Run2: "World "][Run3: "Example"]               │
        /// │  Range [3, 13] -> StartRun=Run1(pos:3), EndRun=Run3(pos:1)                    │
        /// │                                                                                 │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="doc">The Word document to search within</param>
        /// <param name="segId">The segment ID identifying the target text segment</param>
        /// <param name="startPosition">The inclusive start position of the range</param>
        /// <param name="endPosition">The exclusive end position of the range</param>
        /// <returns>
        /// A RangeRunResult containing the start and end runs with their relative positions,
        /// or null if the range cannot be located.
        /// </returns>
        /// <remarks>
        /// This method provides precise run and position information for range operations by:
        /// 1. Finding the primary run identified by the SegId
        /// 2. Traversing runs in the same paragraph to locate start and end positions
        /// 3. Calculating relative positions within each run
        /// 4. Returning both runs and their relative positions for precise range handling
        ///
        /// Key Features:
        /// • Handles both single-run and multi-run ranges
        /// • Provides exact relative positions within start and end runs
        /// • Uses ShouldIncludeRunInSegment heuristics for accurate segment boundaries
        /// • Maintains document order for consistent position calculations
        /// • Optimized for comment range operations and text manipulation
        /// </remarks>
        public RangeRunResult? GetRangeRunBySegment(WordprocessingDocument doc, Segment segment)
        {
            var segId = segment.SegId;
            if (segment.Range != null)
            {
                return GetRangeRunBySegmentId(doc, segId, segment.Range.Start, segment.Range.End);
            }
            else
            {
                var primaryRun = FindRun(doc, segId);
                if (primaryRun == null)
                {
                    context.Logger.LogWarning($"No primary Run found for SegId: {segId}");
                    return null;
                }
                return RangeRunResult.FromSingleRun(primaryRun);
            }
        }

        public RangeRunResult? GetRangeRunBySegmentId(WordprocessingDocument doc, string segId, int startPosition, int endPosition)
        {
            try
            {
                // Validate input parameters
                if (startPosition < 0 || endPosition < startPosition)
                {
                    context.Logger.LogWarning($"Invalid range [{startPosition}, {endPosition}] for SegId: {segId}");
                    return null;
                }

                // Find the primary run identified by the SegId
                var primaryRun = FindRun(doc, segId);
                if (primaryRun == null)
                {
                    context.Logger.LogWarning($"No primary Run found for SegId: {segId}");
                    return null;
                }

                context.Logger.LogInformation($"Locating range [{startPosition}, {endPosition}] start/end runs for SegId: {segId}");

                // Get the paragraph containing the primary run
                var paragraph = primaryRun.Ancestors<Paragraph>().FirstOrDefault();
                if (paragraph == null)
                {
                    context.Logger.LogError($"Primary Run for SegId {segId} is not within a paragraph");
                    return null;
                }

                // Get all runs in the paragraph that should be included in the segment
                var allRuns = GetRunsByConvertRules(paragraph).ToList();
                var primaryRunIndex = allRuns.IndexOf(primaryRun);

                if (primaryRunIndex == -1)
                {
                    context.Logger.LogError($"Primary Run not found in paragraph runs for SegId: {segId}");
                    return null;
                }

                // Start from the primary run and traverse to find start and end positions
                var segmentRuns = new List<Run>();
                int cumulativePosition = 0;
                Run? startRun = null;
                Run? endRun = null;
                int relativeStartPos = 0;
                int relativeEndPos = 0;

                // Traverse runs starting from the primary run
                for (int i = primaryRunIndex; i < allRuns.Count; i++)
                {
                    var currentRun = allRuns[i];

                    // Check if current run should be included in the segment
                    if (ShouldIncludeRunInSegment(currentRun, segmentRuns))
                    {
                        var runText = GetRunText(currentRun);
                        var runLength = runText.Length;
                        int runStart = cumulativePosition;
                        int runEnd = cumulativePosition + runLength;

                        context.Logger.LogDebug($"Evaluating Run: '{runText}' (length: {runLength}), position: [{runStart}, {runEnd})");

                        segmentRuns.Add(currentRun);

                        // Check if this run contains the start position
                        if (startRun == null && startPosition >= runStart && startPosition < runEnd)
                        {
                            startRun = currentRun;
                            relativeStartPos = startPosition - runStart;
                            context.Logger.LogInformation($"Found start run: '{runText}' at relative position {relativeStartPos}");
                        }

                        // Check if this run contains the end position
                        if (endRun == null && endPosition > runStart && endPosition <= runEnd)
                        {
                            endRun = currentRun;
                            relativeEndPos = endPosition - runStart;
                            context.Logger.LogInformation($"Found end run: '{runText}' at relative position {relativeEndPos}");
                            break; // Found end run, no need to continue
                        }

                        cumulativePosition += runLength;
                    }
                    else
                    {
                        context.Logger.LogDebug($"Skipping Run not included in segment: '{GetRunText(currentRun)}'");
                    }
                }

                // Validate that we found both start and end runs
                if (startRun == null)
                {
                    context.Logger.LogWarning($"Could not locate start position {startPosition} for SegId: {segId}");
                    return null;
                }

                if (endRun == null)
                {
                    context.Logger.LogWarning($"Could not locate end position {endPosition} for SegId: {segId}");
                    return null;
                }

                var result = new RangeRunResult
                {
                    StartRun = startRun,
                    EndRun = endRun,
                    RelativeStartPosition = relativeStartPos,
                    RelativeEndPosition = relativeEndPos
                };

                context.Logger.LogInformation($"Successfully located range runs - SpansMultipleRuns: {result.SpansMultipleRuns}");
                return result;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error getting range start/end runs for SegId {segId}: {ex.Message}");
                return null;
            }
        }

        public RangeRunResult? GetRangeRunByOperation(WordprocessingDocument doc, Operation operation)
        {
            var firstSegment = operation.Target.FirstOrDefault();
            var lastSegment = operation.Target.LastOrDefault();

            if (firstSegment == null || lastSegment == null)
            {
                context.Logger.LogWarning("Operation does not contain valid segments for range run retrieval.");
                return null;
            }

            try
            {
                Run? startRun = null;
                Run? endRun = null;
                int relativeStartPos = 0;
                int relativeEndPos = 0;

                // Handle different scenarios based on segment configuration
                if (firstSegment == lastSegment)
                {
                    // Same segment reference - use the improved method that can handle multi-run ranges
                    if (LocateStartAndEndRunsBySegment(doc, firstSegment, out startRun, out endRun, out relativeStartPos, out relativeEndPos))
                    {
                        context.Logger.LogInformation($"Located runs within single segment reference: {firstSegment.SegId}");
                    }
                    else
                    {
                        // Fallback to original method for backward compatibility
                        startRun = LocateRunBySegment(doc, firstSegment, out relativeStartPos, out relativeEndPos);
                        endRun = startRun;
                        context.Logger.LogInformation("Used fallback single-segment method");
                    }
                }
                else if (lastSegment.Range?.Start == 0 && lastSegment.Range.End == 0)
                {
                    // Special case where the last segment is empty - use improved method for the first segment
                    context.Logger.LogInformation("Last segment is empty, using improved method for first segment.");

                    if (LocateStartAndEndRunsBySegment(doc, firstSegment, out startRun, out endRun, out relativeStartPos, out relativeEndPos))
                    {
                        context.Logger.LogInformation($"Successfully located start and end runs within segment: {firstSegment.SegId}");
                    }
                    else
                    {
                        // Fallback to original method
                        startRun = LocateRunBySegment(doc, firstSegment, out relativeStartPos, out relativeEndPos);
                        endRun = startRun;
                        context.Logger.LogInformation("Used fallback method for empty last segment scenario");
                    }
                }
                else
                {
                    // Different segments - locate each segment separately
                    startRun = LocateRunBySegment(doc, firstSegment, out relativeStartPos, out _);
                    endRun = LocateRunBySegment(doc, lastSegment, out _, out relativeEndPos);
                }

                if (startRun == null || endRun == null)
                {
                    context.Logger.LogWarning($"Could not locate start or end run for segments {firstSegment.SegId}, {lastSegment.SegId}");
                    return null;
                }

                // Create and return the result
                var result = new RangeRunResult
                {
                    StartRun = startRun,
                    EndRun = endRun,
                    RelativeStartPosition = relativeStartPos,
                    RelativeEndPosition = relativeEndPos,
                };

                context.Logger.LogInformation($"Successfully located range runs - SpansMultipleRuns: {result.SpansMultipleRuns}");
                return result;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error getting range runs for operation: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets Runs that span from startPosition to endPosition within the same SegId segment,
        /// including runs that were split due to operations like deletions, insertions, or formatting changes.
        /// This method collects all runs that intersect with the specified range.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                    Range-Based Run Collection Strategy                          │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Scenario: SegId "3-1" text spans multiple runs after operations               │
        /// │                                                                                 │
        /// │  Segment Structure:                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Run[1]: "Hello " (pos 0-5)    ◄── Primary run (SegId: 3-1)             │   │
        /// │  │ Run[2]: "World " (pos 6-11)   ◄── Part of logical segment               │   │
        /// │  │ Run[3]: "Great!" (pos 12-17)  ◄── Part of logical segment               │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Collection Process for Range [8, 15]:                                         │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ 1. Find Primary Run: "Hello " (Run 1)                                  │   │
        /// │  │ 2. Get All Paragraph Runs: [Run 1, Run 2, Run 3]                       │   │
        /// │  │ 3. Start from Primary Run index                                         │   │
        /// │  │ 4. Collect Runs that intersect with [8, 15]:                           │   │
        /// │  │    ├─ Run 1: "Hello " (0-5) → No intersection, skip                    │   │
        /// │  │    ├─ Run 2: "World " (6-11) → Intersects [8, 11], include            │   │
        /// │  │    └─ Run 3: "Great!" (12-17) → Intersects [12, 15], include          │   │
        /// │  │ 5. Return: [Run 2, Run 3] covering range [8, 15]                       │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="doc">The WordprocessingDocument</param>
        /// <param name="segId">The segment ID to find runs for</param>
        /// <param name="startPosition">The start position of the range within the segment</param>
        /// <param name="endPosition">The end position of the range within the segment</param>
        /// <returns>List of Runs that intersect with the specified range, ordered by document position</returns>
        /// <remarks>
        /// This method finds runs that intersect with the specified range [startPosition, endPosition] by:
        /// 1. Finding the primary run identified by the SegId
        /// 2. Getting runs in the same paragraph that follow the convert extraction rules
        /// 3. Collecting all runs that have any overlap with the specified range
        ///
        /// The method continues until all runs that intersect with the range are collected,
        /// ensuring that operations spanning multiple runs are handled correctly.
        ///
        /// Key Features:
        /// • Handles run splitting due to formatting or editing operations
        /// • Uses ShouldIncludeRunInSegment heuristics to determine segment boundaries
        /// • Collects all runs that intersect with the specified range
        /// • Maintains document order for consistent text position calculations
        /// • Supports operations that span across multiple runs
        /// • Optimized to only include runs that actually intersect with the range
        /// </remarks>
        public List<Run>? GetRunsInRange(WordprocessingDocument doc, string segId, int startPosition, int endPosition)
        {
            try
            {
                // 首先找到SegId对应的主要Run
                // First find the primary Run corresponding to the SegId
                var primaryRun = FindRun(doc, segId);
                if (primaryRun == null)
                {
                    context.Logger.LogWarning($"No primary Run found for SegId: {segId}");
                    return null;
                }

                // 获取包含此Run的段落
                // Get the paragraph containing this Run
                var paragraph = primaryRun.Ancestors<Paragraph>().FirstOrDefault();
                if (paragraph == null)
                {
                    context.Logger.LogError("Cannot find paragraph containing the primary Run");
                    return null;
                }

                // 获取段落中按convert规则提取的所有Run
                // Get all Runs in the paragraph following convert extraction rules
                var allParagraphRuns = GetRunsByConvertRules(paragraph).ToList();

                // 找到主要Run在段落中的位置
                // Find the position of primary Run in the paragraph
                int primaryRunIndex = allParagraphRuns.IndexOf(primaryRun);
                if (primaryRunIndex == -1)
                {
                    context.Logger.LogWarning($"Primary Run not found in paragraph runs for SegId: {segId}");
                    return [primaryRun]; // 回退到单个Run
                }

                // 构建逻辑片段的Run列表，收集与指定范围相交的所有Run
                // Build the list of Runs for the logical segment that intersect with the specified range
                var segmentRuns = new List<Run>();
                int cumulativePosition = 0;
                bool foundStart = false;

                context.Logger.LogInformation($"Collecting runs for range [{startPosition}, {endPosition}] in SegId: {segId}");

                // 从主要Run开始，向后查找所有与范围相交的Run
                // Starting from primary Run, search forward for all runs that intersect with the range
                for (int i = primaryRunIndex; i < allParagraphRuns.Count; i++)
                {
                    var currentRun = allParagraphRuns[i];

                    // 检查当前Run是否应该包含在片段中
                    // Check if current Run should be included in the segment
                    if (ShouldIncludeRunInSegment(currentRun, segmentRuns.Count == 0 ? [] : segmentRuns))
                    {
                        var runText = GetRunText(currentRun);
                        var runLength = runText.Length;
                        int runStart = cumulativePosition;
                        int runEnd = cumulativePosition + runLength;

                        context.Logger.LogDebug($"Evaluating Run: '{runText}' (length: {runLength}), position: [{runStart}, {runEnd})");

                        // 检查当前Run是否与目标范围相交
                        // Check if current Run intersects with the target range
                        bool intersects = !(runEnd <= startPosition || runStart >= endPosition);

                        if (intersects)
                        {
                            if (!foundStart)
                            {
                                foundStart = true;
                                context.Logger.LogInformation($"Found first intersecting Run at position [{runStart}, {runEnd}): '{runText}'");
                            }

                            segmentRuns.Add(currentRun);
                            context.Logger.LogDebug($"Added intersecting Run to segment: '{runText}' at position [{runStart}, {runEnd})");
                        }
                        else if (foundStart && runStart >= endPosition)
                        {
                            // 如果已经找到开始位置，且当前Run完全在结束位置之后，停止搜索
                            // If we've found the start and current Run is completely after end position, stop searching
                            context.Logger.LogDebug($"Run beyond end position, stopping search: '{runText}' at position [{runStart}, {runEnd})");
                            break;
                        }

                        cumulativePosition += runLength;
                    }
                    else
                    {
                        // 不包含在片段中的Run（如评论引用），跳过但继续查找
                        // Skip runs not included in segment (like comment references) but continue searching
                        context.Logger.LogDebug($"Skipping Run not included in segment: '{GetRunText(currentRun)}'");
                    }
                }

                context.Logger.LogInformation($"Found {segmentRuns.Count} runs intersecting with range [{startPosition}, {endPosition}] for SegId: {segId}");
                return segmentRuns.Count > 0 ? segmentRuns : null;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error getting runs in range [{startPosition}, {endPosition}] for SegId {segId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Determines whether a Run should be included in the current segment using intelligent heuristics.
        /// This method is crucial for maintaining logical text boundaries when runs have been split by operations.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                      Run Inclusion Decision Tree                                │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Input: Run to evaluate + Existing segment runs                                │
        /// │                                                                                 │
        /// │  Decision Flow:                                                                 │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Is this the first run?                                                  │   │
        /// │  │ (existingSegmentRuns.Count == 0)                                        │   │
        /// │  └─────────────────┬───────────────────────────────────────────────────────┘   │
        /// │                    │ YES                                                         │
        /// │                    ▼                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ INCLUDE (Primary run always included)                                  │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                    │ NO                                                          │
        /// │                    ▼                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Is this a comment reference run?                                        │   │
        /// │  │ (Contains CommentReference but no text)                                 │   │
        /// │  └─────────────────┬───────────────────────────────────────────────────────┘   │
        /// │                    │ YES                                                         │
        /// │                    ▼                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ EXCLUDE (Comment references don't contribute to text content)          │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                    │ NO                                                          │
        /// │                    ▼                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ Does the run contain actual text?                                       │   │
        /// │  │ (!string.IsNullOrEmpty(GetRunText(run)))                                │   │
        /// │  └─────────────────┬───────────────────────────────────────────────────────┘   │
        /// │                    │ YES                                                         │
        /// │                    ▼                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ INCLUDE (Text-bearing runs are part of the segment)                    │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                    │ NO                                                          │
        /// │                    ▼                                                             │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ EXCLUDE (Empty runs don't contribute to segment)                       │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <param name="run">The Run to evaluate</param>
        /// <param name="existingSegmentRuns">Runs already included in the segment</param>
        /// <returns>True if the Run should be included in the segment</returns>
        /// <remarks>
        /// This method uses heuristics to determine if runs belong to the same logical segment:
        /// 1. Always include the first run (primary run)
        /// 2. Include subsequent runs that contain actual text content
        /// 3. Skip comment references and empty runs but don't stop searching
        /// 4. Continue until we find a clear segment boundary
        ///
        /// The logic may need refinement based on specific document structures and requirements.
        ///
        /// Examples of Run Types:
        /// • Text Run: <w:r><w:t>Hello</w:t></w:r> → INCLUDE
        /// • Comment Ref: <w:r><w:commentReference w:id="1"/></w:r> → EXCLUDE
        /// • Empty Run: <w:r><w:rPr>...</w:rPr></w:r> → EXCLUDE
        /// • Mixed Run: <w:r><w:t>Text</w:t><w:commentReference w:id="1"/></w:r> → INCLUDE
        /// </remarks>
        private bool ShouldIncludeRunInSegment(Run run, List<Run> existingSegmentRuns)
        {
            // 总是包含第一个Run（主要Run）
            // Always include the first Run (primary Run)
            if (existingSegmentRuns.Count == 0)
            {
                return true;
            }

            // 检查Run是否是评论引用（通常不包含实际文本内容）
            // Check if Run is a comment reference (usually doesn't contain actual text content)
            if (IsCommentReferenceRun(run))
            {
                context.Logger.LogDebug("Excluding comment reference Run from segment");
                return false;
            }

            // 检查Run是否包含实际文本内容
            // Check if Run contains actual text content
            var runText = GetRunText(run);
            if (string.IsNullOrEmpty(runText))
            {
                context.Logger.LogDebug("Excluding empty Run from segment");
                return false;
            }

            // 包含有文本内容的Run
            // Include runs with text content
            return true;
        }

        /// <summary>
        /// Checks if a Run is a comment reference run
        /// 检查Run是否是评论引用Run
        /// </summary>
        /// <param name="run">The Run to check</param>
        /// <returns>True if the Run contains only comment reference elements</returns>
        private static bool IsCommentReferenceRun(Run run)
        {
            if (run == null) return false;

            // 检查Run是否只包含CommentReference元素
            // Check if Run contains only CommentReference elements
            var hasCommentReference = run.Elements<CommentReference>().Any();
            var hasText = run.Elements<Text>().Any(t => !string.IsNullOrEmpty(t.Text));

            // 如果有CommentReference但没有实际文本，认为是评论引用Run
            // If has CommentReference but no actual text, consider it a comment reference Run
            return hasCommentReference && !hasText;
        }

        /// <summary>
        /// Extracts all runs corresponding to segIds and returns them in a dictionary
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within</param>
        /// <param name="segIds">Collection of segment IDs to extract runs for</param>
        /// <returns>Dictionary where key is segId and value is the corresponding Run element</returns>
        /// <exception cref="ArgumentNullException">Thrown when doc or segIds is null</exception>
        /// <remarks>
        /// This method uses the same logic as RunLocator to find runs, ensuring consistency
        /// with other operations. If a segId cannot be found, it will be logged but not included
        /// in the result dictionary.
        /// </remarks>
        public Dictionary<string, Run> ExtractRunsBySegIds(WordprocessingDocument doc, IEnumerable<string> segIds)
        {
            var result = new Dictionary<string, Run>();
            var segIdList = segIds.ToList();

            context.Logger.LogInformation($"Starting extraction of runs for {segIdList.Count} segIds");

            foreach (var segId in segIdList)
            {
                if (string.IsNullOrEmpty(segId))
                {
                    context.Logger.LogWarning("Skipping null or empty segId");
                    continue;
                }

                try
                {
                    var run = FindRun(doc, segId);

                    if (run != null)
                    {
                        result[segId] = run;
                        context.Logger.LogDebug($"Successfully extracted run for segId: {segId}");
                    }
                    else
                    {
                        context.Logger.LogWarning($"Could not find run for segId: {segId}");
                    }
                }
                catch (Exception ex)
                {
                    context.Logger.LogError($"Error extracting run for segId '{segId}': {ex.Message}");
                }
            }

            context.Logger.LogInformation($"Successfully extracted {result.Count} runs out of {segIdList.Count} requested segIds");
            return result;
        }

        /// <summary>
        /// Extracts all runs corresponding to segIds from a WordTextPatch and returns them in a dictionary
        /// </summary>
        /// <param name="doc">The WordprocessingDocument to search within</param>
        /// <param name="patch">The patch containing operations with target segIds</param>
        /// <returns>Dictionary where key is segId and value is the corresponding Run element</returns>
        /// <exception cref="ArgumentNullException">Thrown when doc or patch is null</exception>
        /// <remarks>
        /// This method extracts unique segIds from all operations in the patch and then
        /// retrieves the corresponding runs. Duplicate segIds are automatically handled.
        /// </remarks>
        public Dictionary<string, Run> ExtractRunsFromPatch(WordprocessingDocument doc, WordTextPatch patch)
        {
            if (patch.Operations == null)
            {
                return [];
            }

            // Extract unique segIds from all operations in the patch
            var segIds = new HashSet<string>();

            foreach (var operation in patch.Operations)
            {
                foreach (var segment in operation.Target?.Segments ?? [])
                {
                    // Handle multiple segIds in target
                    if (!string.IsNullOrEmpty(segment.SegId))
                    {
                        segIds.Add(segment.SegId);
                    }
                }
            }

            context.Logger.LogInformation($"Extracted {segIds.Count} unique segIds from patch operations");

            return ExtractRunsBySegIds(doc, segIds);
        }

        /// <summary>
        /// Logs information about the Run type and its parent revision element (if any)
        /// 记录Run类型和其父修订元素（如果有）的信息
        /// </summary>
        /// <param name="run">The Run element to analyze</param>
        private void LogRunTypeInfo(Run run)
        {
            if (run?.Parent == null) return;

            var parentType = run.Parent.GetType().Name;
            context.Logger.LogDebug($"Run parent type: {parentType}");

            // 检查Run是否在修订元素内
            // Check if Run is within a revision element
            switch (run.Parent)
            {
                case InsertedRun insertedRun:
                    context.Logger.LogDebug($"Run is within InsertedRun (ID: {insertedRun.Id}, Author: {insertedRun.Author})");
                    break;
                case DeletedRun deletedRun:
                    context.Logger.LogDebug($"Run is within DeletedRun (ID: {deletedRun.Id}, Author: {deletedRun.Author})");
                    break;
                case MoveFromRun moveFromRun:
                    context.Logger.LogDebug($"Run is within MoveFromRun (ID: {moveFromRun.Id}, Author: {moveFromRun.Author})");
                    break;
                case MoveToRun moveToRun:
                    context.Logger.LogDebug($"Run is within MoveToRun (ID: {moveToRun.Id}, Author: {moveToRun.Author})");
                    break;
                case Paragraph:
                    context.Logger.LogDebug("Run is a direct child of Paragraph");
                    break;
                default:
                    context.Logger.LogDebug($"Run is within other element type: {parentType}");
                    break;
            }
        }

        /// <summary>
        /// Clears the internal paragraph and run caches, freeing memory and ensuring fresh lookups
        /// for subsequent search operations. This is essential for memory management and cache invalidation.
        ///
        /// ┌─────────────────────────────────────────────────────────────────────────────────┐
        /// │                           Cache Management Strategy                             │
        /// ├─────────────────────────────────────────────────────────────────────────────────┤
        /// │                                                                                 │
        /// │  Cache Structure:                                                               │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ paragraphCache: Dictionary<string, Paragraph>                           │   │
        /// │  │ ├─ Key: paragraphId (e.g., "5")                                         │   │
        /// │  │ └─ Value: Paragraph element reference                                   │   │
        /// │  │                                                                         │   │
        /// │  │ runCache: Dictionary<string, Run>                                       │   │
        /// │  │ ├─ Key: segId (e.g., "5-2")                                             │   │
        /// │  │ └─ Value: Run element reference                                         │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  Cache Lifecycle:                                                               │
        /// │  ┌─────────────────────────────────────────────────────────────────────────┐   │
        /// │  │ 1. First Access → Cache Miss → Lookup & Store                          │   │
        /// │  │ 2. Subsequent Access → Cache Hit → Return Cached                       │   │
        /// │  │ 3. Document Change → ClearCache() → Fresh Lookups                      │   │
        /// │  │ 4. Memory Pressure → ClearCache() → Free Memory                        │   │
        /// │  └─────────────────────────────────────────────────────────────────────────┘   │
        /// │                                                                                 │
        /// │  When to Clear Cache:                                                           │
        /// │  • Document structure modifications                                             │
        /// │  • Switching between different documents                                       │
        /// │  • Memory optimization after large operations                                  │
        /// │  • Test isolation (ensuring clean state)                                       │
        /// └─────────────────────────────────────────────────────────────────────────────────┘
        /// </summary>
        /// <remarks>
        /// This method should be called when you want to ensure that paragraph lookups
        /// will fetch fresh data from the document, or when you need to free memory
        /// used by the cache. This is particularly useful when working with multiple
        /// documents or when the document structure might have changed.
        ///
        /// Performance Impact:
        /// • Immediate: Frees memory used by cached elements
        /// • Short-term: Next lookups will be slower (cache miss)
        /// • Long-term: Prevents memory leaks and stale data issues
        /// </remarks>
        /// <example>
        /// <code>
        /// // After processing a large document, clear cache to free memory
        /// locator.ClearCache();
        ///
        /// // Or before switching to a different document
        /// locator.ClearCache();
        /// var newRun = locator.FindRun(newDocument, "1-0");
        /// </code>
        /// </example>
        public void ClearCache()
        {
            paragraphCache.Clear();
            runCache.Clear();
        }

        /// <summary>
        /// Resets the singleton instance for testing purposes.
        /// This method ensures test isolation by completely resetting the singleton instance
        /// and clearing all cached data.
        /// </summary>
        /// <remarks>
        /// This method is designed specifically for unit testing scenarios where test isolation
        /// is required. It completely resets the singleton instance and clears all caches,
        /// ensuring that each test starts with a fresh RunLocator instance.
        ///
        /// IMPORTANT: This method should only be called from test code, never from production code.
        /// </remarks>
        public static void ResetInstanceForTesting()
        {
            lock (_lock)
            {
                // Clear caches before resetting instance
                _instance?.ClearCache();
                _instance = null;
            }
        }

        /// <summary>
        /// Gets the current singleton instance if it exists, without creating a new one.
        /// This method is primarily used for testing purposes to check instance state.
        /// </summary>
        /// <returns>The current singleton instance, or null if no instance exists.</returns>
        internal static RunLocator? GetCurrentInstanceForTesting()
        {
            return _instance;
        }
    }
}
