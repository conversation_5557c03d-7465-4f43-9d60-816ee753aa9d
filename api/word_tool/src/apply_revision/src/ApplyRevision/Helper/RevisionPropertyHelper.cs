using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using System;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// 处理修订元素属性的工具类
    /// </summary>
    public static class RevisionPropertyHelper
    {
        /// <summary>
        /// 获取修订元素的ID
        /// </summary>
        /// <param name="revisionElement">修订元素</param>
        /// <returns>修订元素的ID，如果不存在则返回null</returns>
        public static string? GetRevisionId(OpenXmlElement revisionElement)
        {
            if (revisionElement is DeletedRun deletedRun)
                return deletedRun.Id?.Value;
            if (revisionElement is InsertedRun insertedRun)
                return insertedRun.Id?.Value;
            if (revisionElement is MoveFromRun moveFromRun)
                return moveFromRun.Id?.Value;
            if (revisionElement is MoveToRun moveToRun)
                return moveToRun.Id?.Value;

            return null;
        }

        /// <summary>
        /// 获取修订元素的作者
        /// </summary>
        /// <param name="revisionElement">修订元素</param>
        /// <returns>修订元素的作者，如果不存在则返回null</returns>
        public static string? GetRevisionAuthor(OpenXmlElement revisionElement)
        {
            if (revisionElement is DeletedRun deletedRun)
                return deletedRun.Author?.Value;
            if (revisionElement is InsertedRun insertedRun)
                return insertedRun.Author?.Value;
            if (revisionElement is MoveFromRun moveFromRun)
                return moveFromRun.Author?.Value;
            if (revisionElement is MoveToRun moveToRun)
                return moveToRun.Author?.Value;

            return null;
        }

        /// <summary>
        /// 获取修订元素的日期
        /// </summary>
        /// <param name="revisionElement">修订元素</param>
        /// <returns>修订元素的日期，如果不存在则返回null</returns>
        public static DateTimeValue? GetRevisionDate(OpenXmlElement revisionElement)
        {
            if (revisionElement is DeletedRun deletedRun)
                return deletedRun.Date;
            if (revisionElement is InsertedRun insertedRun)
                return insertedRun.Date;
            if (revisionElement is MoveFromRun moveFromRun)
                return moveFromRun.Date;
            if (revisionElement is MoveToRun moveToRun)
                return moveToRun.Date;

            return null;
        }

        /// <summary>
        /// 设置修订元素的属性
        /// </summary>
        /// <param name="revisionElement">修订元素</param>
        /// <param name="id">修订ID</param>
        /// <param name="author">作者</param>
        /// <param name="date">日期</param>
        public static void SetRevisionProperties(OpenXmlElement revisionElement, string? id, string? author, DateTimeValue? date)
        {
            if (id == null || author == null)
                return;

            if (revisionElement is DeletedRun deletedRun)
            {
                deletedRun.Id = id;
                deletedRun.Author = author;
                deletedRun.Date = date;
            }
            else if (revisionElement is InsertedRun insertedRun)
            {
                insertedRun.Id = id;
                insertedRun.Author = author;
                insertedRun.Date = date;
            }
            else if (revisionElement is MoveFromRun moveFromRun)
            {
                moveFromRun.Id = id;
                moveFromRun.Author = author;
                moveFromRun.Date = date;
            }
            else if (revisionElement is MoveToRun moveToRun)
            {
                moveToRun.Id = id;
                moveToRun.Author = author;
                moveToRun.Date = date;
            }
        }
    }
}