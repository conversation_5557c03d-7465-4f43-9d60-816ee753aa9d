using Amazon.Lambda.Core;
using ApplyRevision.Model;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// Handles offset calculations and position updates for consecutive operations
    /// 处理连续操作的偏移量计算和位置更新
    /// </summary>
    /// <remarks>
    /// This class encapsulates the complex logic for updating operation positions
    /// when applying consecutive operations to a document. It handles different
    /// operation types (Insert, Delete, Replace) and their impact on subsequent
    /// operations within the same segment.
    /// 
    /// Key Features:
    /// - Calculates position adjustments based on operation type and offset change
    /// - Handles overlapping ranges and edge cases
    /// - Supports operations within the same paragraph/segment only
    /// - Provides detailed logging for debugging complex scenarios
    /// </remarks>
    /// <remarks>
    /// Initializes a new instance of the OperationOffsetCalculator
    /// 初始化操作偏移量计算器的新实例
    /// </remarks>
    /// <param name="context">Lambda context for logging</param>
    public class OperationOffsetCalculator(ILambdaContext context)
    {
        private readonly ILambdaContext context = context ?? throw new ArgumentNullException(nameof(context));

        /// <summary>
        /// Recalculates text ranges for operations within the same segment after a completed operation
        /// 根据已完成操作的偏移量重新计算相同片段中操作的文本范围
        /// </summary>
        /// <param name="remainingOperations">剩余操作的列表 / List of remaining operations</param>
        /// <param name="completedOperation">已完成的操作 / The completed operation</param>
        /// <param name="offsetChange">偏移变化量 / Offset change amount</param>
        public void RecalculateTextRangesAfterOperation(List<Operation> remainingOperations, Operation completedOperation, int offsetChange)
        {
            // 只有 insert 和 delete 操作会导致同一个 segment 中的文本发生偏移
            // 而 insert 和 delete 存在且只能存在一个 segment
            if (completedOperation.GetType() != typeof(Operation) ||
                (completedOperation.Op != OperationType.Insert &&
                 completedOperation.Op != OperationType.Delete &&
                 completedOperation.Op != OperationType.Replace))
            {
                context.Logger.LogInformation($"Completed operation is not Insert/Delete/Replace, skipping position updates");
                return; // 只处理插入、删除和替换操作
            }
            if (remainingOperations == null || remainingOperations.Count == 0)
            {
                context.Logger.LogInformation("No remaining operations to process, skipping position updates");
                return; // 没有剩余操作
            }
            if (offsetChange == 0)
            {
                context.Logger.LogInformation($"No position updates needed: offsetChange={offsetChange}, totalOps={remainingOperations.Count}");
                return; // 没有偏移变化或没有剩余操作
            }

            var completedSegment = completedOperation.Target.FirstOrDefault();
            if (completedSegment == null)
            {
                context.Logger.LogError("Completed operation has no target segment, skipping position updates");
                return;
            }
            var completedPosition = completedSegment.Range?.Start;
            if (completedPosition == null)
            {
                context.Logger.LogError("Completed operation has no valid range, skipping position updates");
                return;
            }

            // Filter operations with the same segId using select statement, excluding CommentReply
            var sameSegIdOperations = remainingOperations
                .Where(op => op?.Target?.Any(segment => segment.SegId == completedSegment.SegId) == true)
                .Where(op => !new[] { OperationType.CommentReply }.Contains(op.Op))
                .Select(op => op)
                .ToList();

            context.Logger.LogInformation($"Found {sameSegIdOperations.Count} operations with same SegId '{completedSegment.SegId}' to update");

            foreach (var operation in sameSegIdOperations)
            {
                if (operation == null || operation.Target == null || operation.Target.Count == 0)
                {
                    context.Logger.LogInformation($"Operation is null or has no target segments, skipping");
                    continue; // 跳过无效操作
                }

                foreach (var segment in operation.Target)
                {
                    var targetSegId = segment.SegId;

                    if (targetSegId != completedSegment.SegId)
                    {
                        context.Logger.LogInformation($"Skipping operation {operation.Op} for SegId {targetSegId}, not matching completed operation SegId {completedSegment.SegId}");
                        continue;
                    }

                    var positionUpdate = CalculatePositionUpdate(completedOperation.Op, segment, completedOperation, offsetChange);

                    if (positionUpdate.ShouldUpdate)
                    {
                        if (segment.Range == null)
                        {
                            context.Logger.LogError($"Segment {targetSegId} has no valid range, cannot update operation");
                            continue; // 跳过无效段落
                        }

                        // 更新操作的范围
                        var originalStart = segment.Range.Start;
                        var originalEnd = segment.Range.End;

                        segment.Range.Start = positionUpdate.NewStart;
                        segment.Range.End = positionUpdate.NewEnd;
                        context.Logger.LogInformation($"Updated operation {operation.Op} in SegId {targetSegId}: " +
                            $"[{originalStart}, {originalEnd}] → [{segment.Range.Start}, {segment.Range.End}]");
                    }
                    else
                    {
                        context.Logger.LogInformation($"No position update needed for operation {operation.Op} in SegId {targetSegId}");
                    }
                }
            }
        }

        /// <summary>
        /// Calculates position update for a single operation based on a completed operation
        /// 根据已完成的操作计算单个操作的位置更新
        /// </summary>
        /// <param name="completedOperationType">已完成操作的类型 / Type of the completed operation</param>
        /// <param name="targetSegment">要更新的操作 / Operation to update</param>
        /// <param name="completedOperation">已完成的操作 / Completed operation</param>
        /// <param name="offsetChange">偏移变化量 / Offset change amount</param>
        /// <returns>位置更新结果 / Position update result</returns>
        private PositionUpdateResult CalculatePositionUpdate(OperationType completedOperationType, Segment targetSegment, Operation completedOperation, int offsetChange)
        {
            var originalStart = targetSegment.Range?.Start;
            var originalEnd = targetSegment.Range?.End;
            var completedStart = completedOperation.Target?.FirstOrDefault()?.Range?.Start;
            var completedEnd = completedOperation.Target?.FirstOrDefault()?.Range?.End;
            var completedTextLength = completedOperation.Text?.Length ?? 0;

            if (originalStart == null || originalEnd == null || completedStart == null || completedEnd == null)
            {
                context.Logger.LogError("Invalid segment range or completed operation position, cannot calculate position update");
                return new PositionUpdateResult { ShouldUpdate = false, NewStart = originalStart ?? 0, NewEnd = originalEnd ?? 0 };
            }

            return completedOperationType switch
            {
                OperationType.Insert => CalculateInsertImpact(originalStart.Value, originalEnd.Value, completedStart.Value, offsetChange),
                OperationType.Delete => CalculateDeleteImpact(originalStart.Value, originalEnd.Value, completedStart.Value, offsetChange),
                OperationType.Replace => CalculateReplaceImpact(originalStart.Value, originalEnd.Value, completedStart.Value, completedEnd.Value, completedTextLength, offsetChange),
                OperationType.Format => new PositionUpdateResult { ShouldUpdate = false, NewStart = originalStart.Value, NewEnd = originalEnd.Value }, // Format operations don't change positions
                _ => new PositionUpdateResult { ShouldUpdate = false, NewStart = originalStart.Value, NewEnd = originalEnd.Value }
            };
        }

        /// <summary>
        /// Calculates position impact for insert Segment
        /// </summary>
        private PositionUpdateResult CalculateInsertImpact(int originalStart, int originalEnd, int insertPosition, int offsetChange)
        {
            // 插入操作：影响插入位置及之后的所有位置
            if (originalStart > insertPosition)
            {
                var newStart = originalStart + offsetChange;
                var newEnd = originalEnd + offsetChange;
                context.Logger.LogInformation($"  Insert case: position >= {insertPosition}, shifted both start and end by {offsetChange}");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            else if (originalStart == insertPosition)
            {
                var newStart = originalStart;
                var newEnd = originalEnd + offsetChange;
                context.Logger.LogInformation($"  Insert case: position == {insertPosition} && position == 0, shifted both by {offsetChange}");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            else if (originalEnd > insertPosition)
            {
                // 操作范围跨越插入点，只调整结束位置
                var newEnd = originalEnd + offsetChange;
                context.Logger.LogInformation($"  Insert case: range spans insertion point, shifted only end by {offsetChange}");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = originalStart, NewEnd = newEnd };
            }
            else
            {
                context.Logger.LogInformation($"  Insert case: position < {insertPosition}, no change needed");
                return new PositionUpdateResult { ShouldUpdate = false, NewStart = originalStart, NewEnd = originalEnd };
            }
        }

        /// <summary>
        /// Calculates position impact for delete operations
        /// 计算删除操作的位置影响
        /// </summary>
        private PositionUpdateResult CalculateDeleteImpact(int originalStart, int originalEnd, int deleteStart, int offsetChange)
        {
            var deleteEnd = deleteStart - offsetChange; // offsetChange 是负数

            context.Logger.LogInformation($"  Delete case: delete range [{deleteStart}, {deleteEnd}]");

            if (originalStart >= deleteEnd)
            {
                // 操作在删除范围之后，整体向前移动
                var newStart = originalStart + offsetChange;
                var newEnd = originalEnd + offsetChange;
                context.Logger.LogInformation($"  Delete case: position >= {deleteEnd}, shifted both by {offsetChange}");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            else if (originalStart >= deleteStart && originalStart < deleteEnd)
            {
                // 操作开始位置在删除范围内，调整到删除开始位置
                var adjustment = deleteStart - originalStart;
                var newStart = deleteStart;
                var newEnd = Math.Max(deleteStart, originalEnd + adjustment + offsetChange);
                context.Logger.LogInformation($"  Delete case: start in delete range, adjusted to deleteStart");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            else if (originalEnd > deleteStart)
            {
                // 操作范围与删除范围有重叠，调整结束位置
                if (originalEnd <= deleteEnd)
                {
                    // 结束位置在删除范围内
                    var newEnd = deleteStart;
                    context.Logger.LogInformation($"  Delete case: end in delete range, set end to deleteStart");
                    return new PositionUpdateResult { ShouldUpdate = true, NewStart = originalStart, NewEnd = newEnd };
                }
                else
                {
                    // 结束位置在删除范围之后
                    var newEnd = originalEnd + offsetChange;
                    context.Logger.LogInformation($"  Delete case: end after delete range, shifted end by {offsetChange}");
                    return new PositionUpdateResult { ShouldUpdate = true, NewStart = originalStart, NewEnd = newEnd };
                }
            }
            else
            {
                context.Logger.LogInformation($"  Delete case: no overlap, no change needed");
                return new PositionUpdateResult { ShouldUpdate = false, NewStart = originalStart, NewEnd = originalEnd };
            }
        }

        /// <summary>
        /// Calculates position impact for replace operations
        /// 计算替换操作的位置影响
        /// </summary>
        private PositionUpdateResult CalculateReplaceImpact(int originalStart, int originalEnd, int replaceStart, int replaceEnd, int replaceTextLength, int offsetChange)
        {
            context.Logger.LogInformation($"  Replace case: replace range [{replaceStart}, {replaceEnd}]");

            /* Position update scenario when operation occurs AFTER replace range:
             *     [Replace Range]      [Original Operation]
             *     ================     ==================
             *          ^         ^           ^          ^
             *     replaceStart replaceEnd originalStart originalEnd
             *     
             *     BEFORE: |----[REPLACED]----|----|----[OPERATION]----|
             *     AFTER:  |----[NEW_TEXT]---|----[OPERATION + OFFSET]----|
             *                               ^                             ^
             *                        newStart = originalStart + offsetChange
             *                                            newEnd = originalEnd + offsetChange
             * 
             *     Logic: Since the operation starts after the replace range ends,
             *            it's completely unaffected by the replacement content,
             *            but must be shifted by the offset change caused by
             *            the difference in text length.
             */
            if (originalStart >= replaceEnd)
            {
                var newStart = originalStart + offsetChange;
                var newEnd = originalEnd + offsetChange;
                context.Logger.LogInformation($"  Replace case: position >= {replaceEnd}, shifted both by {offsetChange}");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            /* Position update scenario when operation starts EXACTLY at replace range start:
             * 
             *     [Replace Range]
             *     ================
             *     ^               ^
             *  replaceStart   replaceEnd
             *     ^
             *  originalStart (same position)
             *     
             *     BEFORE: |----[REPLACED & OPERATION OVERLAP]----|
             *     AFTER:  |----[NEW_TEXT]----|----[OPERATION + OFFSET]----|
             *                              ^                               ^
             *                       newStart = originalStart + offsetChange
             *                                         newEnd = originalEnd + offsetChange
             * 
             *     Logic: When operation starts exactly at the replace start position,
             *            treat it as if the operation occurs after replacement.
             *            Both start and end positions are shifted by the offset change.
             */
            else if (originalStart == replaceStart)
            {
                var newStart = originalStart;
                int newEnd;
                if (originalEnd < replaceEnd)
                {
                    // Case 1: Operation end position is within replace range
                    // 操作结束位置在替换范围内
                    //
                    // BEFORE: |----[REPLACE RANGE]----|----[REMAINING]----|
                    //           ^               ^
                    //        replaceStart    replaceEnd
                    //           ^           ^
                    //        originalStart  originalEnd
                    //
                    // AFTER:  |----[NEW TEXT]----|----[REMAINING]----|
                    //           ^               ^
                    //        replaceStart    replaceEnd
                    //           ^           ^
                    //        newStart     newEnd
                    //
                    // Calculation: newEnd = originalEnd - originalStart + newStart
                    // This preserves the relative position within the replacement
                    newEnd = originalEnd - originalStart + newStart;
                }
                else
                {
                    // Case 2: Operation end position is at or after replace range end
                    // 操作结束位置在替换范围结束位置或之后
                    //
                    // BEFORE: |----[REPLACE RANGE]----|----[REMAINING]----|
                    //           ^               ^
                    //        replaceStart    replaceEnd
                    //           ^                           ^
                    //        originalStart                originalEnd
                    //
                    // AFTER:  |----[NEW TEXT]----|----[REMAINING]----|
                    //           ^               ^
                    //        replaceStart    replaceEnd
                    //           ^                           ^
                    //        newStart                     newEnd
                    //
                    // Calculation: newEnd = originalEnd + offsetChange
                    // This applies the full offset change to maintain position after replacement
                    newEnd = originalEnd + offsetChange;
                }
                context.Logger.LogInformation($"  Replace case: replaceStart == {replaceStart} && replaceEnd == {replaceEnd}, shifted both by {offsetChange}");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            /* Operation starts INSIDE replace range - reposition to end of new text:
             * 
             *     [Replace Range]
             *     ================
             *     ^      ^        ^
             * replaceStart  originalStart  replaceEnd
             *     
             *     BEFORE: |----[REPLACED TEXT WITH OPERATION INSIDE]----|
             *     AFTER:  |----[NEW_TEXT]----| -> [REPOSITIONED OPERATION]
             *                              ^                           ^
             *                       newStart = replaceStart + replaceTextLength
             *                                    newEnd = calculated with adjustment
             * 
             *     Logic: When operation starts inside replace range, move it to after
             *            the new replacement text to avoid conflict with replaced content.
             */
            else if (originalStart > replaceStart && originalStart < replaceEnd)
            {
                // 操作开始位置在替换范围内
                var adjustment = replaceStart - originalStart;
                var newStart = replaceStart + replaceTextLength;
                var newEnd = Math.Max(newStart, originalEnd + adjustment + offsetChange);
                context.Logger.LogInformation($"  Replace case: start in replace range, adjusted start");
                return new PositionUpdateResult { ShouldUpdate = true, NewStart = newStart, NewEnd = newEnd };
            }
            /* Operation overlaps with replace range - handle partial overlap scenarios:
             * 
             * Scenario A: Operation end falls WITHIN replace range
             *     [Replace Range]
             *     ================
             *     ^              ^
             * replaceStart   replaceEnd
             * ^         ^
             * originalStart  originalEnd (inside replace range)
             *     
             *     BEFORE: [OPERATION START]----[REPLACED OVERLAP]----|
             *     AFTER:  [OPERATION START]----[NEW_TEXT]------------|
             *                                ^
             *                         newEnd = replaceStart + replaceTextLength
             * 
             * Scenario B: Operation end falls AFTER replace range
             *     [Replace Range]      [Operation End]
             *     ================     ===============
             *     ^              ^           ^
             * replaceStart   replaceEnd  originalEnd
             * ^
             * originalStart (before replace)
             *     
             *     BEFORE: [OPERATION]----[REPLACED]----[OPERATION CONT.]----|
             *     AFTER:  [OPERATION]----[NEW_TEXT]----[OPERATION + OFFSET]----|
             *                                        ^
             *                                 newEnd = originalEnd + offsetChange
             */
            else if (originalEnd > replaceStart)
            {
                // 操作范围与替换范围有重叠
                if (originalEnd <= replaceEnd)
                {
                    // 结束位置在替换范围内
                    var newEnd = replaceStart + replaceTextLength;
                    context.Logger.LogInformation($"  Replace case: end in replace range, adjusted end");
                    return new PositionUpdateResult { ShouldUpdate = true, NewStart = originalStart, NewEnd = newEnd };
                }
                else
                {
                    // 结束位置在替换范围之后
                    var newEnd = originalEnd + offsetChange;
                    context.Logger.LogInformation($"  Replace case: end after replace range, shifted end by {offsetChange}");
                    return new PositionUpdateResult { ShouldUpdate = true, NewStart = originalStart, NewEnd = newEnd };
                }
            }
            /* Operation completely BEFORE replace range - no position change needed:
             * 
             *     [Original Operation]    [Replace Range]
             *     ===================     ================
             *     ^                 ^     ^              ^
             * originalStart   originalEnd  replaceStart  replaceEnd
             *     
             *     BEFORE: |----[OPERATION]----|----[TO BE REPLACED]----|
             *     AFTER:  |----[OPERATION]----|----[NEW_TEXT]----------|
             *                              ^                          ^
             *                       (no change)              (no impact on operation)
             * 
             *     Logic: Since operation occurs entirely before replace range,
             *            replacement doesn't affect operation positions at all.
             */
            else
            {
                context.Logger.LogInformation($"  Replace case: no overlap, no change needed");
                return new PositionUpdateResult { ShouldUpdate = false, NewStart = originalStart, NewEnd = originalEnd };
            }
        }

        /// <summary>
        /// Represents the result of a position update calculation
        /// 表示位置更新计算的结果
        /// </summary>
        private class PositionUpdateResult
        {
            public bool ShouldUpdate { get; set; }
            public int NewStart { get; set; }
            public int NewEnd { get; set; }
        }
    }
}