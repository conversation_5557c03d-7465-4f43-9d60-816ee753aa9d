using System;
using System.Collections.Generic;
using System.Linq;
using Amazon.Lambda.Core;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// 跟踪文档补丁应用后的位置偏移
    /// 
    /// 核心算法：
    /// 1. 记录每个操作对文档造成的位置偏移
    /// 2. 为后续操作计算调整后的正确位置
    /// 3. 特别处理相同原始位置的多个操作
    /// 
    /// 示例场景：
    /// 原文："Hello World"
    /// 操作1：在位置0插入"A" → "AHello World" (偏移+1)
    /// 操作2：在位置0插入"B" → 实际应在位置1插入 → "ABHello World"
    /// 操作3：在位置0插入"C" → 实际应在位置2插入 → "ABCHello World"
    /// </summary>
    public class PatchOffsetTracker
    {
        private readonly Dictionary<string, List<OffsetEntry>> offsetMap = [];
        private readonly ILambdaContext? context;

        public PatchOffsetTracker(ILambdaContext? context = null)
        {
            this.context = context;
        }

        /// <summary>
        /// 记录一个操作导致的偏移
        /// </summary>
        /// <param name="segId">段落ID</param>
        /// <param name="originalPosition">操作的原始位置</param>
        /// <param name="actualPosition">操作的实际执行位置</param>
        /// <param name="offsetChange">偏移变化量（插入为正，删除为负）</param>
        public void RecordOffset(string segId, int originalPosition, int actualPosition, int offsetChange)
        {
            if (!offsetMap.TryGetValue(segId, out List<OffsetEntry>? value))
            {
                value = [];
                offsetMap[segId] = value;
            }

            var entry = new OffsetEntry
            {
                OriginalPosition = originalPosition,
                ActualPosition = actualPosition,
                OffsetChange = offsetChange,
                Timestamp = DateTime.Now
            };

            value.Add(entry);

            // 按原始位置排序，然后按时间戳排序
            offsetMap[segId] = value.OrderBy(e => e.OriginalPosition)
                                    .ThenBy(e => e.Timestamp)
                                    .ToList();

            context?.Logger.LogInformation($"Recorded offset for SegId {segId}: " +
                $"OriginalPos={originalPosition}, ActualPos={actualPosition}, " +
                $"Change={offsetChange:+#;-#;0}");
        }

        /// <summary>
        /// 根据已记录的偏移调整操作位置
        /// 
        /// 算法说明：
        /// 1. 对于目标位置之前的操作，累加其偏移影响
        /// 2. 对于相同原始位置的操作，需要考虑先前相同位置操作的累积影响
        /// 3. 确保调整后的位置不会是负数
        /// </summary>
        /// <param name="segId">段落ID</param>
        /// <param name="originalPosition">原始位置</param>
        /// <returns>调整后的位置</returns>
        public int AdjustPosition(string segId, int originalPosition)
        {
            if (!offsetMap.TryGetValue(segId, out List<OffsetEntry>? entries) || entries.Count == 0)
            {
                context?.Logger.LogInformation($"No offset entries for SegId {segId}, position {originalPosition} unchanged");
                return originalPosition;
            }

            int adjustedPosition = originalPosition;
            int cumulativeOffset = 0;

            context?.Logger.LogInformation($"Adjusting position for SegId {segId}: " +
                $"OriginalPos={originalPosition}, {entries.Count} existing entries");

            foreach (var entry in entries)
            {
                context?.Logger.LogInformation($"  Considering entry: OriginalPos={entry.OriginalPosition}, " +
                    $"ActualPos={entry.ActualPosition}, Change={entry.OffsetChange}");

                // 只考虑在目标原始位置之前或相同位置的操作
                if (entry.OriginalPosition < originalPosition)
                {
                    // 位置在前面的操作，其偏移影响当前位置
                    cumulativeOffset += entry.OffsetChange;
                    context?.Logger.LogInformation($"    Applied offset (before): {entry.OffsetChange}, cumulative: {cumulativeOffset}");
                }
                else if (entry.OriginalPosition == originalPosition)
                {
                    // 相同原始位置的操作，当前操作应该在之前相同位置操作的影响之后
                    cumulativeOffset += entry.OffsetChange;
                    context?.Logger.LogInformation($"    Applied offset (same pos): {entry.OffsetChange}, cumulative: {cumulativeOffset}");
                }
                // 位置在后面的操作不影响当前位置
            }

            adjustedPosition = originalPosition + cumulativeOffset;
            
            // 确保位置不会是负数
            adjustedPosition = Math.Max(0, adjustedPosition);

            context?.Logger.LogInformation($"Position adjusted for SegId {segId}: " +
                $"{originalPosition} → {adjustedPosition} (offset: {cumulativeOffset:+#;-#;0})");

            return adjustedPosition;
        }

        /// <summary>
        /// 清除所有偏移记录
        /// </summary>
        public void Clear()
        {
            context?.Logger.LogInformation("Clearing all offset tracking records");
            offsetMap.Clear();
        }

        /// <summary>
        /// 获取指定段落的偏移信息（用于调试）
        /// </summary>
        /// <param name="segId">段落ID</param>
        /// <returns>偏移条目列表</returns>
        public List<OffsetEntry> GetOffsetEntries(string segId)
        {
            return offsetMap.TryGetValue(segId, out var entries) 
                ? new List<OffsetEntry>(entries) 
                : new List<OffsetEntry>();
        }

        /// <summary>
        /// 偏移条目
        /// </summary>
        public class OffsetEntry
        {
            public int OriginalPosition { get; set; }
            public int ActualPosition { get; set; }
            public int OffsetChange { get; set; }
            public DateTime Timestamp { get; set; }
        }
    }
}
