using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Linq;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// 比较OpenXml元素的工具类
    /// </summary>
    public static class OpenXmlElementComparer
    {
        /// <summary>
        /// 检查两个Run是否等价
        /// </summary>
        /// <param name="run1">第一个Run</param>
        /// <param name="run2">第二个Run</param>
        /// <param name="context">Lambda上下文，用于日志记录</param>
        /// <returns>如果两个Run等价，则返回true</returns>
        public static bool RunsAreEquivalent(Run run1, Run run2, ILambdaContext context)
        {
            // 比较InnerText
            string text1 = run1.InnerText;
            string text2 = run2.InnerText;

            // 比较RunProperties
            bool propertiesEqual = CompareRunProperties(run1.RunProperties, run2.RunProperties);

            bool result = text1 == text2 && propertiesEqual;
            context.Logger.LogInformation($"比较Run: text1='{text1}', text2='{text2}', 属性相等={propertiesEqual}, 结果={result}");

            return result;
        }

        /// <summary>
        /// 比较两个RunProperties
        /// </summary>
        /// <param name="props1">第一个RunProperties</param>
        /// <param name="props2">第二个RunProperties</param>
        /// <returns>如果两个RunProperties等价，则返回true</returns>
        public static bool CompareRunProperties(RunProperties? props1, RunProperties? props2)
        {
            // 如果两者都为null，则相等
            if (props1 == null && props2 == null)
                return true;

            // 如果只有一个为null，则不相等
            if (props1 == null || props2 == null)
                return false;

            // 简单比较：检查是否有相同的子元素类型
            var types1 = props1.ChildElements.Select(e => e.GetType()).ToList();
            var types2 = props2.ChildElements.Select(e => e.GetType()).ToList();

            return types1.Count == types2.Count &&
                   types1.All(t => types2.Contains(t)) &&
                   types2.All(t => types1.Contains(t));
        }

        /// <summary>
        /// 检查两个Text是否等价
        /// </summary>
        /// <param name="text1">第一个Text</param>
        /// <param name="text2">第二个Text</param>
        /// <returns>如果两个Text等价，则返回true</returns>
        public static bool TextsAreEquivalent(Text text1, Text text2)
        {
            return text1.Text == text2.Text &&
                   text1.Space == text2.Space;
        }

        /// <summary>
        /// 检查修订元素是否有内容
        /// </summary>
        /// <param name="element">要检查的元素</param>
        /// <param name="context">Lambda上下文，用于日志记录</param>
        /// <returns>如果元素有内容，则返回true</returns>
        public static bool HasContent(OpenXmlElement element, ILambdaContext context)
        {
            string innerText = element.InnerText;
            bool hasContent = !string.IsNullOrEmpty(innerText);

            context.Logger.LogInformation($"检查元素是否有内容: 类型={element.GetType().Name}, 内容='{innerText}', 结果={hasContent}");

            return hasContent;
        }
    }
}