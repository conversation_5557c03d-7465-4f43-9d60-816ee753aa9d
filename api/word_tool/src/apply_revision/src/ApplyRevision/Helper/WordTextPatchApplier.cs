using Amazon.Lambda.Core;
using ApplyRevision.Abstract;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// Enhanced WordTextPatchApplier that orchestrates document patch application
    /// using a clean, modular architecture with clear separation of concerns.
    /// 
    /// Architecture Components:
    /// - Strategy Pattern for operation handling (InsertStrategy, DeleteStrategy, ReplaceStrategy)
    /// - RunLocator for finding target text positions in the document
    /// - OperationOffsetCalculator for precise position adjustments
    /// - CommentManager for handling revision comments
    /// 
    /// Key Features:
    /// - Dynamic position adjustment for consecutive operations
    /// - Accurate offset calculation based on actual text changes
    /// - Support for insert, delete, and replace operations with comments
    /// - Handles operations within the same paragraph efficiently
    /// - Clean separation of concerns for maintainability and testability
    /// </summary>
    public class WordTextPatchApplier : IWordTextPatchApplier
    {
        private readonly ILambdaContext context;
        private readonly RunLocator runLocator;
        private readonly ICommentManager commentManager;
        private readonly CommentOrchestrator commentOrchestrator;
        private readonly Factory.OperationStrategyFactory strategyFactory;
        private readonly OperationOffsetCalculator offsetCalculator;

        public WordTextPatchApplier(ILambdaContext context)
        {
            this.context = context ?? throw new ArgumentNullException(nameof(context));

            var elementFactory = new ElementFactory();
            var revisionFactory = new RevisionElementFactory();
            var idManager = new IdManager(context);

            runLocator = RunLocator.GetInstance(context);
            commentManager = new CommentManager(context, elementFactory, idManager);
            commentOrchestrator = new CommentOrchestrator(
                context,
                new CommentXmlService(context, elementFactory, idManager),
                new DocumentManipulationService(context, elementFactory),
                new CommentReplyExtensionService(context),
                new ValidationService(context),
                idManager,
                elementFactory);
            strategyFactory = new Factory.OperationStrategyFactory(context, elementFactory, revisionFactory, idManager, commentManager);
            offsetCalculator = new OperationOffsetCalculator(context);
        }

        /// <summary>
        /// Applies a WordTextPatch to a DOCX document using dynamic offset calculation
        /// </summary>
        /// <param name="docxPath">Path to the DOCX file to modify</param>
        /// <param name="patch">The patch containing operations to apply</param>
        /// <returns>True if all operations succeeded, false otherwise</returns>
        public bool ApplyPatch(string docxPath, WordTextPatch patch)
        {
            if (string.IsNullOrEmpty(docxPath) || !File.Exists(docxPath))
            {
                context.Logger.LogError($"Document file not found: {docxPath}");
                return false;
            }

            if (patch?.Operations == null || !patch.Operations.Any())
            {
                context.Logger.LogWarning("No operations to apply");
                return true;
            }

            try
            {
                using var doc = WordprocessingDocument.Open(docxPath, true);

                context.Logger.LogInformation($"Starting patch application with {patch.Operations.Count} operations");

                // Initialize CommentManager with the document
                if (doc.MainDocumentPart != null)
                {
                    commentManager.Initialize(doc.MainDocumentPart);
                }
                else
                {
                    context.Logger.LogError("Document MainDocumentPart is null");
                    return false;
                }

                // 预加载所有需要的操作Run
                runLocator.ExtractRunsFromPatch(doc, patch);

                // 分离引用分离操作和其他操作，避免引用分离影响后续操作的位置计算
                // Separate quotation separation operations from others to avoid affecting position calculations
                var quotationSeparateOperations = new Queue<Operation>();
                var regularOperations = new Queue<Operation>();

                foreach (var op in patch.Operations)
                {
                    var operationCopy = new Operation
                    {
                        Id = op.Id,
                        Op = op.Op,
                        Target = op.Target,
                        Text = op.Text,
                        Props = op.Props,
                        Revision = op.Revision,
                        Comment = op.Comment,
                        CommentId = op.CommentId
                    };

                    if (op.Op == OperationType.QuotationSeparate)
                    {
                        quotationSeparateOperations.Enqueue(operationCopy);
                    }
                    else
                    {
                        regularOperations.Enqueue(operationCopy);
                    }
                }

                context.Logger.LogInformation($"Separated operations: {regularOperations.Count} regular, {quotationSeparateOperations.Count} quotation separations");

                // First process regular operations (insert, delete, replace, format, comments, etc.)
                int operationIndex = 0;
                while (regularOperations.Count > 0)
                {
                    var currentOperation = regularOperations.Dequeue();

                    try
                    {
                        var offsetChange = ApplyOperationAndGetOffset(doc, currentOperation);

                        if (offsetChange == 0)
                        {
                            context.Logger.LogInformation($"Regular operation {operationIndex} ({currentOperation.Op}) did not change document text, skipping position update");
                            operationIndex++;
                            continue;
                        }

                        var remainingRegularOps = regularOperations.ToList();
                        var remainingQuotationOps = quotationSeparateOperations.ToList();

                        if (remainingRegularOps.Count > 0)
                        {
                            context.Logger.LogInformation($"Adjusting offsets for remaining operations after operation {operationIndex} with offset change {offsetChange}");
                            offsetCalculator.RecalculateTextRangesAfterOperation(remainingRegularOps, currentOperation, offsetChange);
                        }
                        else
                        {
                            context.Logger.LogInformation($"No regular operations to adjust after operation {operationIndex}");
                        }

                        if (remainingQuotationOps.Count > 0)
                        {
                            context.Logger.LogInformation($"Adjusting offsets for quotation separation operations after operation {operationIndex} with offset change {offsetChange}");
                            offsetCalculator.RecalculateTextRangesAfterOperation(remainingQuotationOps, currentOperation, offsetChange);
                        }
                        else
                        {
                            context.Logger.LogInformation($"No quotation separation operations to adjust after operation {operationIndex}");
                        }

                        operationIndex++;
                    }
                    catch (Exception ex)
                    {
                        context.Logger.LogError($"Error applying regular operation {operationIndex}: {ex.Message}");
                        return false;
                    }
                }

                // 然后处理引用分离操作（在所有常规操作完成后）
                // Then process quotation separation operations (after all regular operations are complete)
                context.Logger.LogInformation($"Starting quotation separation operations: {quotationSeparateOperations.Count} operations");
                int quotationOperationIndex = 0;
                while (quotationSeparateOperations.Count > 0)
                {
                    var currentOperation = quotationSeparateOperations.Dequeue();

                    try
                    {
                        // 应用引用分离操作
                        var result = ApplyOperationAndGetOffset(doc, currentOperation);
                        if (result > 0)
                        {
                            context.Logger.LogInformation($"Quotation separation operation {quotationOperationIndex} completed successfully, created {result} paragraphs");
                        }
                        else
                        {
                            context.Logger.LogWarning($"Quotation separation operation {quotationOperationIndex} did not create new paragraphs");
                        }

                        quotationOperationIndex++;
                    }
                    catch (Exception ex)
                    {
                        context.Logger.LogError($"Error applying quotation separation operation {quotationOperationIndex}: {ex.Message}");
                        return false;
                    }
                }

                // Save comment changes to ensure they are persisted to the document
                // 保存评论更改以确保它们被持久化到文档中
                try
                {
                    commentManager.Save();
                    context.Logger.LogInformation("Comment changes saved successfully");
                }
                catch (Exception ex)
                {
                    context.Logger.LogError($"Error saving comment changes: {ex.Message}");
                    return false;
                }

                context.Logger.LogInformation("Patch application completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error applying patch: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 应用单个操作并返回实际的偏移变化
        /// Applies a single operation and returns the actual offset change
        /// </summary>
        /// <param name="doc">The WordprocessingDocument</param>
        /// <param name="operation">The operation to apply</param>
        /// <returns>实际的偏移变化量 / Actual offset change amount</returns>
        private int ApplyOperationAndGetOffset(WordprocessingDocument doc, Operation operation)
        {
            try
            {
                var strategy = strategyFactory.CreateStrategy(operation.Op);
                return strategy.Execute(doc, operation);
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error executing {operation.Op} strategy: {ex.Message}");
                return 0;
            }
        }

    }
}
