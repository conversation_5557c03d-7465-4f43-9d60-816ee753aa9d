using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using ApplyRevision.Factory;
using ApplyRevision.Service;
using System;
using System.Collections.Generic;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// 处理文本插入操作
    /// </summary>
    public class InsertOperationHandler
    {
        private readonly ILambdaContext context;
        private readonly IElementFactory elementFactory;
        private readonly IRevisionElementFactory revisionFactory;
        private readonly IIdManager idManager;

        public InsertOperationHandler(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager)
        {
            this.context = context ?? throw new ArgumentNullException(nameof(context));
            this.elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
            this.revisionFactory = revisionFactory ?? throw new ArgumentNullException(nameof(revisionFactory));
            this.idManager = idManager ?? throw new ArgumentNullException(nameof(idManager));
        }

        /// <summary>
        /// 在Text元素开始处插入新文本
        /// </summary>
        /// <param name="run">当前Run</param>
        /// <param name="text">当前Text</param>
        /// <param name="newText">要插入的文本</param>
        /// <param name="revision">修订信息</param>
        /// <param name="props">格式化属性（可选）</param>
        public void InsertBeforeText(Run run, Text text, string newText, Revision revision, Dictionary<string, object>? props = null)
        {
            // 创建带有继承属性的新InsertedRun
            var insertedRun = CreateInsertedRun(newText, revision, run, props);

            // 在原始Run之前插入新Run
            run.Parent?.InsertBefore(insertedRun, run);
        }

        /// <summary>
        /// 在Text元素结束处插入新文本
        /// </summary>
        /// <param name="run">当前Run</param>
        /// <param name="text">当前Text</param>
        /// <param name="newText">要插入的文本</param>
        /// <param name="revision">修订信息</param>
        /// <param name="props">格式化属性（可选）</param>
        public void InsertAfterText(Run run, Text text, string newText, Revision revision, Dictionary<string, object>? props = null)
        {
            // 创建带有继承属性的新InsertedRun
            var insertedRun = CreateInsertedRun(newText, revision, run, props);

            // 在原始Run之后插入新Run
            run.Parent?.InsertAfter(insertedRun, run);
        }

        /// <summary>
        /// 拆分Text元素并插入新文本
        /// </summary>
        /// <param name="run">当前Run</param>
        /// <param name="text">当前Text</param>
        /// <param name="offset">插入位置偏移量</param>
        /// <param name="newText">要插入的文本</param>
        /// <param name="revision">修订信息</param>
        /// <param name="props">格式化属性（可选）</param>
        public void SplitTextAndInsert(Run run, Text text, int offset, string newText, Revision revision, Dictionary<string, object>? props = null)
        {
            // 保存原始文本
            string originalText = text.Text;

            // 拆分文本
            string beforeText = originalText[..offset];
            string afterText = originalText[offset..];

            // 更新原始Text元素
            text.Text = beforeText;

            // 创建带有继承属性的新InsertedRun
            var insertedRun = CreateInsertedRun(newText, revision, run, props);

            // 创建包含文本后半部分的Run，保留原始Run的属性
            Run? afterRun;
            if (!string.IsNullOrEmpty(afterText))
            {
                // 使用深克隆以保留所有属性包括RunProperties
                afterRun = elementFactory?.CloneElement(run, deepClone: true) ?? new Run();

                // 清除克隆Run中的所有文本内容，保留格式属性
                afterRun.RemoveAllChildren<Text>();
                afterRun.RemoveAllChildren<TabChar>();
                afterRun.RemoveAllChildren<Break>();
                afterRun.RemoveAllChildren<CarriageReturn>();
                afterRun.RemoveAllChildren<NoBreakHyphen>();
                afterRun.RemoveAllChildren<SoftHyphen>();
                afterRun.RemoveAllChildren<DayShort>();
                afterRun.RemoveAllChildren<MonthShort>();
                afterRun.RemoveAllChildren<YearShort>();
                afterRun.RemoveAllChildren<DayLong>();
                afterRun.RemoveAllChildren<MonthLong>();
                afterRun.RemoveAllChildren<YearLong>();

                // 添加后半部分文本内容
                var afterTextElement = new Text(afterText) { Space = SpaceProcessingModeValues.Preserve };
                afterRun.Append(afterTextElement);
            }
            else
            {
                // 如果没有后半部分文本，不创建空的run
                afterRun = null;
            }

            // 在原始Run之后插入新Run
            run.Parent?.InsertAfter(insertedRun, run);

            // 只有当afterRun有内容时才插入
            if (afterRun != null)
            {
                run.Parent?.InsertAfter(afterRun, insertedRun);
            }
        }

        /// <summary>
        /// 创建带有可选属性继承的插入修订
        /// </summary>
        /// <param name="text">要插入的文本</param>
        /// <param name="revision">修订信息</param>
        /// <param name="originalRun">原始Run（可选）</param>
        /// <param name="props">格式化属性（可选）</param>
        /// <returns>InsertedRun元素</returns>
        public InsertedRun CreateInsertedRun(string text, Revision revision, Run? originalRun = null, Dictionary<string, object>? props = null)
        {
            // 获取下一个修订ID
            var revisionId = idManager.GetNextRevisionId();

            // 尝试使用工厂创建InsertedRun
            InsertedRun insertedRun;

            if (originalRun != null)
            {
                insertedRun = revisionFactory.CreateInsertedRunElement(
                    text,
                    revisionId.ToString(),
                    revision.Author,
                    revision.Date.ToString("yyyy-MM-ddTHH:mm:ss"),
                    originalRun);
            }
            else
            {
                insertedRun = revisionFactory.CreateInsertedRunElement(
                    text,
                    revisionId.ToString(),
                    revision.Author,
                    revision.Date.ToString("yyyy-MM-ddTHH:mm:ss"));
            }

            // 如果工厂返回null（例如在测试中），手动创建
            if (insertedRun == null)
            {
                // 创建InsertedRun手动
                insertedRun = new InsertedRun();

                // 设置修订属性
                insertedRun.Id = revisionId.ToString();
                insertedRun.Author = revision.Author;
                insertedRun.Date = new DateTimeValue(revision.Date);

                // 创建带有继承属性的文本运行
                Run textRun;
                if (originalRun?.RunProperties != null)
                {
                    // 使用深克隆以保留所有属性包括RunProperties
                    textRun = elementFactory?.CloneElement(originalRun, deepClone: true) ?? new Run();

                    // 清除克隆Run中的所有文本内容，保留格式属性
                    textRun.RemoveAllChildren<Text>();
                    textRun.RemoveAllChildren<TabChar>();
                    textRun.RemoveAllChildren<Break>();
                    textRun.RemoveAllChildren<CarriageReturn>();
                    textRun.RemoveAllChildren<NoBreakHyphen>();
                    textRun.RemoveAllChildren<SoftHyphen>();
                    textRun.RemoveAllChildren<DayShort>();
                    textRun.RemoveAllChildren<MonthShort>();
                    textRun.RemoveAllChildren<YearShort>();
                    textRun.RemoveAllChildren<DayLong>();
                    textRun.RemoveAllChildren<MonthLong>();
                    textRun.RemoveAllChildren<YearLong>();

                    // 添加新的文本内容
                    var textElement = new Text(text) { Space = SpaceProcessingModeValues.Preserve };
                    textRun.Append(textElement);
                }
                else
                {
                    // 没有要继承的属性，使用基本文本运行
                    textRun = elementFactory?.CreateTextRun(text) ?? new Run(new Text(text));
                }

                insertedRun.AppendChild(textRun);
            }

            // 应用格式化属性（如果提供）
            if (props != null && props.Count > 0)
            {
                ApplyFormattingProperties(insertedRun, props);
            }

            return insertedRun;
        }

        /// <summary>
        /// 将格式化属性应用到 InsertedRun 元素
        /// </summary>
        /// <param name="insertedRun">要应用格式的 InsertedRun 元素</param>
        /// <param name="props">格式化属性字典</param>
        private void ApplyFormattingProperties(InsertedRun insertedRun, Dictionary<string, object> props)
        {
            try
            {
                var run = insertedRun.GetFirstChild<Run>();
                if (run == null)
                {
                    context.Logger.LogWarning("InsertedRun does not contain a Run element to apply formatting to");
                    return;
                }

                // 获取或创建 RunProperties
                var runProperties = run.GetFirstChild<RunProperties>();
                if (runProperties == null)
                {
                    runProperties = new RunProperties();
                    run.PrependChild(runProperties);
                }

                // 应用各种格式化属性
                foreach (var prop in props)
                {
                    ApplyFormattingProperty(runProperties, prop.Key.ToLowerInvariant(), prop.Value);
                }

                context.Logger.LogInformation($"Applied {props.Count} formatting properties to InsertedRun");
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Error applying formatting properties: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用单个格式化属性
        /// </summary>
        /// <param name="runProperties">RunProperties 元素</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyValue">属性值</param>
        private void ApplyFormattingProperty(RunProperties runProperties, string propertyName, object propertyValue)
        {
            switch (propertyName)
            {
                case "bold":
                    if (ConvertToBool(propertyValue))
                    {
                        if (runProperties.GetFirstChild<Bold>() == null)
                        {
                            runProperties.Append(new Bold());
                        }
                    }
                    else
                    {
                        runProperties.RemoveAllChildren<Bold>();
                    }
                    break;

                case "italic":
                    if (ConvertToBool(propertyValue))
                    {
                        if (runProperties.GetFirstChild<Italic>() == null)
                        {
                            runProperties.Append(new Italic());
                        }
                    }
                    else
                    {
                        runProperties.RemoveAllChildren<Italic>();
                    }
                    break;

                case "underline":
                    if (ConvertToBool(propertyValue))
                    {
                        if (runProperties.GetFirstChild<Underline>() == null)
                        {
                            runProperties.Append(new Underline { Val = UnderlineValues.Single });
                        }
                    }
                    else
                    {
                        runProperties.RemoveAllChildren<Underline>();
                    }
                    break;

                case "color":
                    var colorValue = propertyValue?.ToString();
                    if (!string.IsNullOrEmpty(colorValue))
                    {
                        // 移除现有的颜色设置
                        runProperties.RemoveAllChildren<Color>();
                        // 添加新的颜色设置
                        runProperties.Append(new Color { Val = colorValue });
                    }
                    break;

                case "fontsize":
                case "size":
                    if (int.TryParse(propertyValue?.ToString(), out int fontSize) && fontSize > 0)
                    {
                        // 移除现有的字体大小设置
                        runProperties.RemoveAllChildren<FontSize>();
                        // 添加新的字体大小设置（Word 使用半点为单位）
                        runProperties.Append(new FontSize { Val = fontSize.ToString() });
                    }
                    break;

                case "fontname":
                case "font":
                    var fontName = propertyValue?.ToString();
                    if (!string.IsNullOrEmpty(fontName))
                    {
                        // 移除现有的字体设置
                        runProperties.RemoveAllChildren<RunFonts>();
                        // 添加新的字体设置
                        runProperties.Append(new RunFonts { Ascii = fontName, HighAnsi = fontName });
                    }
                    break;

                default:
                    context.Logger.LogWarning($"Unsupported formatting property: {propertyName}");
                    break;
            }
        }

        /// <summary>
        /// 将对象转换为布尔值
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <returns>转换后的布尔值</returns>
        private bool ConvertToBool(object value)
        {
            if (value is bool boolValue)
                return boolValue;

            if (value is string stringValue)
            {
                return string.Equals(stringValue, "true", StringComparison.OrdinalIgnoreCase) ||
                       string.Equals(stringValue, "1", StringComparison.OrdinalIgnoreCase);
            }

            if (value is int intValue)
                return intValue != 0;

            return false;
        }
    }
}