using Amazon.Lambda.Core;
using DocumentFormat.OpenXml.Packaging;
using ApplyRevision.Model;
using ApplyRevision.Factory;
using ApplyRevision.Service;

namespace ApplyRevision.Helper
{
    /// <summary>
    /// Run element operation handler using strategy pattern
    /// </summary>
    /// <remarks>
    /// Constructor using factory classes
    /// </remarks>
    /// <param name="context">Lambda context</param>
    /// <param name="elementFactory">Element factory</param>
    /// <param name="revisionFactory">Revision element factory</param>
    /// <param name="idManager">ID manager</param>
    public class RunOperator(
        ILambdaContext context,
        IElementFactory elementFactory,
        IRevisionElementFactory revisionFactory,
        IIdManager idManager,
        ICommentManager commentManager
        )
    {
        private readonly ILambdaContext context = context;
        private readonly Factory.OperationStrategyFactory strategyFactory = new Factory.OperationStrategyFactory(context, elementFactory, revisionFactory, idManager, commentManager);

        /// <summary>
        /// Execute insert operation
        /// </summary>
        /// <param name="doc">Word document</param>
        /// <param name="operation">Operation information</param>
        public void Insert(WordprocessingDocument doc, Operation operation)
        {
            var strategy = strategyFactory.CreateStrategy(OperationType.Insert);
            strategy.Execute(doc, operation);
        }

        /// <summary>
        /// Execute delete operation
        /// </summary>
        /// <param name="doc">Word document</param>
        /// <param name="operation">Operation information</param>
        public void Delete(WordprocessingDocument doc, Operation operation)
        {
            var strategy = strategyFactory.CreateStrategy(OperationType.Delete);
            strategy.Execute(doc, operation);
        }

        /// <summary>
        /// Execute replace operation
        /// </summary>
        /// <param name="doc">Word document</param>
        /// <param name="operation">Operation information</param>
        public void Replace(WordprocessingDocument doc, Operation operation)
        {
            var strategy = strategyFactory.CreateStrategy(OperationType.Replace);
            strategy.Execute(doc, operation);
        }

        /// <summary>
        /// Execute format operation
        /// </summary>
        /// <param name="doc">Word document</param>
        /// <param name="operation">Operation information</param>
        public void Format(WordprocessingDocument doc, Operation operation)
        {
            var strategy = strategyFactory.CreateStrategy(OperationType.Format);
            strategy.Execute(doc, operation);
        }


    }
}
