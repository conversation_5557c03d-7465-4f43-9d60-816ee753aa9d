using System;

namespace ApplyRevision.Exceptions
{
    /// <summary>
    /// Exception thrown when a comment with the specified ID cannot be found
    /// 当找不到指定ID的评论时抛出的异常
    /// </summary>
    public class CommentNotFoundException : Exception
    {
        /// <summary>
        /// Gets the comment ID that was not found
        /// 获取未找到的评论ID
        /// </summary>
        public string CommentId { get; }

        /// <summary>
        /// Initializes a new instance of the CommentNotFoundException class
        /// 初始化CommentNotFoundException类的新实例
        /// </summary>
        /// <param name="commentId">The comment ID that was not found / 未找到的评论ID</param>
        public CommentNotFoundException(string commentId)
            : base($"Comment with ID '{commentId}' was not found.")
        {
            CommentId = commentId;
        }

        /// <summary>
        /// Initializes a new instance of the CommentNotFoundException class with a custom message
        /// 使用自定义消息初始化CommentNotFoundException类的新实例
        /// </summary>
        /// <param name="commentId">The comment ID that was not found / 未找到的评论ID</param>
        /// <param name="message">Custom error message / 自定义错误消息</param>
        public CommentNotFoundException(string commentId, string message)
            : base(message)
        {
            CommentId = commentId;
        }

        /// <summary>
        /// Initializes a new instance of the CommentNotFoundException class with a custom message and inner exception
        /// 使用自定义消息和内部异常初始化CommentNotFoundException类的新实例
        /// </summary>
        /// <param name="commentId">The comment ID that was not found / 未找到的评论ID</param>
        /// <param name="message">Custom error message / 自定义错误消息</param>
        /// <param name="innerException">Inner exception / 内部异常</param>
        public CommentNotFoundException(string commentId, string message, Exception innerException)
            : base(message, innerException)
        {
            CommentId = commentId;
        }
    }
}
