using System;
using System.Collections.Generic;
using System.Linq;
using ApplyRevision.Model;
using Amazon.Lambda.Core;

namespace ApplyRevision.Validation
{
    /// <summary>
    /// CommentAdd操作的专用验证类
    /// Specialized validation class for CommentAdd operations
    /// </summary>
    public static class CommentAddValidation
    {
        /// <summary>
        /// 验证CommentAdd操作的完整输入
        /// Validate complete input for CommentAdd operation
        /// </summary>
        /// <param name="operation">要验证的操作 / Operation to validate</param>
        /// <param name="context">Lambda上下文用于日志记录 / Lambda context for logging</param>
        /// <exception cref="ArgumentException">当输入无效时抛出 / Thrown when input is invalid</exception>
        /// <exception cref="ArgumentNullException">当必需参数为null时抛出 / Thrown when required parameters are null</exception>
        public static void ValidateCommentAddInput(Operation operation, ILambdaContext? context = null)
        {
            ArgumentNullException.ThrowIfNull(operation, nameof(operation));

            context?.Logger.LogDebug("Starting CommentAdd operation validation");

            // 验证操作类型
            // Validate operation type
            if (operation.Op != OperationType.CommentAdd)
            {
                throw new ArgumentException($"Expected CommentAdd operation, but got {operation.Op}", nameof(operation));
            }

            // 验证目标段落信息
            // Validate target paragraph information
            ValidateTarget(operation.Target, context);

            // 验证评论信息
            // Validate comment information
            ValidateComment(operation.Comment, context);

            // 验证CommentAdd特定的约束
            // Validate CommentAdd specific constraints
            ValidateCommentAddConstraints(operation, context);

            context?.Logger.LogDebug("CommentAdd operation validation completed successfully");
        }

        /// <summary>
        /// 验证目标段落信息
        /// Validate target paragraph information
        /// </summary>
        /// <param name="target">目标信息 / Target information</param>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        private static void ValidateTarget(Target target, ILambdaContext? context)
        {
            if (target == null)
            {
                throw new ArgumentNullException(nameof(target), "Target is required for CommentAdd operation");
            }

            foreach (var seg in target.Segments)
            {
                if (seg == null)
                {
                    throw new ArgumentException("Target SegId cannot be null", nameof(target));
                }

                if (string.IsNullOrWhiteSpace(seg.SegId))
                {
                    throw new ArgumentException("Target SegId cannot be null or empty", nameof(target));
                }

                // 验证SegId格式（应该是"段落id-run序号"格式）
                // Validate SegId format (should be "paragraph_id-run_index" format)
                if (!IsValidSegIdFormat(seg.SegId))
                {
                    throw new ArgumentException($"Invalid SegId format: {target.FirstOrDefault()?.SegId}. Expected format: 'paragraphId-runIndex' (e.g., '1-0')", nameof(target));
                }
            }
        }

        /// <summary>
        /// 验证评论信息
        /// Validate comment information
        /// </summary>
        /// <param name="comment">评论信息 / Comment information</param>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        private static void ValidateComment(Comment? comment, ILambdaContext? context)
        {
            if (comment == null)
            {
                throw new ArgumentNullException(nameof(comment), "Comment is required for CommentAdd operation");
            }

            // 验证评论文本
            // Validate comment text
            if (string.IsNullOrWhiteSpace(comment.Text))
            {
                throw new ArgumentException("Comment text cannot be null or empty", nameof(comment));
            }

            // 验证评论文本长度（避免过长的评论）
            // Validate comment text length (avoid overly long comments)
            if (comment.Text.Length > 10000) // 10KB limit
            {
                throw new ArgumentException($"Comment text is too long ({comment.Text.Length} characters). Maximum allowed: 10000 characters", nameof(comment));
            }

            // CommentAdd操作不应该有父评论ID
            // CommentAdd operation should not have parent comment ID
            if (!string.IsNullOrWhiteSpace(comment.ParentCommentId))
            {
                throw new ArgumentException("ParentCommentId should be null or empty for CommentAdd operation. Use CommentReply for reply operations.", nameof(comment));
            }

            context?.Logger.LogDebug($"Comment validation passed. Text length: {comment.Text.Length} characters");
        }

        /// <summary>
        /// 验证CommentAdd特定的约束
        /// Validate CommentAdd specific constraints
        /// </summary>
        /// <param name="operation">操作对象 / Operation object</param>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        private static void ValidateCommentAddConstraints(Operation operation, ILambdaContext? context)
        {
            // CommentAdd操作不应该有CommentId（这是用于CommentReply的）
            // CommentAdd operation should not have CommentId (this is for CommentReply)
            if (!string.IsNullOrWhiteSpace(operation.CommentId))
            {
                throw new ArgumentException("CommentId should be null or empty for CommentAdd operation. CommentId is used for CommentReply operations.", nameof(operation.CommentId));
            }

            // 验证Range和Text字段（虽然CommentAdd不直接使用这些字段，但为了架构一致性需要验证）
            // Validate Range and Text fields (although CommentAdd doesn't directly use these fields, validation is needed for architectural consistency)
            ValidateArchitecturalConsistency(operation, context);

            context?.Logger.LogDebug("CommentAdd constraints validation passed");
        }

        /// <summary>
        /// 验证架构一致性（确保与现有Operation模型兼容）
        /// Validate architectural consistency (ensure compatibility with existing Operation model)
        /// </summary>
        /// <param name="operation">操作对象 / Operation object</param>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        private static void ValidateArchitecturalConsistency(Operation operation, ILambdaContext? context)
        {
            // 对于CommentAdd操作，Range和Text字段是可选的，但如果存在则需要有效
            // For CommentAdd operations, Range and Text fields are optional, but if present they need to be valid
            if (operation.Range != null)
            {
                if (operation.Range.Start < 0 || operation.Range.End < operation.Range.Start)
                {
                    throw new ArgumentException($"Invalid range [{operation.Range.Start}, {operation.Range.End}] in CommentAdd operation", nameof(operation.Range));
                }
            }

            // Text字段对于CommentAdd是可选的（评论文本在Comment.Text中）
            // Text field is optional for CommentAdd (comment text is in Comment.Text)
            // 但如果存在，我们记录一个警告，因为这可能表示配置错误
            // But if present, we log a warning as this might indicate a configuration error
            if (!string.IsNullOrEmpty(operation.Text))
            {
                context?.Logger.LogWarning($"CommentAdd operation contains Text field: '{operation.Text}'. Comment text should be in Comment.Text field instead.");
            }

            context?.Logger.LogDebug("Architectural consistency validation passed");
        }

        /// <summary>
        /// 验证SegId格式是否有效
        /// Validate if SegId format is valid
        /// </summary>
        /// <param name="segId">要验证的SegId / SegId to validate</param>
        /// <returns>是否有效 / Whether valid</returns>
        private static bool IsValidSegIdFormat(string? segId)
        {
            if (segId == null)
                return false;

            if (string.IsNullOrWhiteSpace(segId))
                return false;

            // SegId应该是"段落id-run序号"格式，例如："1-0", "123-2"
            // SegId should be "paragraph_id-run_index" format, e.g., "1-0", "123-2"
            var parts = segId.Split('-');
            if (parts.Length != 2)
                return false;

            // 验证段落ID是正整数
            // Validate paragraph ID is positive integer
            if (!int.TryParse(parts[0], out int paragraphId) || paragraphId < 1)
                return false;

            // 验证run序号是非负整数
            // Validate run index is non-negative integer
            if (!int.TryParse(parts[1], out int runIndex) || runIndex < 0)
                return false;

            return true;
        }

        /// <summary>
        /// 批量验证多个CommentAdd操作
        /// Batch validate multiple CommentAdd operations
        /// </summary>
        /// <param name="operations">操作列表 / List of operations</param>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        /// <returns>验证结果列表 / List of validation results</returns>
        public static List<ValidationResult> ValidateMultipleCommentAddOperations(IEnumerable<Operation> operations, ILambdaContext? context = null)
        {
            var results = new List<ValidationResult>();
            var commentAddOperations = operations.Where(op => op.Op == OperationType.CommentAdd).ToList();

            context?.Logger.LogDebug($"Starting batch validation for {commentAddOperations.Count} CommentAdd operations");

            for (int i = 0; i < commentAddOperations.Count; i++)
            {
                var operation = commentAddOperations[i];
                try
                {
                    ValidateCommentAddInput(operation, context);
                    results.Add(new ValidationResult
                    {
                        OperationIndex = i,
                        IsValid = true,
                        ErrorMessage = null
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new ValidationResult
                    {
                        OperationIndex = i,
                        IsValid = false,
                        ErrorMessage = ex.Message
                    });
                    context?.Logger.LogError($"Validation failed for CommentAdd operation at index {i}: {ex.Message}");
                }
            }

            context?.Logger.LogDebug($"Batch validation completed. Valid: {results.Count(r => r.IsValid)}, Invalid: {results.Count(r => !r.IsValid)}");
            return results;
        }
    }

    /// <summary>
    /// 验证结果
    /// Validation result
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 操作索引
        /// Operation index
        /// </summary>
        public int OperationIndex { get; set; }

        /// <summary>
        /// 是否有效
        /// Whether valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// Error message
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
