using Amazon.Lambda.Core;
using Common.Model;
using ApplyRevision.Executor;
using ApplyRevision.Model;


// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]


namespace ApplyRevision;

public class ApplyRevisionHandler
{
    public static async Task<ResultResponse> Execute(ApplyRevisionEvent evt, ILambdaContext context)
    {
        var executor = new ApplyRevisionExecutor(evt, context);
        var response = await executor.Execute();
        return response;
    }
}
