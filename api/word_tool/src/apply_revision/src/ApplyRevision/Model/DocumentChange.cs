﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using ApplyRevision.Component;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Model
{

    [JsonConverter(typeof(StringEnumConverter))]
    public enum EditType
    {
        inplace,
        prefix,
        suffix,
        wrap
    }

    /// <summary>
    /// Represents the types of revision operations supported in WordprocessingML
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum RevisionOperatorType
    {
        // Basic text operations
        ins,        // Insert text
        del,        // Delete text

        // Move operations
        moveFrom,   // Move source (text moved from this location)
        moveTo,     // Move destination (text moved to this location)

        // Property changes
        rPrChange,  // Run properties change
        pPrChange,  // Paragraph properties change
        sectPrChange, // Section properties change

        // Table operations
        tblPrChange,    // Table properties change
        trPrChange,     // Table row properties change
        tcPrChange,     // Table cell properties change
        cellIns,        // Table cell insertion
        cellDel,        // Table cell deletion
        cellMerge,      // Table cell merge/split

        // Custom XML operations
        customXmlIns,   // Custom XML insertion
        customXmlDel,   // Custom XML deletion
        customXmlMove,  // Custom XML move

        // Math operations
        mathIns,        // Math content insertion
        mathDel,        // Math content deletion

        // Block-level operations
        blockIns,       // Block-level insertion (paragraphs, tables, etc.)
        blockDel        // Block-level deletion
    }

    /// <summary>
    /// Represents the scope of a revision operation
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum RevisionScope
    {
        Run,        // Run-level revision (inline content)
        Paragraph,  // Paragraph-level revision
        Block,      // Block-level revision (multiple paragraphs)
        Table,      // Table-level revision
        Cell,       // Table cell revision
        Row,        // Table row revision
        Section,    // Section-level revision
        Document    // Document-level revision
    }

    /// <summary>
    /// Represents additional properties for specific revision types
    /// </summary>
    public class RevisionProperties
    {
        // For move operations
        public string? MoveId { get; set; }
        public string? MoveName { get; set; }

        // For range operations
        public string? RangeId { get; set; }
        public bool IsRangeStart { get; set; } = false;
        public bool IsRangeEnd { get; set; } = false;

        // For property changes
        public string? PreviousPropertiesXml { get; set; }
        public string? NewPropertiesXml { get; set; }

        // For table operations
        public int? GridSpan { get; set; }
        public MergedCellValues? VMerge { get; set; }
        public int? RowIndex { get; set; }
        public int? CellIndex { get; set; }

        // For custom XML operations
        public string? CustomXmlElement { get; set; }
        public string? CustomXmlNamespace { get; set; }

        // Additional metadata
        public Dictionary<string, object> ExtendedProperties { get; set; } = new Dictionary<string, object>();
    }

    public class DocumentChange
    {
        public required string ParaId { get; set; }
        public string Text { get; set; } = "";
        public List<RevisionInfo> ChangeText { get; set; } = new List<RevisionInfo>();
    }

    public class RevisionInfo
    {
        public required string RevId { get; set; } // "1" or "2"
        public EditType? EditType { get; set; } = null;
        public string? Comment { get; set; } = ""; // comment for the revision
        public List<RevisionOperator> Ops { get; set; } = new List<RevisionOperator>();
        public List<RevisionLink> Link { get; set; } = new List<RevisionLink>();
    }

    public class RevisionMeta
    {
        public string Author { get; set; } = "AI";
        // public string Date { get; set; } = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");
        public string Date { get; set; } = DateTime.Now.ToString("yyyy-MM-dd");
        public string Comment { get; set; } = "";
        public string DisplayText { get; set; } = "";
        public string Uri { get; set; } = "";
    }

    public class RevisionLink : RevisionMeta
    {

    }

    /// <summary>
    /// Enhanced revision operator that supports all types of WordprocessingML revisions
    /// </summary>
    public class RevisionOperator : RevisionMeta
    {
        /// <summary>
        /// The type of revision operation
        /// </summary>
        public required RevisionOperatorType Op { get; set; }

        /// <summary>
        /// Text content for text-based revisions
        /// </summary>
        public string Text { get; set; } = "";

        /// <summary>
        /// Scope of the revision operation
        /// </summary>
        public RevisionScope Scope { get; set; } = RevisionScope.Run;

        /// <summary>
        /// Additional properties specific to the revision type
        /// </summary>
        public RevisionProperties Properties { get; set; } = new RevisionProperties();

        /// <summary>
        /// Whether this revision starts a comment range
        /// </summary>
        public bool CommentStart { get; set; } = false;

        /// <summary>
        /// Whether this revision ends a comment range
        /// </summary>
        public bool CommentEnd { get; set; } = false;

        /// <summary>
        /// For grouped operations, indicates the group identifier
        /// </summary>
        public string? GroupId { get; set; }

        /// <summary>
        /// Sequence number within a group (for operations that must be applied in order)
        /// </summary>
        public int? SequenceNumber { get; set; }

        /// <summary>
        /// Reference to related revision operations (e.g., move source and destination)
        /// </summary>
        public List<string> RelatedRevisionIds { get; set; } = new List<string>();

        /// <summary>
        /// XML content for complex revisions (e.g., entire paragraphs, tables)
        /// </summary>
        public string? XmlContent { get; set; }

        /// <summary>
        /// Indicates if this revision affects formatting only (no content change)
        /// </summary>
        public bool IsFormattingOnly { get; set; } = false;

        /// <summary>
        /// Priority level for revision application (higher numbers applied first)
        /// </summary>
        public int Priority { get; set; } = 0;
    }

    public class RevisionLinkInsert
    {
        public List<CommentComponent> CommentComponents { get; set; } = [];
    }
}
