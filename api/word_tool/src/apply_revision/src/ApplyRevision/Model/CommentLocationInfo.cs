using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Model
{
    /// <summary>
    /// 评论在文档中的位置信息
    /// Comment location information in document
    /// </summary>
    public class CommentLocationInfo
    {
        /// <summary>
        /// 评论ID
        /// Comment ID
        /// </summary>
        public string CommentId { get; set; } = string.Empty;

        /// <summary>
        /// 评论范围开始标记
        /// Comment range start marker
        /// </summary>
        public CommentRangeStart? RangeStart { get; set; }

        /// <summary>
        /// 评论范围结束标记
        /// Comment range end marker
        /// </summary>
        public CommentRangeEnd? RangeEnd { get; set; }

        /// <summary>
        /// 评论引用标记
        /// Comment reference marker
        /// </summary>
        public Run? Reference { get; set; }
    }
}
