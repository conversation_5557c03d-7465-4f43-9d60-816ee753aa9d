using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ApplyRevision.Model
{
    /// <summary>
    /// Represents a Word text patch, containing a series of operations on a Word document
    /// </summary>
    public class WordTextPatch
    {
        /// <summary>
        /// Schema identifier, fixed to "word-text-patch"
        /// </summary>
        public string Schema { get; set; } = "word-text-patch";

        /// <summary>
        /// Version number
        /// </summary>
        public string Version { get; set; } = "2.0";

        /// <summary>
        /// Document ID
        /// </summary>
        public required string DocumentId { get; set; }

        /// <summary>
        /// Default revision information
        /// </summary>
        public Revision? DefaultRevision { get; set; }

        /// <summary>
        /// List of operations
        /// </summary>
        public required List<Operation> Operations { get; set; }
    }

    /// <summary>
    /// Represents an operation on a Word document
    /// </summary>
    public class Operation
    {
        /// <summary>
        /// Operation ID - uses "DefaultId" if not provided for backward compatibility.
        /// This allows distinguishing between IDs from JSON deserialization and default values.
        /// </summary>
        public string Id { get; set; } = "DefaultId";
        /// <summary>
        /// Operation type
        /// </summary>
        [JsonConverter(typeof(StringEnumConverter))]
        public OperationType Op { get; set; }

        /// <summary>
        /// Target position
        /// </summary>
        public Target Target { get; set; } = new Target();

        /// <summary>
        /// Range - Backward compatibility property
        /// When set, automatically updates the first segment's range in Target
        /// When get, returns the first segment's range from Target
        /// </summary>
        [Obsolete("Use Target.FirstOrDefault.Range instead. This property will be removed in future versions.")]
        public Range? Range
        {
            get => Target.FirstOrDefault?.Range;
            set
            {
                // Ensure we have at least one segment for backward compatibility
                if (Target.Count == 0)
                {
                    // If no segments exist, we can't set the range
                    // This maintains the existing behavior where Range was independent
                    return;
                }

                // Update the first segment's range
                var firstSegment = Target.FirstOrDefault;
                if (firstSegment != null)
                {
                    firstSegment.Range = value;
                }
            }
        }

        /// <summary>
        /// Range of the first segment in Target
        /// </summary>
        public Range? FirstSegmentRange
        {
            get => Target.FirstOrDefault?.Range;
        }

        /// <summary>
        /// Text content
        /// </summary>
        public string Text { get; set; } = "";

        /// <summary>
        /// Format properties
        /// </summary>
        public Dictionary<string, object>? Props { get; set; }

        /// <summary>
        /// Comment information
        /// </summary>
        public Comment? Comment { get; set; }

        /// <summary>
        /// Revision information, uses default revision info if null
        /// </summary>
        public Revision? Revision { get; set; }

        /// <summary>
        /// Comment ID, used to identify the target comment for commentReply operations
        /// </summary>
        public string? CommentId { get; set; }
    }

    /// <summary>
    /// Operation type
    /// </summary>
    [JsonConverter(typeof(StringEnumConverter))]
    public enum OperationType
    {
        Insert,
        Delete,
        Replace,
        Format,
        CommentReply,
        CommentAdd,
        QuotationSeparate,
    }

    /// <summary>
    /// 
    /// </summary>
    public class Segment
    {
        public required string SegId { get; set; }
        public Range? Range { get; set; } = null;
    }

    /// <summary>
    /// Represents a paragraph element in a Word document.
    /// Supports both single SegId (for backward compatibility) and multiple SegIds.
    /// </summary>
    [JsonConverter(typeof(TargetJsonConverter))]
    public class Target : IEnumerable<Segment>
    {
        private List<Segment> _segments;

        /// <summary>
        /// Gets or sets the list of segment IDs.
        /// Each segment ID is in format "paragraphId-runIndex"
        /// Example: "1-0" represents run index 0 in paragraph ID 1
        /// </summary>
        public List<Segment> Segments
        {
            get => _segments;
            set => _segments = value ?? [];
        }

        /// <summary>
        /// Gets the first SegId or null if the list is empty.
        /// </summary>
        public Segment? FirstOrDefault => _segments.FirstOrDefault();

        /// <summary>
        /// Gets the count of SegIds.
        /// </summary>
        public int Count => _segments.Count;

        public Target()
        {
            _segments = [];
        }

        /// <summary>
        /// Default constructor initializes with an empty list.
        /// </summary>
        public Target(List<Segment> targetItems)
        {
            _segments = targetItems ?? [];
        }

        /// <summary>
        /// Constructor that accepts a single SegId for backward compatibility.
        /// </summary>
        /// <param name="segId">Single segment ID</param>
        public Target(string segId)
        {
            _segments = [new Segment { SegId = segId }];
        }

        public Target(string? segId, int start, int end)
        {
            _segments = [];
            if (!string.IsNullOrWhiteSpace(segId))
            {
                _segments.Add(new Segment { SegId = segId, Range = new Range { Start = start, End = end } });
            }
        }

        public Target(string? segId, Range? range)
        {
            _segments = [];
            if (!string.IsNullOrWhiteSpace(segId))
            {
                _segments.Add(new Segment { SegId = segId, Range = range });
            }
        }

        /// <summary>
        /// Adds a SegId to the list.
        /// </summary>
        /// <param name="segId">Segment ID to add</param>
        public void AddSegment(string segId, int start = 0, int end = 0)
        {
            if (!string.IsNullOrWhiteSpace(segId))
            {
                _segments.Add(new Segment { SegId = segId, Range = new Range { Start = start, End = end } });
            }
        }

        /// <summary>
        /// Removes a SegId from the list.
        /// </summary>
        /// <param name="segId">Segment ID to remove</param>
        /// <returns>True if the SegId was found and removed; otherwise, false</returns>
        public bool RemoveSegment(string segId)
        {
            return _segments.RemoveAll(item => item.SegId == segId) > 0;
        }

        /// <summary>
        /// Checks if the Target contains the specified SegId.
        /// </summary>
        /// <param name="segId">Segment ID to check</param>
        /// <returns>True if the SegId exists; otherwise, false</returns>
        public bool ContainsSegment(string segId)
        {
            return _segments.Any(item => item.SegId == segId);
        }

        /// <summary>
        /// Clears all SegIds.
        /// </summary>
        public void Clear()
        {
            _segments.Clear();
        }

        /// <summary>
        /// Returns an enumerator that iterates through the SegIds.
        /// </summary>
        /// <returns>An enumerator for the SegIds</returns>
        public IEnumerator<Segment> GetEnumerator()
        {
            return _segments.GetEnumerator();
        }

        /// <summary>
        /// Returns an enumerator that iterates through the SegIds.
        /// </summary>
        /// <returns>An enumerator for the SegIds</returns>
        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }

        /// <summary>
        /// Gets the SegId at the specified index.
        /// </summary>
        /// <param name="index">The zero-based index of the SegId to get</param>
        /// <returns>The SegId at the specified index</returns>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when index is out of range</exception>
        public Segment this[int index]
        {
            get => _segments[index];
            set => _segments[index] = value;
        }

        /// <summary>
        /// Converts the Target to an array of SegIds.
        /// </summary>
        /// <returns>An array containing all SegIds</returns>
        public Segment[] ToArray()
        {
            return [.. _segments];
        }

        public static Target FromJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
            {
                return new Target();
            }

            try
            {
                var segIds = JsonConvert.DeserializeObject<List<Segment>>(json);
                return new Target { Segments = segIds ?? [] };
            }
            catch (JsonException ex)
            {
                throw new ArgumentException("Invalid JSON format for Target", nameof(json), ex);
            }
        }

        /// <summary>
        /// Creates a new Target from an array of SegIds.
        /// </summary>
        /// <param name="segItems">Array of segment IDs</param>
        /// <returns>A new Target instance</returns>
        public static Target FromArray(Segment[] segItems)
        {
            return new Target(segItems?.ToList() ?? []);
        }
    }

    /// <summary>
    /// Represents a text range with half-open interval semantics [Start, End).
    /// This class defines a contiguous range of characters or positions within a text document,
    /// where the start position is inclusive and the end position is exclusive.
    /// </summary>
    /// <remarks>
    /// <para>
    /// The Range uses half-open interval notation, meaning:
    /// - Start position is inclusive (the character at this position is included)
    /// - End position is exclusive (the character at this position is NOT included)
    /// </para>
    /// <para>
    /// For example, a Range with Start=5 and End=10 includes characters at positions 5, 6, 7, 8, 9
    /// but excludes the character at position 10.
    /// </para>
    /// <para>
    /// This half-open interval approach provides several advantages:
    /// - Length calculation: Length = End - Start
    /// - Empty ranges: Start == End represents an empty range
    /// - Adjacent ranges: One range ending at position N and another starting at N don't overlap
    /// - Consistent with common programming conventions (similar to substring operations)
    /// </para>
    /// </remarks>
    /// <example>
    /// <code>
    /// // Create a range covering characters 10-19 (inclusive of 10, exclusive of 20)
    /// var range = new Range { Start = 10, End = 20 };
    /// 
    /// // Length is 10 characters
    /// int length = range.End - range.Start; // 10
    /// 
    /// // Empty range
    /// var emptyRange = new Range { Start = 5, End = 5 };
    /// </code>
    /// </example>
    public class Range
    {
        /// <summary>
        /// Gets or sets the inclusive starting position of the range.
        /// This position represents the first character or element that is included in the range.
        /// </summary>
        /// <value>
        /// A zero-based integer representing the starting position. Must be non-negative
        /// and less than or equal to <see cref="End"/>.
        /// </value>
        /// <remarks>
        /// The Start position is inclusive, meaning the character at this position
        /// is considered part of the range. For text operations, this typically
        /// represents the index of the first character to be included.
        /// </remarks>
        /// <example>
        /// <code>
        /// var range = new Range { Start = 5, End = 10 };
        /// // This range includes the character at position 5
        /// </code>
        /// </example>
        public required int Start { get; set; }

        /// <summary>
        /// Gets or sets the exclusive ending position of the range.
        /// This position represents the first character or element that is NOT included in the range.
        /// </summary>
        /// <value>
        /// A zero-based integer representing the ending position. Must be greater than or equal
        /// to <see cref="Start"/> and represents the first position beyond the range.
        /// </value>
        /// <remarks>
        /// <para>
        /// The End position is exclusive, meaning the character at this position
        /// is NOT considered part of the range. This follows the half-open interval
        /// convention [Start, End).
        /// </para>
        /// <para>
        /// Special cases:
        /// - When End equals Start, the range is empty (contains no elements)
        /// - The actual length of the range is calculated as: End - Start
        /// </para>
        /// </remarks>
        /// <example>
        /// <code>
        /// var range = new Range { Start = 5, End = 10 };
        /// // This range does NOT include the character at position 10
        /// // It includes positions 5, 6, 7, 8, 9 (5 characters total)
        /// 
        /// var emptyRange = new Range { Start = 5, End = 5 };
        /// // This is an empty range containing no characters
        /// </code>
        /// </example>
        public required int End { get; set; }
    }

    /// <summary>
    /// Comment information
    /// </summary>
    public class Comment
    {
        /// <summary>
        /// Comment ID
        /// </summary>
        public string? CommentId { get; set; }

        /// <summary>
        /// Parent comment ID (for replies)
        /// </summary>
        public string? ParentCommentId { get; set; }

        /// <summary>
        /// Comment text
        /// </summary>
        public required string Text { get; set; }
    }

    /// <summary>
    /// </summary>
    public class Revision
    {
        /// <summary>
        /// </summary>
        public required string Author { get; set; }

        /// <summary>
        /// </summary>
        public required DateTime Date { get; set; }
    }

    /// <summary>
    /// Custom JSON converter for Target class to handle serialization of Segment objects
    /// while maintaining backward compatibility with string arrays
    /// </summary>
    public class TargetJsonConverter : JsonConverter<Target>
    {
        public override void WriteJson(JsonWriter writer, Target? value, JsonSerializer serializer)
        {
            if (value == null)
            {
                writer.WriteNull();
                return;
            }

            // Serialize as array of Segment objects
            writer.WriteStartArray();
            foreach (var segment in value.Segments)
            {
                serializer.Serialize(writer, segment);
            }
            writer.WriteEndArray();
        }

        public override Target ReadJson(JsonReader reader, Type objectType, Target? existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return new Target();
            }

            if (reader.TokenType == JsonToken.StartArray)
            {
                var segments = new List<Segment>();
                while (reader.Read() && reader.TokenType != JsonToken.EndArray)
                {
                    if (reader.TokenType == JsonToken.StartObject)
                    {
                        // Deserialize as Segment object
                        var segment = serializer.Deserialize<Segment>(reader);
                        if (segment != null)
                        {
                            segments.Add(segment);
                        }
                    }
                    else if (reader.TokenType == JsonToken.String && reader.Value != null)
                    {
                        // Handle backward compatibility - convert string to Segment
                        var segId = reader.Value.ToString();
                        if (!string.IsNullOrEmpty(segId))
                        {
                            segments.Add(new Segment { SegId = segId });
                        }
                    }
                }
                return new Target(segments);
            }

            // Handle single object - deserialize as single Segment
            if (reader.TokenType == JsonToken.StartObject)
            {
                var segment = serializer.Deserialize<Segment>(reader);
                if (segment != null)
                {
                    return new Target(new List<Segment> { segment });
                }
                return new Target();
            }

            // Handle single string for backward compatibility
            if (reader.TokenType == JsonToken.String && reader.Value != null)
            {
                var segId = reader.Value.ToString();
                if (!string.IsNullOrEmpty(segId))
                {
                    return new Target(segId);
                }
                return new Target();
            }

            throw new JsonSerializationException($"Unexpected token type '{reader.TokenType}' when deserializing Target");
        }
    }
}
