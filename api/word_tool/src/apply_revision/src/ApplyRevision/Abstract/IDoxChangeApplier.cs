﻿using ApplyRevision.Model;


namespace ApplyRevision.Abstract
{
    /// <summary>
    /// Interface for applying revision information represented in DocumentChange format back to a DOCX file.
    /// </summary>
    public interface IDocxChangeApplier
    {
        /// <summary>
        /// Read a set of DocumentChange objects and update the corresponding revision nodes in the specified DOCX file.
        /// </summary>
        /// <param name="docxPath">The full path of the DOCX file to be updated.</param>
        /// <param name="docChanges">A list of DocumentChange that contains information such as revision ID and type.</param>
        bool ApplyChanges(string docxPath, List<DocumentChange> docChanges);
    }
}
