using ApplyRevision.Model;

namespace ApplyRevision.Abstract
{
    /// <summary>
    /// Word文本补丁应用器接口
    /// </summary>
    public interface IWordTextPatchApplier
    {
        /// <summary>
        /// 将补丁应用到Word文档
        /// </summary>
        /// <param name="docxPath">Word文档路径</param>
        /// <param name="patch">补丁信息</param>
        /// <returns>是否应用成功</returns>
        bool ApplyPatch(string docxPath, WordTextPatch patch);
    }

}