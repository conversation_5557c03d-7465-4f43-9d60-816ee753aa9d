using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Service for handling comment range operations and positioning
    /// </summary>
    /// <remarks>
    /// Initialize the comment range service
    /// </remarks>
    /// <param name="context">Lambda context</param>
    /// <param name="elementFactory">Element factory</param>
    /// <param name="runTextService">Run text manipulation service</param>
    public class CommentRangeService(ILambdaContext? context, IElementFactory elementFactory, RunTextManipulationService runTextService)
    {
        private readonly ILambdaContext? _context = context;
        private readonly IElementFactory _elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
        private readonly RunTextManipulationService _runTextService = runTextService ?? throw new ArgumentNullException(nameof(runTextService));
        private MainDocumentPart? _mainPart;

        /// <summary>
        /// Initialize service with document parts
        /// </summary>
        /// <param name="mainPart">Main document part</param>
        public void Initialize(MainDocumentPart mainPart)
        {
            _mainPart = mainPart ?? throw new ArgumentNullException(nameof(mainPart));
            _context?.Logger.LogDebug("CommentRangeService initialized");
        }

        /// <summary>
        /// Handle comment range insertion for a single run
        /// </summary>
        /// <param name="commentId">Comment ID</param>
        /// <param name="targetRun">Target run</param>
        /// <param name="relativeStart">Start position relative to the run</param>
        /// <param name="relativeEnd">End position relative to the run</param>
        /// <param name="commentRangeStart">Comment range start marker</param>
        /// <param name="commentRangeEnd">Comment range end marker</param>
        /// <param name="commentReference">Comment reference marker</param>
        /// <returns>Whether insertion was successful</returns>
        public bool HandleSingleRunCommentRange(string commentId, Run targetRun, int? relativeStart, int? relativeEnd,
            CommentRangeStart commentRangeStart, CommentRangeEnd commentRangeEnd, Run commentReference)
        {
            // If no relative positions specified, insert comment markers around the entire run
            // 如果没有指定相对位置，则在整个Run前后插入评论标记
            if (relativeStart == null && relativeEnd == null)
            {
                targetRun.InsertBeforeSelf(commentRangeStart);
                targetRun.InsertAfterSelf(commentRangeEnd);
                commentRangeEnd.InsertAfterSelf(commentReference);

                _context?.Logger.LogDebug($"Successfully inserted comment markers around entire run for ID {commentId}");
                return true;
            }

            // If relative positions are specified, we need to split the run text
            // 如果指定了相对位置，需要拆分Run中的文本
            return _runTextService.SplitRunAndInsertCommentRange(commentId, targetRun, relativeStart ?? 0, relativeEnd,
                commentRangeStart, commentRangeEnd, commentReference);
        }

        /// <summary>
        /// Handle comment range insertion for multiple runs
        /// </summary>
        /// <param name="commentId">Comment ID</param>
        /// <param name="startRun">Start run</param>
        /// <param name="endRun">End run</param>
        /// <param name="relativeStart">Start position relative to the start run</param>
        /// <param name="relativeEnd">End position relative to the end run</param>
        /// <param name="commentRangeStart">Comment range start marker</param>
        /// <param name="commentRangeEnd">Comment range end marker</param>
        /// <param name="commentReference">Comment reference marker</param>
        /// <returns>Whether insertion was successful</returns>
        public bool HandleCrossRunCommentRange(string commentId, Run startRun, Run endRun, int? relativeStart, int? relativeEnd,
            CommentRangeStart commentRangeStart, CommentRangeEnd commentRangeEnd, Run commentReference)
        {
            // Handle start run - if relativeStart is specified, split the start run
            if (relativeStart.HasValue && relativeStart.Value > 0)
            {
                if (!_runTextService.SplitRunAtPosition(startRun, relativeStart.Value, out Run? beforeRun, out Run? afterRun))
                {
                    _context?.Logger.LogError($"Failed to split start run at position {relativeStart.Value}");
                    return false;
                }

                // Insert comment range start before the after run (the part that should be commented)
                afterRun?.InsertBeforeSelf(commentRangeStart);
            }
            else
            {
                // Insert comment range start before the entire start run
                startRun.InsertBeforeSelf(commentRangeStart);
            }

            // Handle end run - if relativeEnd is specified, split the end run
            if (relativeEnd.HasValue && relativeEnd.Value < endRun.InnerText.Length)
            {
                if (!_runTextService.SplitRunAtPosition(endRun, relativeEnd.Value, out Run? beforeRun, out Run? afterRun))
                {
                    _context?.Logger.LogError($"Failed to split end run at position {relativeEnd.Value}");
                    return false;
                }

                // Insert comment range end and reference after the before run (the part that should be commented)
                beforeRun?.InsertAfterSelf(commentRangeEnd);
                commentRangeEnd.InsertAfterSelf(commentReference);
            }
            else
            {
                // Insert comment range end and reference after the entire end run
                endRun.InsertAfterSelf(commentRangeEnd);
                commentRangeEnd.InsertAfterSelf(commentReference);
            }

            _context?.Logger.LogDebug($"Successfully inserted comment range for ID {commentId} across multiple runs");
            return true;
        }

        /// <summary>
        /// Insert nested comment markers in paragraph with correct positioning
        /// </summary>
        /// <param name="paragraph">Target paragraph</param>
        /// <param name="insertionPoint">Insertion point</param>
        /// <param name="commentRangeStart">Comment range start marker</param>
        /// <param name="commentRangeEnd">Comment range end marker</param>
        /// <param name="commentReference">Comment reference marker</param>
        public void InsertNestedCommentMarkers(Paragraph paragraph, Run insertionPoint,
            CommentRangeStart commentRangeStart, CommentRangeEnd commentRangeEnd, Run commentReference)
        {
            // For regular comment add operations, insert comment markers directly at the specified Run position
            try
            {
                _context?.Logger.LogDebug($"Inserting comment markers for comment {commentRangeStart.Id?.Value} around run with text: '{insertionPoint.InnerText}'");

                // Use paragraph-level insertion methods to ensure correct positioning
                paragraph.InsertBefore(commentRangeStart, insertionPoint);
                paragraph.InsertAfter(commentRangeEnd, insertionPoint);
                paragraph.InsertAfter(commentReference, commentRangeEnd);

                _context?.Logger.LogDebug($"Successfully inserted comment markers around Run for comment {commentRangeStart.Id?.Value}");
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to insert comment markers: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Insert nested comment markers at specified location
        /// Creates proper nested comment structure by placing new comment as innermost layer
        /// </summary>
        /// <param name="location">Comment location info</param>
        /// <param name="rangeStart">New range start marker</param>
        /// <param name="rangeEnd">New range end marker</param>
        /// <param name="reference">New reference marker</param>
        public void InsertNestedCommentMarkersAtLocation(CommentLocationInfo location,
            CommentRangeStart rangeStart, CommentRangeEnd rangeEnd, Run reference)
        {
            if (location.RangeStart == null || location.Reference == null)
            {
                _context?.Logger.LogWarning("Invalid location information for nested comment insertion");
                return;
            }

            // Find the last consecutive CommentRangeStart starting from location.RangeStart
            var currentRangeStart = location.RangeStart;
            var currentRangeEnd = location.RangeEnd ?? throw new InvalidOperationException("Range end cannot be null for nested comment insertion");
            var lastRangeStart = currentRangeStart;
            var lastRangeEnd = currentRangeEnd;

            // Walk through consecutive RangeStart elements to find the innermost position
            var nextElement = currentRangeStart.NextSibling();
            while (nextElement is CommentRangeStart nextRangeStart)
            {
                lastRangeStart = nextRangeStart;
                nextElement = nextRangeStart.NextSibling();
            }

            var nextEndElement = currentRangeEnd.NextSibling();
            while (nextEndElement is CommentRangeEnd nextRangeEnd)
            {
                lastRangeEnd = nextRangeEnd;
                nextEndElement = nextRangeEnd.NextSibling();
            }

            lastRangeStart.InsertAfterSelf(rangeStart);
            lastRangeEnd.InsertAfterSelf(rangeEnd);
            location.Reference.InsertAfterSelf(reference);

            _context?.Logger.LogDebug($"Inserted nested comment markers for comment {rangeStart.Id?.Value}");
        }

        /// <summary>
        /// Find comment location information in document
        /// </summary>
        /// <param name="commentId">Comment ID</param>
        /// <returns>Comment location info or null</returns>
        public CommentLocationInfo? FindCommentLocation(string commentId)
        {
            if (_mainPart?.Document?.Body == null || string.IsNullOrWhiteSpace(commentId))
            {
                return null;
            }

            // Find comment range start marker
            var rangeStart = _mainPart.Document.Body
                .Descendants<CommentRangeStart>()
                .FirstOrDefault(crs => crs.Id?.Value == commentId);

            if (rangeStart == null)
            {
                _context?.Logger.LogDebug($"Comment range start not found for ID {commentId}");
                return null;
            }

            // Find comment range end marker
            var rangeEnd = _mainPart.Document.Body
                .Descendants<CommentRangeEnd>()
                .FirstOrDefault(cre => cre.Id?.Value == commentId);

            // Find comment reference
            var reference = _mainPart.Document.Body
                .Descendants<Run>()
                .FirstOrDefault(r => r.Elements<CommentReference>()
                    .Any(cr => cr.Id?.Value == commentId));

            return new CommentLocationInfo
            {
                CommentId = commentId,
                RangeStart = rangeStart,
                RangeEnd = rangeEnd,
                Reference = reference
            };
        }

        /// <summary>
        /// Check if service is initialized
        /// </summary>
        /// <returns>Whether initialized</returns>
        public bool IsInitialized => _mainPart != null;

        /// <summary>
        /// Ensure service is initialized
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown when service is not initialized</exception>
        private void EnsureInitialized()
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException("CommentRangeService must be initialized before use");
            }
        }
    }
}
