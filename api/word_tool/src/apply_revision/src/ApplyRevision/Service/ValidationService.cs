using Amazon.Lambda.Core;
using ApplyRevision.Exceptions;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// 验证服务，负责各种业务逻辑验证
    /// Validation service responsible for various business logic validations
    /// </summary>
    public class ValidationService
    {
        private readonly ILambdaContext? _context;

        /// <summary>
        /// 初始化验证服务
        /// Initialize validation service
        /// </summary>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        public ValidationService(ILambdaContext? context)
        {
            _context = context;
        }

        /// <summary>
        /// 验证评论是否存在
        /// Validate if comment exists
        /// </summary>
        /// <param name="comment">要验证的评论 / Comment to validate</param>
        /// <param name="commentId">评论ID / Comment ID</param>
        /// <exception cref="CommentNotFoundException">当评论不存在时抛出 / Thrown when comment does not exist</exception>
        public void ValidateCommentExists(Comment? comment, string commentId)
        {
            if (comment == null)
            {
                var message = $"Comment with ID '{commentId}' was not found";
                _context?.Logger.LogError(message);
                throw new CommentNotFoundException(commentId, message);
            }

            _context?.Logger.LogDebug($"Comment validation passed for ID {commentId}");
        }

        /// <summary>
        /// 验证评论回复的必要参数
        /// Validate required parameters for comment reply
        /// </summary>
        /// <param name="parentCommentId">父评论ID / Parent comment ID</param>
        /// <param name="author">作者 / Author</param>
        /// <param name="text">回复内容 / Reply text</param>
        /// <exception cref="ArgumentException">当参数无效时抛出 / Thrown when parameters are invalid</exception>
        public void ValidateCommentReplyParameters(string parentCommentId, string author, string text)
        {
            if (string.IsNullOrWhiteSpace(parentCommentId))
            {
                throw new ArgumentException("Parent comment ID cannot be null or empty", nameof(parentCommentId));
            }

            if (string.IsNullOrWhiteSpace(author))
            {
                throw new ArgumentException("Author cannot be null or empty", nameof(author));
            }

            if (string.IsNullOrWhiteSpace(text))
            {
                throw new ArgumentException("Reply text cannot be null or empty", nameof(text));
            }

            _context?.Logger.LogDebug($"Comment reply parameters validation passed for parent ID {parentCommentId}");
        }

        /// <summary>
        /// 验证字符串参数不为空
        /// Validate string parameter is not null or empty
        /// </summary>
        /// <param name="value">要验证的值 / Value to validate</param>
        /// <param name="parameterName">参数名称 / Parameter name</param>
        /// <exception cref="ArgumentException">当参数为空时抛出 / Thrown when parameter is null or empty</exception>
        public void ValidateNotNullOrEmpty(string? value, string parameterName)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                throw new ArgumentException($"{parameterName} cannot be null or empty", parameterName);
            }
        }

        /// <summary>
        /// 验证对象不为空
        /// Validate object is not null
        /// </summary>
        /// <param name="obj">要验证的对象 / Object to validate</param>
        /// <param name="parameterName">参数名称 / Parameter name</param>
        /// <exception cref="ArgumentNullException">当对象为空时抛出 / Thrown when object is null</exception>
        public void ValidateNotNull(object? obj, string parameterName)
        {
            ArgumentNullException.ThrowIfNull(obj, parameterName);
        }

        /// <summary>
        /// 验证评论ID格式
        /// Validate comment ID format
        /// </summary>
        /// <param name="commentId">评论ID / Comment ID</param>
        /// <exception cref="ArgumentException">当ID格式无效时抛出 / Thrown when ID format is invalid</exception>
        public void ValidateCommentIdFormat(string commentId)
        {
            ValidateNotNullOrEmpty(commentId, nameof(commentId));

            if (!int.TryParse(commentId, out int id) || id < 0)
            {
                throw new ArgumentException($"Comment ID must be a positive integer, got: {commentId}", nameof(commentId));
            }

            _context?.Logger.LogDebug($"Comment ID format validation passed for ID {commentId}");
        }
    }
}
