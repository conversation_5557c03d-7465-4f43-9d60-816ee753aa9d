using Amazon.Lambda.Core;
using ApplyRevision.Component;
using ApplyRevision.Exceptions;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Comment orchestrator interface
    /// </summary>
    public interface ICommentOrchestrator
    {
        /// <summary>
        /// Initialize orchestrator with document parts
        /// </summary>
        /// <param name="mainPart">Main document part</param>
        void Initialize(MainDocumentPart mainPart);

        /// <summary>
        /// Create comment component
        /// </summary>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="message">Message content</param>
        /// <returns>Comment component</returns>
        CommentComponent CreateCommentComponent(string author, string date, string message);

        /// <summary>
        /// Create comment reply component
        /// </summary>
        /// <param name="parentCommentId">Parent comment ID</param>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="text">Reply text</param>
        /// <returns>Comment reply component</returns>
        CommentReplyComponent CreateCommentReplyComponent(string parentCommentId, string author, string date, string text);

        /// <summary>
        /// Handle complete comment reply process
        /// </summary>
        /// <param name="parentCommentId">Parent comment ID</param>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="text">Reply text</param>
        /// <returns>Whether processing was successful</returns>
        bool ProcessCommentReply(string parentCommentId, string author, string date, string text);

        /// <summary>
        /// Handle complete comment reply process with automatic saving
        /// </summary>
        /// <param name="parentCommentId">Parent comment ID</param>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="text">Reply text</param>
        /// <param name="autoSave">Whether to auto-save</param>
        /// <returns>Whether processing was successful</returns>
        bool ProcessCommentReply(string parentCommentId, string author, string date, string text, bool autoSave);

        bool ProcessCommentAddOnRun(Run run, string author, string date, string text, int relativeStart, int relativeEnd);

        bool ProcessCommentAdd(RangeRunResult rangeRunResult, string author, string date, string text);

        /// <summary>
        /// Save all comment changes to document
        /// </summary>
        /// <returns>Whether saving was successful</returns>
        bool Save();

        /// <summary>
        /// Check if orchestrator is initialized
        /// </summary>
        bool IsInitialized { get; }
    }

    /// <summary>
    /// 评论编排器，协调各个服务完成评论相关操作
    /// Comment orchestrator that coordinates various services to complete comment-related operations
    /// </summary>
    /// <param name="context">Lambda上下文 / Lambda context</param>
    /// <param name="commentXmlService">评论XML服务 / Comment XML service</param>
    /// <param name="documentManipulationService">文档操作服务 / Document manipulation service</param>
    /// <param name="commentReplyExtensionService">评论回复扩展服务 / Comment reply extension service</param>
    /// <param name="validationService">验证服务 / Validation service</param>
    /// <param name="idManager">ID管理器 / ID manager</param>
    /// <param name="elementFactory">元素工厂 / Element factory</param>
    public class CommentOrchestrator(
        ILambdaContext? context,
        CommentXmlService commentXmlService,
        DocumentManipulationService documentManipulationService,
        ICommentReplyExtensionService commentReplyExtensionService,
        ValidationService validationService,
        IIdManager idManager,
        IElementFactory elementFactory) : ICommentOrchestrator
    {
        private readonly ILambdaContext? _context = context;
        private readonly CommentXmlService _commentXmlService = commentXmlService ?? throw new ArgumentNullException(nameof(commentXmlService));
        private readonly DocumentManipulationService _documentManipulationService = documentManipulationService ?? throw new ArgumentNullException(nameof(documentManipulationService));
        private readonly ICommentReplyExtensionService _commentReplyExtensionService = commentReplyExtensionService ?? throw new ArgumentNullException(nameof(commentReplyExtensionService));
        private readonly ValidationService _validationService = validationService ?? throw new ArgumentNullException(nameof(validationService));
        private readonly IIdManager _idManager = idManager ?? throw new ArgumentNullException(nameof(idManager));
        private readonly IElementFactory _elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));

        /// <summary>
        /// 初始化编排器，设置文档部分
        /// Initialize orchestrator with document parts
        /// </summary>
        /// <param name="mainPart">主文档部分 / Main document part</param>
        public void Initialize(MainDocumentPart mainPart)
        {
            ArgumentNullException.ThrowIfNull(mainPart);

            _commentXmlService.Initialize(mainPart);
            _documentManipulationService.Initialize(mainPart);
            _commentReplyExtensionService.Initialize(mainPart);

            _context?.Logger.LogDebug("CommentOrchestrator initialized");
        }

        /// <summary>
        /// Create comment component
        /// </summary>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="message">Message content</param>
        /// <returns>Comment component</returns>
        public CommentComponent CreateCommentComponent(string author, string date, string message)
        {
            _validationService.ValidateNotNullOrEmpty(author, nameof(author));
            _validationService.ValidateNotNullOrEmpty(date, nameof(date));
            _validationService.ValidateNotNullOrEmpty(message, nameof(message));

            var comment = _commentXmlService.CreateComment(author, date, message);
            var commentRangeStart = _elementFactory.CreateCommentRangeStart(comment.Id!.Value!);
            var commentRangeEnd = _elementFactory.CreateCommentRangeEnd(comment.Id!.Value!);
            var commentReference = _elementFactory.CreateCommentReference(comment.Id!.Value!);

            var component = new CommentComponent
            {
                Comment = comment,
                RangeStart = commentRangeStart,
                RangeEnd = commentRangeEnd,
                Reference = commentReference
            };

            _context?.Logger.LogDebug($"Created comment component with ID {comment.Id!.Value}");
            return component;
        }

        /// <summary>
        /// Create comment reply component
        /// </summary>
        /// <param name="parentCommentId">Parent comment ID</param>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="text">Reply text</param>
        /// <returns>Comment reply component</returns>
        public CommentReplyComponent CreateCommentReplyComponent(string parentCommentId, string author, string date, string text)
        {
            // Validate parameters
            _validationService.ValidateCommentReplyParameters(parentCommentId, author, text);
            _validationService.ValidateCommentIdFormat(parentCommentId);

            // Find parent comment
            var parentComment = _commentXmlService.FindCommentById(parentCommentId);
            _validationService.ValidateCommentExists(parentComment, parentCommentId);

            // Create reply comment
            var replyComment = _commentXmlService.CreateComment(author, date, text);

            // Generate durable IDs
            var replyDurableId = _idManager.GenerateParaId();
            var parentDurableId = _idManager.GenerateParaId();
            var utcDateString = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ");

            // Create reply component
            var replyComponent = new CommentReplyComponent(
                parentComment!,
                replyComment,
                replyDurableId,
                parentDurableId,
                utcDateString);

            _context?.Logger.LogDebug($"Created comment reply component: Reply {replyComment.Id!.Value} -> Parent {parentCommentId}");
            return replyComponent;
        }

        /// <summary>
        /// Handle complete comment reply process
        /// </summary>
        /// <param name="parentCommentId">父评论ID / Parent comment ID</param>
        /// <param name="author">作者 / Author</param>
        /// <param name="date">日期 / Date</param>
        /// <param name="text">回复内容 / Reply text</param>
        /// <returns>是否成功处理 / Whether processing was successful</returns>
        public bool ProcessCommentReply(string parentCommentId, string author, string date, string text)
        {
            try
            {
                _context?.Logger.LogInformation($"Starting comment reply process for parent ID {parentCommentId}");

                // 创建回复组件
                // Create reply component
                var replyComponent = CreateCommentReplyComponent(parentCommentId, author, date, text);

                // 添加评论
                // Add comment
                // AddCommentToDocument(replyComponent.ReplyComment);
                _commentXmlService.AddComment(replyComponent.ReplyComment);

                // 创建扩展XML文件
                // Create extension XML files
                var extensionSuccess = _commentReplyExtensionService.CreateReplyExtensions(
                    replyComponent.ParentComment,
                    replyComponent.ReplyComment,
                    replyComponent.ReplyDurableId,
                    replyComponent.ParentDurableId,
                    replyComponent.UtcDateString);

                if (!extensionSuccess)
                {
                    _context?.Logger.LogWarning($"Extension XML creation failed for reply {replyComponent.GetReplyCommentId()}");
                    return false;
                }

                // 在文档中插入评论回复引用（在父评论位置）
                // Insert comment reply reference in document (at parent comment location)
                var referenceSuccess = _documentManipulationService.InsertCommentReplyReference(
                    parentCommentId, replyComponent.GetReplyCommentId());

                if (!referenceSuccess)
                {
                    _context?.Logger.LogWarning($"Comment reference insertion failed for reply {replyComponent.GetReplyCommentId()}");
                    // 这不是致命错误，继续处理
                    // This is not a fatal error, continue processing
                }

                _context?.Logger.LogInformation($"Successfully processed comment reply: {replyComponent}");
                return true;
            }
            catch (CommentNotFoundException ex)
            {
                // 记录错误并返回false，而不是重新抛出异常
                // Log error and return false instead of re-throwing exception
                _context?.Logger.LogError($"Failed to process comment reply for parent ID {parentCommentId}: {ex.Message}");
                return false;
            }
            catch (ArgumentException ex)
            {
                // 记录错误并返回false，而不是重新抛出异常
                // Log error and return false instead of re-throwing exception
                _context?.Logger.LogError($"Failed to process comment reply for parent ID {parentCommentId}: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to process comment reply for parent ID {parentCommentId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Handle complete comment reply process with automatic saving
        /// </summary>
        /// <param name="parentCommentId">Parent comment ID</param>
        /// <param name="author">Author</param>
        /// <param name="date">Date</param>
        /// <param name="text">Reply text</param>
        /// <param name="autoSave">Whether to auto-save</param>
        /// <returns>Whether processing was successful</returns>
        public bool ProcessCommentReply(string parentCommentId, string author, string date, string text, bool autoSave)
        {
            var success = ProcessCommentReply(parentCommentId, author, date, text);

            if (success && autoSave)
            {
                var saveSuccess = Save();
                if (!saveSuccess)
                {
                    _context?.Logger.LogWarning($"Comment reply processed successfully but save failed for parent ID {parentCommentId}");
                }
                return saveSuccess;
            }

            return success;
        }

        public bool ProcessCommentAdd(RangeRunResult rangeRunResult, string author, string date, string text)
        {
            try
            {
                _context?.Logger.LogInformation($"Starting comment add process for author: {author}");

                // Validate input parameters
                _validationService.ValidateNotNullOrEmpty(author, nameof(author));
                _validationService.ValidateNotNullOrEmpty(date, nameof(date));
                _validationService.ValidateNotNullOrEmpty(text, nameof(text));

                if (rangeRunResult == null)
                {
                    _context?.Logger.LogError("RangeRunResult is null");
                    return false;
                }

                if (rangeRunResult.StartRun == null || rangeRunResult.EndRun == null)
                {
                    _context?.Logger.LogError("StartRun or EndRun in RangeRunResult is null");
                    return false;
                }

                // Create comment component
                var commentComponent = CreateCommentComponent(author, date, text);
                var commentId = commentComponent.Comment.Id!.Value!.ToString();

                // Add the comment to XML service
                _commentXmlService.AddComment(commentComponent.Comment);

                // Insert comment range using the DocumentManipulationService
                var rangeSuccess = _documentManipulationService.InsertCommentRange(
                    commentId,
                    rangeRunResult.StartRun,
                    rangeRunResult.EndRun,
                    rangeRunResult.RelativeStartPosition,
                    rangeRunResult.RelativeEndPosition);

                if (!rangeSuccess)
                {
                    _context?.Logger.LogWarning($"Comment range insertion failed for comment ID {commentId}");
                    return false;
                }

                _context?.Logger.LogInformation($"Successfully processed comment add for ID {commentId}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to process comment add: {ex.Message}");
                return false;
            }
        }

        public bool ProcessCommentAddOnRun(Run run, string author, string date, string text, int relativeStart, int relativeEnd)
        {
            try
            {
                // Create single comment for the range
                var commentComponent = CreateCommentComponent(author, date, text);
                var commentId = commentComponent.Comment.Id!.Value!.ToString();

                // Add the comment to XML service
                _commentXmlService.AddComment(commentComponent.Comment);

                // Insert comment range: start at first run, end and reference at last run
                // For single SegId, firstRun == lastRun, so it works the same way
                var rangeSuccess = _documentManipulationService.InsertCommentRange(
                    commentId,
                    run,
                    run,
                    relativeStart,
                    relativeEnd);
                return rangeSuccess;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to process comment add range: {ex.Message}");
                return false;
            }

        }

        /// <summary>
        /// Save all comment changes to document
        /// </summary>
        /// <returns>Whether saving was successful</returns>
        public bool Save()
        {
            try
            {
                if (!IsInitialized)
                {
                    _context?.Logger.LogWarning("CommentOrchestrator not initialized, cannot save changes");
                    return false;
                }

                // Save comments part
                _commentXmlService.Save();

                // Save main document part
                _documentManipulationService.Save();

                _context?.Logger.LogInformation("Successfully saved all comment changes to document");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to save comment changes: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if orchestrator is initialized
        /// </summary>
        public bool IsInitialized => _commentXmlService.IsInitialized &&
                                   _documentManipulationService.IsInitialized &&
                                   _commentReplyExtensionService.IsInitialized;
    }
}
