using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Amazon.Lambda.Core;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Implementation of ID manager for revisions and comments
    /// 修订和注释的ID管理器实现
    /// </summary>
    public class IdManager : IIdManager
    {
        private int _maxCommentId;
        private int _maxRevisionId;
        private readonly ILambdaContext? _context;

        public IdManager(ILambdaContext? context = null)
        {
            _context = context;
            Reset();
        }

        /// <summary>
        /// Initialize the ID manager by scanning existing document for current max IDs
        /// </summary>
        /// <param name="commentsPart">Comments part to scan for existing comment IDs</param>
        /// <param name="documentBody">Document body to scan for existing revision IDs</param>
        public void Initialize(WordprocessingCommentsPart? commentsPart, Body? documentBody)
        {
            _maxCommentId = ScanExistingCommentIds(commentsPart);
            _maxRevisionId = ScanExistingRevisionIds(documentBody);

            _context?.Logger.LogInformation($"ID Manager initialized - Max Comment ID: {_maxCommentId}, Max Revision ID: {_maxRevisionId}");
        }

        /// <summary>
        /// Get the next available comment ID
        /// </summary>
        /// <returns>Next comment ID</returns>
        public int GetNextCommentId()
        {
            return ++_maxCommentId;
        }

        /// <summary>
        /// Get the next available revision ID
        /// 获取下一个可用的修订ID
        /// </summary>
        /// <returns>Next revision ID</returns>
        public int GetNextRevisionId()
        {
            return ++_maxRevisionId;
        }

        /// <summary>
        /// Generate a unique paragraph ID for comments
        /// 为注释生成唯一的段落ID
        /// </summary>
        /// <returns>Unique paragraph ID as string</returns>
        public string GenerateParaId()
        {
            return GenerateDurableId();
        }

        /// <summary>
        /// Generate a unique durable ID for comment extensibility (Word 2016+)
        /// 为注释扩展性生成唯一的持久ID（Word 2016+）
        /// </summary>
        /// <returns>8-character hexadecimal durable ID that starts with 0-7</returns>
        public string GenerateDurableId()
        {
            // Generate a GUID and convert to 8-character hex string
            // 生成GUID并转换为8位十六进制字符串
            string durableId;
            int attempts = 0;
            const int maxAttempts = 10;

            do
            {
                var guid = Guid.NewGuid();
                durableId = guid.ToString("N")[..8].ToUpper();
                attempts++;

                // Word requires durableId to start with 0-7 (3-bit range)
                // Word要求durableId以0-7开头（3位范围）
                if (durableId[0] >= '0' && durableId[0] <= '7')
                {
                    break;
                }

                // If first character is 8-9 or A-F, map it to 0-7
                // 如果第一个字符是8-9或A-F，映射到0-7
                char firstChar = durableId[0];
                char replacement;

                if (firstChar >= '8' && firstChar <= '9')
                {
                    // Map 8->0, 9->1
                    replacement = (char)('0' + (firstChar - '8'));
                }
                else if (firstChar >= 'A' && firstChar <= 'F')
                {
                    // Map A->2, B->3, C->4, D->5, E->6, F->7
                    replacement = (char)('2' + (firstChar - 'A'));
                }
                else
                {
                    // Fallback to '0' for any unexpected character
                    replacement = '0';
                }

                durableId = replacement + durableId[1..];
                break;
            }
            while (attempts < maxAttempts);

            _context?.Logger.LogDebug($"Generated durable ID: {durableId} (attempts: {attempts}, first char: {durableId[0]})");
            return durableId;
        }

        /// <summary>
        /// Reset all counters to initial state
        /// 重置所有计数器到初始状态
        /// </summary>
        public void Reset()
        {
            _maxCommentId = 0;
            _maxRevisionId = 0;
        }

        /// <summary>
        /// Scan existing comments to find the highest comment ID
        /// </summary>
        /// <param name="commentsPart">Comments part to scan</param>
        /// <returns>Maximum existing comment ID, or 0 if none found</returns>
        private int ScanExistingCommentIds(WordprocessingCommentsPart? commentsPart)
        {
            if (commentsPart?.Comments == null)
            {
                _context?.Logger.LogDebug("No comments part found, starting comment IDs from 0");
                return 0;
            }

            var commentElements = commentsPart.Comments.Elements<Comment>();
            if (!commentElements.Any())
            {
                _context?.Logger.LogDebug("No existing comments found, starting comment IDs from 0");
                return 0;
            }

            int maxId = 0;
            foreach (var comment in commentElements)
            {
                if (comment.Id?.Value != null && int.TryParse(comment.Id.Value, out int id))
                {
                    if (id > maxId)
                    {
                        maxId = id;
                    }
                }
            }

            _context?.Logger.LogDebug($"Found {commentElements.Count()} existing comments, max ID: {maxId}");
            return maxId;
        }

        /// <summary>
        /// Scan document body to find the highest revision ID
        /// </summary>
        /// <param name="documentBody">Document body to scan</param>
        /// <returns>Maximum existing revision ID, or 0 if none found</returns>
        private int ScanExistingRevisionIds(Body? documentBody)
        {
            if (documentBody == null)
            {
                _context?.Logger.LogWarning("Document body is null, starting revision IDs from 0");
                return 0;
            }

            int maxId = 0;
            var revisionElements = documentBody.Descendants<OpenXmlElement>()
                .Where(element => IsRevisionElement(element));

            foreach (var element in revisionElements)
            {
                string? revisionId = ExtractRevisionId(element);
                if (!string.IsNullOrEmpty(revisionId) && int.TryParse(revisionId, out int id))
                {
                    if (id > maxId)
                    {
                        maxId = id;
                    }
                }
            }

            _context?.Logger.LogDebug($"Scanned document body, found max revision ID: {maxId}");
            return maxId;
        }

        /// <summary>
        /// Check if the element is a revision-related element
        /// </summary>
        /// <param name="element">Element to check</param>
        /// <returns>True if element is revision-related</returns>
        private static bool IsRevisionElement(OpenXmlElement element)
        {
            return element is Inserted ||
                   element is Deleted ||
                   element is InsertedRun ||
                   element is DeletedRun ||
                   element is MoveFromRun ||
                   element is MoveToRun ||
                   element is MoveFromRangeStart ||
                   element is MoveFromRangeEnd ||
                   element is MoveToRangeStart ||
                   element is MoveToRangeEnd ||
                   element is ParagraphPropertiesChange ||
                   element is RunPropertiesChange ||
                   element is SectionPropertiesChange ||
                   element is TablePropertiesChange ||
                   element is TableRowPropertiesChange ||
                   element is TableCellPropertiesChange ||
                   element is MoveFromMathControl ||
                   element is MoveToMathControl;
        }

        /// <summary>
        /// Extract revision ID from revision element
        /// </summary>
        /// <param name="element">Revision element</param>
        /// <returns>Revision ID if found, null otherwise</returns>
        private static string? ExtractRevisionId(OpenXmlElement element)
        {
            return element switch
            {
                Inserted inserted => inserted.Id?.Value,
                Deleted deleted => deleted.Id?.Value,
                InsertedRun insertedRun => insertedRun.Id?.Value,
                DeletedRun deletedRun => deletedRun.Id?.Value,
                MoveFromRun moveFromRun => moveFromRun.Id?.Value,
                MoveToRun moveToRun => moveToRun.Id?.Value,
                MoveFromRangeStart moveFromRangeStart => moveFromRangeStart.Id?.Value,
                MoveFromRangeEnd moveFromRangeEnd => moveFromRangeEnd.Id?.Value,
                MoveToRangeStart moveToRangeStart => moveToRangeStart.Id?.Value,
                MoveToRangeEnd moveToRangeEnd => moveToRangeEnd.Id?.Value,
                ParagraphPropertiesChange paragraphPropertiesChange => paragraphPropertiesChange.Id?.Value,
                RunPropertiesChange runPropertiesChange => runPropertiesChange.Id?.Value,
                SectionPropertiesChange sectionPropertiesChange => sectionPropertiesChange.Id?.Value,
                TablePropertiesChange tablePropertiesChange => tablePropertiesChange.Id?.Value,
                TableRowPropertiesChange tableRowPropertiesChange => tableRowPropertiesChange.Id?.Value,
                TableCellPropertiesChange tableCellPropertiesChange => tableCellPropertiesChange.Id?.Value,
                MoveFromMathControl moveFromMathControl => moveFromMathControl.Id?.Value,
                MoveToMathControl moveToMathControl => moveToMathControl.Id?.Value,
                _ => null
            };
        }
    }
}