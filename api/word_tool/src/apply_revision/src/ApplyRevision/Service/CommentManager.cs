using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Component;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Implementation of comment manager for DOCX documents
    /// Provides unified comment management operations using ElementFactory and IdManager
    /// </summary>
    /// <remarks>
    /// Initialize a new CommentManager instance
    /// </remarks>
    /// <param name="context">Lambda context for logging</param>
    /// <param name="elementFactory">Factory for creating OpenXML elements</param>
    /// <param name="idManager">Manager for generating unique IDs</param>
    public class CommentManager(ILambdaContext? context, IElementFactory elementFactory, IIdManager idManager) : ICommentManager
    {
        private readonly ILambdaContext? _context = context;
        private readonly IElementFactory _elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
        private readonly IIdManager _idManager = idManager ?? throw new ArgumentNullException(nameof(idManager));
        private WordprocessingCommentsPart? _commentsPart;
        private MainDocumentPart? _mainPart;

        /// <summary>
        /// Check if the comment manager has been properly initialized
        /// </summary>
        public bool IsInitialized => _mainPart != null && _commentsPart != null;

        /// <summary>
        /// Get the total number of comments in the document
        /// </summary>
        public int CommentCount
        {
            get
            {
                if (_commentsPart?.Comments == null)
                    return 0;

                return _commentsPart.Comments.Elements<Comment>().Count();
            }
        }

        /// <summary>
        /// Initialize the comment manager with document parts
        /// Sets up the comments part and initializes the ID manager
        /// </summary>
        /// <param name="mainPart">Main document part containing the comments</param>
        public void Initialize(MainDocumentPart mainPart)
        {
            _mainPart = mainPart ?? throw new ArgumentNullException(nameof(mainPart));

            // Initialize or create the comments part
            _commentsPart = _mainPart.WordprocessingCommentsPart ?? _mainPart.AddNewPart<WordprocessingCommentsPart>();

            if (_commentsPart.Comments == null)
            {
                _commentsPart.Comments = new Comments();
            }

            // Initialize the ID manager with current document state
            _idManager.Initialize(_commentsPart, _mainPart.Document?.Body);

            _context?.Logger.LogInformation($"CommentManager initialized. Current comment count: {CommentCount}");
        }

        /// <summary>
        /// Create a Comment element with specified metadata and content
        /// </summary>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <returns>A Comment element with generated ID</returns>
        public Comment CreateComment(string author, string date, string message)
        {
            EnsureInitialized();

            int commentId = _idManager.GetNextCommentId();
            var paraId = _idManager.GenerateParaId();
            var comment = _elementFactory.CreateCommentElement(commentId, author, date, message, paraId);

            _context?.Logger.LogDebug($"Created comment with ID {commentId} by author '{author}'");
            return comment;
        }

        /// <summary>
        /// Create a complete comment component with range markers and reference
        /// </summary>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <returns>A CommentComponent with all related elements</returns>
        public CommentComponent CreateCommentComponent(string author, string date, string message)
        {
            var comment = CreateComment(author, date, message);
            var builder = new CommentComponentBuilder(comment);

            return builder.Build();
        }

        /// <summary>
        /// Create a comment reference run for linking to an existing comment
        /// </summary>
        /// <param name="commentId">ID of the comment to reference</param>
        /// <returns>A Run element containing the comment reference</returns>
        public Run CreateCommentReference(string commentId)
        {
            return _elementFactory.CreateCommentReference(commentId);
        }

        /// <summary>
        /// Add a comment to the document's comment collection
        /// </summary>
        /// <param name="comment">Comment to add</param>
        public void AddComment(Comment comment)
        {
            EnsureInitialized();

            ArgumentNullException.ThrowIfNull(comment);

            _commentsPart!.Comments.Append(comment);
            _context?.Logger.LogDebug($"Added comment with ID {comment.Id} to document");
        }

        /// <summary>
        /// Add multiple comments to the document's comment collection
        /// </summary>
        /// <param name="comments">Comments to add</param>
        public void AddComments(IEnumerable<Comment> comments)
        {
            if (comments == null)
                throw new ArgumentNullException(nameof(comments));

            var commentList = comments.ToList();
            if (!commentList.Any())
                return;

            EnsureInitialized();

            foreach (var comment in commentList)
            {
                _commentsPart!.Comments.Append(comment);
            }

            _context?.Logger.LogDebug($"Added {commentList.Count} comments to document");
        }

        /// <summary>
        /// Create comments for revision links and return comment components
        /// </summary>
        /// <param name="revisionLinks">List of revision links to create comments for</param>
        /// <returns>List of comment components for the revision links</returns>
        public List<CommentComponent> CreateRevisionLinkComments(List<RevisionLink> revisionLinks)
        {
            var commentComponents = new List<CommentComponent>();

            if (revisionLinks == null || !revisionLinks.Any())
                return commentComponents;

            // Process links in reverse order to maintain proper document order
            var reversedLinks = revisionLinks.AsEnumerable().Reverse().ToList();

            foreach (var revisionLink in reversedLinks)
            {
                try
                {
                    var component = CreateCommentComponent(
                        revisionLink.Author,
                        revisionLink.Date,
                        revisionLink.Comment
                    );

                    commentComponents.Add(component);
                }
                catch (Exception ex)
                {
                    _context?.Logger.LogWarning($"Failed to create comment for revision link: {ex.Message}");
                }
            }

            _context?.Logger.LogDebug($"Created {commentComponents.Count} comment components for revision links");
            return commentComponents;
        }

        /// <summary>
        /// Insert comment components around specified elements in the document
        /// </summary>
        /// <param name="startElement">Element to insert comment start before</param>
        /// <param name="endElement">Element to insert comment end and reference after</param>
        /// <param name="commentComponents">Comment components to insert</param>
        public void InsertCommentComponents(OpenXmlElement startElement, OpenXmlElement endElement, IEnumerable<CommentComponent> commentComponents)
        {
            if (startElement == null || endElement == null)
                return;

            var components = commentComponents?.ToList();
            if (components == null || !components.Any())
                return;

            EnsureInitialized();

            var currentStart = startElement;
            var currentEnd = endElement;

            foreach (var component in components)
            {
                try
                {
                    // Add the comment to the document
                    AddComment(component.Comment);

                    // Insert range start before the current start element
                    currentStart.InsertBeforeSelf(component.RangeStart);

                    // Insert range end after the current end element
                    currentEnd.InsertAfterSelf(component.RangeEnd);

                    // Insert comment reference after the range end
                    component.RangeEnd.InsertAfterSelf(component.Reference);

                    // Update positions for nested comments
                    currentStart = component.RangeStart;
                    currentEnd = component.Reference;

                    _context?.Logger.LogDebug($"Inserted comment component with ID {component.Comment.Id}");
                }
                catch (Exception ex)
                {
                    _context?.Logger.LogWarning($"Failed to insert comment component: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get the next available comment ID
        /// </summary>
        /// <returns>Next comment ID</returns>
        public int GetNextCommentId()
        {
            EnsureInitialized();
            return _idManager.GetNextCommentId();
        }

        /// <summary>
        /// Generate initials from author name for comment display
        /// </summary>
        /// <param name="author">Author full name</param>
        /// <returns>Author initials (e.g., "John Doe" -> "JD")</returns>
        public StringValue GetAuthorInitials(string author)
        {
            return _elementFactory.GetCommentInitials(author);
        }

        /// <summary>
        /// Find a comment by its ID
        /// 通过ID查找评论
        /// </summary>
        /// <param name="commentId">Comment ID to search for / 要搜索的评论ID</param>
        /// <returns>Comment element if found, null otherwise / 如果找到则返回评论元素，否则返回null</returns>
        public Comment? FindCommentById(string commentId)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(commentId))
            {
                _context?.Logger.LogWarning("Comment ID cannot be null or empty");
                return null;
            }

            try
            {
                var comment = _commentsPart!.Comments
                    .Elements<Comment>()
                    .FirstOrDefault(c => c.Id?.Value == commentId);

                if (comment != null)
                {
                    _context?.Logger.LogDebug($"Found comment with ID: {commentId}");
                }
                else
                {
                    _context?.Logger.LogWarning($"Comment with ID {commentId} not found");
                }

                return comment;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Error finding comment with ID {commentId}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Save all comment changes to the document
        /// </summary>
        public void Save()
        {
            if (!IsInitialized)
            {
                _context?.Logger.LogWarning("CommentManager not initialized, cannot save comments");
                return;
            }

            try
            {
                _commentsPart!.Comments.Save();
                _mainPart!.Document.Save();

                _context?.Logger.LogInformation($"Saved {CommentCount} comments to document");
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to save comments: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Ensure the comment manager has been properly initialized
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown if not initialized</exception>
        private void EnsureInitialized()
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException("CommentManager must be initialized before use. Call Initialize() method first.");
            }
        }

        /// <summary>
        /// Generate comment content based on revision operator information
        /// </summary>
        /// <param name="revisionOperator">Revision operator containing comment information</param>
        /// <returns>Generated comment content</returns>
        public string GenerateCommentContent(RevisionOperator revisionOperator)
        {
            if (!string.IsNullOrEmpty(revisionOperator.Comment))
                return revisionOperator.Comment;

            string action = revisionOperator.Op switch
            {
                RevisionOperatorType.ins => "Insert",
                RevisionOperatorType.del => "Delete",
                RevisionOperatorType.moveFrom => "Move From",
                RevisionOperatorType.moveTo => "Move To",
                _ => "Change"
            };

            return $"{action}: {revisionOperator.Text}";
        }

        /// <summary>
        /// Create a comment for a revision operator
        /// </summary>
        /// <param name="revisionOperator">Revision operator to create comment for</param>
        /// <returns>Created comment</returns>
        public Comment CreateRevisionComment(RevisionOperator revisionOperator)
        {
            var commentContent = GenerateCommentContent(revisionOperator);
            return CreateComment(revisionOperator.Author, revisionOperator.Date, commentContent);
        }

        /// <summary>
        /// Dispose of resources
        /// </summary>
        public void Dispose()
        {
            // Clean up any resources if needed
            _commentsPart = null;
            _mainPart = null;
        }
    }
}