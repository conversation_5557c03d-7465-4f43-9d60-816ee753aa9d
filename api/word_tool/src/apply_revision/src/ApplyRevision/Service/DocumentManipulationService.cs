using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// 文档操作服务，负责对Word文档内容的直接操作
    /// Document manipulation service responsible for direct operations on Word document content
    /// </summary>
    public class DocumentManipulationService
    {
        private readonly ILambdaContext? _context;
        private readonly IElementFactory _elementFactory;
        private readonly RunTextManipulationService _runTextService;
        private readonly CommentRangeService _commentRangeService;
        private MainDocumentPart? _mainPart;

        /// <summary>
        /// 初始化文档操作服务
        /// Initialize document manipulation service
        /// </summary>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        /// <param name="elementFactory">元素工厂 / Element factory</param>
        public DocumentManipulationService(ILambdaContext? context, IElementFactory elementFactory)
        {
            _context = context;
            _elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
            _runTextService = new RunTextManipulationService(context, elementFactory);
            _commentRangeService = new CommentRangeService(context, elementFactory, _runTextService);
        }

        /// <summary>
        /// 初始化服务，设置文档部分
        /// Initialize service with document parts
        /// </summary>
        /// <param name="mainPart">主文档部分 / Main document part</param>
        public void Initialize(MainDocumentPart mainPart)
        {
            _mainPart = mainPart ?? throw new ArgumentNullException(nameof(mainPart));
            _commentRangeService.Initialize(mainPart);
            _context?.Logger.LogDebug("DocumentManipulationService initialized");
        }

        /// <summary>
        /// 在文档中插入评论引用标记，支持嵌套评论
        /// Insert comment reference markers in document with nested comment support
        /// </summary>
        /// <param name="commentId">评论ID / Comment ID</param>
        /// <param name="insertionPoint">插入点 / Insertion point</param>
        /// <returns>是否成功插入 / Whether insertion was successful</returns>
        public bool InsertCommentReference(string commentId, Run? insertionPoint = null)
        {
            try
            {
                if (_mainPart?.Document?.Body == null)
                {
                    _context?.Logger.LogError("Document body is not available");
                    return false;
                }

                // 创建评论引用标记
                // Create comment reference markers
                var commentRangeStart = _elementFactory.CreateCommentRangeStart(commentId);
                var commentRangeEnd = _elementFactory.CreateCommentRangeEnd(commentId);
                var commentReference = _elementFactory.CreateCommentReference(commentId);

                if (insertionPoint != null)
                {
                    insertionPoint.InsertBeforeSelf(commentRangeStart);
                    insertionPoint.InsertAfterSelf(commentRangeEnd);
                    insertionPoint.InsertAfterSelf(commentReference);
                }
                else
                {
                    // 在文档末尾插入
                    // Insert at document end
                    var lastParagraph = _mainPart.Document.Body.Elements<Paragraph>().LastOrDefault();
                    if (lastParagraph != null)
                    {
                        lastParagraph.Append(commentRangeStart);
                        lastParagraph.Append(commentRangeEnd);
                        lastParagraph.Append(commentReference);
                    }
                    else
                    {
                        // 创建新段落
                        // Create new paragraph
                        var newParagraph = _elementFactory.CreateParagraph();
                        newParagraph.Append(commentRangeStart);
                        newParagraph.Append(commentRangeEnd);
                        newParagraph.Append(commentReference);
                        _mainPart.Document.Body.Append(newParagraph);
                    }
                }

                _context?.Logger.LogDebug($"Successfully inserted comment reference for ID {commentId}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to insert comment reference for ID {commentId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Insert comment range markers spanning from first run to last run
        /// </summary>
        /// <param name="commentId">Comment ID</param>
        /// <param name="startRun">First run in the range</param>
        /// <param name="endRun">Last run in the range</param>
        /// <param name="relativeStart">Start position relative to the startRun</param>
        /// <param name="relativeEnd">End position relative to the endRun</param>
        /// <returns>Whether insertion was successful</returns>
        public bool InsertCommentRange(string commentId, Run startRun, Run endRun, int? relativeStart, int? relativeEnd)
        {
            try
            {
                if (_mainPart?.Document?.Body == null)
                {
                    _context?.Logger.LogError("Document body is not available");
                    return false;
                }

                if (startRun == null || endRun == null)
                {
                    _context?.Logger.LogError("Start run or end run is null");
                    return false;
                }

                // Create comment range markers
                var commentRangeStart = _elementFactory.CreateCommentRangeStart(commentId);
                var commentRangeEnd = _elementFactory.CreateCommentRangeEnd(commentId);
                var commentReference = _elementFactory.CreateCommentReference(commentId);

                // Handle different scenarios based on whether start and end runs are the same
                if (startRun == endRun)
                {
                    return _commentRangeService.HandleSingleRunCommentRange(commentId, startRun, relativeStart, relativeEnd,
                        commentRangeStart, commentRangeEnd, commentReference);
                }
                else
                {
                    return _commentRangeService.HandleCrossRunCommentRange(commentId, startRun, endRun, relativeStart, relativeEnd,
                        commentRangeStart, commentRangeEnd, commentReference);
                }
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to insert comment range for ID {commentId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 在指定评论位置插入新的评论引用标记（用于回复）
        /// Insert new comment reference markers at specified comment location (for replies)
        /// </summary>
        /// <param name="parentCommentId">父评论ID / Parent comment ID</param>
        /// <param name="replyCommentId">回复评论ID / Reply comment ID</param>
        /// <returns>是否成功插入 / Whether insertion was successful</returns>
        public bool InsertCommentReplyReference(string parentCommentId, string replyCommentId)
        {
            try
            {
                // 查找父评论的位置
                // Find parent comment location
                var parentLocation = _commentRangeService.FindCommentLocation(parentCommentId);
                if (parentLocation == null)
                {
                    _context?.Logger.LogWarning($"Parent comment location not found for ID {parentCommentId}");
                    return false;
                }

                // 创建回复评论的引用标记
                // Create reply comment reference markers
                var replyRangeStart = _elementFactory.CreateCommentRangeStart(replyCommentId);
                var replyRangeEnd = _elementFactory.CreateCommentRangeEnd(replyCommentId);
                var replyReference = _elementFactory.CreateCommentReference(replyCommentId);

                // 在父评论的相同位置插入回复评论标记
                // Insert reply comment markers at the same location as parent comment
                _commentRangeService.InsertNestedCommentMarkersAtLocation(parentLocation!, replyRangeStart, replyRangeEnd, replyReference);

                _context?.Logger.LogDebug($"Successfully inserted reply comment reference for ID {replyCommentId} at parent location {parentCommentId}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to insert reply comment reference for parent ID {parentCommentId}, reply ID {replyCommentId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查找文档中的特定段落
        /// Find specific paragraph in document
        /// </summary>
        /// <param name="paraId">段落ID / Paragraph ID</param>
        /// <returns>找到的段落或null / Found paragraph or null</returns>
        public Paragraph? FindParagraphByParaId(string paraId)
        {
            if (_mainPart?.Document?.Body == null)
            {
                return null;
            }

            return _mainPart.Document.Body
                .Descendants<Paragraph>()
                .FirstOrDefault(p => p.ParagraphId?.Value == paraId);
        }

        /// <summary>
        /// 检查服务是否已初始化
        /// Check if service is initialized
        /// </summary>
        /// <returns>是否已初始化 / Whether initialized</returns>
        public bool IsInitialized => _mainPart != null;

        /// <summary>
        /// 确保服务已初始化
        /// Ensure service is initialized
        /// </summary>
        /// <exception cref="InvalidOperationException">当服务未初始化时抛出 / Thrown when service is not initialized</exception>
        private void EnsureInitialized()
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException("DocumentManipulationService must be initialized before use");
            }
        }

        /// <summary>
        /// 保存文档更改
        /// Save document changes
        /// </summary>
        public void Save()
        {
            EnsureInitialized();

            try
            {
                _mainPart!.Document.Save();
                _context?.Logger.LogDebug("Saved document changes");
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to save document: {ex.Message}");
                throw;
            }
        }
    }
}
