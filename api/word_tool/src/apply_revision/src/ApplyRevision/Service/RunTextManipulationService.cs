using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Service for handling text manipulation within runs
    /// 处理Run内文本操作的服务
    /// </summary>
    public class RunTextManipulationService
    {
        private readonly ILambdaContext? _context;
        private readonly IElementFactory _elementFactory;

        /// <summary>
        /// Initialize the run text manipulation service
        /// 初始化Run文本操作服务
        /// </summary>
        /// <param name="context">Lambda context</param>
        /// <param name="elementFactory">Element factory</param>
        public RunTextManipulationService(ILambdaContext? context, IElementFactory elementFactory)
        {
            _context = context;
            _elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
        }

        /// <summary>
        /// Split a run and insert comment range markers around the specified text range
        /// 拆分Run并在指定文本范围周围插入评论范围标记
        /// </summary>
        /// <param name="commentId">Comment ID</param>
        /// <param name="targetRun">Target run to split</param>
        /// <param name="startOffset">Start offset within the run text</param>
        /// <param name="endOffset">End offset within the run text (null means to end of text)</param>
        /// <param name="commentRangeStart">Comment range start marker</param>
        /// <param name="commentRangeEnd">Comment range end marker</param>
        /// <param name="commentReference">Comment reference marker</param>
        /// <returns>Whether the operation was successful</returns>
        public bool SplitRunAndInsertCommentRange(string commentId, Run targetRun, int startOffset, int? endOffset,
            CommentRangeStart commentRangeStart, CommentRangeEnd commentRangeEnd, Run commentReference)
        {
            try
            {
                // Get the text element from the run
                var textElement = targetRun.GetFirstChild<Text>();
                if (textElement == null || string.IsNullOrEmpty(textElement.Text))
                {
                    _context?.Logger.LogWarning($"Target run has no text content for comment {commentId}");
                    return false;
                }

                string originalText = textElement.Text;
                int actualEndOffset = endOffset ?? originalText.Length;

                // Validate offsets
                if (startOffset < 0 || startOffset >= originalText.Length || 
                    actualEndOffset <= startOffset || actualEndOffset > originalText.Length)
                {
                    _context?.Logger.LogError($"Invalid text range [{startOffset}, {actualEndOffset}] for text length {originalText.Length}");
                    return false;
                }

                // Split the text into three parts: before, target (to be commented), after
                string beforeText = originalText[..startOffset];
                string targetText = originalText[startOffset..actualEndOffset];
                string afterText = originalText[actualEndOffset..];

                // Update the original run to contain only the before text
                textElement.Text = beforeText;
                if (RequiresSpacePreservation(beforeText))
                {
                    textElement.Space = SpaceProcessingModeValues.Preserve;
                }

                // Create a new run for the target text (the part to be commented)
                var targetTextRun = _elementFactory.CloneElement(targetRun, deepClone: true);
                targetTextRun.RemoveAllChildren<Text>();
                var targetTextElement = new Text(targetText);
                if (RequiresSpacePreservation(targetText))
                {
                    targetTextElement.Space = SpaceProcessingModeValues.Preserve;
                }
                targetTextRun.Append(targetTextElement);

                // Insert comment range start after the original run (before the target text)
                targetRun.InsertAfterSelf(commentRangeStart);

                // Insert the target text run after the comment range start
                commentRangeStart.InsertAfterSelf(targetTextRun);

                // Insert comment range end after the target text run
                targetTextRun.InsertAfterSelf(commentRangeEnd);

                // Insert comment reference after the comment range end
                commentRangeEnd.InsertAfterSelf(commentReference);

                // Create after run if there's remaining text
                if (!string.IsNullOrEmpty(afterText))
                {
                    var afterRun = _elementFactory.CloneElement(targetRun, deepClone: true);
                    afterRun.RemoveAllChildren<Text>();
                    var afterTextElement = new Text(afterText);
                    if (RequiresSpacePreservation(afterText))
                    {
                        afterTextElement.Space = SpaceProcessingModeValues.Preserve;
                    }
                    afterRun.Append(afterTextElement);

                    // Insert after run at the end
                    commentReference.InsertAfterSelf(afterRun);
                }

                _context?.Logger.LogDebug($"Successfully split run and inserted comment range for ID {commentId}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to split run and insert comment range for ID {commentId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Split a run at the specified position
        /// 在指定位置拆分Run
        /// </summary>
        /// <param name="targetRun">Run to split</param>
        /// <param name="splitPosition">Position to split at</param>
        /// <param name="beforeRun">Output: Run containing text before split position</param>
        /// <param name="afterRun">Output: Run containing text after split position</param>
        /// <returns>Whether the split was successful</returns>
        public bool SplitRunAtPosition(Run targetRun, int splitPosition, out Run? beforeRun, out Run? afterRun)
        {
            beforeRun = null;
            afterRun = null;

            try
            {
                // Get the text element from the run
                var textElement = targetRun.GetFirstChild<Text>();
                if (textElement == null || string.IsNullOrEmpty(textElement.Text))
                {
                    _context?.Logger.LogWarning("Target run has no text content to split");
                    return false;
                }

                string originalText = textElement.Text;

                // Validate split position
                if (splitPosition <= 0 || splitPosition >= originalText.Length)
                {
                    _context?.Logger.LogError($"Invalid split position {splitPosition} for text length {originalText.Length}");
                    return false;
                }

                // Split the text
                string beforeText = originalText[..splitPosition];
                string afterText = originalText[splitPosition..];

                // Update the original run to contain the before text
                textElement.Text = beforeText;
                if (RequiresSpacePreservation(beforeText))
                {
                    textElement.Space = SpaceProcessingModeValues.Preserve;
                }
                beforeRun = targetRun;

                // Create a new run for the after text
                afterRun = _elementFactory.CloneElement(targetRun, deepClone: true);
                afterRun.RemoveAllChildren<Text>();
                var afterTextElement = new Text(afterText);
                if (RequiresSpacePreservation(afterText))
                {
                    afterTextElement.Space = SpaceProcessingModeValues.Preserve;
                }
                afterRun.Append(afterTextElement);

                // Insert the after run after the original run
                targetRun.InsertAfterSelf(afterRun);

                _context?.Logger.LogDebug($"Successfully split run at position {splitPosition}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to split run at position {splitPosition}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if text requires xml:space="preserve" attribute
        /// 检查文本是否需要 xml:space="preserve" 属性
        /// </summary>
        /// <param name="text">Text to check</param>
        /// <returns>True if space preservation is needed</returns>
        private static bool RequiresSpacePreservation(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // Check for leading spaces, trailing spaces, multiple consecutive spaces, or tabs
            return text.StartsWith(' ') ||
                   text.EndsWith(' ') ||
                   text.Contains("  ") ||  // Multiple consecutive spaces
                   text.Contains('\t');    // Tab characters
        }

        /// <summary>
        /// Create a run with specified text and formatting inherited from source run
        /// 创建具有指定文本和从源Run继承格式的Run
        /// </summary>
        /// <param name="sourceRun">Source run to inherit formatting from</param>
        /// <param name="text">Text content for the new run</param>
        /// <returns>New run with inherited formatting</returns>
        public Run CreateRunWithInheritedFormatting(Run sourceRun, string text)
        {
            var newRun = _elementFactory.CloneElement(sourceRun, deepClone: true);
            newRun.RemoveAllChildren<Text>();
            
            var textElement = new Text(text);
            if (RequiresSpacePreservation(text))
            {
                textElement.Space = SpaceProcessingModeValues.Preserve;
            }
            newRun.Append(textElement);
            
            return newRun;
        }

        /// <summary>
        /// Extract text content from a run
        /// 从Run中提取文本内容
        /// </summary>
        /// <param name="run">Run to extract text from</param>
        /// <returns>Text content or empty string if no text found</returns>
        public string ExtractTextFromRun(Run run)
        {
            if (run == null)
                return string.Empty;

            var textElement = run.GetFirstChild<Text>();
            return textElement?.Text ?? string.Empty;
        }

        /// <summary>
        /// Validate text range within a run
        /// 验证Run内的文本范围
        /// </summary>
        /// <param name="run">Run to validate against</param>
        /// <param name="startOffset">Start offset</param>
        /// <param name="endOffset">End offset (null means end of text)</param>
        /// <returns>True if range is valid</returns>
        public bool ValidateTextRange(Run run, int startOffset, int? endOffset = null)
        {
            var text = ExtractTextFromRun(run);
            if (string.IsNullOrEmpty(text))
                return false;

            int actualEndOffset = endOffset ?? text.Length;
            
            return startOffset >= 0 && 
                   startOffset < text.Length && 
                   actualEndOffset > startOffset && 
                   actualEndOffset <= text.Length;
        }
    }
}
