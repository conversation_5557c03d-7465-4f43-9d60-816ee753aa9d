using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using ApplyRevision.Component;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Manages comments in DOCX documents, providing operations for creating, inserting, and managing comment elements
    /// </summary>
    /// <example>
    /// Basic usage:
    /// <code>
    /// var commentManager = new CommentManager(context, elementFactory, idManager);
    /// 
    /// // Initialize with document parts
    /// commentManager.Initialize(mainPart);
    /// 
    /// // Create a simple comment
    /// var comment = commentManager.CreateComment("John Doe", "2024-01-15", "Please review this");
    /// 
    /// // Create a comment component with range markers
    /// var component = commentManager.CreateCommentComponent("<PERSON>", "2024-01-15", "Needs clarification");
    /// 
    /// // Add comment to document
    /// commentManager.AddComment(comment);
    /// 
    /// // Save changes
    /// commentManager.Save();
    /// </code>
    /// </example>
    public interface ICommentManager
    {
        /// <summary>
        /// Initialize the comment manager with document parts
        /// </summary>
        /// <param name="mainPart">Main document part containing the comments</param>
        /// <example>
        /// <code>
        /// using (var document = WordprocessingDocument.Open("document.docx", true))
        /// {
        ///     commentManager.Initialize(document.MainDocumentPart);
        ///     // ... perform comment operations
        /// }
        /// </code>
        /// </example>
        void Initialize(MainDocumentPart mainPart);

        /// <summary>
        /// Create a Comment element with specified metadata and content
        /// </summary>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <returns>A Comment element with generated ID</returns>
        /// <example>
        /// <code>
        /// // Create a review comment
        /// var comment = commentManager.CreateComment("Reviewer", "2024-01-15T10:30:00", "This section needs revision");
        /// 
        /// // Create an AI-generated comment
        /// var aiComment = commentManager.CreateComment("AI Assistant", DateTime.Now.ToString(), "Suggested improvement");
        /// 
        /// // Create a deletion tracking comment
        /// var delComment = commentManager.CreateComment("Editor", "2024-01-15", "Deleted: original text");
        /// </code>
        /// </example>
        Comment CreateComment(string author, string date, string message);

        /// <summary>
        /// Create a complete comment component with range markers and reference
        /// </summary>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <returns>A CommentComponent with all related elements</returns>
        /// <example>
        /// <code>
        /// // Create a comment component for text annotation
        /// var component = commentManager.CreateCommentComponent("Proofreader", "2024-01-15", "Grammar error here");
        /// 
        /// // Insert the component around specific text
        /// targetElement.InsertBeforeSelf(component.RangeStart);
        /// targetElement.InsertAfterSelf(component.RangeEnd);
        /// component.RangeEnd.InsertAfterSelf(component.Reference);
        /// 
        /// // Add the comment to the document
        /// commentManager.AddComment(component.Comment);
        /// </code>
        /// </example>
        CommentComponent CreateCommentComponent(string author, string date, string message);

        /// <summary>
        /// Create a comment reference run for linking to an existing comment
        /// </summary>
        /// <param name="commentId">ID of the comment to reference</param>
        /// <returns>A Run element containing the comment reference</returns>
        /// <example>
        /// <code>
        /// // Create reference to comment with ID "5"
        /// var reference = commentManager.CreateCommentReference("5");
        /// 
        /// // Insert reference after comment range end
        /// commentRangeEnd.InsertAfterSelf(reference);
        /// </code>
        /// </example>
        Run CreateCommentReference(string commentId);

        /// <summary>
        /// Add a comment to the document's comment collection
        /// </summary>
        /// <param name="comment">Comment to add</param>
        /// <example>
        /// <code>
        /// var comment = commentManager.CreateComment("Author", "2024-01-15", "Review needed");
        /// commentManager.AddComment(comment);
        /// </code>
        /// </example>
        void AddComment(Comment comment);

        /// <summary>
        /// Add multiple comments to the document's comment collection
        /// </summary>
        /// <param name="comments">Comments to add</param>
        /// <example>
        /// <code>
        /// var comments = new List&lt;Comment&gt;
        /// {
        ///     commentManager.CreateComment("Author1", "2024-01-15", "First comment"),
        ///     commentManager.CreateComment("Author2", "2024-01-15", "Second comment")
        /// };
        /// commentManager.AddComments(comments);
        /// </code>
        /// </example>
        void AddComments(IEnumerable<Comment> comments);

        /// <summary>
        /// Create comments for revision links and return comment components
        /// </summary>
        /// <param name="revisionLinks">List of revision links to create comments for</param>
        /// <returns>List of comment components for the revision links</returns>
        /// <example>
        /// <code>
        /// var revisionLinks = new List&lt;RevisionLink&gt;
        /// {
        ///     new RevisionLink { Author = "Editor", Date = "2024-01-15", Comment = "Link to reference" }
        /// };
        /// var components = commentManager.CreateRevisionLinkComments(revisionLinks);
        /// 
        /// // Apply the comment components to the document
        /// foreach (var component in components)
        /// {
        ///     commentManager.AddComment(component.Comment);
        /// }
        /// </code>
        /// </example>
        List<CommentComponent> CreateRevisionLinkComments(List<RevisionLink> revisionLinks);

        /// <summary>
        /// Insert comment components around specified elements in the document
        /// </summary>
        /// <param name="startElement">Element to insert comment start before</param>
        /// <param name="endElement">Element to insert comment end and reference after</param>
        /// <param name="commentComponents">Comment components to insert</param>
        /// <example>
        /// <code>
        /// var components = commentManager.CreateRevisionLinkComments(revisionLinks);
        /// commentManager.InsertCommentComponents(revisionElement, revisionElement, components);
        /// </code>
        /// </example>
        void InsertCommentComponents(OpenXmlElement startElement, OpenXmlElement endElement, IEnumerable<CommentComponent> commentComponents);

        /// <summary>
        /// Get the next available comment ID
        /// </summary>
        /// <returns>Next comment ID</returns>
        /// <example>
        /// <code>
        /// int nextId = commentManager.GetNextCommentId();
        /// var customComment = new Comment { Id = nextId.ToString(), ... };
        /// </code>
        /// </example>
        int GetNextCommentId();

        /// <summary>
        /// Generate initials from author name for comment display
        /// </summary>
        /// <param name="author">Author full name</param>
        /// <returns>Author initials (e.g., "John Doe" -> "JD")</returns>
        /// <example>
        /// <code>
        /// var initials = commentManager.GetAuthorInitials("John Smith");     // Returns "JS"
        /// var aiInitials = commentManager.GetAuthorInitials("");           // Returns "AI"
        /// var singleInitials = commentManager.GetAuthorInitials("Mary");   // Returns "M"
        /// </code>
        /// </example>
        StringValue GetAuthorInitials(string author);

        /// <summary>
        /// Save all comment changes to the document
        /// </summary>
        /// <example>
        /// <code>
        /// // After making comment changes
        /// commentManager.AddComment(newComment);
        /// commentManager.Save(); // Persist changes
        /// </code>
        /// </example>
        void Save();

        /// <summary>
        /// Check if the comment manager has been properly initialized
        /// </summary>
        /// <returns>True if initialized, false otherwise</returns>
        bool IsInitialized { get; }

        /// <summary>
        /// Get the total number of comments in the document
        /// </summary>
        /// <returns>Count of comments</returns>
        /// <example>
        /// <code>
        /// int commentCount = commentManager.CommentCount;
        /// Console.WriteLine($"Document has {commentCount} comments");
        /// </code>
        /// </example>
        int CommentCount { get; }

        /// <summary>
        /// Find a comment by its ID
        /// 通过ID查找评论
        /// </summary>
        /// <param name="commentId">Comment ID to search for / 要搜索的评论ID</param>
        /// <returns>Comment element if found, null otherwise / 如果找到则返回评论元素，否则返回null</returns>
        /// <example>
        /// <code>
        /// var comment = commentManager.FindCommentById("5");
        /// if (comment != null)
        /// {
        ///     Console.WriteLine($"Found comment by {comment.Author}: {comment.InnerText}");
        /// }
        /// </code>
        /// </example>
        Comment? FindCommentById(string commentId);
    }
}