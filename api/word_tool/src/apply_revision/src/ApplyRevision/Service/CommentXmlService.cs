using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// 评论XML服务，负责基础评论XML操作
    /// Comment XML service responsible for basic comment XML operations
    /// </summary>
    public class CommentXmlService
    {
        private readonly ILambdaContext? _context;
        private readonly IElementFactory _elementFactory;
        private readonly IIdManager _idManager;
        private WordprocessingCommentsPart? _commentsPart;

        /// <summary>
        /// 初始化评论XML服务
        /// Initialize comment XML service
        /// </summary>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        /// <param name="elementFactory">元素工厂 / Element factory</param>
        /// <param name="idManager">ID管理器 / ID manager</param>
        public CommentXmlService(ILambdaContext? context, IElementFactory elementFactory, IIdManager idManager)
        {
            _context = context;
            _elementFactory = elementFactory ?? throw new ArgumentNullException(nameof(elementFactory));
            _idManager = idManager ?? throw new ArgumentNullException(nameof(idManager));
        }

        /// <summary>
        /// 初始化服务，设置评论部分
        /// Initialize service with comment parts
        /// </summary>
        /// <param name="mainPart">主文档部分 / Main document part</param>
        public void Initialize(MainDocumentPart mainPart)
        {
            ArgumentNullException.ThrowIfNull(mainPart);

            // 获取或创建评论部分
            // Get or create comments part
            _commentsPart = mainPart.WordprocessingCommentsPart ?? mainPart.AddNewPart<WordprocessingCommentsPart>();

            // 确保评论根元素存在
            // Ensure comments root element exists
            if (_commentsPart.Comments == null)
            {
                _commentsPart.Comments = new Comments();
            }

            _context?.Logger.LogDebug("CommentXmlService initialized");
        }

        /// <summary>
        /// 创建评论元素
        /// Create comment element
        /// </summary>
        /// <param name="author">作者 / Author</param>
        /// <param name="date">日期 / Date</param>
        /// <param name="message">消息内容 / Message content</param>
        /// <returns>创建的评论元素 / Created comment element</returns>
        public Comment CreateComment(string author, string date, string message)
        {
            EnsureInitialized();

            int commentId = _idManager.GetNextCommentId();
            var paraId = _idManager.GenerateParaId();
            var comment = _elementFactory.CreateCommentElement(commentId, author, date, message, paraId);

            _context?.Logger.LogDebug($"Created comment with ID {commentId} by author '{author}'");
            return comment;
        }

        /// <summary>
        /// 添加评论到 comment.xml, 不是添加引用到 document.xml
        /// Add comment to comment.xml  
        /// </summary>
        /// <param name="comment">要添加的评论 / Comment to add</param>
        public void AddComment(Comment comment)
        {
            EnsureInitialized();
            ArgumentNullException.ThrowIfNull(comment);

            _commentsPart!.Comments.Append(comment);
            _context?.Logger.LogDebug($"Added comment with ID {comment.Id} to comment.xml");
        }

        /// <summary>
        /// 根据ID查找评论
        /// Find comment by ID
        /// </summary>
        /// <param name="commentId">评论ID / Comment ID</param>
        /// <returns>找到的评论或null / Found comment or null</returns>
        public Comment? FindCommentById(string commentId)
        {
            EnsureInitialized();

            if (string.IsNullOrWhiteSpace(commentId))
            {
                return null;
            }

            var comment = _commentsPart!.Comments
                .Elements<Comment>()
                .FirstOrDefault(c => c.Id?.Value == commentId);

            if (comment != null)
            {
                _context?.Logger.LogDebug($"Found comment with ID {commentId}");
            }
            else
            {
                _context?.Logger.LogDebug($"Comment with ID {commentId} not found");
            }

            return comment;
        }

        /// <summary>
        /// 获取所有评论
        /// Get all comments
        /// </summary>
        /// <returns>评论集合 / Collection of comments</returns>
        public IEnumerable<Comment> GetAllComments()
        {
            EnsureInitialized();
            return _commentsPart!.Comments.Elements<Comment>();
        }

        /// <summary>
        /// 获取下一个可用的评论ID
        /// Get next available comment ID
        /// </summary>
        /// <returns>下一个评论ID / Next comment ID</returns>
        public int GetNextCommentId()
        {
            EnsureInitialized();
            return _idManager.GetNextCommentId();
        }

        /// <summary>
        /// 生成作者缩写
        /// Generate author initials
        /// </summary>
        /// <param name="author">作者全名 / Author full name</param>
        /// <returns>作者缩写 / Author initials</returns>
        public StringValue GetAuthorInitials(string author)
        {
            return _elementFactory.GetCommentInitials(author);
        }

        /// <summary>
        /// 检查服务是否已初始化
        /// Check if service is initialized
        /// </summary>
        /// <returns>是否已初始化 / Whether initialized</returns>
        public bool IsInitialized => _commentsPart != null;

        /// <summary>
        /// 确保服务已初始化
        /// Ensure service is initialized
        /// </summary>
        /// <exception cref="InvalidOperationException">当服务未初始化时抛出 / Thrown when service is not initialized</exception>
        private void EnsureInitialized()
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException("CommentXmlService must be initialized before use");
            }
        }

        /// <summary>
        /// 获取评论总数
        /// Get total comment count
        /// </summary>
        /// <returns>评论总数 / Total comment count</returns>
        public int GetCommentCount()
        {
            EnsureInitialized();
            return _commentsPart!.Comments.Elements<Comment>().Count();
        }

        /// <summary>
        /// 删除指定ID的评论
        /// Remove comment by ID
        /// </summary>
        /// <param name="commentId">评论ID / Comment ID</param>
        /// <returns>是否成功删除 / Whether removal was successful</returns>
        public bool RemoveComment(string commentId)
        {
            EnsureInitialized();

            var comment = FindCommentById(commentId);
            if (comment != null)
            {
                comment.Remove();
                _context?.Logger.LogDebug($"Removed comment with ID {commentId}");
                return true;
            }

            _context?.Logger.LogWarning($"Cannot remove comment with ID {commentId} - not found");
            return false;
        }

        /// <summary>
        /// 保存评论更改到文档
        /// Save comment changes to document
        /// </summary>
        public void Save()
        {
            EnsureInitialized();

            try
            {
                _commentsPart!.Comments.Save();
                _context?.Logger.LogDebug($"Saved {GetCommentCount()} comments to document");
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to save comments: {ex.Message}");
                throw;
            }
        }
    }
}
