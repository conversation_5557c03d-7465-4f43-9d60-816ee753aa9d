using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// Manages the generation of unique IDs for revisions and comments in DOCX documents
    /// 管理DOCX文档中修订和注释的唯一ID生成
    /// </summary>
    public interface IIdManager
    {
        /// <summary>
        /// Get the next available comment ID
        /// 获取下一个可用的注释ID
        /// </summary>
        /// <returns>Next comment ID</returns>
        int GetNextCommentId();

        /// <summary>
        /// Get the next available revision ID
        /// 获取下一个可用的修订ID
        /// </summary>
        /// <returns>Next revision ID</returns>
        int GetNextRevisionId();

        /// <summary>
        /// Generate a unique paragraph ID for comments
        /// 为注释生成唯一的段落ID
        /// </summary>
        /// <returns>Unique paragraph ID as string</returns>
        string GenerateParaId();

        /// <summary>
        /// Generate a unique durable ID for comment extensibility (Word 2016+)
        /// 为注释扩展性生成唯一的持久ID（Word 2016+）
        /// </summary>
        /// <returns>8-character hexadecimal durable ID</returns>
        string GenerateDurableId();

        /// <summary>
        /// Initialize the ID manager with current document state
        /// 使用当前文档状态初始化ID管理器
        /// </summary>
        /// <param name="commentsPart">Comments part to scan for existing comment IDs</param>
        /// <param name="documentBody">Document body to scan for existing revision IDs</param>
        void Initialize(WordprocessingCommentsPart? commentsPart, Body? documentBody);

        /// <summary>
        /// Reset all counters (mainly for testing purposes)
        /// 重置所有计数器（主要用于测试目的）
        /// </summary>
        void Reset();
    }
}