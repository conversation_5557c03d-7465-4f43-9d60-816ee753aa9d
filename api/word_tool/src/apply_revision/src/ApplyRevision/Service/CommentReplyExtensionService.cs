using Amazon.Lambda.Core;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Xml.Linq;

namespace ApplyRevision.Service
{
    /// <summary>
    /// 评论回复扩展服务，负责处理Word评论回复的扩展XML文件操作
    /// Comment reply extension service responsible for handling Word comment reply extension XML file operations
    /// </summary>
    public class CommentReplyExtensionService : ICommentReplyExtensionService
    {
        private readonly ILambdaContext? _context;
        private MainDocumentPart? _mainPart;

        /// <summary>
        /// 初始化评论回复扩展服务
        /// Initialize comment reply extension service
        /// </summary>
        /// <param name="context">Lambda上下文 / Lambda context</param>
        public CommentReplyExtensionService(ILambdaContext? context)
        {
            _context = context;
        }

        /// <summary>
        /// 初始化服务，设置文档部分
        /// Initialize service with document parts
        /// </summary>
        /// <param name="mainPart">主文档部分 / Main document part</param>
        public void Initialize(MainDocumentPart mainPart)
        {
            _mainPart = mainPart ?? throw new ArgumentNullException(nameof(mainPart));
            _context?.Logger.LogDebug("CommentReplyExtensionService initialized");
        }

        /// <summary>
        /// 创建评论回复的扩展XML文件
        /// Create extension XML files for comment reply
        /// </summary>
        /// <param name="parentComment">父评论 / Parent comment</param>
        /// <param name="replyComment">回复评论 / Reply comment</param>
        /// <param name="replyDurableId">回复持久ID / Reply durable ID</param>
        /// <param name="parentDurableId">父评论持久ID / Parent durable ID</param>
        /// <param name="utcDateString">UTC日期字符串 / UTC date string</param>
        /// <returns>是否成功创建 / Whether creation was successful</returns>
        public bool CreateReplyExtensions(
            Comment parentComment,
            Comment replyComment,
            string replyDurableId,
            string parentDurableId,
            string utcDateString)
        {
            EnsureInitialized();

            try
            {
                // 提取paraId
                // Extract paraId
                var parentParaId = ExtractParaIdFromComment(parentComment);
                var replyParaId = ExtractParaIdFromComment(replyComment);

                if (parentParaId == null || replyParaId == null)
                {
                    _context?.Logger.LogError("Failed to extract paraId from comments");
                    return false;
                }

                // 创建三个扩展XML文件
                // Create three extension XML files
                var success1 = CreateCommentsExtendedPart(parentParaId, replyParaId);
                var success2 = CreateCommentsExtensiblePart(replyDurableId, parentDurableId, utcDateString);
                var success3 = CreateCommentsIdsPart(replyParaId, parentParaId, replyDurableId, parentDurableId);

                var overallSuccess = success1 && success2 && success3;

                if (overallSuccess)
                {
                    _context?.Logger.LogInformation($"Successfully created all extension parts for reply {replyParaId} -> parent {parentParaId}");
                }
                else
                {
                    _context?.Logger.LogWarning($"Some extension parts failed to create for reply {replyParaId} -> parent {parentParaId}");
                }

                return overallSuccess;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to create comment reply extensions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建或更新commentsExtended.xml部分
        /// Create or update commentsExtended.xml part
        /// </summary>
        private bool CreateCommentsExtendedPart(string paraIdParent, string paraId)
        {
            try
            {
                var extendedPart = _mainPart!.WordprocessingCommentsExPart ?? _mainPart.AddNewPart<WordprocessingCommentsExPart>();
                var updatedXml = UpdateCommentsExtendedXml(extendedPart, paraIdParent, paraId);

                using var stream = extendedPart.GetStream(FileMode.Create);
                using var writer = new StreamWriter(stream);
                writer.Write(updatedXml);

                _context?.Logger.LogDebug($"Updated commentsExtended.xml content for reply {paraId} -> parent {paraIdParent}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to update commentsExtended.xml: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建或更新commentsExtensible.xml部分
        /// Create or update commentsExtensible.xml part
        /// </summary>
        private bool CreateCommentsExtensiblePart(string replyDurableId, string parentDurableId, string utcDateString)
        {
            try
            {
                var extensiblePart = _mainPart!.WordCommentsExtensiblePart ?? _mainPart.AddNewPart<WordCommentsExtensiblePart>();
                var updatedXml = UpdateCommentsExtensibleXml(extensiblePart, replyDurableId, parentDurableId, utcDateString);

                using var stream = extensiblePart.GetStream(FileMode.Create);
                using var writer = new StreamWriter(stream);
                writer.Write(updatedXml);

                _context?.Logger.LogDebug($"Updated commentsExtensible.xml content for reply with durable ID {replyDurableId}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to update commentsExtensible.xml: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建或更新commentsIds.xml部分
        /// Create or update commentsIds.xml part
        /// </summary>
        private bool CreateCommentsIdsPart(string replyCommentId, string parentCommentId, string replyDurableId, string parentDurableId)
        {
            try
            {
                var idsPart = _mainPart!.WordprocessingCommentsIdsPart ?? _mainPart.AddNewPart<WordprocessingCommentsIdsPart>();
                var updatedXml = UpdateCommentsIdsXml(idsPart, replyCommentId, parentCommentId, replyDurableId, parentDurableId);

                using var stream = idsPart.GetStream(FileMode.Create);
                using var writer = new StreamWriter(stream);
                writer.Write(updatedXml);

                _context?.Logger.LogDebug($"Updated commentsIds.xml content for reply {replyCommentId}");
                return true;
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to update commentsIds.xml: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从Comment对象中提取paraId
        /// Extract paraId from Comment object
        /// </summary>
        private static string? ExtractParaIdFromComment(Comment comment)
        {
            ArgumentNullException.ThrowIfNull(comment);

            // NOTE: When multiple paragraphs exist in comment, always use the last paragraph's paraId as parentId
            // Example: <w:comment> contains multiple <w:p> elements with different w14:paraId values
            // Current implementation correctly uses LastOrDefault() to select the final paragraph
            var paragraph = comment.Descendants<Paragraph>().LastOrDefault();
            if (paragraph != null)
            {
                var paraIdAttr = paragraph.GetAttribute("paraId", "http://schemas.microsoft.com/office/word/2010/wordml");
                if (!string.IsNullOrEmpty(paraIdAttr.Value))
                {
                    return paraIdAttr.Value;
                }
            }
            return null;
        }

        /// <summary>
        /// 检查服务是否已初始化
        /// Check if service is initialized
        /// </summary>
        public bool IsInitialized => _mainPart != null;

        /// <summary>
        /// 确保服务已初始化
        /// Ensure service is initialized
        /// </summary>
        private void EnsureInitialized()
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException("CommentReplyExtensionService must be initialized before use");
            }
        }

        /// <summary>
        /// 更新commentsExtended.xml内容
        /// Update commentsExtended.xml content
        /// </summary>
        private string UpdateCommentsExtendedXml(WordprocessingCommentsExPart extendedPart, string paraIdParent, string paraId)
        {
            try
            {
                XDocument? existingDoc = null;
                try
                {
                    using var stream = extendedPart.GetStream(FileMode.Open);
                    existingDoc = XDocument.Load(stream);
                }
                catch
                {
                    _context?.Logger.LogDebug("No existing commentsExtended.xml found, creating new one");
                }

                XNamespace w15 = "http://schemas.microsoft.com/office/word/2012/wordml";
                XDocument doc;
                XElement root;

                if (existingDoc?.Root != null)
                {
                    doc = existingDoc;
                    root = doc.Root;
                    _context?.Logger.LogDebug("Found existing commentsExtended.xml, will append new entries");
                }
                else
                {
                    doc = new XDocument(new XDeclaration("1.0", "UTF-8", "yes"));
                    root = new XElement(w15 + "commentsEx");
                    doc.Add(root);
                    _context?.Logger.LogDebug("Creating new commentsExtended.xml document");
                }

                // 检查父评论条目是否已存在
                var existingParent = root.Elements(w15 + "commentEx")
                    .FirstOrDefault(e => e.Attribute(w15 + "paraId")?.Value == paraIdParent);

                if (existingParent == null)
                {
                    var parentElement = new XElement(w15 + "commentEx",
                        new XAttribute(w15 + "paraId", paraIdParent),
                        new XAttribute(w15 + "done", "0"));
                    root.Add(parentElement);
                    _context?.Logger.LogDebug($"Added parent comment entry with paraId: {paraIdParent}");
                }

                // 检查回复评论条目是否已存在
                var existingReply = root.Elements(w15 + "commentEx")
                    .FirstOrDefault(e => e.Attribute(w15 + "paraId")?.Value == paraId);

                if (existingReply == null)
                {
                    var replyElement = new XElement(w15 + "commentEx",
                        new XAttribute(w15 + "paraId", paraId),
                        new XAttribute(w15 + "paraIdParent", paraIdParent),
                        new XAttribute(w15 + "done", "0"));
                    root.Add(replyElement);
                    _context?.Logger.LogDebug($"Added reply comment entry with paraId: {paraId}");
                }

                return doc.ToString();
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to update commentsExtended.xml: {ex.Message}");
                return CreateCommentsExtendedXml(paraIdParent, paraId);
            }
        }

        /// <summary>
        /// 创建commentsExtended.xml的XML内容
        /// Create XML content for commentsExtended.xml
        /// </summary>
        private static string CreateCommentsExtendedXml(string paraIdParent, string paraId)
        {
            return $@"<?xml version=""1.0"" encoding=""UTF-8"" standalone=""yes""?>
<w15:commentsEx xmlns:w15=""http://schemas.microsoft.com/office/word/2012/wordml"">
    <w15:commentEx w15:paraId=""{paraIdParent}"" w15:done=""0""/>
    <w15:commentEx w15:paraId=""{paraId}"" w15:paraIdParent=""{paraIdParent}"" w15:done=""0""/>
</w15:commentsEx>";
        }

        /// <summary>
        /// 更新commentsExtensible.xml内容
        /// Update commentsExtensible.xml content
        /// </summary>
        private string UpdateCommentsExtensibleXml(WordCommentsExtensiblePart extensiblePart, string replyDurableId, string parentDurableId, string utcDateString)
        {
            try
            {
                XDocument? existingDoc = null;
                try
                {
                    using var stream = extensiblePart.GetStream(FileMode.Open);
                    existingDoc = XDocument.Load(stream);
                }
                catch
                {
                    _context?.Logger.LogDebug("No existing commentsExtensible.xml found, creating new one");
                }

                XNamespace w16cex = "http://schemas.microsoft.com/office/word/2018/wordml/cex";
                XDocument doc;
                XElement root;

                if (existingDoc?.Root != null)
                {
                    doc = existingDoc;
                    root = doc.Root;
                    _context?.Logger.LogDebug("Found existing commentsExtensible.xml, will append new entries");
                }
                else
                {
                    doc = new XDocument(new XDeclaration("1.0", "UTF-8", "yes"));
                    root = new XElement(w16cex + "commentsExtensible");
                    doc.Add(root);
                    _context?.Logger.LogDebug("Creating new commentsExtensible.xml document");
                }

                // 检查父评论条目是否已存在
                var existingParent = root.Elements(w16cex + "commentExtensible")
                    .FirstOrDefault(e => e.Attribute(w16cex + "durableId")?.Value == parentDurableId);

                if (existingParent == null)
                {
                    var parentElement = new XElement(w16cex + "commentExtensible",
                        new XAttribute(w16cex + "durableId", parentDurableId),
                        new XAttribute(w16cex + "dateUtc", utcDateString));
                    root.Add(parentElement);
                    _context?.Logger.LogDebug($"Added parent comment entry with durableId: {parentDurableId}");
                }

                // 检查回复评论条目是否已存在
                var existingReply = root.Elements(w16cex + "commentExtensible")
                    .FirstOrDefault(e => e.Attribute(w16cex + "durableId")?.Value == replyDurableId);

                if (existingReply == null)
                {
                    var replyElement = new XElement(w16cex + "commentExtensible",
                        new XAttribute(w16cex + "durableId", replyDurableId),
                        new XAttribute(w16cex + "dateUtc", utcDateString),
                        new XElement(w16cex + "threadingInfo",
                            new XAttribute(w16cex + "parentDurableId", parentDurableId)));
                    root.Add(replyElement);
                    _context?.Logger.LogDebug($"Added reply comment entry with durableId: {replyDurableId}");
                }

                return doc.ToString();
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to update commentsExtensible.xml: {ex.Message}");
                return CreateCommentsExtensibleXml(replyDurableId, parentDurableId, utcDateString);
            }
        }

        /// <summary>
        /// 创建commentsExtensible.xml的XML内容
        /// Create XML content for commentsExtensible.xml
        /// </summary>
        private static string CreateCommentsExtensibleXml(string replyDurableId, string parentDurableId, string utcDateString)
        {
            return $@"<?xml version=""1.0"" encoding=""UTF-8"" standalone=""yes""?>
<w16cex:commentsExtensible xmlns:w16cex=""http://schemas.microsoft.com/office/word/2018/wordml/cex"">
    <w16cex:commentExtensible w16cex:durableId=""{parentDurableId}"" w16cex:dateUtc=""{utcDateString}""/>
    <w16cex:commentExtensible w16cex:durableId=""{replyDurableId}"" w16cex:dateUtc=""{utcDateString}"">
        <w16cex:threadingInfo w16cex:parentDurableId=""{parentDurableId}""/>
    </w16cex:commentExtensible>
</w16cex:commentsExtensible>";
        }

        /// <summary>
        /// 更新commentsIds.xml内容
        /// Update commentsIds.xml content
        /// </summary>
        private string UpdateCommentsIdsXml(WordprocessingCommentsIdsPart idsPart, string replyParaId, string parentParaId, string replyDurableId, string parentDurableId)
        {
            try
            {
                XDocument? existingDoc = null;
                try
                {
                    using var stream = idsPart.GetStream(FileMode.Open);
                    existingDoc = XDocument.Load(stream);
                }
                catch
                {
                    _context?.Logger.LogDebug("No existing commentsIds.xml found, creating new one");
                }

                XNamespace w16cid = "http://schemas.microsoft.com/office/word/2016/wordml/cid";
                XDocument doc;
                XElement root;

                if (existingDoc?.Root != null)
                {
                    doc = existingDoc;
                    root = doc.Root;
                    _context?.Logger.LogDebug("Found existing commentsIds.xml, will append new entries");
                }
                else
                {
                    doc = new XDocument(new XDeclaration("1.0", "UTF-8", "yes"));
                    root = new XElement(w16cid + "commentsIds");
                    doc.Add(root);
                    _context?.Logger.LogDebug("Creating new commentsIds.xml document");
                }

                // 检查父评论条目是否已存在
                var existingParent = root.Elements(w16cid + "commentId")
                    .FirstOrDefault(e => e.Attribute(w16cid + "paraId")?.Value == parentParaId);

                if (existingParent == null)
                {
                    var parentElement = new XElement(w16cid + "commentId",
                        new XAttribute(w16cid + "paraId", parentParaId),
                        new XAttribute(w16cid + "durableId", parentDurableId));
                    root.Add(parentElement);
                    _context?.Logger.LogDebug($"Added parent comment ID entry with paraId: {parentParaId}");
                }

                // 检查回复评论条目是否已存在
                var existingReply = root.Elements(w16cid + "commentId")
                    .FirstOrDefault(e => e.Attribute(w16cid + "paraId")?.Value == replyParaId);

                if (existingReply == null)
                {
                    var replyElement = new XElement(w16cid + "commentId",
                        new XAttribute(w16cid + "paraId", replyParaId),
                        new XAttribute(w16cid + "durableId", replyDurableId));
                    root.Add(replyElement);
                    _context?.Logger.LogDebug($"Added reply comment ID entry with paraId: {replyParaId}");
                }

                return doc.ToString();
            }
            catch (Exception ex)
            {
                _context?.Logger.LogError($"Failed to update commentsIds.xml: {ex.Message}");
                return CreateCommentsIdsXml(replyParaId, parentParaId, replyDurableId, parentDurableId);
            }
        }

        /// <summary>
        /// 创建commentsIds.xml的XML内容
        /// Create XML content for commentsIds.xml
        /// </summary>
        private static string CreateCommentsIdsXml(string replyParaId, string parentParaId, string replyDurableId, string parentDurableId)
        {
            return $@"<?xml version=""1.0"" encoding=""UTF-8"" standalone=""yes""?>
<w16cid:commentsIds xmlns:w16cid=""http://schemas.microsoft.com/office/word/2016/wordml/cid"">
    <w16cid:commentId w16cid:paraId=""{parentParaId}"" w16cid:durableId=""{parentDurableId}""/>
    <w16cid:commentId w16cid:paraId=""{replyParaId}"" w16cid:durableId=""{replyDurableId}""/>
</w16cid:commentsIds>";
        }
    }
}
