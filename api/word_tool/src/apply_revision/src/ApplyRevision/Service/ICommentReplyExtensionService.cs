using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace ApplyRevision.Service
{
    /// <summary>
    /// 评论回复扩展服务接口，负责处理Word评论回复的扩展XML文件操作
    /// Comment reply extension service interface responsible for handling Word comment reply extension XML file operations
    /// </summary>
    public interface ICommentReplyExtensionService
    {
        /// <summary>
        /// 初始化服务，设置文档部分
        /// Initialize service with document parts
        /// </summary>
        /// <param name="mainPart">主文档部分 / Main document part</param>
        void Initialize(MainDocumentPart mainPart);

        /// <summary>
        /// 创建评论回复的扩展XML文件
        /// Create extension XML files for comment reply
        /// </summary>
        /// <param name="parentComment">父评论 / Parent comment</param>
        /// <param name="replyComment">回复评论 / Reply comment</param>
        /// <param name="replyDurableId">回复持久ID / Reply durable ID</param>
        /// <param name="parentDurableId">父评论持久ID / Parent durable ID</param>
        /// <param name="utcDateString">UTC日期字符串 / UTC date string</param>
        /// <returns>是否成功创建 / Whether creation was successful</returns>
        bool CreateReplyExtensions(
            Comment parentComment,
            Comment replyComment,
            string replyDurableId,
            string parentDurableId,
            string utcDateString);

        /// <summary>
        /// 检查服务是否已初始化
        /// Check if service is initialized
        /// </summary>
        bool IsInitialized { get; }
    }
}
