using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Factory
{
    /// <summary>
    /// Factory interface for creating basic OpenXML elements (non-revision specific)
    /// This factory focuses on fundamental document elements without revision tracking metadata
    /// For revision-specific elements with tracking metadata, use IRevisionElementFactory
    /// </summary>
    /// <remarks>
    /// <para><strong>Use IElementFactory when you need to:</strong></para>
    /// <list type="bullet">
    /// <item><description>Create basic document structure (paragraphs, runs, tables)</description></item>
    /// <item><description>Add simple text content without revision tracking</description></item>
    /// <item><description>Apply formatting to text and paragraphs</description></item>
    /// <item><description>Create comments for general annotation (not revision-related)</description></item>
    /// <item><description>Build document templates or static content</description></item>
    /// <item><description>Generate reports or documents from scratch</description></item>
    /// </list>
    /// 
    /// <para><strong>Use IRevisionElementFactory when you need to:</strong></para>
    /// <list type="bullet">
    /// <item><description>Track document changes with author and timestamp metadata</description></item>
    /// <item><description>Create insertion/deletion revision marks</description></item>
    /// <item><description>Handle move operations with proper linking</description></item>
    /// <item><description>Track property changes (formatting, table structure, etc.)</description></item>
    /// <item><description>Create revision-specific comments tied to change tracking</description></item>
    /// <item><description>Process RevisionOperator objects from change requests</description></item>
    /// </list>
    /// </remarks>
    /// <example>
    /// Basic usage scenarios:
    /// <code>
    /// var factory = new ElementFactory();
    /// 
    /// // Scenario 1: Building document structure
    /// var paragraph = factory.CreateParagraph("Introduction");
    /// var table = factory.CreateTable(3, 4, true);
    /// 
    /// // Scenario 2: Adding formatted content
    /// var titleRun = factory.CreateFormattedRun("Document Title", isBold: true, fontSize: 32);
    /// var bodyRun = factory.CreateTextRun("Regular body text");
    /// 
    /// // Scenario 3: Creating general comments
    /// var reviewComment = factory.CreateCommentElement(1, "Reviewer", "2024-01-15", "Please verify this section");
    /// 
    /// // Scenario 4: Text processing for display
    /// var textSegments = new List&lt;string&gt; { "First", "Second", "Third" };
    /// var runs = factory.CreateTextRuns(textSegments);
    /// 
    /// // NOT for revision tracking - use IRevisionElementFactory instead:
    /// // var insertedRun = revisionFactory.CreateInsertedRunElement("New content", "1", "Author", "2024-01-15");
    /// </code>
    /// </example>
    public interface IElementFactory
    {
        #region Basic Text Elements

        /// <summary>
        /// Create a Run element containing text or deleted text based on the revision operator type
        /// This method handles text formatting for revision content but does not create revision tracking elements
        /// </summary>
        /// <param name="text">The text content to include in the run</param>
        /// <param name="opType">The revision operator type (affects text element type: Text vs DeletedText)</param>
        /// <returns>A Run element with appropriate text content</returns>
        /// <example>
        /// <code>
        /// // Create a run with normal text
        /// var insertRun = factory.CreateRunElement("New text", RevisionOperatorType.ins);
        /// 
        /// // Create a run with deleted text formatting
        /// var deleteRun = factory.CreateRunElement("Old text", RevisionOperatorType.del);
        /// 
        /// // Text with line breaks will be handled automatically
        /// var multiLineRun = factory.CreateRunElement("Line 1\nLine 2\nLine 3", RevisionOperatorType.ins);
        /// </code>
        /// </example>
        Run CreateRunElement(string text, RevisionOperatorType opType);

        /// <summary>
        /// Create a simple Run element with plain text
        /// </summary>
        /// <param name="text">The text content</param>
        /// <param name="preserveSpace">Whether to preserve whitespace</param>
        /// <returns>A Run element with Text child</returns>
        /// <example>
        /// <code>
        /// var run = factory.CreateTextRun("Simple text", true);
        /// var runNoSpace = factory.CreateTextRun("No space preservation", false);
        /// </code>
        /// </example>
        Run CreateTextRun(string text, bool preserveSpace = true);

        /// <summary>
        /// Create a Paragraph element with optional text content
        /// </summary>
        /// <param name="text">Optional text content for the paragraph</param>
        /// <returns>A Paragraph element</returns>
        /// <example>
        /// <code>
        /// var emptyPara = factory.CreateParagraph();
        /// var textPara = factory.CreateParagraph("This is a paragraph");
        /// </code>
        /// </example>
        Paragraph CreateParagraph(string? text = null);

        /// <summary>
        /// Create a Break element for line breaks
        /// </summary>
        /// <param name="breakType">Type of break (line, page, column)</param>
        /// <returns>A Break element</returns>
        /// <example>
        /// <code>
        /// var lineBreak = factory.CreateBreak(BreakValues.TextWrapping);
        /// var pageBreak = factory.CreateBreak(BreakValues.Page);
        /// </code>
        /// </example>
        Break CreateBreak(BreakValues? breakType = null);

        #endregion

        #region Comment Elements (Basic Structure)

        /// <summary>
        /// Create a Comment element with the specified metadata and content
        /// This creates the basic comment structure without revision tracking
        /// </summary>
        /// <param name="commentId">Unique comment ID</param>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <returns>A Comment element with the specified properties</returns>
        /// <example>
        /// <code>
        /// // Create a comment with author and date
        /// var comment = factory.CreateCommentElement(1, "John Doe", "2024-01-15T10:30:00", "Please review this section");
        /// 
        /// // Create a comment with AI author (empty name)
        /// var aiComment = factory.CreateCommentElement(2, "", "2024-01-15T10:30:00", "AI suggested change");
        /// </code>
        /// </example>
        Comment CreateCommentElement(int commentId, string author, string date, string message);
        Comment CreateCommentElement(int commentId, string author, string date, string message, string paraId);

        /// <summary>
        /// Create a Run element containing a comment reference
        /// </summary>
        /// <param name="commentId">The ID of the comment to reference</param>
        /// <returns>A Run element containing a CommentReference</returns>
        /// <example>
        /// <code>
        /// // Create a comment reference to link to comment with ID "1"
        /// var reference = factory.CreateCommentReference("1");
        /// 
        /// // Use with StringValue
        /// StringValue commentId = "5";
        /// var reference2 = factory.CreateCommentReference(commentId);
        /// </code>
        /// </example>
        Run CreateCommentReference(StringValue commentId);

        #region Comment Range Elements

        /// <summary>
        /// Create a CommentRangeStart element for marking the beginning of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range start refers to</param>
        /// <returns>A CommentRangeStart element</returns>
        CommentRangeStart CreateCommentRangeStart(StringValue commentId);

        /// <summary>
        /// Create a CommentRangeEnd element for marking the end of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range end refers to</param>
        /// <returns>A CommentRangeEnd element</returns>
        CommentRangeEnd CreateCommentRangeEnd(StringValue commentId);

        #endregion

        #endregion

        #region Formatting Elements

        /// <summary>
        /// Create a Run with specific formatting properties
        /// </summary>
        /// <param name="text">Text content</param>
        /// <param name="isBold">Whether text should be bold</param>
        /// <param name="isItalic">Whether text should be italic</param>
        /// <param name="isUnderline">Whether text should be underlined</param>
        /// <param name="fontSize">Font size in half-points (e.g., 24 for 12pt)</param>
        /// <param name="fontName">Font family name</param>
        /// <returns>A formatted Run element</returns>
        /// <example>
        /// <code>
        /// var boldRun = factory.CreateFormattedRun("Bold text", isBold: true);
        /// var styledRun = factory.CreateFormattedRun("Styled text", true, true, true, 28, "Arial");
        /// </code>
        /// </example>
        Run CreateFormattedRun(string text, bool isBold = false, bool isItalic = false,
            bool isUnderline = false, int? fontSize = null, string? fontName = null);

        /// <summary>
        /// Create RunProperties element with specified formatting
        /// </summary>
        /// <param name="isBold">Whether text should be bold</param>
        /// <param name="isItalic">Whether text should be italic</param>
        /// <param name="isUnderline">Whether text should be underlined</param>
        /// <param name="fontSize">Font size in half-points</param>
        /// <param name="fontName">Font family name</param>
        /// <returns>A RunProperties element</returns>
        /// <example>
        /// <code>
        /// var props = factory.CreateRunProperties(true, false, true, 24, "Times New Roman");
        /// </code>
        /// </example>
        RunProperties CreateRunProperties(bool isBold = false, bool isItalic = false,
            bool isUnderline = false, int? fontSize = null, string? fontName = null);

        /// <summary>
        /// Create ParagraphProperties element with specified formatting
        /// </summary>
        /// <param name="alignment">Text alignment</param>
        /// <param name="indentation">Left indentation in twips</param>
        /// <param name="spacingBefore">Space before paragraph in twips</param>
        /// <param name="spacingAfter">Space after paragraph in twips</param>
        /// <returns>A ParagraphProperties element</returns>
        /// <example>
        /// <code>
        /// var props = factory.CreateParagraphProperties(JustificationValues.Center, 720, 240, 240);
        /// </code>
        /// </example>
        ParagraphProperties CreateParagraphProperties(JustificationValues? alignment = null,
            int? indentation = null, int? spacingBefore = null, int? spacingAfter = null);

        #endregion

        #region Table Elements

        /// <summary>
        /// Create a basic Table element with specified rows and columns
        /// </summary>
        /// <param name="rows">Number of rows</param>
        /// <param name="columns">Number of columns</param>
        /// <param name="hasHeader">Whether the first row should be treated as header</param>
        /// <returns>A Table element with the specified structure</returns>
        /// <example>
        /// <code>
        /// var table = factory.CreateTable(3, 4, true); // 3 rows, 4 columns, with header
        /// var simpleTable = factory.CreateTable(2, 2, false);
        /// </code>
        /// </example>
        Table CreateTable(int rows, int columns, bool hasHeader = false);

        /// <summary>
        /// Create a TableRow element with specified number of cells
        /// </summary>
        /// <param name="cellCount">Number of cells in the row</param>
        /// <param name="isHeader">Whether this is a header row</param>
        /// <returns>A TableRow element</returns>
        /// <example>
        /// <code>
        /// var headerRow = factory.CreateTableRow(4, true);
        /// var dataRow = factory.CreateTableRow(4, false);
        /// </code>
        /// </example>
        TableRow CreateTableRow(int cellCount, bool isHeader = false);

        /// <summary>
        /// Create a TableCell element with optional text content
        /// </summary>
        /// <param name="text">Optional text content for the cell</param>
        /// <param name="isHeader">Whether this is a header cell</param>
        /// <returns>A TableCell element</returns>
        /// <example>
        /// <code>
        /// var headerCell = factory.CreateTableCell("Header", true);
        /// var dataCell = factory.CreateTableCell("Data", false);
        /// var emptyCell = factory.CreateTableCell();
        /// </code>
        /// </example>
        TableCell CreateTableCell(string? text = null, bool isHeader = false);

        #endregion

        #region Utility Methods

        /// <summary>
        /// Get initials for a comment author name
        /// </summary>
        /// <param name="author">Full author name</param>
        /// <returns>Author initials (e.g., "John Lee" -> "JL")</returns>
        /// <example>
        /// <code>
        /// var initials1 = factory.GetCommentInitials("John Doe");        // Returns "JD"
        /// var initials2 = factory.GetCommentInitials("Mary Jane Smith"); // Returns "MJS"
        /// var initials3 = factory.GetCommentInitials("");               // Returns "AI"
        /// var initials4 = factory.GetCommentInitials("SingleName");     // Returns "S"
        /// var initials5 = factory.GetCommentInitials("jane, doe");      // Returns "JD"
        /// </code>
        /// </example>
        StringValue GetCommentInitials(string author);

        /// <summary>
        /// Validate that an element is properly formed for Word document insertion
        /// </summary>
        /// <param name="element">Element to validate</param>
        /// <returns>True if element is valid, false otherwise</returns>
        /// <example>
        /// <code>
        /// var run = factory.CreateTextRun("Test");
        /// bool isValid = factory.ValidateElement(run);
        /// </code>
        /// </example>
        bool ValidateElement(OpenXmlElement element);

        /// <summary>
        /// Clone an existing OpenXML element with optional modifications
        /// </summary>
        /// <param name="sourceElement">Element to clone</param>
        /// <param name="deepClone">Whether to perform deep clone of child elements</param>
        /// <returns>Cloned element</returns>
        /// <example>
        /// <code>
        /// var originalRun = factory.CreateTextRun("Original");
        /// var clonedRun = factory.CloneElement(originalRun, true);
        /// </code>
        /// </example>
        T CloneElement<T>(T sourceElement, bool deepClone = true) where T : OpenXmlElement;

        /// <summary>
        /// Create a collection of basic runs from text content
        /// This is a utility method for converting simple text to document elements
        /// </summary>
        /// <param name="textSegments">List of text segments to convert</param>
        /// <param name="preserveSpace">Whether to preserve whitespace</param>
        /// <returns>List of Run elements</returns>
        /// <example>
        /// <code>
        /// var texts = new List&lt;string&gt; { "First", "Second", "Third" };
        /// var runs = factory.CreateTextRuns(texts, true);
        /// </code>
        /// </example>
        List<Run> CreateTextRuns(List<string> textSegments, bool preserveSpace = true);

        #endregion
    }
}