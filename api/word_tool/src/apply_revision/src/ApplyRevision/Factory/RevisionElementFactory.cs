using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using System.Globalization;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Factory
{
    /// <summary>
    /// Factory implementation for creating OpenXML revision elements used in document change tracking
    /// This class provides comprehensive support for all revision types in WordprocessingML
    /// </summary>
    /// <example>
    /// Basic usage:
    /// <code>
    /// var factory = new RevisionElementFactory();
    /// 
    /// // Create basic revision elements
    /// var insertedRun = factory.CreateInsertedRunElement("New content", "1", "John Doe", "2024-01-15");
    /// var deletedRun = factory.CreateDeletedRunElement("Old content", "2", "Jane Smith", "2024-01-15");
    /// 
    /// // Create move operations
    /// var moveFromRun = factory.CreateMoveFromRunElement("Moved text", "3", "Editor", "2024-01-15");
    /// var moveToRun = factory.CreateMoveToRunElement("Moved text", "4", "Editor", "2024-01-15");
    /// 
    /// // Create range markers
    /// var rangeStart = factory.CreateMoveFromRangeStart("5", "move_op_1", "Editor", "2024-01-15");
    /// var rangeEnd = factory.CreateMoveFromRangeEnd("5");
    /// 
    /// // Create property change revisions
    /// var propChange = factory.CreateRunPropertiesChange("6", "Author", "2024-01-15", previousProps);
    /// 
    /// // Create revisions using enhanced RevisionOperator
    /// var complexOp = new RevisionOperator 
    /// { 
    ///     Op = RevisionOperatorType.cellMerge, 
    ///     Author = "Table Editor",
    ///     Date = "2024-01-15",
    ///     Scope = RevisionScope.Cell,
    ///     Properties = new RevisionProperties 
    ///     { 
    ///         VMerge = MergedCellValues.Restart,
    ///         GridSpan = 2
    ///     }
    /// };
    /// var cellMerge = factory.CreateRevisionElement(complexOp, "7");
    /// </code>
    /// </example>
    public class RevisionElementFactory : IRevisionElementFactory
    {
        #region Basic Text Revision Elements

        /// <summary>
        /// Create an InsertedRun element with the specified content and metadata
        /// </summary>
        /// <param name="text">The text content to be inserted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>An InsertedRun element with the specified properties</returns>
        public InsertedRun CreateInsertedRunElement(string text, string revisionId, string author, string date)
        {
            var insertedRun = new InsertedRun
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            var run = new Run();
            var runText = new Text(text);
            run.Append(runText);
            insertedRun.Append(run);

            return insertedRun;
        }

        /// <summary>
        /// Create an InsertedRun element with the specified content and metadata, with optional property inheritance
        /// </summary>
        /// <param name="text">The text content to be inserted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="originalRun">Original run to inherit properties from</param>
        /// <returns>An InsertedRun element with the specified properties and inherited formatting</returns>
        public InsertedRun CreateInsertedRunElement(string text, string revisionId, string author, string date, Run originalRun)
        {
            var insertedRun = new InsertedRun
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            // Create run with inherited properties
            Run run;
            if (originalRun?.RunProperties != null)
            {
                // Clone the original run with deep clone to preserve all properties including RunProperties
                run = originalRun.CloneNode(true) as Run ?? new Run();

                // Clear existing text content from cloned run
                run.RemoveAllChildren<Text>();
                run.RemoveAllChildren<TabChar>();
                run.RemoveAllChildren<Break>();
                run.RemoveAllChildren<CarriageReturn>();
                run.RemoveAllChildren<NoBreakHyphen>();
                run.RemoveAllChildren<SoftHyphen>();
                run.RemoveAllChildren<DayShort>();
                run.RemoveAllChildren<MonthShort>();
                run.RemoveAllChildren<YearShort>();
                run.RemoveAllChildren<DayLong>();
                run.RemoveAllChildren<MonthLong>();
                run.RemoveAllChildren<YearLong>();

                // Add the new text content
                var runText = new Text(text) { Space = SpaceProcessingModeValues.Preserve };
                run.Append(runText);
            }
            else
            {
                // No properties to inherit, create basic run
                run = new Run();
                var runText = new Text(text) { Space = SpaceProcessingModeValues.Preserve };
                run.Append(runText);
            }

            insertedRun.Append(run);
            return insertedRun;
        }

        /// <summary>
        /// Create a DeletedRun element with the specified content and metadata
        /// </summary>
        /// <param name="text">The text content that was deleted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A DeletedRun element with the specified properties</returns>
        public DeletedRun CreateDeletedRunElement(string text, string revisionId, string author, string date)
        {
            var deletedRun = new DeletedRun
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            var run = new Run();
            var deletedText = new DeletedText(text);
            run.Append(deletedText);
            deletedRun.Append(run);

            return deletedRun;
        }

        /// <summary>
        /// Create a DeletedRun element with the specified content and metadata, with optional property inheritance
        /// </summary>
        /// <param name="text">The text content that was deleted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="originalRun">Original run to inherit properties from</param>
        /// <returns>A DeletedRun element with the specified properties and inherited formatting</returns>
        public DeletedRun CreateDeletedRunElement(string text, string revisionId, string author, string date, Run originalRun)
        {
            var deletedRun = new DeletedRun
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            // Create run with inherited properties
            Run run;
            if (originalRun?.RunProperties != null)
            {
                // Clone the original run with deep clone to preserve all properties including RunProperties
                run = originalRun.CloneNode(true) as Run ?? new Run();

                // Clear existing text content from cloned run
                run.RemoveAllChildren<Text>();
                run.RemoveAllChildren<TabChar>();
                run.RemoveAllChildren<Break>();
                run.RemoveAllChildren<CarriageReturn>();
                run.RemoveAllChildren<NoBreakHyphen>();
                run.RemoveAllChildren<SoftHyphen>();
                run.RemoveAllChildren<DayShort>();
                run.RemoveAllChildren<MonthShort>();
                run.RemoveAllChildren<YearShort>();
                run.RemoveAllChildren<DayLong>();
                run.RemoveAllChildren<MonthLong>();
                run.RemoveAllChildren<YearLong>();

                // Add the new deleted text content
                var deletedText = new DeletedText(text) { Space = SpaceProcessingModeValues.Preserve };
                run.Append(deletedText);
            }
            else
            {
                // No properties to inherit, create basic run
                run = new Run();
                var deletedText = new DeletedText(text) { Space = SpaceProcessingModeValues.Preserve };
                run.Append(deletedText);
            }

            deletedRun.Append(run);
            return deletedRun;
        }

        /// <summary>
        /// Create an Inserted element for block-level insertions
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>An Inserted element with the specified properties</returns>
        public Inserted CreateInsertedElement(string revisionId, string author, string date)
        {
            return new Inserted
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a Deleted element for block-level deletions
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A Deleted element with the specified properties</returns>
        public Deleted CreateDeletedElement(string revisionId, string author, string date)
        {
            return new Deleted
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        #endregion

        #region Move Operation Elements

        /// <summary>
        /// Create a MoveFromRun element for move source content
        /// </summary>
        /// <param name="text">The text content being moved from this location</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveFromRun element with the specified properties</returns>
        public MoveFromRun CreateMoveFromRunElement(string text, string revisionId, string author, string date)
        {
            var moveFromRun = new MoveFromRun
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            var run = new Run();
            var runText = new Text(text);
            run.Append(runText);
            moveFromRun.Append(run);

            return moveFromRun;
        }

        /// <summary>
        /// Create a MoveToRun element for move destination content
        /// </summary>
        /// <param name="text">The text content being moved to this location</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveToRun element with the specified properties</returns>
        public MoveToRun CreateMoveToRunElement(string text, string revisionId, string author, string date)
        {
            var moveToRun = new MoveToRun
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            var run = new Run();
            var runText = new Text(text);
            run.Append(runText);
            moveToRun.Append(run);

            return moveToRun;
        }

        /// <summary>
        /// Create a MoveFromRangeStart element to mark the beginning of a move source range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <param name="moveName">Name identifying the move operation</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveFromRangeStart element</returns>
        public MoveFromRangeStart CreateMoveFromRangeStart(string rangeId, string moveName, string author, string date)
        {
            return new MoveFromRangeStart
            {
                Id = rangeId,
                Name = moveName,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a MoveFromRangeEnd element to mark the end of a move source range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <returns>A MoveFromRangeEnd element</returns>
        public MoveFromRangeEnd CreateMoveFromRangeEnd(string rangeId)
        {
            return new MoveFromRangeEnd
            {
                Id = rangeId
            };
        }

        /// <summary>
        /// Create a MoveToRangeStart element to mark the beginning of a move destination range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <param name="moveName">Name identifying the move operation</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveToRangeStart element</returns>
        public MoveToRangeStart CreateMoveToRangeStart(string rangeId, string moveName, string author, string date)
        {
            return new MoveToRangeStart
            {
                Id = rangeId,
                Name = moveName,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a MoveToRangeEnd element to mark the end of a move destination range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <returns>A MoveToRangeEnd element</returns>
        public MoveToRangeEnd CreateMoveToRangeEnd(string rangeId)
        {
            return new MoveToRangeEnd
            {
                Id = rangeId
            };
        }

        /// <summary>
        /// Create a move from operation wrapped with range markers
        /// This creates the full structure: MoveFromRangeStart → MoveFromRun → MoveFromRangeEnd
        /// </summary>
        /// <param name="text">The text content being moved from this location</param>
        /// <param name="moveFromRangeId">Unique ID for the move from range</param>
        /// <param name="moveFromRevisionId">Unique revision ID for the move from element</param>
        /// <param name="moveName">Name identifying the move operation (shared with move to)</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A list containing MoveFromRangeStart, MoveFromRun, and MoveFromRangeEnd elements</returns>
        public List<OpenXmlElement> CreateMoveFromWithRanges(string text, string moveFromRangeId, string moveFromRevisionId, string moveName, string author, string date)
        {
            var elements = new List<OpenXmlElement>();

            // Create MoveFromRangeStart
            var moveFromRangeStart = CreateMoveFromRangeStart(moveFromRangeId, moveName, author, date);
            elements.Add(moveFromRangeStart);

            // Create MoveFromRun with the actual content (this is the correct element that can contain runs)
            var moveFromRun = CreateMoveFromRunElement(text, moveFromRevisionId, author, date);
            elements.Add(moveFromRun);

            // Create MoveFromRangeEnd
            var moveFromRangeEnd = CreateMoveFromRangeEnd(moveFromRangeId);
            elements.Add(moveFromRangeEnd);

            return elements;
        }

        /// <summary>
        /// Create a move to operation wrapped with range markers
        /// This creates the full structure: MoveToRangeStart → MoveToRun → MoveToRangeEnd
        /// </summary>
        /// <param name="text">The text content being moved to this location</param>
        /// <param name="moveToRangeId">Unique ID for the move to range</param>
        /// <param name="moveToRevisionId">Unique revision ID for the move to element</param>
        /// <param name="moveName">Name identifying the move operation (shared with move from)</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A list containing MoveToRangeStart, MoveToRun, and MoveToRangeEnd elements</returns>
        public List<OpenXmlElement> CreateMoveToWithRanges(string text, string moveToRangeId, string moveToRevisionId, string moveName, string author, string date)
        {
            var elements = new List<OpenXmlElement>();

            // Create MoveToRangeStart
            var moveToRangeStart = CreateMoveToRangeStart(moveToRangeId, moveName, author, date);
            elements.Add(moveToRangeStart);

            // Create MoveToRun with the actual content (this is the correct element that can contain runs)
            var moveToRun = CreateMoveToRunElement(text, moveToRevisionId, author, date);
            elements.Add(moveToRun);

            // Create MoveToRangeEnd
            var moveToRangeEnd = CreateMoveToRangeEnd(moveToRangeId);
            elements.Add(moveToRangeEnd);

            return elements;
        }

        /// <summary>
        /// Create a complete move operation (both from and to) with proper linking
        /// This creates both source and destination with shared move name for proper linking
        /// </summary>
        /// <param name="text">The text content being moved</param>
        /// <param name="moveFromRangeId">Unique ID for the move from range</param>
        /// <param name="moveFromRevisionId">Unique revision ID for the move from element</param>
        /// <param name="moveToRangeId">Unique ID for the move to range</param>
        /// <param name="moveToRevisionId">Unique revision ID for the move to element</param>
        /// <param name="moveName">Name identifying the move operation (links from and to)</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A tuple containing move from elements and move to elements</returns>
        public (List<OpenXmlElement> moveFromElements, List<OpenXmlElement> moveToElements) CreateLinkedMoveOperationWithRanges(
            string text,
            string moveFromRangeId,
            string moveFromRevisionId,
            string moveToRangeId,
            string moveToRevisionId,
            string moveName,
            string author,
            string date)
        {
            var moveFromElements = CreateMoveFromWithRanges(text, moveFromRangeId, moveFromRevisionId, moveName, author, date);
            var moveToElements = CreateMoveToWithRanges(text, moveToRangeId, moveToRevisionId, moveName, author, date);

            return (moveFromElements, moveToElements);
        }

        /// <summary>
        /// Create move operations using the exact structure from your XML example
        /// This creates MoveFromRangeStart → MoveFrom → MoveFromRangeEnd and MoveToRangeStart → MoveTo → MoveToRangeEnd
        /// Note: This method attempts to use the OpenXML MoveFrom/MoveTo elements directly
        /// </summary>
        /// <param name="text">The text content being moved</param>
        /// <param name="moveFromRangeId">Range ID for move from</param>
        /// <param name="moveFromRevisionId">Revision ID for move from</param>
        /// <param name="moveToRangeId">Range ID for move to</param>
        /// <param name="moveToRevisionId">Revision ID for move to</param>
        /// <param name="moveName">Shared move name for linking</param>
        /// <param name="author">Author of the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>Tuple containing move from and move to element lists</returns>
        public (List<OpenXmlElement> moveFromElements, List<OpenXmlElement> moveToElements) CreateExactMoveStructure(
            string text,
            string moveFromRangeId,
            string moveFromRevisionId,
            string moveToRangeId,
            string moveToRevisionId,
            string moveName,
            string author,
            string date)
        {
            var moveFromElements = new List<OpenXmlElement>();
            var moveToElements = new List<OpenXmlElement>();

            // Move From Structure
            moveFromElements.Add(CreateMoveFromRangeStart(moveFromRangeId, moveName, author, date));

            // Try to create direct MoveFrom element (may not work with current OpenXML version)
            try
            {
                // Create a placeholder for the exact structure you showed
                // This would need the actual MoveFrom element from OpenXML if available
                var moveFromComment = new OpenXmlUnknownElement("w", "moveFrom", "http://schemas.openxmlformats.org/wordprocessingml/2006/main");
                moveFromComment.SetAttribute(new OpenXmlAttribute("w", "id", "http://schemas.openxmlformats.org/wordprocessingml/2006/main", moveFromRevisionId));
                moveFromComment.SetAttribute(new OpenXmlAttribute("w", "author", "http://schemas.openxmlformats.org/wordprocessingml/2006/main", author));
                moveFromComment.SetAttribute(new OpenXmlAttribute("w", "date", "http://schemas.openxmlformats.org/wordprocessingml/2006/main", date));
                moveFromComment.SetAttribute(new OpenXmlAttribute("w16du", "dateUtc", "http://schemas.microsoft.com/office/word/2023/wordml/word16du", date));

                // Add content as a run
                var run = new Run(new Text(text));
                moveFromComment.AppendChild(run);
                moveFromElements.Add(moveFromComment);
            }
            catch
            {
                // Fallback to MoveFromRun if MoveFrom is not available
                moveFromElements.Add(CreateMoveFromRunElement(text, moveFromRevisionId, author, date));
            }

            moveFromElements.Add(CreateMoveFromRangeEnd(moveFromRangeId));

            // Move To Structure
            moveToElements.Add(CreateMoveToRangeStart(moveToRangeId, moveName, author, date));

            // Try to create direct MoveTo element (may not work with current OpenXML version)
            try
            {
                // Create a placeholder for the exact structure you showed
                var moveToComment = new OpenXmlUnknownElement("w", "moveTo", "http://schemas.openxmlformats.org/wordprocessingml/2006/main");
                moveToComment.SetAttribute(new OpenXmlAttribute("w", "id", "http://schemas.openxmlformats.org/wordprocessingml/2006/main", moveToRevisionId));
                moveToComment.SetAttribute(new OpenXmlAttribute("w", "author", "http://schemas.openxmlformats.org/wordprocessingml/2006/main", author));
                moveToComment.SetAttribute(new OpenXmlAttribute("w", "date", "http://schemas.openxmlformats.org/wordprocessingml/2006/main", date));
                moveToComment.SetAttribute(new OpenXmlAttribute("w16du", "dateUtc", "http://schemas.microsoft.com/office/word/2023/wordml/word16du", date));

                // Add content as a run
                var run = new Run(new Text(text));
                moveToComment.AppendChild(run);
                moveToElements.Add(moveToComment);
            }
            catch
            {
                // Fallback to MoveToRun if MoveTo is not available
                moveToElements.Add(CreateMoveToRunElement(text, moveToRevisionId, author, date));
            }

            moveToElements.Add(CreateMoveToRangeEnd(moveToRangeId));

            return (moveFromElements, moveToElements);
        }

        #endregion

        #region Properties Change Elements

        /// <summary>
        /// Create a RunPropertiesChange element for tracking run property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous run properties before the change</param>
        /// <returns>A RunPropertiesChange element</returns>
        public RunPropertiesChange CreateRunPropertiesChange(string revisionId, string author, string date, RunProperties? previousProperties = null)
        {
            var runPropertiesChange = new RunPropertiesChange
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            if (previousProperties != null)
            {
                var previousRunProperties = new PreviousRunProperties();
                previousRunProperties.Append(previousProperties.CloneNode(true));
                runPropertiesChange.Append(previousRunProperties);
            }

            return runPropertiesChange;
        }

        /// <summary>
        /// Create a ParagraphPropertiesChange element for tracking paragraph property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous paragraph properties before the change</param>
        /// <returns>A ParagraphPropertiesChange element</returns>
        public ParagraphPropertiesChange CreateParagraphPropertiesChange(string revisionId, string author, string date, ParagraphProperties? previousProperties = null)
        {
            var paragraphPropertiesChange = new ParagraphPropertiesChange
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            if (previousProperties != null)
            {
                var previousParagraphProperties = new PreviousParagraphProperties();
                previousParagraphProperties.Append(previousProperties.CloneNode(true));
                paragraphPropertiesChange.Append(previousParagraphProperties);
            }

            return paragraphPropertiesChange;
        }

        /// <summary>
        /// Create a SectionPropertiesChange element for tracking section property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous section properties before the change</param>
        /// <returns>A SectionPropertiesChange element</returns>
        public SectionPropertiesChange CreateSectionPropertiesChange(string revisionId, string author, string date, SectionProperties? previousProperties = null)
        {
            var sectionPropertiesChange = new SectionPropertiesChange
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            if (previousProperties != null)
            {
                var previousSectionProperties = new PreviousSectionProperties();
                previousSectionProperties.Append(previousProperties.CloneNode(true));
                sectionPropertiesChange.Append(previousSectionProperties);
            }

            return sectionPropertiesChange;
        }

        #endregion

        #region Table Revision Elements

        /// <summary>
        /// Create a TablePropertiesChange element for tracking table property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous table properties before the change</param>
        /// <returns>A TablePropertiesChange element</returns>
        public TablePropertiesChange CreateTablePropertiesChange(string revisionId, string author, string date, TableProperties? previousProperties = null)
        {
            var tablePropertiesChange = new TablePropertiesChange
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            if (previousProperties != null)
            {
                var previousTableProperties = new PreviousTableProperties();
                previousTableProperties.Append(previousProperties.CloneNode(true));
                tablePropertiesChange.Append(previousTableProperties);
            }

            return tablePropertiesChange;
        }

        /// <summary>
        /// Create a TableRowPropertiesChange element for tracking table row property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous table row properties before the change</param>
        /// <returns>A TableRowPropertiesChange element</returns>
        public TableRowPropertiesChange CreateTableRowPropertiesChange(string revisionId, string author, string date, TableRowProperties? previousProperties = null)
        {
            var tableRowPropertiesChange = new TableRowPropertiesChange
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            if (previousProperties != null)
            {
                var previousTableRowProperties = new PreviousTableRowProperties();
                previousTableRowProperties.Append(previousProperties.CloneNode(true));
                tableRowPropertiesChange.Append(previousTableRowProperties);
            }

            return tableRowPropertiesChange;
        }

        /// <summary>
        /// Create a TableCellPropertiesChange element for tracking table cell property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous table cell properties before the change</param>
        /// <returns>A TableCellPropertiesChange element</returns>
        public TableCellPropertiesChange CreateTableCellPropertiesChange(string revisionId, string author, string date, TableCellProperties? previousProperties = null)
        {
            var tableCellPropertiesChange = new TableCellPropertiesChange
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };

            if (previousProperties != null)
            {
                var previousTableCellProperties = new PreviousTableCellProperties();
                previousTableCellProperties.Append(previousProperties.CloneNode(true));
                tableCellPropertiesChange.Append(previousTableCellProperties);
            }

            return tableCellPropertiesChange;
        }

        /// <summary>
        /// Create a table cell insertion element
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CellInsertion element</returns>
        public CellInsertion CreateCellInsertion(string revisionId, string author, string date)
        {
            return new CellInsertion
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a table cell deletion element
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CellDeletion element</returns>
        public CellDeletion CreateCellDeletion(string revisionId, string author, string date)
        {
            return new CellDeletion
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a table cell merge element
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="vMerge">Vertical merge type (restart/continue)</param>
        /// <returns>A CellMerge element</returns>
        public CellMerge CreateCellMerge(string revisionId, string author, string date, MergedCellValues vMerge)
        {
            return new CellMerge
            {
                Id = revisionId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
                // Note: CellMerge doesn't have VerticalMerge property, removing this line
            };
        }

        #endregion

        #region Custom XML and Math Elements

        /// <summary>
        /// Create a CustomXmlInsRangeStart element for custom XML insertion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CustomXmlInsRangeStart element</returns>
        public CustomXmlInsRangeStart CreateCustomXmlInsRangeStart(string rangeId, string author, string date)
        {
            return new CustomXmlInsRangeStart
            {
                Id = rangeId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a CustomXmlInsRangeEnd element for custom XML insertion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <returns>A CustomXmlInsRangeEnd element</returns>
        public CustomXmlInsRangeEnd CreateCustomXmlInsRangeEnd(string rangeId)
        {
            return new CustomXmlInsRangeEnd
            {
                Id = rangeId
            };
        }

        /// <summary>
        /// Create a CustomXmlDelRangeStart element for custom XML deletion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CustomXmlDelRangeStart element</returns>
        public CustomXmlDelRangeStart CreateCustomXmlDelRangeStart(string rangeId, string author, string date)
        {
            return new CustomXmlDelRangeStart
            {
                Id = rangeId,
                Author = author,
                Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
            };
        }

        /// <summary>
        /// Create a CustomXmlDelRangeEnd element for custom XML deletion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <returns>A CustomXmlDelRangeEnd element</returns>
        public CustomXmlDelRangeEnd CreateCustomXmlDelRangeEnd(string rangeId)
        {
            return new CustomXmlDelRangeEnd
            {
                Id = rangeId
            };
        }

        #endregion

        #region Comment Range Elements

        /// <summary>
        /// Create a CommentRangeStart element for marking the beginning of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range start refers to</param>
        /// <returns>A CommentRangeStart element</returns>
        public CommentRangeStart CreateCommentRangeStart(StringValue commentId)
        {
            return new CommentRangeStart
            {
                Id = commentId
            };
        }

        /// <summary>
        /// Create a CommentRangeEnd element for marking the end of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range end refers to</param>
        /// <returns>A CommentRangeEnd element</returns>
        public CommentRangeEnd CreateCommentRangeEnd(StringValue commentId)
        {
            return new CommentRangeEnd
            {
                Id = commentId
            };
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Create a revision element based on the revision operator type and properties
        /// This method handles all supported revision types including basic text operations,
        /// move operations, property changes, table operations, and more
        /// </summary>
        /// <param name="revisionOperator">The revision operator containing operation type, scope, and metadata</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <returns>An OpenXmlElement representing the revision, or null if the operation type is not supported</returns>
        /// <example>
        /// <code>
        /// // Basic text operations
        /// var insertOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.ins, 
        ///     Text = "New content", 
        ///     Author = "John Doe",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Run
        /// };
        /// var insertElement = factory.CreateRevisionElement(insertOp, "rev001"); // Returns InsertedRun
        /// 
        /// // Move operations
        /// var moveFromOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.moveFrom, 
        ///     Text = "Moved content", 
        ///     Author = "Editor",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Run,
        ///     Properties = new RevisionProperties 
        ///     { 
        ///         MoveId = "move_001", 
        ///         MoveName = "content_relocation" 
        ///     }
        /// };
        /// var moveElement = factory.CreateRevisionElement(moveFromOp, "rev002"); // Returns MoveFromRun
        /// 
        /// // Property changes
        /// var propChangeOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.rPrChange, 
        ///     Author = "Formatter",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Run,
        ///     IsFormattingOnly = true,
        ///     Properties = new RevisionProperties 
        ///     { 
        ///         PreviousPropertiesXml = "&lt;w:rPr&gt;&lt;w:b/&gt;&lt;/w:rPr&gt;" 
        ///     }
        /// };
        /// var propElement = factory.CreateRevisionElement(propChangeOp, "rev003"); // Returns RunPropertiesChange
        /// 
        /// // Table operations
        /// var cellDelOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.cellDel, 
        ///     Author = "Table Editor",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Cell,
        ///     Properties = new RevisionProperties 
        ///     { 
        ///         GridSpan = 2,
        ///         RowIndex = 1,
        ///         CellIndex = 2
        ///     }
        /// };
        /// var cellElement = factory.CreateRevisionElement(cellDelOp, "rev004"); // Returns CellDeletion
        /// 
        /// // Block-level operations
        /// var blockInsOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.blockIns, 
        ///     Author = "Content Creator",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Paragraph,
        ///     XmlContent = "&lt;w:p&gt;&lt;w:r&gt;&lt;w:t&gt;New paragraph&lt;/w:t&gt;&lt;/w:r&gt;&lt;/w:p&gt;"
        /// };
        /// var blockElement = factory.CreateRevisionElement(blockInsOp, "rev005"); // Returns Inserted
        /// </code>
        /// </example>
        public OpenXmlElement? CreateRevisionElement(RevisionOperator revisionOperator, string revisionId)
        {
            if (revisionOperator == null)
                return null;

            try
            {
                return revisionOperator.Op switch
                {
                    RevisionOperatorType.ins => CreateInsertionElement(revisionOperator, revisionId),
                    RevisionOperatorType.del => CreateDeletionElement(revisionOperator, revisionId),
                    RevisionOperatorType.moveFrom => CreateMoveFromElement(revisionOperator, revisionId),
                    RevisionOperatorType.moveTo => CreateMoveToElement(revisionOperator, revisionId),
                    RevisionOperatorType.rPrChange => CreateRunPropertiesChange(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.pPrChange => CreateParagraphPropertiesChange(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.sectPrChange => CreateSectionPropertiesChange(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.tblPrChange => CreateTablePropertiesChange(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.trPrChange => CreateTableRowPropertiesChange(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.tcPrChange => CreateTableCellPropertiesChange(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.cellIns => CreateCellInsertion(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.cellDel => CreateCellDeletion(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.cellMerge => CreateCellMergeElement(revisionOperator, revisionId),
                    RevisionOperatorType.blockIns => CreateInsertedElement(revisionId, revisionOperator.Author, revisionOperator.Date),
                    RevisionOperatorType.blockDel => CreateDeletedElement(revisionId, revisionOperator.Author, revisionOperator.Date),
                    _ => null
                };
            }
            catch (Exception)
            {
                // Log the exception if needed
                return null;
            }
        }

        /// <summary>
        /// Create a collection of revision elements and comments based on a list of revision operators
        /// This method handles the creation of complete revision structures including comments
        /// </summary>
        /// <param name="revisionOperators">List of revision operators to process</param>
        /// <param name="editType">The type of edit being performed</param>
        /// <param name="startingRevisionId">The starting revision ID for generating unique IDs</param>
        /// <param name="startingCommentId">The starting comment ID for generating unique comment IDs</param>
        /// <returns>A tuple containing the list of revision elements and associated comments</returns>
        public (List<OpenXmlElement> revisionElements, List<Comment> comments) CreateRevisionElements(
            List<RevisionOperator> revisionOperators,
            EditType editType,
            int startingRevisionId,
            int startingCommentId)
        {
            var revisionElements = new List<OpenXmlElement>();
            var comments = new List<Comment>();
            int revisionIdCounter = startingRevisionId;
            int commentIdCounter = startingCommentId;

            foreach (var revisionOperator in revisionOperators)
            {
                var revisionId = revisionIdCounter.ToString();
                var element = CreateRevisionElement(revisionOperator, revisionId);

                if (element != null)
                {
                    revisionElements.Add(element);
                }

                // Create comments if needed for certain revision types
                if (ShouldCreateComment(revisionOperator, editType))
                {
                    var comment = CreateComment(revisionOperator, commentIdCounter.ToString());
                    if (comment != null)
                    {
                        comments.Add(comment);
                        commentIdCounter++;
                    }
                }

                revisionIdCounter++;
            }

            return (revisionElements, comments);
        }

        /// <summary>
        /// Create a revision tracking element for paragraph marks that are deleted or moved
        /// </summary>
        /// <param name="revisionType">Type of revision (delete/move)</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A revision element for paragraph mark tracking</returns>
        public OpenXmlElement CreateParagraphMarkRevision(RevisionOperatorType revisionType, string revisionId, string author, string date)
        {
            return revisionType switch
            {
                RevisionOperatorType.del => new DeletedRun
                {
                    Id = revisionId,
                    Author = author,
                    Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
                },
                RevisionOperatorType.moveFrom => new MoveFromRun
                {
                    Id = revisionId,
                    Author = author,
                    Date = DateTime.Parse(date, CultureInfo.InvariantCulture)
                },
                _ => throw new ArgumentException($"Unsupported revision type for paragraph mark: {revisionType}")
            };
        }

        #endregion

        #region Private Helper Methods

        private OpenXmlElement? CreateInsertionElement(RevisionOperator revisionOperator, string revisionId)
        {
            if (revisionOperator.Scope == RevisionScope.Run && !string.IsNullOrEmpty(revisionOperator.Text))
            {
                return CreateInsertedRunElement(revisionOperator.Text, revisionId, revisionOperator.Author, revisionOperator.Date);
            }
            else
            {
                return CreateInsertedElement(revisionId, revisionOperator.Author, revisionOperator.Date);
            }
        }

        private OpenXmlElement? CreateDeletionElement(RevisionOperator revisionOperator, string revisionId)
        {
            if (revisionOperator.Scope == RevisionScope.Run && !string.IsNullOrEmpty(revisionOperator.Text))
            {
                return CreateDeletedRunElement(revisionOperator.Text, revisionId, revisionOperator.Author, revisionOperator.Date);
            }
            else
            {
                return CreateDeletedElement(revisionId, revisionOperator.Author, revisionOperator.Date);
            }
        }

        private OpenXmlElement? CreateMoveFromElement(RevisionOperator revisionOperator, string revisionId)
        {
            if (revisionOperator.Scope == RevisionScope.Run && !string.IsNullOrEmpty(revisionOperator.Text))
            {
                return CreateMoveFromRunElement(revisionOperator.Text, revisionId, revisionOperator.Author, revisionOperator.Date);
            }
            return null;
        }

        private OpenXmlElement? CreateMoveToElement(RevisionOperator revisionOperator, string revisionId)
        {
            if (revisionOperator.Scope == RevisionScope.Run && !string.IsNullOrEmpty(revisionOperator.Text))
            {
                return CreateMoveToRunElement(revisionOperator.Text, revisionId, revisionOperator.Author, revisionOperator.Date);
            }
            return null;
        }

        private CellMerge CreateCellMergeElement(RevisionOperator revisionOperator, string revisionId)
        {
            var vMerge = MergedCellValues.Restart; // Default value
            if (revisionOperator.Properties?.VMerge.HasValue == true)
            {
                vMerge = revisionOperator.Properties.VMerge.Value;
            }

            return CreateCellMerge(revisionId, revisionOperator.Author, revisionOperator.Date, vMerge);
        }

        private bool ShouldCreateComment(RevisionOperator revisionOperator, EditType editType)
        {
            // Create comments for significant changes or when explicitly requested
            return revisionOperator.Op == RevisionOperatorType.del ||
                   revisionOperator.Op == RevisionOperatorType.moveFrom ||
                   revisionOperator.Op == RevisionOperatorType.cellDel;
        }

        private Comment? CreateComment(RevisionOperator revisionOperator, string commentId)
        {
            var comment = new Comment
            {
                Id = commentId,
                Author = revisionOperator.Author,
                Date = DateTime.Parse(revisionOperator.Date, CultureInfo.InvariantCulture)
            };

            // Create comment content
            var paragraph = new Paragraph();
            var run = new Run();
            var text = new Text($"Revision: {revisionOperator.Op} by {revisionOperator.Author}");

            if (!string.IsNullOrEmpty(revisionOperator.Text))
            {
                text = new Text($"Revision: {revisionOperator.Op} - \"{revisionOperator.Text}\" by {revisionOperator.Author}");
            }

            run.Append(text);
            paragraph.Append(run);
            comment.Append(paragraph);

            return comment;
        }

        #endregion
    }
}
