using System.Text.RegularExpressions;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using Common.Helper;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Factory
{
    /// <summary>
    /// Factory implementation for creating basic OpenXML elements (non-revision specific)
    /// </summary>
    public class ElementFactory : IElementFactory
    {
        #region Basic Text Elements

        /// <summary>
        /// Create a Run element containing text or deleted text based on the revision operator type
        /// </summary>
        /// <param name="text">The text content to include in the run</param>
        /// <param name="opType">The revision operator type (insert/delete)</param>
        /// <returns>A Run element with appropriate text content</returns>
        public Run CreateRunElement(string text, RevisionOperatorType opType)
        {
            // Handle null or empty text
            if (string.IsNullOrEmpty(text))
                return new Run();

            var normalized = Regex.Replace(text, @"\n{2,}", "\n");
            var lines = normalized.Split('\n');
            var run = new Run();

            // Filter out trailing empty segments to avoid extra breaks
            var nonEmptySegments = new List<string>();
            for (int i = 0; i < lines.Length; i++)
            {
                var segment = lines[i];
                // Include non-empty segments, or empty segments that are not at the end
                if (!string.IsNullOrEmpty(segment) || i < lines.Length - 1)
                {
                    nonEmptySegments.Add(segment);
                }
            }

            for (int i = 0; i < nonEmptySegments.Count; i++)
            {
                var segment = nonEmptySegments[i];
                if (!string.IsNullOrEmpty(segment))
                {
                    if (opType == RevisionOperatorType.del)
                        run.AppendChild(new DeletedText(segment) { Space = SpaceProcessingModeValues.Preserve });
                    else
                        run.AppendChild(new Text(segment) { Space = SpaceProcessingModeValues.Preserve });
                }
                if (i < nonEmptySegments.Count - 1)
                    run.AppendChild(new Break());
            }
            return run;
        }

        /// <summary>
        /// Create a simple Run element with plain text
        /// </summary>
        /// <param name="text">The text content</param>
        /// <param name="preserveSpace">Whether to preserve whitespace</param>
        /// <returns>A Run element with Text child</returns>
        public Run CreateTextRun(string text, bool preserveSpace = true)
        {
            if (string.IsNullOrEmpty(text))
                return new Run();

            var run = new Run();
            var textElement = new Text(text);

            if (preserveSpace)
                textElement.Space = SpaceProcessingModeValues.Preserve;

            run.Append(textElement);
            return run;
        }

        /// <summary>
        /// Create a Paragraph element with optional text content
        /// </summary>
        /// <param name="text">Optional text content for the paragraph</param>
        /// <returns>A Paragraph element</returns>
        public Paragraph CreateParagraph(string? text = null)
        {
            var paragraph = new Paragraph();

            if (!string.IsNullOrEmpty(text))
            {
                var run = CreateTextRun(text);
                paragraph.Append(run);
            }

            return paragraph;
        }

        /// <summary>
        /// Create a Break element for line breaks
        /// </summary>
        /// <param name="breakType">Type of break (line, page, column)</param>
        /// <returns>A Break element</returns>
        public Break CreateBreak(BreakValues? breakType = null)
        {
            var breakElement = new Break();

            if (breakType.HasValue)
                breakElement.Type = breakType.Value;

            return breakElement;
        }

        #endregion

        #region Comment Elements (Basic Structure)

        /// <summary>
        /// Create a Comment element with the specified metadata and content
        /// </summary>
        /// <param name="commentId">Unique comment ID</param>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <returns>A Comment element with the specified properties</returns>
        public Comment CreateCommentElement(int commentId, string author, string date, string message)
        {
            var _date = DateHepler.SafeParseDate(date);
            Comment comment = new()
            {
                Id = commentId.ToString(),
                Author = author,
                Initials = GetCommentInitials(author),
                Date = new DateTimeValue(_date)
            };
            // comment element always ins text
            var run = CreateRunElement(message, RevisionOperatorType.ins);
            var paragraph = new Paragraph(run);
            comment.Append(paragraph);
            return comment;
        }

        /// <summary>
        /// Create a Comment element with the specified metadata and content
        /// </summary>
        /// <param name="commentId">Unique comment ID</param>
        /// <param name="author">Comment author name</param>
        /// <param name="date">Comment creation date</param>
        /// <param name="message">Comment text content</param>
        /// <param name="paraId">Comment para ID - will be set on the paragraph element inside the comment</param>
        public Comment CreateCommentElement(int commentId, string author, string date, string message, string paraId)
        {
            var _date = DateHepler.SafeParseDate(date);
            Comment comment = new()
            {
                Id = commentId.ToString(),
                Author = author,
                Initials = GetCommentInitials(author),
                Date = new DateTimeValue(_date)
            };

            // Create the paragraph with paraId attribute
            // 创建带有 paraId 属性的段落
            var run = CreateRunElement(message, RevisionOperatorType.ins);
            var paragraph = new Paragraph(run);

            // Set paraId on the paragraph element, not on the comment element
            // 在段落元素上设置 paraId，而不是在评论元素上
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", paraId));

            comment.Append(paragraph);
            return comment;
        }

        /// <summary>
        /// Create a Run element containing a comment reference
        /// </summary>
        /// <param name="commentId">The ID of the comment to reference</param>
        /// <returns>A Run element containing a CommentReference</returns>
        public Run CreateCommentReference(StringValue commentId)
        {
            var reference = new CommentReference() { Id = commentId };
            var run = new Run(reference)
            {
                RunProperties = new RunProperties(
                    new RunStyle { Val = "CommentReference" }
                )
            };
            return run;
        }

        /// <summary>
        /// Create a CommentRangeStart element for marking the beginning of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range start refers to</param>
        /// <returns>A CommentRangeStart element</returns>
        public CommentRangeStart CreateCommentRangeStart(StringValue commentId)
        {
            return new CommentRangeStart
            {
                Id = commentId
            };
        }

        /// <summary>
        /// Create a CommentRangeEnd element for marking the end of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range end refers to</param>
        /// <returns>A CommentRangeEnd element</returns>
        public CommentRangeEnd CreateCommentRangeEnd(StringValue commentId)
        {
            return new CommentRangeEnd
            {
                Id = commentId
            };
        }


        #endregion

        #region Formatting Elements

        /// <summary>
        /// Create a Run with specific formatting properties
        /// </summary>
        /// <param name="text">Text content</param>
        /// <param name="isBold">Whether text should be bold</param>
        /// <param name="isItalic">Whether text should be italic</param>
        /// <param name="isUnderline">Whether text should be underlined</param>
        /// <param name="fontSize">Font size in half-points (e.g., 24 for 12pt)</param>
        /// <param name="fontName">Font family name</param>
        /// <returns>A formatted Run element</returns>
        public Run CreateFormattedRun(string text, bool isBold = false, bool isItalic = false,
            bool isUnderline = false, int? fontSize = null, string? fontName = null)
        {
            var run = CreateTextRun(text);
            var runProperties = CreateRunProperties(isBold, isItalic, isUnderline, fontSize, fontName);

            if (runProperties.HasChildren)
            {
                run.PrependChild(runProperties);
            }

            return run;
        }

        /// <summary>
        /// Create RunProperties element with specified formatting
        /// </summary>
        /// <param name="isBold">Whether text should be bold</param>
        /// <param name="isItalic">Whether text should be italic</param>
        /// <param name="isUnderline">Whether text should be underlined</param>
        /// <param name="fontSize">Font size in half-points</param>
        /// <param name="fontName">Font family name</param>
        /// <returns>A RunProperties element</returns>
        public RunProperties CreateRunProperties(bool isBold = false, bool isItalic = false,
            bool isUnderline = false, int? fontSize = null, string? fontName = null)
        {
            var runProperties = new RunProperties();

            if (isBold)
                runProperties.Append(new Bold());

            if (isItalic)
                runProperties.Append(new Italic());

            if (isUnderline)
                runProperties.Append(new Underline { Val = UnderlineValues.Single });

            if (fontSize.HasValue)
                runProperties.Append(new FontSize { Val = fontSize.Value.ToString() });

            if (!string.IsNullOrEmpty(fontName))
                runProperties.Append(new RunFonts { Ascii = fontName, HighAnsi = fontName });

            return runProperties;
        }

        /// <summary>
        /// Create ParagraphProperties element with specified formatting
        /// </summary>
        /// <param name="alignment">Text alignment</param>
        /// <param name="indentation">Left indentation in twips</param>
        /// <param name="spacingBefore">Space before paragraph in twips</param>
        /// <param name="spacingAfter">Space after paragraph in twips</param>
        /// <returns>A ParagraphProperties element</returns>
        public ParagraphProperties CreateParagraphProperties(JustificationValues? alignment = null,
            int? indentation = null, int? spacingBefore = null, int? spacingAfter = null)
        {
            var paragraphProperties = new ParagraphProperties();

            if (alignment.HasValue)
                paragraphProperties.Append(new Justification { Val = alignment.Value });

            if (indentation.HasValue)
                paragraphProperties.Append(new Indentation { Left = indentation.Value.ToString() });

            if (spacingBefore.HasValue || spacingAfter.HasValue)
            {
                var spacing = new SpacingBetweenLines();
                if (spacingBefore.HasValue)
                    spacing.Before = spacingBefore.Value.ToString();
                if (spacingAfter.HasValue)
                    spacing.After = spacingAfter.Value.ToString();
                paragraphProperties.Append(spacing);
            }

            return paragraphProperties;
        }

        #endregion

        #region Table Elements

        /// <summary>
        /// Create a basic Table element with specified rows and columns
        /// </summary>
        /// <param name="rows">Number of rows</param>
        /// <param name="columns">Number of columns</param>
        /// <param name="hasHeader">Whether the first row should be treated as header</param>
        /// <returns>A Table element with the specified structure</returns>
        public Table CreateTable(int rows, int columns, bool hasHeader = false)
        {
            if (rows <= 0 || columns <= 0)
                throw new ArgumentException("Rows and columns must be greater than 0");

            var table = new Table();

            // Add table properties
            var tableProperties = new TableProperties(
                new TableBorders(
                    new TopBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new BottomBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new LeftBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new RightBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new InsideHorizontalBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new InsideVerticalBorder { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 }
                )
            );
            table.Append(tableProperties);

            // Create rows
            for (int i = 0; i < rows; i++)
            {
                bool isHeaderRow = hasHeader && i == 0;
                var row = CreateTableRow(columns, isHeaderRow);
                table.Append(row);
            }

            return table;
        }

        /// <summary>
        /// Create a TableRow element with specified number of cells
        /// </summary>
        /// <param name="cellCount">Number of cells in the row</param>
        /// <param name="isHeader">Whether this is a header row</param>
        /// <returns>A TableRow element</returns>
        public TableRow CreateTableRow(int cellCount, bool isHeader = false)
        {
            if (cellCount <= 0)
                throw new ArgumentException("Cell count must be greater than 0");

            var tableRow = new TableRow();

            if (isHeader)
            {
                var tableRowProperties = new TableRowProperties(
                    new TableHeader { Val = new EnumValue<OnOffOnlyValues>(OnOffOnlyValues.On) }
                );
                tableRow.Append(tableRowProperties);
            }

            // Create cells
            for (int i = 0; i < cellCount; i++)
            {
                var cell = CreateTableCell(null, isHeader);
                tableRow.Append(cell);
            }

            return tableRow;
        }

        /// <summary>
        /// Create a TableCell element with optional text content
        /// </summary>
        /// <param name="text">Optional text content for the cell</param>
        /// <param name="isHeader">Whether this is a header cell</param>
        /// <returns>A TableCell element</returns>
        public TableCell CreateTableCell(string? text = null, bool isHeader = false)
        {
            var tableCell = new TableCell();

            // Add cell properties
            var tableCellProperties = new TableCellProperties();
            if (isHeader)
            {
                tableCellProperties.Append(new Shading
                {
                    Val = ShadingPatternValues.Clear,
                    Color = "auto",
                    Fill = "D9D9D9"
                });
            }
            tableCell.Append(tableCellProperties);

            // Add content
            var paragraph = CreateParagraph(text);
            if (isHeader && paragraph.Elements<Run>().Any())
            {
                // Make header text bold
                var run = paragraph.Elements<Run>().First();
                var runProperties = run.Elements<RunProperties>().FirstOrDefault() ?? new RunProperties();
                runProperties.Append(new Bold());
                if (!run.Elements<RunProperties>().Any())
                    run.PrependChild(runProperties);
            }

            tableCell.Append(paragraph);
            return tableCell;
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Get initials for a comment author name
        /// </summary>
        /// <param name="author">Full author name</param>
        /// <returns>Author initials (e.g., "John Lee" -> "JL")</returns>
        public StringValue GetCommentInitials(string author)
        {
            if (string.IsNullOrWhiteSpace(author))
                return "AI";

            var parts = author.Split(new char[] {' ', ','}, StringSplitOptions.RemoveEmptyEntries);
            return new string([.. parts.Select(p => char.ToUpperInvariant(p[0]))]);
        }

        /// <summary>
        /// Validate that an element is properly formed for Word document insertion
        /// </summary>
        /// <param name="element">Element to validate</param>
        /// <returns>True if element is valid, false otherwise</returns>
        public bool ValidateElement(OpenXmlElement element)
        {
            if (element == null)
                return false;

            try
            {
                // Basic validation - check if element can be cloned (indicates proper structure)
                var cloned = element.CloneNode(false);

                // Additional validation based on element type
                switch (element)
                {
                    case Run run:
                        // Run should have at least one child element
                        return run.HasChildren;

                    case Paragraph paragraph:
                        // Paragraph is valid even if empty
                        return true;

                    case Table table:
                        // Table should have at least one row
                        return table.Elements<TableRow>().Any();

                    case TableRow row:
                        // Row should have at least one cell
                        return row.Elements<TableCell>().Any();

                    case TableCell cell:
                        // Cell should have at least one paragraph
                        return cell.Elements<Paragraph>().Any();

                    case Comment comment:
                        // Comment should have ID and at least one paragraph
                        return !string.IsNullOrEmpty(comment.Id) && comment.Elements<Paragraph>().Any();

                    default:
                        return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Clone an existing OpenXML element with optional modifications
        /// </summary>
        /// <param name="sourceElement">Element to clone</param>
        /// <param name="deepClone">Whether to perform deep clone of child elements</param>
        /// <returns>Cloned element</returns>
        public T CloneElement<T>(T sourceElement, bool deepClone = true) where T : OpenXmlElement
        {
            if (sourceElement == null)
                throw new ArgumentNullException(nameof(sourceElement));

            return (T)sourceElement.CloneNode(deepClone);
        }

        /// <summary>
        /// Create a collection of basic runs from text content
        /// </summary>
        /// <param name="textSegments">List of text segments to convert</param>
        /// <param name="preserveSpace">Whether to preserve whitespace</param>
        /// <returns>List of Run elements</returns>
        public List<Run> CreateTextRuns(List<string> textSegments, bool preserveSpace = true)
        {
            if (textSegments == null)
                return new List<Run>();

            var runs = new List<Run>();

            foreach (var segment in textSegments)
            {
                if (!string.IsNullOrEmpty(segment))
                {
                    var run = CreateTextRun(segment, preserveSpace);
                    runs.Add(run);
                }
            }

            return runs;
        }

        #endregion
    }
}