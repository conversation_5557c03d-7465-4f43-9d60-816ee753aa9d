using Amazon.Lambda.Core;
using ApplyRevision.Strategy;
using ApplyRevision.Service;
using ApplyRevision.Model;
using ApplyRevision.Helper;

namespace ApplyRevision.Factory
{
    /// <summary>
    /// Factory for creating operation strategies based on operation type
    /// </summary>
    public class OperationStrategyFactory(
        ILambdaContext context,
        IElementFactory elementFactory,
        IRevisionElementFactory revisionFactory,
        IIdManager idManager,
        ICommentManager commentManager
        )
    {
        private readonly ILambdaContext context = context;
        private readonly IElementFactory elementFactory = elementFactory;
        private readonly IRevisionElementFactory revisionFactory = revisionFactory;
        private readonly IIdManager idManager = idManager;
        private readonly ICommentManager commentManager = commentManager;

        /// <summary>
        /// Create strategy based on operation type
        /// </summary>
        /// <param name="operationType">Type of operation (insert/delete/replace/format/commentreply)</param>
        /// <returns>Appropriate strategy instance</returns>
        public IOperationStrategy CreateStrategy(string operationType)
        {
            return operationType?.ToLowerInvariant() switch
            {
                "insert" => new InsertStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                "delete" => new DeleteStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                "replace" => new ReplaceStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                "format" => new FormatStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                "commentreply" => CreateCommentReplyStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                "commentadd" => new CommentAddStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                "quotationseparate" => new QuotationSeparateStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                _ => throw new ArgumentException($"Unsupported operation type: {operationType}", nameof(operationType))
            };
        }

        /// <summary>
        /// Create strategy based on operation type enum
        /// </summary>
        /// <param name="operationType">Type of operation</param>
        /// <returns>Appropriate strategy instance</returns>
        public IOperationStrategy CreateStrategy(OperationType operationType)
        {
            return operationType switch
            {
                OperationType.Insert => new InsertStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                OperationType.Delete => new DeleteStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                OperationType.Replace => new ReplaceStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                OperationType.Format => new FormatStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                OperationType.CommentReply => CreateCommentReplyStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                OperationType.CommentAdd => new CommentAddStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                OperationType.QuotationSeparate => new QuotationSeparateStrategy(context, elementFactory, revisionFactory, idManager, commentManager),
                _ => throw new NotSupportedException($"Operation type {operationType} is not supported")
            };
        }

        /// <summary>
        /// Create operation strategy for an operation
        /// 为操作创建操作策略
        /// </summary>
        /// <param name="operation">The operation / 操作</param>
        /// <returns>Operation strategy instance / 操作策略实例</returns>
        public IOperationStrategy CreateStrategy(Operation operation)
        {
            return CreateStrategy(operation.Op);
        }

        /// <summary>
        /// Create CommentReplyStrategy with new orchestrator architecture
        /// 使用新编排器架构创建CommentReplyStrategy
        /// </summary>
        /// <param name="context">Lambda context / Lambda上下文</param>
        /// <param name="elementFactory">Element factory / 元素工厂</param>
        /// <param name="revisionFactory">Revision factory / 修订工厂</param>
        /// <param name="idManager">ID manager / ID管理器</param>
        /// <param name="commentManager">Comment manager / 评论管理器</param>
        /// <returns>CommentReplyStrategy instance / CommentReplyStrategy实例</returns>
        private static CommentReplyStrategy CreateCommentReplyStrategy(
            ILambdaContext context,
            IElementFactory elementFactory,
            IRevisionElementFactory revisionFactory,
            IIdManager idManager,
            ICommentManager commentManager)
        {
            // Create services for new architecture
            // 为新架构创建服务
            var commentXmlService = new CommentXmlService(context, elementFactory, idManager);
            var documentManipulationService = new DocumentManipulationService(context, elementFactory);
            var commentReplyExtensionService = new CommentReplyExtensionService(context);
            var validationService = new ValidationService(context);

            // Create orchestrator
            // 创建编排器
            ICommentOrchestrator commentOrchestrator = new CommentOrchestrator(
                context,
                commentXmlService,
                documentManipulationService,
                commentReplyExtensionService,
                validationService,
                idManager,
                elementFactory);

            // Create strategy with orchestrator
            // 使用编排器创建策略
            return new CommentReplyStrategy(
                context,
                elementFactory,
                revisionFactory,
                idManager,
                commentManager,
                commentOrchestrator);
        }
    }
}
