using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Model;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Factory
{
    /// <summary>
    /// Factory interface for creating OpenXML revision elements used in document change tracking
    /// This interface provides comprehensive support for all revision types in WordprocessingML
    /// For basic document elements without revision tracking, use IElementFactory
    /// </summary>
    /// <remarks>
    /// <para><strong>Use IRevisionElementFactory when you need to:</strong></para>
    /// <list type="bullet">
    /// <item><description>Track document changes with author and timestamp metadata</description></item>
    /// <item><description>Create insertion/deletion revision marks (InsertedRun, DeletedRun)</description></item>
    /// <item><description>Handle move operations with proper linking (MoveFromRun, MoveToRun)</description></item>
    /// <item><description>Track property changes (formatting, table structure, etc.)</description></item>
    /// <item><description>Create revision-specific comments tied to change tracking</description></item>
    /// <item><description>Process RevisionOperator objects from change requests</description></item>
    /// <item><description>Generate Word's Track Changes compatible elements</description></item>
    /// <item><description>Create complex revision ranges and markers</description></item>
    /// </list>
    /// 
    /// <para><strong>Use IElementFactory when you need to:</strong></para>
    /// <list type="bullet">
    /// <item><description>Create basic document structure without revision tracking</description></item>
    /// <item><description>Add simple text content or formatting</description></item>
    /// <item><description>Build document templates or static content</description></item>
    /// <item><description>Create general comments for annotation (not revision-related)</description></item>
    /// </list>
    /// </remarks>
    /// <example>
    /// Revision tracking usage scenarios:
    /// <code>
    /// var revisionFactory = new RevisionElementFactory();
    /// 
    /// // Scenario 1: Track text insertions and deletions
    /// var insertedRun = revisionFactory.CreateInsertedRunElement("New content", "1", "John Doe", "2024-01-15");
    /// var deletedRun = revisionFactory.CreateDeletedRunElement("Old content", "2", "Jane Smith", "2024-01-15");
    /// 
    /// // Scenario 2: Handle move operations with proper linking
    /// var (moveFromElements, moveToElements) = revisionFactory.CreateLinkedMoveOperationWithRanges(
    ///     "Moved text", "range1", "rev3", "range2", "rev4", "move_op_1", "Editor", "2024-01-15");
    /// 
    /// // Scenario 3: Track property changes
    /// var propChange = revisionFactory.CreateRunPropertiesChange("5", "Formatter", "2024-01-15", previousProps);
    /// 
    /// // Scenario 4: Process RevisionOperator objects (from API requests)
    /// var revisionOp = new RevisionOperator 
    /// { 
    ///     Op = RevisionOperatorType.ins, 
    ///     Text = "API content", 
    ///     Author = "API User",
    ///     Date = "2024-01-15"
    /// };
    /// var element = revisionFactory.CreateRevisionElement(revisionOp, "rev6");
    /// 
    /// // Scenario 5: Batch process multiple revisions
    /// var (elements, comments) = revisionFactory.CreateRevisionElements(
    ///     revisionOperators, EditType.inplace, 1, 1);
    /// 
    /// // NOT for basic document creation - use IElementFactory instead:
    /// // var basicRun = elementFactory.CreateTextRun("Simple text");
    /// </code>
    /// </example>
    public interface IRevisionElementFactory
    {
        #region Basic Text Revision Elements

        /// <summary>
        /// Create an InsertedRun element with the specified content and metadata
        /// </summary>
        /// <param name="text">The text content to be inserted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>An InsertedRun element with the specified properties</returns>
        InsertedRun CreateInsertedRunElement(string text, string revisionId, string author, string date);

        /// <summary>
        /// Create an InsertedRun element with the specified content and metadata, with optional property inheritance
        /// </summary>
        /// <param name="text">The text content to be inserted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="originalRun">Original run to inherit properties from (optional)</param>
        /// <returns>An InsertedRun element with the specified properties and inherited formatting</returns>
        InsertedRun CreateInsertedRunElement(string text, string revisionId, string author, string date, Run originalRun);

        /// <summary>
        /// Create a DeletedRun element with the specified content and metadata
        /// </summary>
        /// <param name="text">The text content that was deleted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A DeletedRun element with the specified properties</returns>
        DeletedRun CreateDeletedRunElement(string text, string revisionId, string author, string date);

        /// <summary>
        /// Create a DeletedRun element with the specified content and metadata, with optional property inheritance
        /// </summary>
        /// <param name="text">The text content that was deleted</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="originalRun">Original run to inherit properties from</param>
        /// <returns>A DeletedRun element with the specified properties and inherited formatting</returns>
        DeletedRun CreateDeletedRunElement(string text, string revisionId, string author, string date, Run originalRun);

        /// <summary>
        /// Create an Inserted element for block-level insertions
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>An Inserted element with the specified properties</returns>
        Inserted CreateInsertedElement(string revisionId, string author, string date);

        /// <summary>
        /// Create a Deleted element for block-level deletions
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A Deleted element with the specified properties</returns>
        Deleted CreateDeletedElement(string revisionId, string author, string date);

        #endregion

        #region Move Operation Elements

        /// <summary>
        /// Create a MoveFromRun element for move source content
        /// </summary>
        /// <param name="text">The text content being moved from this location</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveFromRun element with the specified properties</returns>
        MoveFromRun CreateMoveFromRunElement(string text, string revisionId, string author, string date);

        /// <summary>
        /// Create a MoveToRun element for move destination content
        /// </summary>
        /// <param name="text">The text content being moved to this location</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveToRun element with the specified properties</returns>
        MoveToRun CreateMoveToRunElement(string text, string revisionId, string author, string date);

        /// <summary>
        /// Create a MoveFromRangeStart element to mark the beginning of a move source range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <param name="moveName">Name identifying the move operation</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveFromRangeStart element</returns>
        MoveFromRangeStart CreateMoveFromRangeStart(string rangeId, string moveName, string author, string date);

        /// <summary>
        /// Create a MoveFromRangeEnd element to mark the end of a move source range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <returns>A MoveFromRangeEnd element</returns>
        MoveFromRangeEnd CreateMoveFromRangeEnd(string rangeId);

        /// <summary>
        /// Create a MoveToRangeStart element to mark the beginning of a move destination range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <param name="moveName">Name identifying the move operation</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A MoveToRangeStart element</returns>
        MoveToRangeStart CreateMoveToRangeStart(string rangeId, string moveName, string author, string date);

        /// <summary>
        /// Create a MoveToRangeEnd element to mark the end of a move destination range
        /// </summary>
        /// <param name="rangeId">Unique ID for the move range</param>
        /// <returns>A MoveToRangeEnd element</returns>
        MoveToRangeEnd CreateMoveToRangeEnd(string rangeId);

        /// <summary>
        /// Create a move from operation wrapped with range markers
        /// This creates the full structure: MoveFromRangeStart → MoveFromRun → MoveFromRangeEnd
        /// </summary>
        /// <param name="text">The text content being moved from this location</param>
        /// <param name="moveFromRangeId">Unique ID for the move from range</param>
        /// <param name="moveFromRevisionId">Unique revision ID for the move from element</param>
        /// <param name="moveName">Name identifying the move operation (shared with move to)</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A list containing MoveFromRangeStart, MoveFromRun, and MoveFromRangeEnd elements</returns>
        List<OpenXmlElement> CreateMoveFromWithRanges(string text, string moveFromRangeId, string moveFromRevisionId, string moveName, string author, string date);

        /// <summary>
        /// Create a move to operation wrapped with range markers
        /// This creates the full structure: MoveToRangeStart → MoveToRun → MoveToRangeEnd
        /// </summary>
        /// <param name="text">The text content being moved to this location</param>
        /// <param name="moveToRangeId">Unique ID for the move to range</param>
        /// <param name="moveToRevisionId">Unique revision ID for the move to element</param>
        /// <param name="moveName">Name identifying the move operation (shared with move from)</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A list containing MoveToRangeStart, MoveToRun, and MoveToRangeEnd elements</returns>
        List<OpenXmlElement> CreateMoveToWithRanges(string text, string moveToRangeId, string moveToRevisionId, string moveName, string author, string date);

        /// <summary>
        /// Create a complete move operation (both from and to) with proper linking
        /// This creates both source and destination with shared move name for proper linking
        /// </summary>
        /// <param name="text">The text content being moved</param>
        /// <param name="moveFromRangeId">Unique ID for the move from range</param>
        /// <param name="moveFromRevisionId">Unique revision ID for the move from element</param>
        /// <param name="moveToRangeId">Unique ID for the move to range</param>
        /// <param name="moveToRevisionId">Unique revision ID for the move to element</param>
        /// <param name="moveName">Name identifying the move operation (links from and to)</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A tuple containing move from elements and move to elements</returns>
        (List<OpenXmlElement> moveFromElements, List<OpenXmlElement> moveToElements) CreateLinkedMoveOperationWithRanges(
            string text,
            string moveFromRangeId,
            string moveFromRevisionId,
            string moveToRangeId,
            string moveToRevisionId,
            string moveName,
            string author,
            string date);

        #endregion

        #region Properties Change Elements

        /// <summary>
        /// Create a RunPropertiesChange element for tracking run property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous run properties before the change</param>
        /// <returns>A RunPropertiesChange element</returns>
        RunPropertiesChange CreateRunPropertiesChange(string revisionId, string author, string date, RunProperties? previousProperties = null);

        /// <summary>
        /// Create a ParagraphPropertiesChange element for tracking paragraph property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous paragraph properties before the change</param>
        /// <returns>A ParagraphPropertiesChange element</returns>
        ParagraphPropertiesChange CreateParagraphPropertiesChange(string revisionId, string author, string date, ParagraphProperties? previousProperties = null);

        /// <summary>
        /// Create a SectionPropertiesChange element for tracking section property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous section properties before the change</param>
        /// <returns>A SectionPropertiesChange element</returns>
        SectionPropertiesChange CreateSectionPropertiesChange(string revisionId, string author, string date, SectionProperties? previousProperties = null);

        #endregion

        #region Table Revision Elements

        /// <summary>
        /// Create a TablePropertiesChange element for tracking table property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous table properties before the change</param>
        /// <returns>A TablePropertiesChange element</returns>
        TablePropertiesChange CreateTablePropertiesChange(string revisionId, string author, string date, TableProperties? previousProperties = null);

        /// <summary>
        /// Create a TableRowPropertiesChange element for tracking table row property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous table row properties before the change</param>
        /// <returns>A TableRowPropertiesChange element</returns>
        TableRowPropertiesChange CreateTableRowPropertiesChange(string revisionId, string author, string date, TableRowProperties? previousProperties = null);

        /// <summary>
        /// Create a TableCellPropertiesChange element for tracking table cell property changes
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="previousProperties">The previous table cell properties before the change</param>
        /// <returns>A TableCellPropertiesChange element</returns>
        TableCellPropertiesChange CreateTableCellPropertiesChange(string revisionId, string author, string date, TableCellProperties? previousProperties = null);

        /// <summary>
        /// Create a table cell insertion element
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CellInsertion element</returns>
        CellInsertion CreateCellInsertion(string revisionId, string author, string date);

        /// <summary>
        /// Create a table cell deletion element
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CellDeletion element</returns>
        CellDeletion CreateCellDeletion(string revisionId, string author, string date);

        /// <summary>
        /// Create a table cell merge element
        /// </summary>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <param name="vMerge">Vertical merge type (restart/continue)</param>
        /// <returns>A CellMerge element</returns>
        CellMerge CreateCellMerge(string revisionId, string author, string date, MergedCellValues vMerge);

        #endregion

        #region Custom XML and Math Elements

        /// <summary>
        /// Create a CustomXmlInsRangeStart element for custom XML insertion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CustomXmlInsRangeStart element</returns>
        CustomXmlInsRangeStart CreateCustomXmlInsRangeStart(string rangeId, string author, string date);

        /// <summary>
        /// Create a CustomXmlInsRangeEnd element for custom XML insertion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <returns>A CustomXmlInsRangeEnd element</returns>
        CustomXmlInsRangeEnd CreateCustomXmlInsRangeEnd(string rangeId);

        /// <summary>
        /// Create a CustomXmlDelRangeStart element for custom XML deletion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A CustomXmlDelRangeStart element</returns>
        CustomXmlDelRangeStart CreateCustomXmlDelRangeStart(string rangeId, string author, string date);

        /// <summary>
        /// Create a CustomXmlDelRangeEnd element for custom XML deletion tracking
        /// </summary>
        /// <param name="rangeId">Unique ID for the custom XML range</param>
        /// <returns>A CustomXmlDelRangeEnd element</returns>
        CustomXmlDelRangeEnd CreateCustomXmlDelRangeEnd(string rangeId);

        #endregion

        #region Comment Range Elements

        /// <summary>
        /// Create a CommentRangeStart element for marking the beginning of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range start refers to</param>
        /// <returns>A CommentRangeStart element</returns>
        CommentRangeStart CreateCommentRangeStart(StringValue commentId);

        /// <summary>
        /// Create a CommentRangeEnd element for marking the end of a comment range
        /// </summary>
        /// <param name="commentId">The ID of the comment this range end refers to</param>
        /// <returns>A CommentRangeEnd element</returns>
        CommentRangeEnd CreateCommentRangeEnd(StringValue commentId);

        #endregion

        #region Utility Methods

        /// <summary>
        /// Create a revision element based on the revision operator type and properties
        /// This method handles all supported revision types including basic text operations,
        /// move operations, property changes, table operations, and more
        /// </summary>
        /// <param name="revisionOperator">The revision operator containing operation type, scope, and metadata</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <returns>An OpenXmlElement representing the revision, or null if the operation type is not supported</returns>
        /// <example>
        /// <code>
        /// // Basic text operations
        /// var insertOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.ins, 
        ///     Text = "New content", 
        ///     Author = "John Doe",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Run
        /// };
        /// var insertElement = factory.CreateRevisionElement(insertOp, "rev001"); // Returns InsertedRun
        /// 
        /// // Move operations
        /// var moveFromOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.moveFrom, 
        ///     Text = "Moved content", 
        ///     Author = "Editor",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Run,
        ///     Properties = new RevisionProperties 
        ///     { 
        ///         MoveId = "move_001", 
        ///         MoveName = "content_relocation" 
        ///     }
        /// };
        /// var moveElement = factory.CreateRevisionElement(moveFromOp, "rev002"); // Returns MoveFromRun
        /// 
        /// // Property changes
        /// var propChangeOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.rPrChange, 
        ///     Author = "Formatter",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Run,
        ///     IsFormattingOnly = true,
        ///     Properties = new RevisionProperties 
        ///     { 
        ///         PreviousPropertiesXml = "&lt;w:rPr&gt;&lt;w:b/&gt;&lt;/w:rPr&gt;" 
        ///     }
        /// };
        /// var propElement = factory.CreateRevisionElement(propChangeOp, "rev003"); // Returns RunPropertiesChange
        /// 
        /// // Table operations
        /// var cellDelOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.cellDel, 
        ///     Author = "Table Editor",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Cell,
        ///     Properties = new RevisionProperties 
        ///     { 
        ///         GridSpan = 2,
        ///         RowIndex = 1,
        ///         CellIndex = 2
        ///     }
        /// };
        /// var cellElement = factory.CreateRevisionElement(cellDelOp, "rev004"); // Returns CellDeletion
        /// 
        /// // Block-level operations
        /// var blockInsOp = new RevisionOperator 
        /// { 
        ///     Op = RevisionOperatorType.blockIns, 
        ///     Author = "Content Creator",
        ///     Date = "2024-01-15",
        ///     Scope = RevisionScope.Paragraph,
        ///     XmlContent = "&lt;w:p&gt;&lt;w:r&gt;&lt;w:t&gt;New paragraph&lt;/w:t&gt;&lt;/w:r&gt;&lt;/w:p&gt;"
        /// };
        /// var blockElement = factory.CreateRevisionElement(blockInsOp, "rev005"); // Returns Inserted
        /// </code>
        /// </example>
        OpenXmlElement? CreateRevisionElement(RevisionOperator revisionOperator, string revisionId);

        /// <summary>
        /// Create a collection of revision elements and comments based on a list of revision operators
        /// This method handles the creation of complete revision structures including comments
        /// </summary>
        /// <param name="revisionOperators">List of revision operators to process</param>
        /// <param name="editType">The type of edit being performed</param>
        /// <param name="startingRevisionId">The starting revision ID for generating unique IDs</param>
        /// <param name="startingCommentId">The starting comment ID for generating unique comment IDs</param>
        /// <returns>A tuple containing the list of revision elements and associated comments</returns>
        (List<OpenXmlElement> revisionElements, List<Comment> comments) CreateRevisionElements(
            List<RevisionOperator> revisionOperators,
            EditType editType,
            int startingRevisionId,
            int startingCommentId);

        /// <summary>
        /// Create a revision tracking element for paragraph marks that are deleted or moved
        /// </summary>
        /// <param name="revisionType">Type of revision (delete/move)</param>
        /// <param name="revisionId">Unique revision ID for tracking</param>
        /// <param name="author">Author who made the revision</param>
        /// <param name="date">Date of the revision</param>
        /// <returns>A revision element for paragraph mark tracking</returns>
        OpenXmlElement CreateParagraphMarkRevision(RevisionOperatorType revisionType, string revisionId, string author, string date);

        #endregion
    }
}
