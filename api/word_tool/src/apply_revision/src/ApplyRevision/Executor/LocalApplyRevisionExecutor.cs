using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using Common.Model;
using Newtonsoft.Json;

namespace ApplyRevision.Executor;

/// <summary>
/// Local executor for ApplyRevision that works with local files instead of S3.
/// This is designed for local development and testing scenarios.
/// </summary>
public class LocalApplyRevisionExecutor
{
    private readonly ILambdaContext _context;

    public LocalApplyRevisionExecutor(ILambdaContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Executes the revision application process using local files.
    /// </summary>
    /// <param name="documentPath">Path to the local Word document</param>
    /// <param name="revisionPath">Path to the local revision JSON file</param>
    /// <param name="outputPath">Path where the modified document should be saved</param>
    /// <returns>Result indicating success or failure</returns>
    public async Task<LocalExecutionResult> ExecuteAsync(string documentPath, string revisionPath, string outputPath)
    {
        try
        {
            _context.Logger.LogInformation("Starting local ApplyRevision execution...");

            // Resolve relative paths to absolute paths
            var absoluteDocumentPath = Path.GetFullPath(documentPath);
            var absoluteRevisionPath = Path.GetFullPath(revisionPath);
            var absoluteOutputPath = Path.GetFullPath(outputPath);

            _context.Logger.LogInformation($"Resolved document path: {absoluteDocumentPath}");
            _context.Logger.LogInformation($"Resolved revision path: {absoluteRevisionPath}");
            _context.Logger.LogInformation($"Resolved output path: {absoluteOutputPath}");

            // Validate input files
            if (!File.Exists(absoluteDocumentPath))
            {
                var error = $"Document file not found: {absoluteDocumentPath}";
                _context.Logger.LogError(error);
                return LocalExecutionResult.Error(error);
            }

            if (!File.Exists(absoluteRevisionPath))
            {
                var error = $"Revision file not found: {absoluteRevisionPath}";
                _context.Logger.LogError(error);
                return LocalExecutionResult.Error(error);
            }

            // Read and parse revision file
            _context.Logger.LogInformation($"Reading revision file: {absoluteRevisionPath}");
            WordTextPatch? patch = null;
            try
            {
                var revisionJson = await File.ReadAllTextAsync(absoluteRevisionPath);
                patch = JsonConvert.DeserializeObject<WordTextPatch>(revisionJson);
            }
            catch (Exception ex)
            {
                var error = $"Failed to parse revision file: {ex.Message}";
                _context.Logger.LogError(error);
                return LocalExecutionResult.Error(error);
            }

            if (patch?.Operations == null || !patch.Operations.Any())
            {
                _context.Logger.LogWarning("No operations found in revision file. Copying original document.");
                File.Copy(absoluteDocumentPath, absoluteOutputPath, true);
                return LocalExecutionResult.Success("No operations to apply - original document copied");
            }

            _context.Logger.LogInformation($"Found {patch.Operations.Count} operations to apply");

            // Create a temporary working copy
            var tempFilePath = Path.GetTempFileName();
            try
            {
                File.Copy(absoluteDocumentPath, tempFilePath, true);
                _context.Logger.LogInformation($"Created temporary working copy: {tempFilePath}");

                // Apply the patch
                var applier = new WordTextPatchApplier(_context);
                var success = applier.ApplyPatch(tempFilePath, patch);

                if (success)
                {
                    // Ensure output directory exists
                    var outputDir = Path.GetDirectoryName(absoluteOutputPath);
                    if (!string.IsNullOrEmpty(outputDir) && !Directory.Exists(outputDir))
                    {
                        Directory.CreateDirectory(outputDir);
                    }

                    // Copy result to final output location
                    File.Copy(tempFilePath, absoluteOutputPath, true);
                    _context.Logger.LogInformation($"Successfully applied patch and saved to: {absoluteOutputPath}");

                    return LocalExecutionResult.Success($"Patch applied successfully. Output saved to: {absoluteOutputPath}");
                }
                else
                {
                    var error = "Failed to apply patch to document";
                    _context.Logger.LogError(error);
                    return LocalExecutionResult.Error(error);
                }
            }
            finally
            {
                // Clean up temporary file
                if (File.Exists(tempFilePath))
                {
                    File.Delete(tempFilePath);
                    _context.Logger.LogInformation("Cleaned up temporary file");
                }
            }
        }
        catch (Exception ex)
        {
            var error = $"Unexpected error during execution: {ex.Message}";
            _context.Logger.LogError($"{error}\nStack trace: {ex.StackTrace}");
            return LocalExecutionResult.Error(error);
        }
    }

    /// <summary>
    /// Validates that the required files exist and are accessible.
    /// </summary>
    /// <param name="documentPath">Path to the Word document</param>
    /// <param name="revisionPath">Path to the revision JSON file</param>
    /// <returns>Validation result</returns>
    public LocalExecutionResult ValidateInputs(string documentPath, string revisionPath)
    {
        var errors = new List<string>();

        // Resolve relative paths to absolute paths for validation
        var absoluteDocumentPath = string.IsNullOrWhiteSpace(documentPath) ? documentPath : Path.GetFullPath(documentPath);
        var absoluteRevisionPath = string.IsNullOrWhiteSpace(revisionPath) ? revisionPath : Path.GetFullPath(revisionPath);

        if (string.IsNullOrWhiteSpace(documentPath))
        {
            errors.Add("Document path is required");
        }
        else if (!File.Exists(absoluteDocumentPath))
        {
            errors.Add($"Document file not found: {absoluteDocumentPath}");
        }
        else if (!documentPath.EndsWith(".docx", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("Document must be a .docx file");
        }

        if (string.IsNullOrWhiteSpace(revisionPath))
        {
            errors.Add("Revision path is required");
        }
        else if (!File.Exists(absoluteRevisionPath))
        {
            errors.Add($"Revision file not found: {absoluteRevisionPath}");
        }
        else if (!revisionPath.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("Revision file must be a .json file");
        }

        if (errors.Any())
        {
            return LocalExecutionResult.Error(string.Join("; ", errors));
        }

        return LocalExecutionResult.Success("Input validation passed");
    }
}

/// <summary>
/// Result object for local execution operations.
/// </summary>
public class LocalExecutionResult
{
    public bool IsSuccess { get; private set; }
    public string Message { get; private set; }
    public Exception? Exception { get; private set; }

    private LocalExecutionResult(bool isSuccess, string message, Exception? exception = null)
    {
        IsSuccess = isSuccess;
        Message = message;
        Exception = exception;
    }

    public static LocalExecutionResult Success(string message)
    {
        return new LocalExecutionResult(true, message);
    }

    public static LocalExecutionResult Error(string message, Exception? exception = null)
    {
        return new LocalExecutionResult(false, message, exception);
    }

    public override string ToString()
    {
        return $"{(IsSuccess ? "SUCCESS" : "ERROR")}: {Message}";
    }
}
