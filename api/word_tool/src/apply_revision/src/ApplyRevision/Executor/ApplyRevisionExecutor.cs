﻿using Amazon.Lambda.Core;
using Amazon.S3.Model;
using Newtonsoft.Json;
using ApplyRevision.Abstract;
using ApplyRevision.Model;
using ApplyRevision.Helper;
using Common.Model;
using Common.Abstract;
using Common.Helper;
using Common.Service;


namespace ApplyRevision.Executor
{
    public class ApplyRevisionExecutor : Common.Abstract.Executor
    {
        private ApplyRevisionEvent applyRevisionEvent;

        public ApplyRevisionExecutor(ApplyRevisionEvent _applyRevisionEvent, ILambdaContext _context)
        {
            applyRevisionEvent = _applyRevisionEvent;
            context = _context;
            s3Service = new S3Service(settings.Region, context);
        }

        public ApplyRevisionExecutor(IS3Service _s3Service, ApplyRevisionEvent _applyRevisionEvent, ILambdaContext _context)
        {
            applyRevisionEvent = _applyRevisionEvent;
            context = _context;
            s3Service = _s3Service;
        }

        private string GetTargetFilename(string originName)
        {
            var originExt = Path.GetExtension(originName);
            var targetName = $"{originName.Replace(originExt, "").TrimEnd()}_{TimestampHelper.Timestamp()}";
            return targetName + originExt;
        }

        public async override Task<ResultResponse> Run()
        {
            var errorResponse = ResponseFactory<string>.error(
                data: "",
                message: "Event is empty."
            );

            if (applyRevisionEvent == null)
            {
                context.Logger.LogError("Apply revision event is empty.");
                return errorResponse;
            }

            if (applyRevisionEvent.Document == null || !IsParameterValid(applyRevisionEvent.Document))
            {
                errorResponse.Message = "The document bucket or key is empty.";
                return errorResponse;
            }

            if (applyRevisionEvent.Revision == null || !IsParameterValid(applyRevisionEvent.Revision))
            {
                errorResponse.Message = "The revision bucket or key is empty.";
                return errorResponse;
            }

#pragma warning disable CS8604
            Task<GetObjectResponse?> documentTask = s3Service.GetObjectAsync(

                applyRevisionEvent.Document.Bucket,
                applyRevisionEvent.Document.Key
            );
            Task<GetObjectResponse?> revisionTask = s3Service.GetObjectAsync(
                applyRevisionEvent.Revision.Bucket,
                applyRevisionEvent.Revision.Key
            );

            await Task.WhenAll(documentTask, revisionTask);

            var documentRes = documentTask.Result;
            var revisionRes = revisionTask.Result;
            if (documentRes == null || revisionRes == null)
            {
                errorResponse.Message = "Request Document or Revision failed.";
                return errorResponse;
            }

            if (documentRes.ContentLength == 0)
            {
                errorResponse.Message = "The document is empty.";
                return errorResponse;
            }

            WordTextPatch? patch = null;
            try
            {
                var revisionStr = await StreamHelper.ReadStreamToStringAsync(revisionRes.ResponseStream);
                patch = JsonConvert.DeserializeObject<WordTextPatch>(revisionStr);
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Parse revision patch error occurs, ex: {ex.ToString()}");
            }

            if (patch == null || patch.Operations == null || patch.Operations.Count == 0)
            {
                context.Logger.LogWarning("The revision patch is empty or contains no operations.");
                // Return the original document
                return ResponseFactory<S3Request>.success(applyRevisionEvent.Document);
            }

            var tempFilePath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.temp.docx");
            using (FileStream fileStream = new FileStream(tempFilePath, FileMode.Create))
            {
                await documentRes.ResponseStream.CopyToAsync(fileStream);
            }

            try
            {
                IWordTextPatchApplier applier = new WordTextPatchApplier(context);
                bool applyStatus = applier.ApplyPatch(tempFilePath, patch);
                if (applyStatus)
                {
                    var targetKey = GetTargetFilename(applyRevisionEvent.Document.Key);
                    var status = await s3Service.PutObjectAsync(applyRevisionEvent.Document.Bucket, targetKey, tempFilePath);
                    if (status)
                    {
                        var data = new S3Request
                        {
                            Bucket = applyRevisionEvent.Document.Bucket,
                            Key = targetKey
                        };
                        return ResponseFactory<S3Request>.success(data);
                    }
                }
            }
            catch (Exception ex)
            {
                context.Logger.LogError($"Apply revision changes error occurs, ex: {ex.ToString()}");
            }

            if (File.Exists(tempFilePath))
            {
                File.Delete(tempFilePath);
            }

            errorResponse.Message = "Apply revision changes failed.";
            return errorResponse;
        }
    }
}
