using System.CommandLine;
using Amazon.Lambda.Core;

namespace ApplyRevision;

/// <summary>
/// Local development entry point for ApplyRevision functionality.
/// This allows running the application locally without AWS Lambda environment.
/// </summary>
public class Program
{
    public static async Task<int> Main(string[] args)
    {
        // Create command line interface for local development
        var documentOption = new Option<string>(
            name: "--document",
            description: "Path to the Word document file (.docx)")
        {
            IsRequired = true
        };

        var revisionOption = new Option<string>(
            name: "--revision",
            description: "Path to the revision patch file (.json)")
        {
            IsRequired = true
        };

        var outputOption = new Option<string>(
            name: "--output",
            description: "Output path for the modified document (optional, defaults to adding '_modified' suffix)")
        {
            IsRequired = false
        };

        var rootCommand = new RootCommand("ApplyRevision - Local Development Tool")
        {
            documentOption,
            revisionOption,
            outputOption
        };

        rootCommand.SetHandler(async (documentPath, revisionPath, outputPath) =>
        {
            await RunLocalAsync(documentPath, revisionPath, outputPath);
        }, documentOption, revisionOption, outputOption);

        return await rootCommand.InvokeAsync(args);
    }

    /// <summary>
    /// Runs the ApplyRevision functionality locally using file paths instead of S3.
    /// </summary>
    /// <param name="documentPath">Local path to the Word document</param>
    /// <param name="revisionPath">Local path to the revision JSON file</param>
    /// <param name="outputPath">Local path for the output document</param>
    private static async Task RunLocalAsync(string documentPath, string revisionPath, string? outputPath)
    {
        // Create mock Lambda context for local development
        var mockContext = new LocalLambdaContext();
        var executor = new Executor.LocalApplyRevisionExecutor(mockContext);

        // Determine output path - default to current directory
        if (string.IsNullOrEmpty(outputPath))
        {
            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(documentPath);
            var extension = Path.GetExtension(documentPath);
            // Output to current working directory instead of document's directory
            outputPath = Path.Combine(Directory.GetCurrentDirectory(), $"{fileNameWithoutExtension}_modified{extension}");
        }

        Console.WriteLine("🚀 Starting ApplyRevision local execution...");
        Console.WriteLine($"📄 Document: {documentPath}");
        Console.WriteLine($"📝 Revision: {revisionPath}");
        Console.WriteLine($"💾 Output: {outputPath}");

        // Validate inputs
        var validationResult = executor.ValidateInputs(documentPath, revisionPath);
        if (!validationResult.IsSuccess)
        {
            Console.WriteLine($"❌ Validation Error: {validationResult.Message}");
            return;
        }

        // Execute the revision application
        var result = await executor.ExecuteAsync(documentPath, revisionPath, outputPath);

        if (result.IsSuccess)
        {
            Console.WriteLine($"✅ {result.Message}");
        }
        else
        {
            Console.WriteLine($"❌ {result.Message}");
            if (result.Exception != null)
            {
                Console.WriteLine($"Exception: {result.Exception}");
            }
        }
    }
}

/// <summary>
/// Mock implementation of ILambdaContext for local development.
/// Provides console-based logging instead of CloudWatch.
/// </summary>
public class LocalLambdaContext : ILambdaContext
{
    public string AwsRequestId => Guid.NewGuid().ToString();
    public IClientContext ClientContext => null!;
    public string FunctionName => "ApplyRevision-Local";
    public string FunctionVersion => "1.0.0";
    public ICognitoIdentity Identity => null!;
    public string InvokedFunctionArn => "arn:aws:lambda:local:000000000000:function:ApplyRevision-Local";
    public ILambdaLogger Logger { get; }
    public string LogGroupName => "/aws/lambda/ApplyRevision-Local";
    public string LogStreamName => DateTime.Now.ToString("yyyy/MM/dd/HHmmss");
    public int MemoryLimitInMB => 512;
    public TimeSpan RemainingTime => TimeSpan.FromMinutes(5);

    public LocalLambdaContext()
    {
        Logger = new LocalLambdaLogger();
    }
}

/// <summary>
/// Mock implementation of ILambdaLogger for local development.
/// Outputs log messages to console with appropriate formatting.
/// </summary>
public class LocalLambdaLogger : ILambdaLogger
{
    private string GetCallerInfo()
    {
        var frame = new System.Diagnostics.StackFrame(2, true);
        var fileName = System.IO.Path.GetFileName(frame.GetFileName()) ?? "Unknown";
        var lineNumber = frame.GetFileLineNumber();
        var methodName = frame.GetMethod()?.Name ?? "Unknown";
        return $"{fileName}:{lineNumber} [{methodName}]";
    }

    public void Log(string message)
    {
        Console.WriteLine($"[LOG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
    }

    public void LogLine(string message)
    {
        Console.WriteLine($"[LOG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
    }

    public void LogInformation(string message)
    {
        Console.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
    }

    public void LogWarning(string message)
    {
        Console.WriteLine($"[WARN] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
    }

    public void LogError(string message)
    {
        Console.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
    }

    public void LogCritical(string message)
    {
        Console.WriteLine($"[CRITICAL] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {GetCallerInfo()} {message}");
    }
}
