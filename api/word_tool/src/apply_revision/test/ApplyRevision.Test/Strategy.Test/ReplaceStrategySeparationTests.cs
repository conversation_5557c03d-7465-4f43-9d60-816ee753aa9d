using System;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Component;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Strategy.Test
{
    /// <summary>
    /// Tests for the revision parent separation functionality in ReplaceStrategy
    /// 测试 ReplaceStrategy 中修订父元素分离功能
    /// </summary>
    public class ReplaceStrategySeparationTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly ReplaceStrategy _strategy;
        private readonly string _tempFilePath;

        public ReplaceStrategySeparationTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();

            // Setup mock logger
            var mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            _strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            _tempFilePath = Path.GetTempFileName();
        }

        [Fact]
        public void SeparateMultipleRunsInRevisionParent_WithElementsAfterTargetRun_ShouldSeparateCorrectly()
        {
            // Arrange
            CreateDocumentWithComplexInsertedRun(_tempFilePath);

            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().First();
            var insertedRun = paragraph.Elements<InsertedRun>().First();

            // Get the second run (target run) which should be "Bill"
            var allRuns = insertedRun.Elements<Run>().ToList();
            var targetRun = allRuns[1]; // Second run containing "Bill"

            // Verify initial structure: run1, commentRangeStart, run2(target), commentRangeEnd, run3
            Assert.Equal(3, allRuns.Count);
            Assert.Equal(5, insertedRun.Elements().Count()); // 3 runs + commentRangeStart + commentRangeEnd

            // Act - Use reflection to call the private method
            var method = typeof(ReplaceStrategy).GetMethod("SeparateMultipleRunsInRevisionParent",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(_strategy, new object[] { insertedRun, targetRun });

            // Assert
            var allInsertedRuns = paragraph.Elements<InsertedRun>().ToList();
            Assert.Equal(2, allInsertedRuns.Count); // Should now have 2 separate InsertedRun elements

            // First InsertedRun should contain elements up to and including the target run
            var firstInsertedRun = allInsertedRuns[0];
            var firstRunElements = firstInsertedRun.Elements<Run>().ToList();
            Assert.Equal(2, firstRunElements.Count); // run1 and target run (run2)
            Assert.Equal(" (the", firstRunElements[0].InnerText);
            Assert.Equal("Bill", firstRunElements[1].InnerText);

            // Should contain commentRangeStart but not commentRangeEnd
            Assert.True(firstInsertedRun.Elements<CommentRangeStart>().Any());
            Assert.False(firstInsertedRun.Elements<CommentRangeEnd>().Any());

            // Second InsertedRun should contain elements after the target run
            var secondInsertedRun = allInsertedRuns[1];
            var secondRunElements = secondInsertedRun.Elements<Run>().ToList();
            Assert.Single(secondRunElements); // Only run3
            Assert.Equal(" more text", secondRunElements[0].InnerText);

            // Should contain commentRangeEnd but not commentRangeStart
            Assert.False(secondInsertedRun.Elements<CommentRangeStart>().Any());
            Assert.True(secondInsertedRun.Elements<CommentRangeEnd>().Any());
        }

        [Fact]
        public void SeparateMultipleRunsInRevisionParent_WithSingleRun_ShouldNotSeparate()
        {
            // Arrange
            CreateDocumentWithSingleRunInsertedRun(_tempFilePath);

            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().First();
            var insertedRun = paragraph.Elements<InsertedRun>().First();
            var targetRun = insertedRun.Elements<Run>().First();

            // Verify initial structure
            Assert.Single(insertedRun.Elements<Run>());

            // Act
            var method = typeof(ReplaceStrategy).GetMethod("SeparateMultipleRunsInRevisionParent",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(_strategy, new object[] { insertedRun, targetRun });

            // Assert - Should remain unchanged
            var allInsertedRuns = paragraph.Elements<InsertedRun>().ToList();
            Assert.Single(allInsertedRuns); // Should still have only 1 InsertedRun element
            Assert.Single(allInsertedRuns[0].Elements<Run>()); // Should still have only 1 Run
        }

        private void CreateDocumentWithComplexInsertedRun(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var insertedRun = new InsertedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            // Add first run
            var run1 = new Run(new Text(" (the") { Space = SpaceProcessingModeValues.Preserve });
            insertedRun.AppendChild(run1);

            // Add comment range start
            var commentRangeStart = new CommentRangeStart() { Id = "40" };
            insertedRun.AppendChild(commentRangeStart);

            // Add second run (target run)
            var run2 = new Run(new Text("Bill"));
            insertedRun.AppendChild(run2);

            // Add comment range end
            var commentRangeEnd = new CommentRangeEnd() { Id = "40" };
            insertedRun.AppendChild(commentRangeEnd);

            // Add third run (after target run)
            var run3 = new Run(new Text(" more text") { Space = SpaceProcessingModeValues.Preserve });
            insertedRun.AppendChild(run3);

            paragraph.AppendChild(insertedRun);
            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateDocumentWithSingleRunInsertedRun(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var insertedRun = new InsertedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            var run = new Run(new Text("Single Run Text"));
            insertedRun.AppendChild(run);

            paragraph.AppendChild(insertedRun);
            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        public void Dispose()
        {
            try
            {
                if (File.Exists(_tempFilePath))
                {
                    File.Delete(_tempFilePath);
                }
            }
            catch
            {
                // Ignore cleanup exceptions
            }
        }
    }
}
