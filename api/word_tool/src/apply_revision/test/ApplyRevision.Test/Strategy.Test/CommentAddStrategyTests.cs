using System;
using System.IO;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Strategy.Test
{
    /// <summary>
    /// Unit tests for CommentAddStrategy class
    /// CommentAddStrategy类的单元测试
    /// </summary>
    public class CommentAddStrategyTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;

        public CommentAddStrategyTests()
        {
            // Setup mock objects / 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
        }

        #region Constructor Tests / 构造函数测试

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var strategy = new CommentAddStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert - If constructor doesn't properly initialize dependencies, it will throw an exception
            // 断言 - 如果构造函数没有正确初始化依赖项，会抛出异常
            Assert.NotNull(strategy);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new CommentAddStrategy(
                null,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("context", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new CommentAddStrategy(
                _mockContext.Object,
                null,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("elementFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullRevisionFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new CommentAddStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                null,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("revisionFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new CommentAddStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                null,
                _mockCommentManager.Object
            ));

            Assert.Equal("idManager", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullCommentManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new CommentAddStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                null
            ));

            Assert.Equal("commentManager", exception.ParamName);
        }

        #endregion

        #region Interface Implementation Tests / 接口实现测试

        [Fact]
        public void CommentAddStrategy_ShouldImplementIOperationStrategy()
        {
            // Arrange & Act
            var strategy = new CommentAddStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert
            Assert.IsAssignableFrom<IOperationStrategy>(strategy);
        }

        [Fact]
        public void CommentAddStrategy_ShouldInheritFromBaseOperationStrategy()
        {
            // Arrange & Act
            var strategy = new CommentAddStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert
            Assert.IsAssignableFrom<BaseOperationStrategy>(strategy);
        }

        #endregion
    }
}
