using System;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using Common.Tests.Attributes;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Strategy.Test
{
    public class DeleteStrategyTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly string _tempFilePath;

        public DeleteStrategyTests()
        {
            // 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _mockIdManager.Setup(m => m.GetNextRevisionId()).Returns(123);

            // 设置临时文件路径
            _tempFilePath = Path.GetTempFileName();
        }

        #region 构造函数测试

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert - 如果构造函数没有正确初始化依赖项，会抛出异常
            Assert.NotNull(strategy);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new DeleteStrategy(
                null,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("context", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new DeleteStrategy(
                _mockContext.Object,
                null,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("elementFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullRevisionFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                null,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("revisionFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                null,
                _mockCommentManager.Object
            ));

            Assert.Equal("idManager", exception.ParamName);
        }

        #endregion

        #region Execute方法测试

        [Fact]
        public void Execute_WithNullRun_ShouldLogErrorAndReturn()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("nonexistent-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            _mockLogger.Verify(
                l => l.LogError(It.Is<string>(s => s.Contains("Cannot find Run for SegId"))),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void Execute_WithNullText_ShouldLogErrorAndReturn()
        {
            try
            {
                // Arrange
                _mockLogger.Setup(l => l.Log(It.IsAny<string>())).Callback<string>(s => Console.WriteLine($"Log: {s}"));

                // 创建测试文档
                CreateTestDocx(_tempFilePath);

                // 首先修改文档，然后关闭它
                using (var docToModify = WordprocessingDocument.Open(_tempFilePath, true))
                {
                    var run = docToModify.MainDocumentPart.Document.Body.Descendants<Run>().First();
                    run.RemoveAllChildren<Text>();
                    docToModify.Save();
                }

                // 确保文件句柄已释放
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // 创建策略
                var strategy = new DeleteStrategy(
                    _mockContext.Object,
                    _mockElementFactory.Object,
                    _mockRevisionFactory.Object,
                    _mockIdManager.Object,
                    _mockCommentManager.Object
                );

                var operation = new Operation
                {
                    Op = OperationType.Delete,
                    Target = new("12345-0"),
                    Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                    Text = "Test text",
                    Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                };

                // 然后再打开文档执行操作
                using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
                {
                    strategy.Execute(doc, operation);
                }

                // Assert
                _mockLogger.Verify(
                    l => l.LogError(It.Is<string>(s => s.Contains("Cannot locate text position 0 within Run for delete operation"))),
                    Times.Once);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                throw;
            }
            finally
            {
                // Cleanup
                try
                {
                    if (File.Exists(_tempFilePath))
                    {
                        File.Delete(_tempFilePath);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"删除临时文件时发生异常: {ex.Message}");
                }
            }
        }

        [Fact]
        public void Execute_WithRevision_ShouldMarkAsDeletedRevision()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 }, // 删除前5个字符
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟DeletedRun
            var deletedRun = new DeletedRun();
            deletedRun.AppendChild(new Run(new DeletedText("Origi")));

            _mockRevisionFactory.Setup(f => f.CreateDeletedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()
            )).Returns(deletedRun);

            // 设置模拟CloneElement
            _mockElementFactory.Setup(f => f.CloneElement(
                It.IsAny<Run>(),
                It.IsAny<bool>()
            )).Returns<Run, bool>((run, deep) =>
            {
                var newRun = new Run();
                newRun.AppendChild(new Text("nal text"));
                return newRun;
            });

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证文档中添加了DeletedRun
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
            Assert.Equal("Origi", deletedRuns[0].Descendants<DeletedText>().First().Text);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("Deletion across multiple Text elements without revision")]
        public void Execute_WithoutRevision_ShouldDeleteTextDirectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 }, // 删除前5个字符
                Text = "Test text",
                Revision = null
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证文本已被直接删除
            var text = doc.MainDocumentPart.Document.Body.Descendants<Text>().First().Text;
            Assert.Equal("nal text", text); // "Original text" -> "nal text"

            // Cleanup
            File.Delete(_tempFilePath);
        }

        #endregion

        #region CreateDeletedRun方法测试

        [UnsupportedFeatureFact("Deletion across multiple Text elements without revision")]
        public void Execute_WithRevisionFactory_ShouldUseFactory()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            var deletedRun = new DeletedRun();
            deletedRun.AppendChild(new Run(new DeletedText("Origi")));

            _mockRevisionFactory.Setup(f => f.CreateDeletedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()
            )).Returns(deletedRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            _mockRevisionFactory.Verify(f => f.CreateDeletedRunElement(
                "Origi",
                "123",
                "Test Author",
                It.IsAny<string>()
            ), Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("Deletion across multiple Text elements without revision")]
        public void Execute_WithoutRevisionFactory_ShouldCreateManually()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            // 设置模拟RevisionFactory返回null，模拟旧行为
            _mockRevisionFactory.Setup(f => f.CreateDeletedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()
            )).Returns((DeletedRun)null);

            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟CreateRunElement
            var deleteTextRun = new Run(new DeletedText("Origi"));
            _mockElementFactory.Setup(f => f.CreateRunElement(
                It.IsAny<string>(),
                It.Is<RevisionOperatorType>(t => t == RevisionOperatorType.del)
            )).Returns(deleteTextRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
            Assert.Equal("123", deletedRuns[0].Id);
            Assert.Equal("Test Author", deletedRuns[0].Author);
            Assert.Equal(operation.Revision.Date, deletedRuns[0].Date.Value);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        #endregion

        #region 辅助方法

        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Original text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        #endregion

        #region 跨Text元素删除测试

        [UnsupportedFeatureFact("Deletion across multiple Text elements without revision")]
        public void Execute_WithRevisionAcrossMultipleTextElements_ShouldHandleMultipleTextElements()
        {
            // Arrange
            using var doc = WordprocessingDocument.Create(_tempFilePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // 创建一个包含多个Text元素的Run
            var run = new Run();
            run.AppendChild(new Text("First"));
            run.AppendChild(new Text(" Second"));
            run.AppendChild(new Text(" Third"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
            doc.Dispose();

            // 创建策略
            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 3, End = 10 }, // 跨越多个Text元素
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟DeletedRun
            var deletedRun = new DeletedRun();
            deletedRun.AppendChild(new Run(new DeletedText("Text deletion across multiple elements")));

            _mockRevisionFactory.Setup(f => f.CreateDeletedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()
            )).Returns(deletedRun);

            // Act
            using var docToModify = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(docToModify, operation);

            // Assert
            _mockLogger.Verify(
                l => l.LogWarning(It.Is<string>(s => s.Contains("Deletion across multiple Text elements"))),
                Times.Once);

            var deletedRuns = docToModify.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
            Assert.Equal("Text deletion across multiple elements", deletedRuns[0].Descendants<DeletedText>().First().Text);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("Deletion across multiple Text elements without revision")]
        public void Execute_WithoutRevisionAcrossMultipleTextElements_ShouldHandleMultipleTextElements()
        {
            // Arrange
            using var doc = WordprocessingDocument.Create(_tempFilePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // 创建一个包含多个Text元素的Run
            var run = new Run();
            run.AppendChild(new Text("First"));
            run.AppendChild(new Text(" Second"));
            run.AppendChild(new Text(" Third"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
            doc.Dispose();

            // 创建策略
            var strategy = new DeleteStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Delete,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 3, End = 10 }, // 跨越多个Text元素
                Text = "Test text",
                Revision = null // 无修订信息
            };

            // Act
            using var docToModify = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(docToModify, operation);

            // Assert
            _mockLogger.Verify(
                l => l.LogWarning(It.Is<string>(s => s.Contains("Deletion across multiple Text elements"))),
                Times.Once);

            var firstText = docToModify.MainDocumentPart.Document.Body.Descendants<Text>().First().Text;
            Assert.Equal("Fir", firstText); // "First" -> "Fir"

            // Cleanup
            File.Delete(_tempFilePath);
        }

        #endregion
    }
}