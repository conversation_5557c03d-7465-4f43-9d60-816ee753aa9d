using System;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Strategy.Test
{
    /// <summary>
    /// Unit tests for QuotationSeparateStrategy class.
    /// Tests the quotation separation functionality including edge cases and error scenarios.
    /// </summary>
    public class QuotationSeparateStrategyTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly string _tempFilePath;

        public QuotationSeparateStrategyTests()
        {
            // Setup mock objects / 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _mockIdManager.Setup(m => m.GenerateParaId()).Returns("P12345");

            // Setup temporary file path / 设置临时文件路径
            _tempFilePath = Path.GetTempFileName();
        }

        #region Constructor Tests / 构造函数测试

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert - If constructor doesn't properly initialize dependencies, it will throw an exception
            // 断言 - 如果构造函数没有正确初始化依赖项，会抛出异常
            Assert.NotNull(strategy);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new QuotationSeparateStrategy(
                null,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("context", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new QuotationSeparateStrategy(
                _mockContext.Object,
                null,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("elementFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullRevisionFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                null,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("revisionFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                null,
                _mockCommentManager.Object
            ));

            Assert.Equal("idManager", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullCommentManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                null
            ));

            Assert.Equal("commentManager", exception.ParamName);
        }

        #endregion

        #region Interface Implementation Tests / 接口实现测试

        [Fact]
        public void QuotationSeparateStrategy_ShouldImplementIOperationStrategy()
        {
            // Arrange & Act
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert
            Assert.IsAssignableFrom<IOperationStrategy>(strategy);
        }

        #endregion

        #region Execute Method Tests / Execute方法测试

        [Fact]
        public void Execute_WithNullTarget_ShouldThrowArgumentException()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Id = "test-null-target",
                Op = OperationType.QuotationSeparate,
                Target = null
            };

            // Act & Assert
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var exception = Assert.Throws<ArgumentException>(() => strategy.Execute(doc, operation));
            Assert.Contains("Target segment cannot be null", exception.Message);
        }

        [Fact]
        public void Execute_WithEmptyTarget_ShouldThrowArgumentException()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Id = "test-empty-target",
                Op = OperationType.QuotationSeparate,
                Target = new Target()
            };

            // Act & Assert
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var exception = Assert.Throws<ArgumentException>(() => strategy.Execute(doc, operation));
            Assert.Contains("Target segment cannot be null", exception.Message);
        }

        [Fact]
        public void Execute_WithNullRange_ShouldThrowArgumentException()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Id = "test-null-range",
                Op = OperationType.QuotationSeparate,
                Target = new Target("12345-0") // No range specified
            };

            // Act & Assert
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var exception = Assert.Throws<ArgumentException>(() => strategy.Execute(doc, operation));
            Assert.Contains("Range must be specified", exception.Message);
        }

        [Fact]
        public void Execute_WithEmptySegId_ShouldThrowArgumentException()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Create a target with empty SegId - this will result in FirstOrDefault being null
            // because Target constructor filters out empty/null SegIds
            var operation = new Operation
            {
                Id = "test-empty-segid",
                Op = OperationType.QuotationSeparate,
                Target = new Target("", new ApplyRevision.Model.Range { Start = 5, End = 5 })
            };

            // Act & Assert
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var exception = Assert.Throws<ArgumentException>(() => strategy.Execute(doc, operation));
            // Since Target constructor filters out empty SegIds, FirstOrDefault will be null
            // So we expect the "Target segment cannot be null" message
            Assert.Contains("Target segment cannot be null", exception.Message);
        }

        [Fact]
        public void Execute_WithActualEmptySegId_ShouldThrowArgumentException()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Manually create a Target with a Segment that has empty SegId
            // This bypasses the Target constructor's filtering
            var target = new Target();
            target.Segments.Add(new Segment { SegId = "", Range = new ApplyRevision.Model.Range { Start = 5, End = 5 } });

            var operation = new Operation
            {
                Id = "test-actual-empty-segid",
                Op = OperationType.QuotationSeparate,
                Target = target
            };

            // Act & Assert
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var exception = Assert.Throws<ArgumentException>(() => strategy.Execute(doc, operation));
            Assert.Contains("SegId cannot be null or empty", exception.Message);
        }

        [Fact]
        public void Execute_WithNonExistentParagraph_ShouldReturnZero()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Id = "test-nonexistent",
                Op = OperationType.QuotationSeparate,
                Target = new Target("999-0", new ApplyRevision.Model.Range { Start = 5, End = 5 }) // Non-existent paragraph
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var result = strategy.Execute(doc, operation);

            // Assert
            Assert.Equal(0, result); // Should return 0 when paragraph not found
        }

        #endregion

        #region Helper Methods / 辅助方法

        private static void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // Create a test paragraph with paraId
            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Hello World! This is a test paragraph for quotation separation."));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        [Fact]
        public void Execute_ValidOperation_ShouldExecuteSuccessfully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Id = "test-valid-operation",
                Op = OperationType.QuotationSeparate,
                Target = new Target("12345-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }) // Split after "Hello "
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var result = strategy.Execute(doc, operation);

            // Assert
            Assert.Equal(0, result); // QuotationSeparateStrategy returns 0 based on implementation

            // Verify that the operation completed without throwing exceptions
            _mockLogger.Verify(
                l => l.LogInformation(It.Is<string>(s => s.Contains("Starting quotation separation operation"))),
                Times.Once);
        }

        #endregion

        #region IDisposable Implementation / IDisposable实现

        public void Dispose()
        {
            try
            {
                if (File.Exists(_tempFilePath))
                {
                    File.Delete(_tempFilePath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除临时文件时发生异常: {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }

        #endregion
    }

    /// <summary>
    /// Integration tests for QuotationSeparateStrategy that test the complete operation
    /// within a more realistic document context.
    /// </summary>
    public class QuotationSeparateStrategyIntegrationTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly string _tempFilePath;

        public QuotationSeparateStrategyIntegrationTests()
        {
            // Setup mock objects / 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _mockIdManager.Setup(m => m.GenerateParaId()).Returns("P12345");

            // Setup temporary file path / 设置临时文件路径
            _tempFilePath = Path.GetTempFileName();
        }

        [Fact]
        public void Execute_ComplexFormattedParagraph_PreservesFormatting()
        {
            // Arrange
            CreateComplexTestDocx(_tempFilePath);
            var strategy = new QuotationSeparateStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Id = "integration-test",
                Op = OperationType.QuotationSeparate,
                Target = new Target("12345-0", new ApplyRevision.Model.Range { Start = 15, End = 15 }) // Split after "Bold text normal"
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            var result = strategy.Execute(doc, operation);

            // Assert
            Assert.Equal(0, result); // QuotationSeparateStrategy returns 0 based on implementation

            // Verify document structure changes
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2); // Should have at least 2 paragraphs after split
        }

        private static void CreateComplexTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // Create a complex paragraph with multiple runs and formatting
            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // Add paragraph properties
            var paraProps = new ParagraphProperties();
            paraProps.AppendChild(new ParagraphStyleId { Val = "Normal" });
            paragraph.AppendChild(paraProps);

            // Add multiple runs with different formatting
            var run1 = new Run();
            var runProps1 = new RunProperties();
            runProps1.AppendChild(new Bold());
            run1.AppendChild(runProps1);
            run1.AppendChild(new Text("Bold text "));
            paragraph.AppendChild(run1);

            var run2 = new Run();
            run2.AppendChild(new Text("normal text "));
            paragraph.AppendChild(run2);

            var run3 = new Run();
            var runProps3 = new RunProperties();
            runProps3.AppendChild(new Italic());
            run3.AppendChild(runProps3);
            run3.AppendChild(new Text("italic text"));
            paragraph.AppendChild(run3);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        public void Dispose()
        {
            try
            {
                if (File.Exists(_tempFilePath))
                {
                    File.Delete(_tempFilePath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除临时文件时发生异常: {ex.Message}");
            }
            GC.SuppressFinalize(this);
        }
    }
}