using System;
using System.IO;
using System.Linq;
using System.Reflection;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using Common.Tests.Attributes;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Strategy.Test
{
    public class InsertStrategyTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly string _tempFilePath;

        public InsertStrategyTests()
        {
            // 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _mockIdManager.Setup(m => m.GetNextRevisionId()).Returns(123);

            // 设置临时文件路径
            _tempFilePath = Path.GetTempFileName();
        }

        #region 构造函数测试

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert - 如果构造函数没有正确初始化依赖项，会抛出异常
            Assert.NotNull(strategy);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new InsertStrategy(
                null,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("context", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new InsertStrategy(
                _mockContext.Object,
                null,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("elementFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullRevisionFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                null,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("revisionFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                null,
                _mockCommentManager.Object
            ));

            Assert.Equal("idManager", exception.ParamName);
        }

        #endregion

        #region Execute方法测试

        [Fact]
        public void Execute_WithNullRun_ShouldLogErrorAndReturn()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("nonexistent-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 我们不需要模拟RunLocator，因为InsertStrategy内部会创建它
            // 测试将使用实际的文档，但使用不存在的segId

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            _mockLogger.Verify(
                l => l.LogError(It.Is<string>(s => s.Contains("Cannot find Run for SegId: nonexistent-0"))),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithNullRevision_ShouldLogWarningAndReturn()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test text",
                Revision = null
            };

            using var doc = WordprocessingDocument.Open(_tempFilePath, true);

            // Act
            strategy.Execute(doc, operation);

            // Assert
            _mockLogger.Verify(
                l => l.LogWarning(It.Is<string>(s => s.Contains("No revision information provided"))),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void Execute_WithNullText_ShouldLogErrorAndReturn()
        {
            try
            {
                // Arrange
                _mockLogger.Setup(l => l.Log(It.IsAny<string>())).Callback<string>(s => Console.WriteLine($"Log: {s}"));
                Console.WriteLine($"临时文件路径: {_tempFilePath}");

                // 创建测试文档
                CreateTestDocx(_tempFilePath);
                Console.WriteLine("测试文档已创建");

                // 首先修改文档，然后关闭它
                Console.WriteLine("准备修改文档，移除Text元素");
                using (var docToModify = WordprocessingDocument.Open(_tempFilePath, true))
                {
                    var run = docToModify.MainDocumentPart.Document.Body.Descendants<Run>().First();
                    run.RemoveAllChildren<Text>();
                    docToModify.Save();
                    Console.WriteLine("文档修改完成并保存");
                }

                Console.WriteLine("修改文档的using块已结束");

                // 确保文件句柄已释放
                GC.Collect();
                GC.WaitForPendingFinalizers();
                Console.WriteLine("已执行垃圾回收，等待文件句柄释放");

                // 创建策略
                var strategy = new InsertStrategy(
                    _mockContext.Object,
                    _mockElementFactory.Object,
                    _mockRevisionFactory.Object,
                    _mockIdManager.Object,
                    _mockCommentManager.Object
                );

                var operation = new Operation
                {
                    Op = OperationType.Insert,
                    Target = new("12345-0"),
                    Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                    Text = "Test text",
                    Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                };

                Console.WriteLine("准备打开文档执行策略");

                // 然后再打开文档执行操作
                using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
                {
                    Console.WriteLine("文档已打开，准备执行策略");
                    strategy.Execute(doc, operation);
                    Console.WriteLine("策略执行完成");
                }

                // Assert
                _mockLogger.Verify(
                    l => l.LogError(It.Is<string>(s => s.Contains("Cannot locate text position within Run"))),
                    Times.Once);

                Console.WriteLine("测试断言验证完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");
                throw;
            }
            finally
            {
                // Cleanup
                try
                {
                    if (File.Exists(_tempFilePath))
                    {
                        File.Delete(_tempFilePath);
                        Console.WriteLine("临时文件已删除");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"删除临时文件时发生异常: {ex.Message}");
                }
            }
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithPositionAtStart_ShouldInsertBeforeText()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟InsertedRun
            var insertedRun = new InsertedRun();
            insertedRun.AppendChild(new Run(new Text("Test text")));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns(insertedRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证文档中添加了InsertedRun
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("Test text", insertedRuns[0].Descendants<Text>().First().Text);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void Execute_WithPositionAtEnd_ShouldInsertAfterText()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 13, End = 13 }, // "Original text"的长度
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟InsertedRun
            var insertedRun = new InsertedRun();
            insertedRun.AppendChild(new Run(new Text("Test text")));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns(insertedRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证文档中添加了InsertedRun
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("Test text", insertedRuns[0].Descendants<Text>().First().Text);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void Execute_WithPositionInMiddle_ShouldSplitTextAndInsert()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 5, End = 5 }, // 在"Original text"中间
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟InsertedRun
            var insertedRun = new InsertedRun();
            insertedRun.AppendChild(new Run(new Text("Test text")));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns(insertedRun);

            // 设置模拟CloneElement
            _mockElementFactory.Setup(f => f.CloneElement(
                It.IsAny<Run>(),
                It.IsAny<bool>()
            )).Returns<Run, bool>((run, deep) =>
            {
                var newRun = new Run();
                newRun.AppendChild(new Text("inal text"));
                return newRun;
            });

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证文档中添加了InsertedRun和分割后的Run
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("Test text", insertedRuns[0].Descendants<Text>().First().Text);

            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();
            Assert.True(runs.Count >= 2); // 原始Run + 分割后的Run

            // Cleanup
            File.Delete(_tempFilePath);
        }

        #endregion

        #region CreateInsertedRun方法测试

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithRevisionFactory_ShouldUseFactory()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            var insertedRun = new InsertedRun();
            insertedRun.AppendChild(new Run(new Text("Test text")));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns(insertedRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            _mockRevisionFactory.Verify(f => f.CreateInsertedRunElement(
                "Test text",
                "123",
                "Test Author",
                It.IsAny<string>(),
                It.IsAny<Run>()
            ), Times.Once);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithoutRevisionFactory_ShouldCreateManually()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            // 设置模拟RevisionFactory返回null，模拟旧行为
            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns((InsertedRun)null);

            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object, // 使用模拟的RevisionFactory
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            var clonedRun = new Run();
            clonedRun.AppendChild(new Text("Test text") { Space = SpaceProcessingModeValues.Preserve });

            _mockElementFactory.Setup(f => f.CloneElement(
                It.IsAny<Run>(),
                It.IsAny<bool>()
            )).Returns(clonedRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("123", insertedRuns[0].Id);
            Assert.Equal("Test Author", insertedRuns[0].Author);
            Assert.Equal(operation.Revision.Date, insertedRuns[0].Date.Value);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithoutRevisionFactoryAndNoRunProperties_ShouldCreateBasicRun()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            // 移除Run的所有属性
            using (var docToModify = WordprocessingDocument.Open(_tempFilePath, true))
            {
                var run = docToModify.MainDocumentPart.Document.Body.Descendants<Run>().First();
                run.RunProperties = null;
                docToModify.Save();
            }

            // 设置模拟RevisionFactory返回null，模拟旧行为
            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns((InsertedRun)null);

            var strategy = new InsertStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object, // 使用模拟的RevisionFactory
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test text",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            var textRun = new Run();
            textRun.AppendChild(new Text("Test text"));

            // 在Moq的表达式树中，我们需要显式提供所有参数，不能使用可选参数
            _mockElementFactory.Setup(f => f.CreateTextRun(
                It.Is<string>(s => s == "Test text"),
                It.IsAny<bool>()
            )).Returns(textRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("123", insertedRuns[0].Id);
            Assert.Equal("Test Author", insertedRuns[0].Author);
            Assert.Equal(operation.Revision.Date, insertedRuns[0].Date.Value);
        }

        #endregion

        #region 辅助方法

        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Original text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        // 这个方法在我们的测试中不再使用，因为我们直接测试公共方法的行为

        #endregion
    }
}