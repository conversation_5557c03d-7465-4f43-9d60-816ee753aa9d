using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Tests.Strategy.Test
{
    /// <summary>
    /// Integration tests for CommentAddStrategy class
    /// CommentAddStrategy类的集成测试
    /// </summary>
    public class CommentAddStrategyIntegrationTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;

        public CommentAddStrategyIntegrationTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
        }

        /// <summary>
        /// Create a test Word document with sample content
        /// 创建包含示例内容的测试Word文档
        /// </summary>
        /// <returns>WordprocessingDocument for testing</returns>
        private WordprocessingDocument CreateTestDocument()
        {
            var stream = new MemoryStream();
            var doc = WordprocessingDocument.Create(stream, DocumentFormat.OpenXml.WordprocessingDocumentType.Document);

            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document();
            var body = new Body();

            // Add a paragraph with some text and set paraId for SegId "12345-0"
            var paragraph = new Paragraph();
            paragraph.ParagraphId = "12345"; // Set paraId to match the SegId used in tests
            var run = new Run();
            run.Append(new Text("This is a sample document for testing comment addition functionality."));
            paragraph.Append(run);
            body.Append(paragraph);

            mainPart.Document.Append(body);
            return doc;
        }

        /// <summary>
        /// Save test document to TestResults directory for inspection
        /// 将测试文档保存到TestResults目录以供检查
        /// </summary>
        /// <param name="doc">Document to save</param>
        /// <param name="fileName">File name</param>
        private void SaveTestDocument(WordprocessingDocument doc, string fileName)
        {
            var testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "apply_revision", "TestResults");
            Directory.CreateDirectory(testResultsDir);

            var filePath = Path.Combine(testResultsDir, fileName);

            // Clone the document to a new file
            using var fileStream = File.Create(filePath);
            doc.Clone(fileStream);
        }
    }
}
