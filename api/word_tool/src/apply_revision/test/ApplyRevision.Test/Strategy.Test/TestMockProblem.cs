using System;
using Moq;
using Xunit;
using Xunit.Abstractions;
using ApplyRevision.Strategy;
using Amazon.Lambda.Core;
using DocumentFormat.OpenXml.Packaging;
using ApplyRevision.Model;
using ApplyRevision.Factory;
using ApplyRevision.Service;

namespace ApplyRevision.Tests.Strategy.Test
{
    public class TestMockProblem
    {
        private readonly ITestOutputHelper _output;

        public TestMockProblem(ITestOutputHelper output)
        {
            _output = output;
        }

        [Fact]
        public void Test_MockInterface_ShouldWork()
        {
            // 模拟接口应该可以工作
            _output.WriteLine("尝试模拟 IOperationStrategy 接口");
            var mockStrategy = new Mock<IOperationStrategy>();
            mockStrategy.Setup(s => s.Execute(It.IsAny<WordprocessingDocument>(), It.IsAny<Operation>()));

            // 如果能执行到这里，说明模拟接口没有问题
            _output.WriteLine("成功模拟 IOperationStrategy 接口");
            Assert.True(true);
        }

        [Fact]
        public void Test_MockConcreteClass_ShouldFail()
        {
            // 模拟具体类应该会失败，因为 Execute 方法不是虚拟的
            _output.WriteLine("准备模拟 DeleteStrategy 类");
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            var mockElementFactory = new Mock<IElementFactory>();
            var mockRevisionFactory = new Mock<IRevisionElementFactory>();
            var mockIdManager = new Mock<IIdManager>();

            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            try
            {
                _output.WriteLine("尝试创建 DeleteStrategy 的模拟对象");
                var mockDeleteStrategy = new Mock<DeleteStrategy>(
                    mockContext.Object,
                    mockElementFactory.Object,
                    mockRevisionFactory.Object,
                    mockIdManager.Object
                );

                _output.WriteLine("尝试模拟 DeleteStrategy.Execute 方法");
                // 这里应该会抛出异常
                mockDeleteStrategy.Setup(s => s.Execute(It.IsAny<WordprocessingDocument>(), It.IsAny<Operation>()));

                _output.WriteLine("警告：没有抛出预期的异常");
                Assert.Fail("应该抛出异常但没有");
            }
            catch (Exception ex)
            {
                // 预期会抛出异常
                _output.WriteLine($"捕获到异常: {ex.GetType().Name}");
                _output.WriteLine($"异常消息: {ex.Message}");
                Assert.Contains("应该抛出异常但没有", ex.Message);
            }
        }

        [Fact]
        public void Test_VerifyDeleteStrategy_ShouldFail()
        {
            _output.WriteLine("测试验证 DeleteStrategy.Execute 方法调用");
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            var mockElementFactory = new Mock<IElementFactory>();
            var mockRevisionFactory = new Mock<IRevisionElementFactory>();
            var mockIdManager = new Mock<IIdManager>();

            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            try
            {
                var mockDeleteStrategy = new Mock<DeleteStrategy>(
                    mockContext.Object,
                    mockElementFactory.Object,
                    mockRevisionFactory.Object,
                    mockIdManager.Object
                );

                // 尝试验证方法调用，这应该会失败
                _output.WriteLine("尝试验证 DeleteStrategy.Execute 方法调用");
                mockDeleteStrategy.Verify(
                    s => s.Execute(
                        It.IsAny<WordprocessingDocument>(),
                        It.IsAny<Operation>()
                    ),
                    Times.Never
                );

                _output.WriteLine("警告：没有抛出预期的异常");
                Assert.Fail("应该抛出异常但没有");
            }
            catch (Exception ex)
            {
                // 预期会抛出异常
                _output.WriteLine($"捕获到异常: {ex.GetType().Name}");
                _output.WriteLine($"异常消息: {ex.Message}");
                Assert.Contains("应该抛出异常但没有", ex.Message);
            }
        }
    }
}