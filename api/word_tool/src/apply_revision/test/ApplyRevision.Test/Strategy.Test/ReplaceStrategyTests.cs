using System;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using Common.Tests.Attributes;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Strategy.Test
{
    public class ReplaceStrategyTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly string _tempFilePath;

        public ReplaceStrategyTests()
        {
            // 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _mockIdManager.Setup(m => m.GetNextRevisionId()).Returns(123);

            // 设置临时文件路径
            _tempFilePath = Path.GetTempFileName();
        }

        #region 构造函数测试

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // Assert - 如果构造函数没有正确初始化依赖项，会抛出异常
            Assert.NotNull(strategy);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new ReplaceStrategy(
                null,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("context", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new ReplaceStrategy(
                _mockContext.Object,
                null,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("elementFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullRevisionFactory_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                null,
                _mockIdManager.Object,
                _mockCommentManager.Object
            ));

            Assert.Equal("revisionFactory", exception.ParamName);
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                null,
                _mockCommentManager.Object
            ));

            Assert.Equal("idManager", exception.ParamName);
        }

        #endregion

        #region Execute方法测试

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_ShouldLogInformation()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Replace,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Replaced",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证日志记录
            _mockLogger.Verify(
                l => l.LogInformation(It.Is<string>(s => s.Contains("Executing replace operation"))),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_ShouldCallDeleteStrategy()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            // 创建一个可以被监视的IOperationStrategy模拟对象
            var mockDeleteStrategy = new Mock<IOperationStrategy>();

            // 使用反射设置私有字段
            var strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // 使用反射替换私有字段
            var deleteStrategyField = typeof(ReplaceStrategy).GetField("deleteStrategy",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            deleteStrategyField.SetValue(strategy, mockDeleteStrategy.Object);

            var operation = new Operation
            {
                Op = OperationType.Replace,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Replaced",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证DeleteStrategy.Execute被调用
            mockDeleteStrategy.Verify(
                s => s.Execute(
                    It.IsAny<WordprocessingDocument>(),
                    It.Is<Operation>(o =>
                        o.Target == operation.Target &&
                        o.Range.Start == operation.Range.Start &&
                        o.Range.End == operation.Range.End &&
                        o.Text == "" && // 删除操作的Text应为空
                        o.Revision == operation.Revision
                    )
                ),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_ShouldCallInsertStrategy()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            // 创建一个可以被监视的IOperationStrategy模拟对象
            var mockInsertStrategy = new Mock<IOperationStrategy>();

            // 使用反射设置私有字段
            var strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // 使用反射替换私有字段
            var insertStrategyField = typeof(ReplaceStrategy).GetField("insertStrategy",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            insertStrategyField.SetValue(strategy, mockInsertStrategy.Object);

            var operation = new Operation
            {
                Op = OperationType.Replace,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Replaced",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证InsertStrategy.Execute被调用
            mockInsertStrategy.Verify(
                s => s.Execute(
                    It.IsAny<WordprocessingDocument>(),
                    It.Is<Operation>(o =>
                        o.Target == operation.Target &&
                        o.Range.Start == operation.Range.Start &&
                        o.Range.End == operation.Range.Start && // 插入操作的Start和End应相同
                        o.Text == operation.Text &&
                        o.Revision == operation.Revision
                    )
                ),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithNullRun_ShouldLogErrorAndReturn()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            // 创建一个可以被监视的IOperationStrategy模拟对象
            var mockDeleteStrategy = new Mock<IOperationStrategy>();

            // 设置IOperationStrategy.Execute方法的行为，模拟找不到Run的情况
            mockDeleteStrategy.Setup(s => s.Execute(
                It.IsAny<WordprocessingDocument>(),
                It.IsAny<Operation>()
            )).Callback(() =>
            {
                _mockLogger.Object.LogError("Run with ID 'nonexistent-0' not found for delete operation.");
            });

            var strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            // 使用反射替换私有字段
            var deleteStrategyField = typeof(ReplaceStrategy).GetField("deleteStrategy",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            deleteStrategyField.SetValue(strategy, mockDeleteStrategy.Object);

            var operation = new Operation
            {
                Op = OperationType.Replace,
                Target = new("nonexistent-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                Text = "Replaced",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证错误日志
            _mockLogger.Verify(
                l => l.LogError(It.Is<string>(s => s.Contains("not found for delete operation"))),
                Times.Once);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [UnsupportedFeatureFact("No revision information provided")]
        public void Execute_WithIntegration_ShouldReplaceText()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);

            var strategy = new ReplaceStrategy(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );

            var operation = new Operation
            {
                Op = OperationType.Replace,
                Target = new("12345-0"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 8 }, // 替换"Original"
                Text = "Replaced",
                Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
            };

            // 设置模拟DeletedRun
            var deletedRun = new DeletedRun();
            deletedRun.AppendChild(new Run(new DeletedText("Original")));

            _mockRevisionFactory.Setup(f => f.CreateDeletedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()
            )).Returns(deletedRun);

            // 设置模拟InsertedRun
            var insertedRun = new InsertedRun();
            insertedRun.AppendChild(new Run(new Text("Replaced")));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()
            )).Returns(insertedRun);

            // Act
            using var doc = WordprocessingDocument.Open(_tempFilePath, true);
            strategy.Execute(doc, operation);

            // Assert - 验证文档中添加了DeletedRun和InsertedRun
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
            Assert.Equal("Original", deletedRuns[0].Descendants<DeletedText>().First().Text);

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("Replaced", insertedRuns[0].Descendants<Text>().First().Text);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        #endregion

        #region 辅助方法

        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Original text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        #endregion
    }
}

