using ApplyRevision.Model;
using Common.Model;
using Xunit;

namespace ApplyRevision.Tests.Model
{
    public class ApplyRevisionEventTests
    {
        [Fact]
        public void ApplyRevisionEvent_ShouldInitializeProperties()
        {
            // Arrange
            var document = new S3Request
            {
                Bucket = "test-bucket",
                Key = "test-document-key"
            };

            var revision = new S3Request
            {
                Bucket = "test-bucket",
                Key = "test-revision-key"
            };

            // Act
            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = document,
                Revision = revision
            };

            // Assert
            Assert.NotNull(applyRevisionEvent.Document);
            Assert.NotNull(applyRevisionEvent.Revision);
            Assert.Equal("test-bucket", applyRevisionEvent.Document?.Bucket);
            Assert.Equal("test-document-key", applyRevisionEvent.Document?.Key);
            Assert.Equal("test-bucket", applyRevisionEvent.Revision?.Bucket);
            Assert.Equal("test-revision-key", applyRevisionEvent.Revision?.Key);
        }

        [Fact]
        public void ApplyRevisionEvent_ShouldAllowNullProperties()
        {
            // Act
            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = null,
                Revision = null
            };

            // Assert
            Assert.Null(applyRevisionEvent.Document);
            Assert.Null(applyRevisionEvent.Revision);
        }
    }
}
