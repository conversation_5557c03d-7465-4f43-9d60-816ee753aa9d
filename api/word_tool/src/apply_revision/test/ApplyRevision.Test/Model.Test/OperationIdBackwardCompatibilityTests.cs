using ApplyRevision.Model;
using Newtonsoft.Json;
using Xunit;

namespace ApplyRevision.Test.Model.Test
{
    /// <summary>
    /// Tests for Operation.Id backward compatibility functionality
    /// </summary>
    public class OperationIdBackwardCompatibilityTests
    {
        [Fact]
        public void Operation_DefaultConstructor_ShouldHaveDefaultId()
        {
            // Arrange & Act
            var operation = new Operation();

            // Assert
            Assert.Equal("DefaultId", operation.Id);
        }

        [Fact]
        public void Operation_WithExplicitId_ShouldUseProvidedId()
        {
            // Arrange
            var customId = "CustomOperationId123";

            // Act
            var operation = new Operation { Id = customId };

            // Assert
            Assert.Equal(customId, operation.Id);
        }

        [Fact]
        public void Operation_JsonDeserialization_WithId_ShouldUseJsonId()
        {
            // Arrange
            var jsonWithId = @"{
                ""Id"": ""JsonProvidedId"",
                ""Op"": ""Insert"",
                ""Target"": [{""SegId"": ""1-0""}],
                ""Text"": ""Test text""
            }";

            // Act
            var operation = JsonConvert.DeserializeObject<Operation>(jsonWithId);

            // Assert
            Assert.NotNull(operation);
            Assert.Equal("JsonProvidedId", operation.Id);
        }

        [Fact]
        public void Operation_JsonDeserialization_WithoutId_ShouldUseDefaultId()
        {
            // Arrange - JSON without Id property
            var jsonWithoutId = @"{
                ""Op"": ""Insert"",
                ""Target"": [{""SegId"": ""1-0""}],
                ""Text"": ""Test text""
            }";

            // Act
            var operation = JsonConvert.DeserializeObject<Operation>(jsonWithoutId);

            // Assert
            Assert.NotNull(operation);
            Assert.Equal("DefaultId", operation.Id);
        }

        [Fact]
        public void Operation_CanDistinguishBetweenJsonAndDefaultId()
        {
            // Arrange
            var operationWithDefault = new Operation();
            var operationFromJson = JsonConvert.DeserializeObject<Operation>(@"{
                ""Id"": ""FromJson"",
                ""Op"": ""Insert"",
                ""Target"": [{""SegId"": ""1-0""}],
                ""Text"": ""Test""
            }");

            // Act & Assert
            Assert.Equal("DefaultId", operationWithDefault.Id);
            Assert.NotNull(operationFromJson);
            Assert.Equal("FromJson", operationFromJson.Id);

            // Demonstrate how to distinguish
            Assert.True(IsDefaultId(operationWithDefault.Id));
            Assert.False(IsDefaultId(operationFromJson.Id));
        }

        [Fact]
        public void WordTextPatch_JsonDeserialization_MixedOperations_ShouldHandleCorrectly()
        {
            // Arrange - Mix of operations with and without IDs
            var patchJson = @"{
                ""DocumentId"": ""test-doc"",
                ""Operations"": [
                    {
                        ""Id"": ""ExplicitId1"",
                        ""Op"": ""Insert"",
                        ""Target"": [{""SegId"": ""1-0""}],
                        ""Text"": ""Text with explicit ID""
                    },
                    {
                        ""Op"": ""Delete"",
                        ""Target"": [{""SegId"": ""2-0""}],
                        ""Text"": ""Text without ID""
                    }
                ]
            }";

            // Act
            var patch = JsonConvert.DeserializeObject<WordTextPatch>(patchJson);

            // Assert
            Assert.NotNull(patch);
            Assert.Equal(2, patch.Operations.Count);
            
            // First operation should have explicit ID
            Assert.Equal("ExplicitId1", patch.Operations[0].Id);
            Assert.False(IsDefaultId(patch.Operations[0].Id));
            
            // Second operation should have default ID
            Assert.Equal("DefaultId", patch.Operations[1].Id);
            Assert.True(IsDefaultId(patch.Operations[1].Id));
        }

        /// <summary>
        /// Helper method to check if an ID is the default value
        /// </summary>
        /// <param name="id">The ID to check</param>
        /// <returns>True if the ID is the default value</returns>
        private static bool IsDefaultId(string id)
        {
            return id == "DefaultId";
        }
    }
}
