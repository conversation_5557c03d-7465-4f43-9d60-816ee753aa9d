using System;
using System.Collections.Generic;
using ApplyRevision.Model;
using Xunit;

namespace ApplyRevision.Tests
{
    public class DocumentChangeTests
    {
        [Fact]
        public void DocumentChange_ShouldInitializeWithDefaultValues()
        {
            // Arrange
            var paraId = "123";

            // Act
            var documentChange = new DocumentChange
            {
                ParaId = paraId
            };

            // Assert
            Assert.Equal(paraId, documentChange.ParaId);
            Assert.Equal("", documentChange.Text);
            Assert.NotNull(documentChange.ChangeText);
            Assert.Empty(documentChange.ChangeText);
        }

        [Fact]
        public void DocumentChange_ShouldAllowAddingRevisionInfo()
        {
            // Arrange
            var paraId = "123";
            var revisionInfo = new RevisionInfo
            {
                RevId = "1",
                EditType = EditType.inplace,
                Comment = "Test comment"
            };

            var documentChange = new DocumentChange
            {
                ParaId = paraId
            };

            // Act
            documentChange.ChangeText.Add(revisionInfo);

            // Assert
            Assert.Single(documentChange.ChangeText);
            Assert.Equal(revisionInfo, documentChange.ChangeText[0]);
        }


        [Fact]
        public void RevisionInfo_ShouldInitializeCorrectly()
        {
            // Arrange
            var revId = "1";
            var editType = EditType.inplace;
            var comment = "Test comment";
            var ops = new List<RevisionOperator>
            {
                new RevisionOperator
                {
                    Op = RevisionOperatorType.ins,
                    Text = "Inserted text",
                    Author = "TestAuthor",
                    Date = "2023-10-01",
                    CommentStart = true,
                    CommentEnd = false
                }
            };

            // Act
            var revisionInfo = new RevisionInfo
            {
                RevId = revId,
                EditType = editType,
                Comment = comment,
                Ops = ops
            };

            // Assert
            Assert.Equal(revId, revisionInfo.RevId);
            Assert.Equal(editType, revisionInfo.EditType);
            Assert.Equal(comment, revisionInfo.Comment);
            Assert.Single(revisionInfo.Ops);
            Assert.Equal(RevisionOperatorType.ins, revisionInfo.Ops[0].Op);
            Assert.Equal("Inserted text", revisionInfo.Ops[0].Text);
            Assert.Equal("TestAuthor", revisionInfo.Ops[0].Author);
            Assert.Equal("2023-10-01", revisionInfo.Ops[0].Date);
            Assert.True(revisionInfo.Ops[0].CommentStart);
            Assert.False(revisionInfo.Ops[0].CommentEnd);
        }

        [Fact]
        public void RevisionOperator_DefaultValues_ShouldBeSetCorrectly()
        {
            // Arrange
            var revisionOperator = new RevisionOperator
            {
                Op = RevisionOperatorType.ins,
                Text = "Sample text"
            };

            // Act & Assert
            Assert.Equal(RevisionOperatorType.ins, revisionOperator.Op);
            Assert.Equal("Sample text", revisionOperator.Text);
            Assert.Equal("AI", revisionOperator.Author);
            Assert.Equal(DateTime.Now.ToString("yyyy-MM-dd"), revisionOperator.Date);
            Assert.False(revisionOperator.CommentStart);
            Assert.False(revisionOperator.CommentEnd);
        }


        [Fact]
        public void RevisionLinkInsert_ShouldInitializeEmptyLists()
        {
            // Arrange & Act
            var revisionLinkInsert = new RevisionLinkInsert();

            Assert.NotNull(revisionLinkInsert.CommentComponents);
            Assert.Empty(revisionLinkInsert.CommentComponents);
        }
    }
}
