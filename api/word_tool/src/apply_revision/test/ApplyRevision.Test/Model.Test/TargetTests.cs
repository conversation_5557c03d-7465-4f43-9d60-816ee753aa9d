using ApplyRevision.Model;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace ApplyRevision.Tests.Model.Test
{
    /// <summary>
    /// Tests for Target class to verify Segment-based functionality and JSON serialization
    /// </summary>
    public class TargetTests
    {
        #region Constructor Tests

        [Fact]
        public void Constructor_Default_ShouldCreateEmptyTarget()
        {
            // Arrange & Act
            var target = new Target();

            // Assert
            Assert.Empty(target.Segments);
            Assert.Equal(0, target.Count);
            Assert.Null(target.FirstOrDefault);
        }

        [Fact]
        public void Constructor_WithSingleSegId_ShouldWork()
        {
            // Arrange & Act
            var target = new Target("12345-0");

            // Assert
            Assert.Single(target.Segments);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.Equal("12345-0", target.FirstOrDefault?.SegId);
        }

        [Fact]
        public void Constructor_WithSegmentList_ShouldWork()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new Segment { SegId = "12345-0" },
                new Segment { SegId = "12345-1", Range = new Range { Start = 5, End = 10 } }
            };

            // Act
            var target = new Target(segments);

            // Assert
            Assert.Equal(2, target.Count);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.Equal("12345-1", target.Segments[1].SegId);
            Assert.NotNull(target.Segments[1].Range);
            Assert.Equal(5, target.Segments[1].Range.Start);
            Assert.Equal(10, target.Segments[1].Range.End);
        }

        [Fact]
        public void Constructor_WithSegIdAndRange_ShouldWork()
        {
            // Arrange & Act
            var target = new Target("12345-0", 5, 10);

            // Assert
            Assert.Single(target.Segments);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.NotNull(target.Segments[0].Range);
            Assert.Equal(5, target.Segments[0].Range.Start);
            Assert.Equal(10, target.Segments[0].Range.End);
        }

        #endregion

        #region Segment Management Tests

        [Fact]
        public void AddSegment_ShouldAddToList()
        {
            // Arrange
            var target = new Target("12345-0");

            // Act
            target.AddSegment("12345-1", 5, 10);
            target.AddSegment("67890-0");

            // Assert
            Assert.Equal(3, target.Count);
            Assert.Equal("12345-0", target.FirstOrDefault?.SegId);
            Assert.True(target.ContainsSegment("12345-1"));
            Assert.True(target.ContainsSegment("67890-0"));

            // Check range was set correctly
            var segment1 = target.Segments.First(s => s.SegId == "12345-1");
            Assert.NotNull(segment1.Range);
            Assert.Equal(5, segment1.Range.Start);
            Assert.Equal(10, segment1.Range.End);
        }

        [Fact]
        public void RemoveSegment_ShouldRemoveFromList()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };
            var target = new Target(segments);

            // Act
            var removed = target.RemoveSegment("12345-1");

            // Assert
            Assert.True(removed);
            Assert.Equal(2, target.Count);
            Assert.False(target.ContainsSegment("12345-1"));
            Assert.True(target.ContainsSegment("12345-0"));
            Assert.True(target.ContainsSegment("67890-0"));
        }

        [Fact]
        public void ContainsSegment_ShouldReturnCorrectResult()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" }
            };
            var target = new Target(segments);

            // Act & Assert
            Assert.True(target.ContainsSegment("12345-0"));
            Assert.True(target.ContainsSegment("12345-1"));
            Assert.False(target.ContainsSegment("67890-0"));
        }

        [Fact]
        public void Clear_ShouldRemoveAllSegments()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };
            var target = new Target(segments);

            // Act
            target.Clear();

            // Assert
            Assert.Empty(target.Segments);
            Assert.Equal(0, target.Count);
            Assert.Null(target.FirstOrDefault);
        }

        #endregion

        #region Enumeration Tests

        [Fact]
        public void Target_ShouldBeEnumerable()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };
            var target = new Target(segments);

            // Act
            var enumeratedSegments = new List<Segment>();
            foreach (var segment in target)
            {
                enumeratedSegments.Add(segment);
            }

            // Assert
            Assert.Equal(3, enumeratedSegments.Count);
            Assert.Equal("12345-0", enumeratedSegments[0].SegId);
            Assert.Equal("12345-1", enumeratedSegments[1].SegId);
            Assert.Equal("67890-0", enumeratedSegments[2].SegId);
        }

        [Fact]
        public void Target_ShouldSupportLinq()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };
            var target = new Target(segments);

            // Act
            var filteredSegments = target.Where(s => s.SegId.StartsWith("12345")).ToList();

            // Assert
            Assert.Equal(2, filteredSegments.Count);
            Assert.Contains(filteredSegments, s => s.SegId == "12345-0");
            Assert.Contains(filteredSegments, s => s.SegId == "12345-1");
        }

        [Fact]
        public void Indexer_ShouldWorkCorrectly()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };
            var target = new Target(segments);

            // Act & Assert
            Assert.Equal("12345-0", target[0].SegId);
            Assert.Equal("12345-1", target[1].SegId);
            Assert.Equal("67890-0", target[2].SegId);

            // Test setter
            target[1] = new Segment { SegId = "99999-5" };
            Assert.Equal("99999-5", target[1].SegId);
        }

        [Fact]
        public void ToArray_ShouldReturnCorrectArray()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };
            var target = new Target(segments);

            // Act
            var array = target.ToArray();

            // Assert
            Assert.Equal(3, array.Length);
            Assert.Equal("12345-0", array[0].SegId);
            Assert.Equal("12345-1", array[1].SegId);
            Assert.Equal("67890-0", array[2].SegId);
        }

        [Fact]
        public void FromArray_ShouldCreateTargetCorrectly()
        {
            // Arrange
            var segments = new Segment[]
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1" },
                new() { SegId = "67890-0" }
            };

            // Act
            var target = Target.FromArray(segments);

            // Assert
            Assert.Equal(3, target.Count);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.Equal("12345-1", target.Segments[1].SegId);
            Assert.Equal("67890-0", target.Segments[2].SegId);
        }

        #endregion

        #region Edge Cases

        [Fact]
        public void AddSegment_WithNullOrEmpty_ShouldNotAdd()
        {
            // Arrange
            var target = new Target("12345-0");

            // Act
            target.AddSegment(null);
            target.AddSegment("");
            target.AddSegment("   ");

            // Assert
            Assert.Single(target.Segments);
            Assert.Equal("12345-0", target.FirstOrDefault?.SegId);
        }

        [Fact]
        public void Segments_SetToNull_ShouldCreateEmptyList()
        {
            // Arrange
            var target = new Target("12345-0");

            // Act
            target.Segments = null;

            // Assert
            Assert.Empty(target.Segments);
            Assert.Null(target.FirstOrDefault);
        }

        #endregion

        #region JSON Serialization Tests

        [Fact]
        public void Target_ShouldSerializeAndDeserializeCorrectly_WithNewtonsoftJson()
        {
            // Arrange
            var segments = new List<Segment>
            {
                new() { SegId = "12345-0" },
                new() { SegId = "12345-1", Range = new Range { Start = 5, End = 10 } },
                new() { SegId = "67890-0" }
            };
            var original = new Target(segments);

            // Act
            var json = JsonConvert.SerializeObject(original);
            System.Console.WriteLine($"Serialized JSON: {json}");
            var deserialized = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.NotNull(deserialized);
            Assert.Equal(original.Count, deserialized.Count);

            // Verify each segment
            for (int i = 0; i < original.Count; i++)
            {
                Assert.Equal(original.Segments[i].SegId, deserialized.Segments[i].SegId);
                if (original.Segments[i].Range != null)
                {
                    Assert.NotNull(deserialized.Segments[i].Range);
                    Assert.Equal(original.Segments[i].Range.Start, deserialized.Segments[i].Range.Start);
                    Assert.Equal(original.Segments[i].Range.End, deserialized.Segments[i].Range.End);
                }
                else
                {
                    Assert.Null(deserialized.Segments[i].Range);
                }
            }
        }

        [Fact]
        public void Target_ShouldDeserializeFromSingleString_ForBackwardCompatibility()
        {
            // Arrange
            var json = "\"12345-0\"";

            // Act
            var target = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.NotNull(target);
            Assert.Equal(1, target.Count);
            Assert.Equal("12345-0", target.FirstOrDefault?.SegId);
        }

        [Fact]
        public void Target_ShouldDeserializeFromStringArray_ForBackwardCompatibility()
        {
            // Arrange
            var json = "[\"12345-0\", \"12345-1\", \"67890-0\"]";

            // Act
            var target = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.NotNull(target);
            Assert.Equal(3, target.Count);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.Equal("12345-1", target.Segments[1].SegId);
            Assert.Equal("67890-0", target.Segments[2].SegId);

            // All ranges should be null for backward compatibility
            Assert.All(target.Segments, s => Assert.Null(s.Range));
        }

        [Fact]
        public void Target_ShouldSerializeSegmentWithRange()
        {
            // Arrange
            var target = new Target("12345-0", 5, 10);

            // Act
            var json = JsonConvert.SerializeObject(target);
            var deserialized = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.NotNull(deserialized);
            Assert.Single(deserialized.Segments);
            Assert.Equal("12345-0", deserialized.Segments[0].SegId);
            Assert.NotNull(deserialized.Segments[0].Range);
            Assert.Equal(5, deserialized.Segments[0].Range.Start);
            Assert.Equal(10, deserialized.Segments[0].Range.End);
        }

        [Fact]
        public void Target_ShouldDeserializeFromSingleObject_WithSegmentAndRange()
        {
            // Arrange - JSON representing a single Segment object
            var json = """
                {
                    "SegId": "12345-0",
                    "Range": {
                        "Start": 5,
                        "End": 10
                    }
                }
                """;

            // Act
            var target = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.NotNull(target);
            Assert.Single(target.Segments);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.NotNull(target.Segments[0].Range);
            Assert.Equal(5, target.Segments[0].Range.Start);
            Assert.Equal(10, target.Segments[0].Range.End);
        }

        [Fact]
        public void Target_ShouldDeserializeFromSingleObject_WithSegmentOnly()
        {
            // Arrange - JSON representing a single Segment object without Range
            var json = """
                {
                    "SegId": "12345-0"
                }
                """;

            // Act
            var target = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.NotNull(target);
            Assert.Single(target.Segments);
            Assert.Equal("12345-0", target.Segments[0].SegId);
            Assert.Null(target.Segments[0].Range);
        }

        [Fact]
        public void Target_ShouldHandleNullSerialization()
        {
            // Arrange
            Target target = null;

            // Act
            var json = JsonConvert.SerializeObject(target);
            var deserialized = JsonConvert.DeserializeObject<Target>(json);

            // Assert
            Assert.Null(deserialized);
        }

        #endregion
    }
}
