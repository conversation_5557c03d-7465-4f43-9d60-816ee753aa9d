using Xunit;
using Moq;
using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Service;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using System.IO;
using System;
using System.Collections.Generic;
using System.Linq;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Test.Service.Test
{
    /// <summary>
    /// Unit tests for CommentManager class
    /// </summary>
    public class CommentManagerTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly CommentManager _commentManager;

        public CommentManagerTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockIdManager = new Mock<IIdManager>();

            _mockContext.Setup(x => x.Logger).Returns(_mockLogger.Object);

            _commentManager = new CommentManager(_mockContext.Object, _mockElementFactory.Object, _mockIdManager.Object);
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidDependencies_ShouldInitialize()
        {
            // Arrange & Act
            var manager = new CommentManager(_mockContext.Object, _mockElementFactory.Object, _mockIdManager.Object);

            // Assert
            Assert.NotNull(manager);
            Assert.False(manager.IsInitialized);
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Arrange, Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new CommentManager(_mockContext.Object, null!, _mockIdManager.Object));
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Arrange, Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new CommentManager(_mockContext.Object, _mockElementFactory.Object, null!));
        }

        #endregion

        #region Initialization Tests

        [Fact]
        public void Initialize_WithValidMainPart_ShouldSetIsInitializedTrue()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;

                // Act
                _commentManager.Initialize(mainPart);

                // Assert
                Assert.True(_commentManager.IsInitialized);
                _mockIdManager.Verify(x => x.Initialize(It.IsAny<WordprocessingCommentsPart>(), It.IsAny<Body>()), Times.Once);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void Initialize_WithNullMainPart_ShouldThrowArgumentNullException()
        {
            // Arrange, Act & Assert
            Assert.Throws<ArgumentNullException>(() => _commentManager.Initialize(null!));
        }

        [Fact]
        public void CommentCount_BeforeInitialization_ShouldReturnZero()
        {
            // Arrange & Act
            int count = _commentManager.CommentCount;

            // Assert
            Assert.Equal(0, count);
        }

        #endregion

        #region Comment Creation Tests

        [Fact]
        public void CreateComment_WhenInitialized_ShouldReturnComment()
        {
            // Arrange
            var tempFile = CreateTestDocument();
            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                var expectedComment = new Comment { Id = "1" };

                _mockIdManager.Setup(x => x.GetNextCommentId()).Returns(1);
                _mockIdManager.Setup(x => x.GenerateParaId()).Returns("12345678");
                _mockElementFactory.Setup(x => x.CreateCommentElement(1, "Author", "2024-01-15", "Message", "12345678"))
                    .Returns(expectedComment);

                // Act
                var result = _commentManager.CreateComment("Author", "2024-01-15", "Message");

                // Assert
                Assert.Equal(expectedComment, result);
                _mockElementFactory.Verify(x => x.CreateCommentElement(1, "Author", "2024-01-15", "Message", "12345678"), Times.Once);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateComment_WhenNotInitialized_ShouldThrowInvalidOperationException()
        {
            // Arrange, Act & Assert
            Assert.Throws<InvalidOperationException>(() =>
                _commentManager.CreateComment("Author", "2024-01-15", "Message"));
        }

        [Fact]
        public void CreateCommentComponent_WhenInitialized_ShouldReturnComponent()
        {
            // Arrange
            var tempFile = CreateTestDocument();
            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                var comment = new Comment { Id = "1" };

                _mockIdManager.Setup(x => x.GetNextCommentId()).Returns(1);
                _mockIdManager.Setup(x => x.GenerateParaId()).Returns("12345678");
                _mockElementFactory.Setup(x => x.CreateCommentElement(1, "Author", "2024-01-15", "Message", "12345678"))
                    .Returns(comment);

                // Act
                var result = _commentManager.CreateCommentComponent("Author", "2024-01-15", "Message");

                // Assert
                Assert.NotNull(result);
                Assert.Equal(comment, result.Comment);
                Assert.NotNull(result.RangeStart);
                Assert.NotNull(result.RangeEnd);
                Assert.NotNull(result.Reference);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        #endregion

        #region Comment Management Tests

        [Fact]
        public void AddComment_WithValidComment_ShouldAddToDocument()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                var comment = new Comment { Id = "1", Author = "Test" };

                // Act
                _commentManager.AddComment(comment);

                // Assert
                Assert.Equal(1, _commentManager.CommentCount);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void AddComment_WithNullComment_ShouldThrowArgumentNullException()
        {
            // Arrange
            SetupInitializedManager();

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _commentManager.AddComment(null!));
        }

        [Fact]
        public void AddComments_WithMultipleComments_ShouldAddAllToDocument()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                var comments = new List<Comment>
                {
                    new Comment { Id = "1", Author = "Author1" },
                    new Comment { Id = "2", Author = "Author2" }
                };

                // Act
                _commentManager.AddComments(comments);

                // Assert
                Assert.Equal(2, _commentManager.CommentCount);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        #endregion

        #region Revision Link Tests

        [Fact]
        public void CreateRevisionLinkComments_WithValidLinks_ShouldReturnComponents()
        {
            // Arrange
            var tempFile = CreateTestDocument();
            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                var revisionLinks = new List<RevisionLink>
                {
                    new RevisionLink { Author = "Author1", Date = "2024-01-15", Comment = "Comment1" },
                    new RevisionLink { Author = "Author2", Date = "2024-01-15", Comment = "Comment2" }
                };

                var comment1 = new Comment { Id = "1" };
                var comment2 = new Comment { Id = "2" };

                _mockIdManager.SetupSequence(x => x.GetNextCommentId())
                    .Returns(2)  // Reverse order processing
                    .Returns(1);
                _mockIdManager.SetupSequence(x => x.GenerateParaId())
                    .Returns("87654321")
                    .Returns("12345678");

                _mockElementFactory.Setup(x => x.CreateCommentElement(2, "Author2", "2024-01-15", "Comment2", "87654321"))
                    .Returns(comment2);
                _mockElementFactory.Setup(x => x.CreateCommentElement(1, "Author1", "2024-01-15", "Comment1", "12345678"))
                    .Returns(comment1);

                // Act
                var result = _commentManager.CreateRevisionLinkComments(revisionLinks);

                // Assert
                Assert.Equal(2, result.Count);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateRevisionLinkComments_WithEmptyList_ShouldReturnEmptyList()
        {
            // Arrange
            var emptyLinks = new List<RevisionLink>();

            // Act
            var result = _commentManager.CreateRevisionLinkComments(emptyLinks);

            // Assert
            Assert.Empty(result);
        }

        #endregion

        #region Utility Tests

        [Fact]
        public void GetNextCommentId_WhenInitialized_ShouldReturnNextId()
        {
            // Arrange
            SetupInitializedManager();
            _mockIdManager.Setup(x => x.GetNextCommentId()).Returns(5);

            // Act
            var result = _commentManager.GetNextCommentId();

            // Assert
            Assert.Equal(5, result);
        }

        [Fact]
        public void GetAuthorInitials_ShouldDelegateToElementFactory()
        {
            // Arrange
            _mockElementFactory.Setup(x => x.GetCommentInitials("John Doe")).Returns("JD");

            // Act
            var result = _commentManager.GetAuthorInitials("John Doe");

            // Assert
            Assert.Equal("JD", result.Value);
            _mockElementFactory.Verify(x => x.GetCommentInitials("John Doe"), Times.Once);
        }

        [Fact]
        public void GenerateCommentContent_WithExistingComment_ShouldReturnComment()
        {
            // Arrange
            var revisionOp = new RevisionOperator
            {
                Comment = "Existing comment",
                Op = RevisionOperatorType.ins,
                Text = "Some text"
            };

            // Act
            var result = _commentManager.GenerateCommentContent(revisionOp);

            // Assert
            Assert.Equal("Existing comment", result);
        }

        [Fact]
        public void GenerateCommentContent_WithInsertOp_ShouldGenerateInsertComment()
        {
            // Arrange
            var revisionOp = new RevisionOperator
            {
                Comment = "",
                Op = RevisionOperatorType.ins,
                Text = "New text"
            };

            // Act
            var result = _commentManager.GenerateCommentContent(revisionOp);

            // Assert
            Assert.Equal("Insert: New text", result);
        }

        [Fact]
        public void GenerateCommentContent_WithDeleteOp_ShouldGenerateDeleteComment()
        {
            // Arrange
            var revisionOp = new RevisionOperator
            {
                Comment = "",
                Op = RevisionOperatorType.del,
                Text = "Deleted text"
            };

            // Act
            var result = _commentManager.GenerateCommentContent(revisionOp);

            // Assert
            Assert.Equal("Delete: Deleted text", result);
        }

        #endregion

        #region FindCommentById Tests

        [Fact]
        public void FindCommentById_WithValidId_ShouldReturnComment()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                // Create and add a test comment
                var testComment = new Comment { Id = "123", Author = "Test Author", Initials = "TA" };
                testComment.Append(new Paragraph(new Run(new Text("Test comment content"))));
                _commentManager.AddComment(testComment);

                // Act
                var result = _commentManager.FindCommentById("123");

                // Assert
                Assert.NotNull(result);
                Assert.Equal("123", result.Id?.Value);
                Assert.Equal("Test Author", result.Author?.Value);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void FindCommentById_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentManager.Initialize(mainPart);

                // Act
                var result = _commentManager.FindCommentById("nonexistent");

                // Assert
                Assert.Null(result);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void FindCommentById_WithNullId_ShouldReturnNull()
        {
            // Arrange
            SetupInitializedManager();

            // Act
            var result = _commentManager.FindCommentById(null!);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void FindCommentById_WithEmptyId_ShouldReturnNull()
        {
            // Arrange
            SetupInitializedManager();

            // Act
            var result = _commentManager.FindCommentById("");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void FindCommentById_WhenNotInitialized_ShouldThrowInvalidOperationException()
        {
            // Arrange, Act & Assert
            Assert.Throws<InvalidOperationException>(() =>
                _commentManager.FindCommentById("123"));
        }

        #endregion


        #region Helper Methods

        private void SetupInitializedManager()
        {
            var tempFile = CreateTestDocument();
            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _commentManager.Initialize(mainPart);
            File.Delete(tempFile);
        }

        private string CreateTestDocument()
        {
            var tempFile = Path.GetTempFileName();
            using var document = WordprocessingDocument.Create(tempFile, WordprocessingDocumentType.Document);

            var mainPart = document.AddMainDocumentPart();
            mainPart.Document = new Document(new Body(new Paragraph(new Run(new Text("Test document")))));

            return tempFile;
        }

        /// <summary>
        /// Copy generated test files to the TestResults directory for inspection
        /// </summary>
        /// <param name="sourceFileName">Name of the source file in current directory</param>
        /// <param name="testName">Name of the test (used for organizing files)</param>
        private void CopyTestFileToResults(string sourceFileName, string testName)
        {
            try
            {
                // Create TestResults directory structure
                var testResultsDir = Path.Combine("..", "..", "..", "TestResults");
                var commentTestsDir = Path.Combine(testResultsDir, "CommentManagerTests");

                Directory.CreateDirectory(commentTestsDir);

                var sourceFile = Path.Combine(Environment.CurrentDirectory, sourceFileName);
                if (File.Exists(sourceFile))
                {
                    // Add timestamp to filename for uniqueness
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var fileName = Path.GetFileNameWithoutExtension(sourceFileName);
                    var extension = Path.GetExtension(sourceFileName);
                    var targetFileName = $"{fileName}_{testName}_{timestamp}{extension}";

                    var targetFile = Path.Combine(commentTestsDir, targetFileName);
                    File.Copy(sourceFile, targetFile, true);

                    _mockContext.Object.Logger.LogInformation($"📁 Test file copied to: {targetFile}");
                }
            }
            catch (Exception ex)
            {
                _mockContext.Object.Logger.LogWarning($"Failed to copy test file {sourceFileName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Copy all test files to TestResults directory
        /// </summary>
        /// <param name="testName">Name of the test suite</param>
        private void CopyAllTestFilesToResults(string testName)
        {
            var testFiles = new[]
            {
                "test_comments_basic.docx",
                "test_revision_comments.docx",
                "test_complex_comments.docx"
            };

            foreach (var file in testFiles)
            {
                CopyTestFileToResults(file, testName);
            }
        }

        /// <summary>
        /// Clean up test files from current directory
        /// </summary>
        private void CleanupTestFiles()
        {
            var testFiles = Directory.GetFiles(Environment.CurrentDirectory, "test_*.docx");
            foreach (var file in testFiles)
            {
                try
                {
                    File.Delete(file);
                }
                catch (Exception ex)
                {
                    _mockContext.Object.Logger.LogWarning($"Failed to delete test file {file}: {ex.Message}");
                }
            }
        }

        #endregion

        #region Integration Tests - Real DOCX File Generation

        [Fact]
        public void IntegrationTest_CreateDocumentWithComments_ShouldGenerateValidDocx()
        {
            // Arrange
            var outputFile = Path.Combine(Environment.CurrentDirectory, "test_comments_basic.docx");
            var realElementFactory = new ElementFactory();
            var realIdManager = new IdManager(_mockContext.Object);
            var realCommentManager = new CommentManager(_mockContext.Object, realElementFactory, realIdManager);

            try
            {
                // Create a new DOCX document
                using (var document = WordprocessingDocument.Create(outputFile, WordprocessingDocumentType.Document))
                {
                    var mainPart = document.AddMainDocumentPart();

                    // Create document structure
                    var body = new Body();

                    // Add some paragraphs with text
                    var para1 = new Paragraph(new Run(new Text("This is the first paragraph. It contains some text that will be commented.")));
                    var para2 = new Paragraph(new Run(new Text("This is the second paragraph with different content for testing.")));
                    var para3 = new Paragraph(new Run(new Text("The third paragraph will demonstrate multiple comments.")));

                    body.Append(para1, para2, para3);
                    mainPart.Document = new Document(body);

                    // Initialize the comment manager
                    realCommentManager.Initialize(mainPart);

                    // Create and add various types of comments
                    var reviewComment = realCommentManager.CreateComment("Reviewer", "2024-01-15T10:30:00", "This paragraph needs revision for clarity.");
                    var aiComment = realCommentManager.CreateComment("AI Assistant", "2024-01-15T11:00:00", "Suggested improvement: Consider adding more details here.");
                    var editorComment = realCommentManager.CreateComment("Editor", "2024-01-15T14:30:00", "Grammar check completed. Minor corrections needed.");

                    realCommentManager.AddComments(new[] { reviewComment, aiComment, editorComment });

                    // Create comment components for specific text ranges
                    var component1 = realCommentManager.CreateCommentComponent("Proofreader", "2024-01-15T16:00:00", "Spelling error detected in this section.");
                    var component2 = realCommentManager.CreateCommentComponent("Subject Expert", "2024-01-15T17:15:00", "Technical accuracy verified.");

                    // Insert comment ranges around the first paragraph's text
                    var firstRun = para1.Elements<Run>().First();
                    firstRun.InsertBeforeSelf(component1.RangeStart);
                    firstRun.InsertAfterSelf(component1.RangeEnd);
                    component1.RangeEnd.InsertAfterSelf(component1.Reference);
                    realCommentManager.AddComment(component1.Comment);

                    // Insert another comment range around the third paragraph
                    var thirdRun = para3.Elements<Run>().First();
                    thirdRun.InsertBeforeSelf(component2.RangeStart);
                    thirdRun.InsertAfterSelf(component2.RangeEnd);
                    component2.RangeEnd.InsertAfterSelf(component2.Reference);
                    realCommentManager.AddComment(component2.Comment);

                    // Save the document
                    realCommentManager.Save();
                }

                // Verify the document was created and has comments
                Assert.True(File.Exists(outputFile));

                // Verify comment count by reopening the document
                using (var document = WordprocessingDocument.Open(outputFile, false))
                {
                    var commentsPart = document.MainDocumentPart?.WordprocessingCommentsPart;
                    Assert.NotNull(commentsPart);

                    var comments = commentsPart.Comments.Elements<Comment>().ToList();
                    Assert.Equal(5, comments.Count); // 3 regular comments + 2 component comments

                    // Verify comment content
                    Assert.Contains(comments, c => c.InnerText.Contains("needs revision"));
                    Assert.Contains(comments, c => c.Author?.Value == "AI Assistant");
                    Assert.Contains(comments, c => c.InnerText.Contains("Spelling error"));
                }

                _mockContext.Object.Logger.LogInformation($"✅ Integration test completed. Document created: {outputFile}");

                // Copy test file to TestResults directory
                CopyTestFileToResults("test_comments_basic.docx", "BasicComments");
            }
            finally
            {
                // Clean up test files from current directory
                CleanupTestFiles();
            }
        }

        [Fact]
        public void IntegrationTest_CreateDocumentWithRevisionComments_ShouldGenerateDocxWithTrackChanges()
        {
            // Arrange
            var outputFile = Path.Combine(Environment.CurrentDirectory, "test_revision_comments.docx");
            var realElementFactory = new ElementFactory();
            var realIdManager = new IdManager(_mockContext.Object);
            var realCommentManager = new CommentManager(_mockContext.Object, realElementFactory, realIdManager);

            try
            {
                using (var document = WordprocessingDocument.Create(outputFile, WordprocessingDocumentType.Document))
                {
                    var mainPart = document.AddMainDocumentPart();
                    var body = new Body();

                    // Create content with revision tracking
                    var para = new Paragraph();
                    para.Append(new Run(new Text("Original text that will be ")));

                    // Add inserted text with revision tracking
                    var insertedRun = new InsertedRun()
                    {
                        Id = "1",
                        Author = "Editor",
                        Date = DateTime.Now
                    };
                    insertedRun.Append(new Run(new Text("inserted ")));
                    para.Append(insertedRun);

                    // Add deleted text with revision tracking
                    var deletedRun = new DeletedRun()
                    {
                        Id = "2",
                        Author = "Reviewer",
                        Date = DateTime.Now
                    };
                    deletedRun.Append(new Run(new DeletedText("removed ")));
                    para.Append(deletedRun);

                    para.Append(new Run(new Text("and continues here.")));
                    body.Append(para);

                    mainPart.Document = new Document(body);
                    realCommentManager.Initialize(mainPart);

                    // Create revision-related comments
                    var revisionOps = new List<RevisionOperator>
                    {
                        new RevisionOperator
                        {
                            Op = RevisionOperatorType.ins,
                            Author = "Editor",
                            Date = "2024-01-15T10:00:00",
                            Text = "inserted text",
                            Comment = "This insertion improves clarity"
                        },
                        new RevisionOperator
                        {
                            Op = RevisionOperatorType.del,
                            Author = "Reviewer",
                            Date = "2024-01-15T11:00:00",
                            Text = "removed text",
                            Comment = "" // Will be auto-generated
                        },
                        new RevisionOperator
                        {
                            Op = RevisionOperatorType.moveFrom,
                            Author = "Content Manager",
                            Date = "2024-01-15T12:00:00",
                            Text = "moved content"
                        }
                    };

                    // Create comments for revisions
                    foreach (var revisionOp in revisionOps)
                    {
                        var comment = realCommentManager.CreateRevisionComment(revisionOp);
                        realCommentManager.AddComment(comment);
                    }

                    // Create revision links
                    var revisionLinks = new List<RevisionLink>
                    {
                        new RevisionLink
                        {
                            Author = "Technical Writer",
                            Date = "2024-01-15T13:00:00",
                            Comment = "Reference: See style guide section 3.2",
                            Uri = "https://company.com/style-guide#3.2",
                            DisplayText = "style guide"
                        },
                        new RevisionLink
                        {
                            Author = "Subject Expert",
                            Date = "2024-01-15T14:00:00",
                            Comment = "Cross-reference: Related to document ABC-123"
                        }
                    };

                    var linkComponents = realCommentManager.CreateRevisionLinkComments(revisionLinks);

                    // Insert link comment components around specific elements
                    if (linkComponents.Any())
                    {
                        var targetRun = para.Elements<Run>().FirstOrDefault();
                        if (targetRun != null)
                        {
                            realCommentManager.InsertCommentComponents(targetRun, targetRun, linkComponents);
                        }
                    }

                    realCommentManager.Save();
                }

                // Verify the document
                Assert.True(File.Exists(outputFile));

                using (var document = WordprocessingDocument.Open(outputFile, false))
                {
                    var commentsPart = document.MainDocumentPart?.WordprocessingCommentsPart;
                    Assert.NotNull(commentsPart);

                    var comments = commentsPart.Comments.Elements<Comment>().ToList();
                    Assert.True(comments.Count >= 3); // At least 3 revision comments + link comments

                    // Verify revision comment content
                    var commentTexts = comments.Select(c => c.InnerText).ToList();
                    Assert.Contains(commentTexts, text => text.Contains("insertion improves"));
                    Assert.Contains(commentTexts, text => text.Contains("Delete:"));
                    Assert.Contains(commentTexts, text => text.Contains("Move From:"));
                }

                _mockContext.Object.Logger.LogInformation($"✅ Revision comments test completed. Document created: {outputFile}");

                // Copy test file to TestResults directory
                CopyTestFileToResults("test_revision_comments.docx", "RevisionComments");
            }
            finally
            {
                // Clean up test files from current directory
                CleanupTestFiles();
            }
        }

        [Fact]
        public void IntegrationTest_CreateDocumentWithComplexCommentStructure_ShouldGenerateDocx()
        {
            // Arrange
            var outputFile = Path.Combine(Environment.CurrentDirectory, "test_complex_comments.docx");
            var realElementFactory = new ElementFactory();
            var realIdManager = new IdManager(_mockContext.Object);
            var realCommentManager = new CommentManager(_mockContext.Object, realElementFactory, realIdManager);

            try
            {
                using (var document = WordprocessingDocument.Create(outputFile, WordprocessingDocumentType.Document))
                {
                    var mainPart = document.AddMainDocumentPart();
                    var body = new Body();

                    // Create multiple paragraphs with different content
                    var paragraphs = new[]
                    {
                        "This document demonstrates complex comment structures in Word documents.",
                        "Multiple authors can add comments to different sections of the text.",
                        "Comments can be nested, linked, and associated with specific revisions.",
                        "The comment management system handles all these scenarios automatically.",
                        "This final paragraph shows how comments work with longer text content."
                    };

                    var docParas = new List<Paragraph>();
                    foreach (var text in paragraphs)
                    {
                        var para = new Paragraph(new Run(new Text(text)));
                        docParas.Add(para);
                        body.Append(para);
                    }

                    mainPart.Document = new Document(body);
                    realCommentManager.Initialize(mainPart);

                    // Create comments from different authors
                    var authors = new[] { "Alice Johnson", "Bob Smith", "Carol Davis", "AI Assistant", "David Wilson" };
                    var commentTypes = new[]
                    {
                        "Please review this section for technical accuracy.",
                        "Grammar correction needed here.",
                        "Consider rewording for better clarity.",
                        "AI suggestion: This could be simplified.",
                        "Final review completed - approved."
                    };

                    // Add comments to each paragraph
                    for (int i = 0; i < Math.Min(docParas.Count, authors.Length); i++)
                    {
                        var component = realCommentManager.CreateCommentComponent(
                            authors[i],
                            DateTime.Now.AddHours(-i).ToString("yyyy-MM-ddTHH:mm:ss"),
                            commentTypes[i]
                        );

                        var runs = docParas[i].Elements<Run>().ToList();
                        if (runs.Any())
                        {
                            var firstRun = runs.First();
                            var lastRun = runs.Last();

                            firstRun.InsertBeforeSelf(component.RangeStart);
                            lastRun.InsertAfterSelf(component.RangeEnd);
                            component.RangeEnd.InsertAfterSelf(component.Reference);
                            realCommentManager.AddComment(component.Comment);
                        }
                    }

                    // Add some standalone comments
                    var standaloneComments = new[]
                    {
                        realCommentManager.CreateComment("Project Manager", DateTime.Now.ToString(), "Document timeline: Due by end of week"),
                        realCommentManager.CreateComment("Legal Review", DateTime.Now.ToString(), "Legal compliance check - passed"),
                        realCommentManager.CreateComment("Quality Assurance", DateTime.Now.ToString(), "QA review completed successfully")
                    };

                    realCommentManager.AddComments(standaloneComments);

                    // Test author initials generation
                    var initialTests = new Dictionary<string, string>
                    {
                        { "John Doe", "JD" },
                        { "Mary Jane Watson", "MJW" },
                        { "", "AI" },
                        { "SingleName", "S" }
                    };

                    foreach (var test in initialTests)
                    {
                        var initials = realCommentManager.GetAuthorInitials(test.Key);
                        var expectedComment = realCommentManager.CreateComment(
                            test.Key,
                            DateTime.Now.ToString(),
                            $"Testing initials generation: {test.Key} -> {initials}"
                        );
                        realCommentManager.AddComment(expectedComment);
                    }

                    realCommentManager.Save();
                }

                // Verify the final document
                Assert.True(File.Exists(outputFile));

                using (var document = WordprocessingDocument.Open(outputFile, false))
                {
                    var commentsPart = document.MainDocumentPart?.WordprocessingCommentsPart;
                    Assert.NotNull(commentsPart);

                    var comments = commentsPart.Comments.Elements<Comment>().ToList();
                    Assert.True(comments.Count >= 12); // 5 paragraph comments + 3 standalone + 4 initial tests

                    // Verify different author initials
                    var johnComment = comments.FirstOrDefault(c => c.Author?.Value == "John Doe");
                    Assert.NotNull(johnComment);
                    Assert.Equal("JD", johnComment.Initials?.Value);

                    var aiComment = comments.FirstOrDefault(c => c.Author?.Value == "");
                    Assert.NotNull(aiComment);
                    Assert.Equal("AI", aiComment.Initials?.Value);
                }

                _mockContext.Object.Logger.LogInformation($"✅ Complex comments test completed. Document created: {outputFile}");
                _mockContext.Object.Logger.LogInformation($"📄 Total comments in document: {realCommentManager.CommentCount}");

                // Copy test file to TestResults directory
                CopyTestFileToResults("test_complex_comments.docx", "ComplexComments");
            }
            finally
            {
                // Clean up test files from current directory
                CleanupTestFiles();
            }
        }

        [Fact]
        public void IntegrationTest_RunAllCommentManagerTests_ShouldGenerateAllDocxFiles()
        {
            // This is a comprehensive test that runs all comment manager functionality
            // and organizes the output files in TestResults directory

            try
            {
                // Clean up any existing files first
                CleanupTestFiles();

                // Run basic comments test
                IntegrationTest_CreateDocumentWithComments_ShouldGenerateValidDocx();

                // Run revision comments test  
                IntegrationTest_CreateDocumentWithRevisionComments_ShouldGenerateDocxWithTrackChanges();

                // Run complex comments test
                IntegrationTest_CreateDocumentWithComplexCommentStructure_ShouldGenerateDocx();

                // Copy all files to results with a comprehensive test name
                CopyAllTestFilesToResults("ComprehensiveTest");

                _mockContext.Object.Logger.LogInformation("🎉 All CommentManager integration tests completed successfully!");
                _mockContext.Object.Logger.LogInformation("📁 All test files have been organized in TestResults/CommentManagerTests directory");
            }
            catch (Exception ex)
            {
                _mockContext.Object.Logger.LogError($"❌ Comprehensive test failed: {ex.Message}");
                throw;
            }
            finally
            {
                // Final cleanup
                CleanupTestFiles();
            }
        }

        #endregion

        public void Dispose()
        {
            _commentManager?.Dispose();
        }
    }
}