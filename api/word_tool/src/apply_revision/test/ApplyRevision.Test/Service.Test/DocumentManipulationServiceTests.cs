using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Service;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Test.Service.Test
{
    /// <summary>
    /// DocumentManipulationService 的单元测试
    /// Unit tests for DocumentManipulationService
    /// </summary>
    public class DocumentManipulationServiceTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly DocumentManipulationService _service;
        private readonly List<string> _tempFiles;

        public DocumentManipulationServiceTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(x => x.Logger).Returns(mockLogger.Object);
            _mockElementFactory = new Mock<IElementFactory>();
            _service = new DocumentManipulationService(_mockContext.Object, _mockElementFactory.Object);
            _tempFiles = new List<string>();
        }

        public void Dispose()
        {
            // 清理临时文件
            // Clean up temporary files
            foreach (var file in _tempFiles)
            {
                if (File.Exists(file))
                {
                    File.Delete(file);
                }
            }
        }

        #region Helper Methods

        /// <summary>
        /// 创建测试文档
        /// Create test document
        /// </summary>
        /// <returns>临时文件路径 / Temporary file path</returns>
        private string CreateTestDocument()
        {
            var tempFile = Path.GetTempFileName();
            _tempFiles.Add(tempFile);

            using var document = WordprocessingDocument.Create(tempFile, WordprocessingDocumentType.Document);
            var mainPart = document.AddMainDocumentPart();
            var body = new Body();
            var paragraph = new Paragraph();
            var run = new Run(new Text("Test document with comment"));
            paragraph.Append(run);
            body.Append(paragraph);
            mainPart.Document = new Document(body);

            return tempFile;
        }

        /// <summary>
        /// 创建包含现有评论的测试文档
        /// Create test document with existing comments
        /// </summary>
        /// <returns>临时文件路径 / Temporary file path</returns>
        private string CreateTestDocumentWithExistingComments()
        {
            var tempFile = Path.GetTempFileName();
            _tempFiles.Add(tempFile);

            using var document = WordprocessingDocument.Create(tempFile, WordprocessingDocumentType.Document);
            var mainPart = document.AddMainDocumentPart();
            var body = new Body();
            var paragraph = new Paragraph();

            // 添加现有的评论标记
            // Add existing comment markers
            paragraph.Append(new CommentRangeStart { Id = "0" });
            paragraph.Append(new CommentRangeStart { Id = "1" });
            paragraph.Append(new CommentRangeStart { Id = "2" });

            var run = new Run(new Text("Test document with comment"));
            paragraph.Append(run);

            paragraph.Append(new CommentRangeEnd { Id = "0" });
            paragraph.Append(new Run(new CommentReference { Id = "0" }));
            paragraph.Append(new CommentRangeEnd { Id = "1" });
            paragraph.Append(new Run(new CommentReference { Id = "1" }));
            paragraph.Append(new CommentRangeEnd { Id = "2" });
            paragraph.Append(new Run(new CommentReference { Id = "2" }));

            body.Append(paragraph);
            mainPart.Document = new Document(body);

            return tempFile;
        }

        #endregion

        #region Initialization Tests

        [Fact]
        public void Initialize_WithValidMainPart_ShouldSetIsInitializedTrue()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;

            // Act
            _service.Initialize(mainPart);

            // Assert
            Assert.True(_service.IsInitialized);
        }

        [Fact]
        public void Initialize_WithNullMainPart_ShouldThrowArgumentNullException()
        {
            // Arrange, Act & Assert
            Assert.Throws<ArgumentNullException>(() => _service.Initialize(null!));
        }

        #endregion

        #region InsertCommentReference Tests

        [Fact]
        public void InsertCommentReference_WithValidCommentId_ShouldReturnTrue()
        {
            // Arrange
            var tempFile = CreateTestDocument();
            var commentId = "3";

            _mockElementFactory.Setup(x => x.CreateCommentRangeStart(commentId))
                .Returns(new CommentRangeStart { Id = commentId });
            _mockElementFactory.Setup(x => x.CreateCommentRangeEnd(commentId))
                .Returns(new CommentRangeEnd { Id = commentId });
            _mockElementFactory.Setup(x => x.CreateCommentReference(commentId))
                .Returns(new Run(new CommentReference { Id = commentId }));

            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _service.Initialize(mainPart);

            var targetRun = mainPart.Document.Body!.Descendants<Run>().First();

            // Act
            var result = _service.InsertCommentReference(commentId, targetRun);

            // Assert
            Assert.True(result);
            _mockElementFactory.Verify(x => x.CreateCommentRangeStart(commentId), Times.Once);
            _mockElementFactory.Verify(x => x.CreateCommentRangeEnd(commentId), Times.Once);
            _mockElementFactory.Verify(x => x.CreateCommentReference(commentId), Times.Once);
        }

        [Fact]
        public void InsertCommentReference_WithoutInitialization_ShouldReturnFalse()
        {
            // Arrange
            var commentId = "1";

            // Act
            var result = _service.InsertCommentReference(commentId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void InsertCommentReference_WithNullInsertionPoint_ShouldInsertAtDocumentEnd()
        {
            // Arrange
            var tempFile = CreateTestDocument();
            var commentId = "1";

            _mockElementFactory.Setup(x => x.CreateCommentRangeStart(commentId))
                .Returns(new CommentRangeStart { Id = commentId });
            _mockElementFactory.Setup(x => x.CreateCommentRangeEnd(commentId))
                .Returns(new CommentRangeEnd { Id = commentId });
            _mockElementFactory.Setup(x => x.CreateCommentReference(commentId))
                .Returns(new Run(new CommentReference { Id = commentId }));

            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _service.Initialize(mainPart);

            // Act
            var result = _service.InsertCommentReference(commentId, null);

            // Assert
            Assert.True(result);
        }

        #endregion

        #region Nested Comment Tests

        [Fact]
        public void InsertCommentReference_WithExistingComments_ShouldCreateNestedStructure()
        {
            // Arrange
            var tempFile = CreateTestDocumentWithExistingComments();
            var newCommentId = "3";

            _mockElementFactory.Setup(x => x.CreateCommentRangeStart(newCommentId))
                .Returns(new CommentRangeStart { Id = newCommentId });
            _mockElementFactory.Setup(x => x.CreateCommentRangeEnd(newCommentId))
                .Returns(new CommentRangeEnd { Id = newCommentId });
            _mockElementFactory.Setup(x => x.CreateCommentReference(newCommentId))
                .Returns(new Run(new CommentReference { Id = newCommentId }));

            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _service.Initialize(mainPart);

            var targetRun = mainPart.Document.Body!.Descendants<Run>()
                .First(r => r.Elements<Text>().Any());

            // Act
            var result = _service.InsertCommentReference(newCommentId, targetRun);

            // Assert
            Assert.True(result);

            // 验证嵌套结构
            // Verify nested structure
            var paragraph = mainPart.Document.Body.Elements<Paragraph>().First();
            var rangeStarts = paragraph.Elements<CommentRangeStart>().ToList();
            var rangeEnds = paragraph.Elements<CommentRangeEnd>().ToList();
            var references = paragraph.Elements<Run>()
                .Where(r => r.Elements<CommentReference>().Any()).ToList();

            // 应该有4个开始标记（原有3个 + 新增1个）
            // Should have 4 start markers (3 existing + 1 new)
            Assert.Equal(4, rangeStarts.Count);

            // 应该有4个结束标记
            // Should have 4 end markers
            Assert.Equal(4, rangeEnds.Count);

            // 应该有4个引用
            // Should have 4 references
            Assert.Equal(4, references.Count);

            // 验证新评论ID存在
            // Verify new comment ID exists
            Assert.Contains(rangeStarts, rs => rs.Id == newCommentId);
            Assert.Contains(rangeEnds, re => re.Id == newCommentId);
            Assert.Contains(references, r => r.Elements<CommentReference>().Any(cr => cr.Id == newCommentId));
        }

        #endregion

        #region FindParagraphByParaId Tests

        [Fact]
        public void FindParagraphByParaId_WithValidParaId_ShouldReturnParagraph()
        {
            // Arrange
            var tempFile = CreateTestDocument();
            var paraId = "test-para-id";

            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;
            var paragraph = mainPart.Document.Body!.Elements<Paragraph>().First();
            paragraph.ParagraphId = new(paraId);

            _service.Initialize(mainPart);

            // Act
            var result = _service.FindParagraphByParaId(paraId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(paraId, result.ParagraphId?.Value);
        }

        [Fact]
        public void FindParagraphByParaId_WithInvalidParaId_ShouldReturnNull()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            using var document = WordprocessingDocument.Open(tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _service.Initialize(mainPart);

            // Act
            var result = _service.FindParagraphByParaId("non-existent-id");

            // Assert
            Assert.Null(result);
        }

        #endregion
    }
}
