using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.IO;

#nullable enable

namespace ApplyRevision.Tests.Service.Test
{
    /// <summary>
    /// Helper class to create real DOCX documents for testing
    /// </summary>
    public class TestDocumentBuilder : IDisposable
    {
        private readonly string _filePath;
        private WordprocessingDocument? _document;
        private MainDocumentPart? _mainPart;
        private bool _isDisposed = false;

        public TestDocumentBuilder()
        {
            _filePath = Path.GetTempFileName() + ".docx";
            _document = WordprocessingDocument.Create(_filePath, WordprocessingDocumentType.Document);
            _mainPart = _document.AddMainDocumentPart();
            _mainPart.Document = new Document(new Body());
        }

        public string FilePath => _filePath;
        public WordprocessingDocument Document => _document ?? throw new ObjectDisposedException(nameof(TestDocumentBuilder));
        public MainDocumentPart MainPart => _mainPart ?? throw new ObjectDisposedException(nameof(TestDocumentBuilder));
        public Body Body => MainPart.Document.Body ?? throw new InvalidOperationException("Document body is not initialized");

        /// <summary>
        /// Add comments with specified IDs to the document
        /// </summary>
        public TestDocumentBuilder WithComments(params string[] commentIds)
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(TestDocumentBuilder));

            var commentsPart = MainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();

            foreach (var id in commentIds)
            {
                var comment = new Comment()
                {
                    Id = id,
                    Author = "Test Author",
                    Date = DateTime.Now,
                    Initials = "TA"
                };
                comment.Append(new Paragraph(new Run(new Text($"Comment {id}"))));
                commentsPart.Comments.Append(comment);
            }

            return this;
        }

        /// <summary>
        /// Add inserted runs with specified revision IDs
        /// </summary>
        public TestDocumentBuilder WithInsertedRuns(params string[] revisionIds)
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(TestDocumentBuilder));

            var paragraph = new Paragraph();

            foreach (var id in revisionIds)
            {
                var insertedRun = new InsertedRun()
                {
                    Id = id,
                    Author = "Test Author",
                    Date = DateTime.Now
                };
                insertedRun.Append(new Run(new Text($"Inserted text {id}")));
                paragraph.Append(insertedRun);
            }

            Body.Append(paragraph);
            return this;
        }

        /// <summary>
        /// Add deleted runs with specified revision IDs
        /// </summary>
        public TestDocumentBuilder WithDeletedRuns(params string[] revisionIds)
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(TestDocumentBuilder));

            var paragraph = new Paragraph();

            foreach (var id in revisionIds)
            {
                var deletedRun = new DeletedRun()
                {
                    Id = id,
                    Author = "Test Author",
                    Date = DateTime.Now
                };

                // Create a run with deleted text
                var run = new Run();
                run.Append(new DeletedText($"Deleted text {id}"));
                deletedRun.Append(run);
                paragraph.Append(deletedRun);
            }

            Body.Append(paragraph);
            return this;
        }

        /// <summary>
        /// Add mixed revision types to test complex scenarios
        /// </summary>
        public TestDocumentBuilder WithMixedRevisions()
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(TestDocumentBuilder));

            var paragraph = new Paragraph();

            // Add normal run
            paragraph.Append(new Run(new Text("Normal text ")));

            // Add inserted run
            var insertedRun = new InsertedRun() { Id = "100", Author = "Author1", Date = DateTime.Now };
            insertedRun.Append(new Run(new Text("inserted ")));
            paragraph.Append(insertedRun);

            // Add deleted run
            var deletedRun = new DeletedRun() { Id = "200", Author = "Author2", Date = DateTime.Now };
            var deletedRunContent = new Run();
            deletedRunContent.Append(new DeletedText("deleted "));
            deletedRun.Append(deletedRunContent);
            paragraph.Append(deletedRun);

            Body.Append(paragraph);
            return this;
        }

        /// <summary>
        /// Add comments with invalid IDs for edge case testing
        /// </summary>
        public TestDocumentBuilder WithInvalidCommentIds()
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(TestDocumentBuilder));

            var commentsPart = MainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();

            // Valid IDs
            var comment1 = new Comment() { Id = "5", Author = "Test", Date = DateTime.Now };
            comment1.Append(new Paragraph(new Run(new Text("Valid comment 5"))));
            commentsPart.Comments.Append(comment1);

            // Invalid ID (not a number)
            var comment2 = new Comment() { Id = "invalid", Author = "Test", Date = DateTime.Now };
            comment2.Append(new Paragraph(new Run(new Text("Invalid ID comment"))));
            commentsPart.Comments.Append(comment2);

            // Empty ID
            var comment3 = new Comment() { Id = "", Author = "Test", Date = DateTime.Now };
            comment3.Append(new Paragraph(new Run(new Text("Empty ID comment"))));
            commentsPart.Comments.Append(comment3);

            // Null ID (will be skipped by OpenXML)
            var comment4 = new Comment() { Author = "Test", Date = DateTime.Now };
            comment4.Append(new Paragraph(new Run(new Text("No ID comment"))));
            commentsPart.Comments.Append(comment4);

            // Another valid ID
            var comment5 = new Comment() { Id = "10", Author = "Test", Date = DateTime.Now };
            comment5.Append(new Paragraph(new Run(new Text("Valid comment 10"))));
            commentsPart.Comments.Append(comment5);

            return this;
        }

        /// <summary>
        /// Save and close the document, making it available for external access
        /// </summary>
        public TestDocumentBuilder Save()
        {
            if (_isDisposed) throw new ObjectDisposedException(nameof(TestDocumentBuilder));

            _mainPart?.Document.Save();

            // Close the document to release file handle
            _document?.Dispose();
            _document = null;
            _mainPart = null;

            return this;
        }

        /// <summary>
        /// Clean up resources and delete the temporary file
        /// </summary>
        public void Dispose()
        {
            if (!_isDisposed)
            {
                _document?.Dispose();

                if (File.Exists(_filePath))
                {
                    try
                    {
                        File.Delete(_filePath);
                    }
                    catch (IOException)
                    {
                        // File might still be locked, try again after a short delay
                        System.Threading.Thread.Sleep(100);
                        try
                        {
                            File.Delete(_filePath);
                        }
                        catch (IOException)
                        {
                            // If still can't delete, ignore - OS will clean up temp files
                        }
                    }
                }

                _isDisposed = true;
            }
        }
    }
}