using Xunit;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Service;
using ApplyRevision.Factory;
using System.IO;
using System;
using System.Linq;

namespace ApplyRevision.Test.Service.Test
{
    /// <summary>
    /// 专门测试扩展评论部分创建功能的测试类
    /// Tests for extended comments parts creation functionality
    /// </summary>
    public class ExtendedCommentsPartsTests : IDisposable
    {
        private readonly CommentReplyExtensionService _extensionService;
        private readonly string _tempFile;

        public ExtendedCommentsPartsTests()
        {
            // Create a real extension service for integration testing
            // 创建真实的扩展服务进行集成测试
            var elementFactory = new ElementFactory();
            var idManager = new IdManager();
            _extensionService = new CommentReplyExtensionService(null);

            // Create a temporary test document
            // 创建临时测试文档
            _tempFile = Path.GetTempFileName();
            CreateTestDocument(_tempFile);
        }

        [Fact]
        public void WordprocessingCommentsExPart_ShouldBeCreatedSuccessfully()
        {
            // Arrange
            using var document = WordprocessingDocument.Open(_tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _extensionService.Initialize(mainPart);

            // Act - Create extension parts by creating a reply
            var parentComment = CreateTestComment("0", "Test Author", "Original comment");
            var replyComment = CreateTestComment("1", "Reply Author", "Reply text");
            var success = _extensionService.CreateReplyExtensions(
                parentComment,
                replyComment,
                "12345678",
                "87654321",
                DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));

            // Assert
            Assert.True(success);
            Assert.NotNull(mainPart.WordprocessingCommentsExPart);
            Assert.Equal("application/vnd.openxmlformats-officedocument.wordprocessingml.commentsExtended+xml",
                mainPart.WordprocessingCommentsExPart.ContentType);
        }

        [Fact]
        public void WordCommentsExtensiblePart_ShouldBeCreatedSuccessfully()
        {
            // Arrange
            using var document = WordprocessingDocument.Open(_tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _extensionService.Initialize(mainPart);

            // Act - Create extension parts by creating a reply
            var parentComment = CreateTestComment("0", "Test Author", "Original comment");
            var replyComment = CreateTestComment("1", "Reply Author", "Reply text");
            var success = _extensionService.CreateReplyExtensions(
                parentComment,
                replyComment,
                "12345678",
                "87654321",
                DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));

            // Assert
            Assert.True(success);
            Assert.NotNull(mainPart.WordCommentsExtensiblePart);
            Assert.Equal("application/vnd.openxmlformats-officedocument.wordprocessingml.commentsExtensible+xml",
                mainPart.WordCommentsExtensiblePart.ContentType);
        }

        [Fact]
        public void ExtendedCommentsPart_ShouldBeReusedIfExists()
        {
            // Arrange
            using var document = WordprocessingDocument.Open(_tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _extensionService.Initialize(mainPart);

            // Act - Create extension parts twice
            var parentComment = CreateTestComment("0", "Test Author", "Original comment");
            var replyComment1 = CreateTestComment("1", "Reply Author 1", "Reply text 1");
            var replyComment2 = CreateTestComment("2", "Reply Author 2", "Reply text 2");

            var success1 = _extensionService.CreateReplyExtensions(parentComment, replyComment1, "12345678", "87654321", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));
            var part1 = mainPart.WordprocessingCommentsExPart;

            var success2 = _extensionService.CreateReplyExtensions(parentComment, replyComment2, "23456789", "98765432", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));
            var part2 = mainPart.WordprocessingCommentsExPart;

            // Assert - Should reuse the same part instance
            Assert.True(success1);
            Assert.True(success2);
            Assert.NotNull(part1);
            Assert.NotNull(part2);
            Assert.Same(part1, part2);
        }

        [Fact]
        public void ExtendedCommentsParts_ShouldHaveCorrectContentTypes()
        {
            // Arrange
            using var document = WordprocessingDocument.Open(_tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _extensionService.Initialize(mainPart);

            // Act - Create extension parts
            var parentComment = CreateTestComment("0", "Test Author", "Original comment");
            var replyComment = CreateTestComment("1", "Reply Author", "Reply text");
            var success = _extensionService.CreateReplyExtensions(
                parentComment,
                replyComment,
                "12345678",
                "87654321",
                DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));

            // Assert - Verify parts are created and have correct content types
            Assert.True(success);

            var extendedPart = mainPart.WordprocessingCommentsExPart;
            var extensibilityPart = mainPart.WordCommentsExtensiblePart;
            var idsPart = mainPart.WordprocessingCommentsIdsPart;

            Assert.NotNull(extendedPart);
            Assert.NotNull(extensibilityPart);
            Assert.NotNull(idsPart);

            Assert.Equal("application/vnd.openxmlformats-officedocument.wordprocessingml.commentsExtended+xml", extendedPart.ContentType);
            Assert.Equal("application/vnd.openxmlformats-officedocument.wordprocessingml.commentsExtensible+xml", extensibilityPart.ContentType);
            Assert.Equal("application/vnd.openxmlformats-officedocument.wordprocessingml.commentsIds+xml", idsPart.ContentType);
        }

        [Fact]
        public void ExtendedCommentsParts_ShouldCreateValidXmlStructure()
        {
            // Arrange
            using var document = WordprocessingDocument.Open(_tempFile, true);
            var mainPart = document.MainDocumentPart!;
            _extensionService.Initialize(mainPart);

            // Act - Create extension parts
            var parentComment = CreateTestComment("0", "Test Author", "Original comment");
            var replyComment = CreateTestComment("1", "Reply Author", "Reply text");
            var success = _extensionService.CreateReplyExtensions(
                parentComment,
                replyComment,
                "12345678",
                "87654321",
                DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"));

            // Assert - Verify XML structure is valid
            Assert.True(success);

            var extendedPart = mainPart.WordprocessingCommentsExPart;
            var extensibilityPart = mainPart.WordCommentsExtensiblePart;

            Assert.NotNull(extendedPart);
            Assert.NotNull(extensibilityPart);

            // Test that we can parse the XML without errors
            using (var extendedStream = extendedPart.GetStream())
            {
                var extendedDoc = System.Xml.Linq.XDocument.Load(extendedStream);
                Assert.NotNull(extendedDoc.Root);
                Assert.Equal("commentsEx", extendedDoc.Root.Name.LocalName);
            }

            using (var extensibilityStream = extensibilityPart.GetStream())
            {
                var extensibilityDoc = System.Xml.Linq.XDocument.Load(extensibilityStream);
                Assert.NotNull(extensibilityDoc.Root);
                Assert.Equal("commentsExtensible", extensibilityDoc.Root.Name.LocalName);
            }
        }

        private Comment CreateTestComment(string id, string author, string text)
        {
            var comment = new Comment
            {
                Id = id,
                Author = author,
                Date = DateTime.Now,
                Initials = author.Substring(0, Math.Min(2, author.Length))
            };

            // Create paragraph with paraId attribute for extension XML generation
            // 为扩展XML生成创建带有paraId属性的段落
            var paragraph = new Paragraph(new Run(new Text(text)));

            // Generate a test paraId - using a simple format for testing
            // 生成测试用的paraId - 使用简单格式进行测试
            var paraId = $"TEST{id.PadLeft(8, '0')}";
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", paraId));

            comment.Append(paragraph);

            return comment;
        }

        private void CreateTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Test document for extended comments parts.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        public void Dispose()
        {
            if (File.Exists(_tempFile))
            {
                File.Delete(_tempFile);
            }
        }
    }
}
