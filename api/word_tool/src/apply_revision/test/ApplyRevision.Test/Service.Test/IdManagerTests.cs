using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Service;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;
using System;
using System.IO;
using System.Collections.Generic;

namespace ApplyRevision.Tests.Service.Test
{
    public class IdManagerTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;

        public IdManagerTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
        }

        #region Helper Methods

        /// <summary>
        /// Creates a test document and returns the file path
        /// </summary>
        private string CreateTestDocument(Action<TestDocumentBuilder> configureDocument)
        {
            using var docBuilder = new TestDocumentBuilder();
            configureDocument(docBuilder);
            docBuilder.Save();

            // Return a copy of the file path since the original will be cleaned up
            var tempPath = Path.GetTempFileName() + ".docx";
            File.Copy(docBuilder.FilePath, tempPath, true);
            return tempPath;
        }

        /// <summary>
        /// Helper to open document and run test with cleanup
        /// </summary>
        private static void WithTestDocument(string filePath, Action<WordprocessingCommentsPart, Body> testAction)
        {
            try
            {
                using var document = WordprocessingDocument.Open(filePath, false);
                var commentsPart = document.MainDocumentPart?.WordprocessingCommentsPart;
                var body = document.MainDocumentPart?.Document.Body;
                testAction(commentsPart, body);
            }
            finally
            {
                try
                {
                    File.Delete(filePath);
                }
                catch (IOException)
                {
                    // Ignore cleanup errors
                }
            }
        }

        #endregion

        #region Basic Functionality Tests

        [Fact]
        public void Constructor_ShouldInitializeWithZeroIds()
        {
            // Arrange & Act
            var idManager = new IdManager(_mockContext.Object);

            // Assert
            Assert.Equal(1, idManager.GetNextCommentId());
            Assert.Equal(1, idManager.GetNextRevisionId());
        }

        [Fact]
        public void GetNextCommentId_ShouldIncrementSequentially()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act & Assert
            Assert.Equal(1, idManager.GetNextCommentId());
            Assert.Equal(2, idManager.GetNextCommentId());
            Assert.Equal(3, idManager.GetNextCommentId());
        }

        [Fact]
        public void GetNextRevisionId_ShouldIncrementSequentially()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act & Assert
            Assert.Equal(1, idManager.GetNextRevisionId());
            Assert.Equal(2, idManager.GetNextRevisionId());
            Assert.Equal(3, idManager.GetNextRevisionId());
        }

        [Fact]
        public void Reset_ShouldResetAllCounters()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Generate some IDs first
            idManager.GetNextCommentId();
            idManager.GetNextCommentId();
            idManager.GetNextRevisionId();

            // Act
            idManager.Reset();

            // Assert
            Assert.Equal(1, idManager.GetNextCommentId());
            Assert.Equal(1, idManager.GetNextRevisionId());
        }

        #endregion

        #region Real Document Tests - Comments

        [Fact]
        public void Initialize_WithRealDocumentWithComments_ShouldSetCorrectStartingId()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithComments("1", "5", "3", "8"));

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(9, idManager.GetNextCommentId()); // Should be max(8) + 1
            });
        }

        [Fact]
        public void Initialize_WithRealDocumentWithNoComments_ShouldStartFromOne()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder => { }); // Empty document

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(1, idManager.GetNextCommentId());
            });
        }

        [Fact]
        public void Initialize_WithInvalidCommentIds_ShouldIgnoreInvalidOnes()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithInvalidCommentIds());

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(11, idManager.GetNextCommentId()); // Should be max(10) + 1, ignoring invalid IDs
            });
        }

        #endregion

        #region Real Document Tests - Revisions

        [Fact]
        public void Initialize_WithRealDocumentWithInsertedRuns_ShouldSetCorrectStartingId()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithInsertedRuns("2", "7", "4", "15"));

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(16, idManager.GetNextRevisionId()); // Should be max(15) + 1
            });
        }

        [Fact]
        public void Initialize_WithRealDocumentWithDeletedRuns_ShouldSetCorrectStartingId()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithDeletedRuns("1", "3", "9"));

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(10, idManager.GetNextRevisionId()); // Should be max(9) + 1
            });
        }

        [Fact]
        public void Initialize_WithRealDocumentWithMixedRevisions_ShouldFindMaxId()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithMixedRevisions()); // Contains IDs: 100, 200

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(201, idManager.GetNextRevisionId()); // Should be max(200) + 1
            });
        }

        [Fact]
        public void Initialize_WithRealDocumentWithNoRevisions_ShouldStartFromOne()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder => { }); // Empty document

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(1, idManager.GetNextRevisionId());
            });
        }

        #endregion

        #region Edge Cases and Complex Scenarios

        [Fact]
        public void Initialize_WithRealDocumentWithBothCommentsAndRevisions_ShouldSetBothCorrectly()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithComments("1", "5", "3")        // Max comment ID: 5
                       .WithInsertedRuns("2", "8", "4"));    // Max revision ID: 8

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(6, idManager.GetNextCommentId());   // max(5) + 1
                Assert.Equal(9, idManager.GetNextRevisionId());  // max(8) + 1
            });
        }

        [Fact]
        public void Initialize_WithNullDocumentBody_ShouldLogWarningAndStartFromOne()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            idManager.Initialize(null, null);

            // Assert
            Assert.Equal(1, idManager.GetNextCommentId());
            Assert.Equal(1, idManager.GetNextRevisionId());

            // Verify warning was logged
            _mockLogger.Verify(
                x => x.LogWarning(It.Is<string>(s => s.Contains("Document body is null"))),
                Times.Once);
        }

        [Fact]
        public void Initialize_WithRealDocumentWithInvalidRevisionIds_ShouldIgnoreInvalidOnes()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
            {
                // Add inserted runs with invalid IDs manually
                var paragraph = new Paragraph();

                // Valid ID
                var validRun = new InsertedRun() { Id = "5", Author = "Test" };
                validRun.Append(new Run(new Text("Valid")));
                paragraph.Append(validRun);

                // Invalid ID - this will be ignored during parsing
                var invalidRun = new InsertedRun() { Id = "invalid", Author = "Test" };
                invalidRun.Append(new Run(new Text("Invalid")));
                paragraph.Append(invalidRun);

                // Another valid ID
                var validRun2 = new InsertedRun() { Id = "10", Author = "Test" };
                validRun2.Append(new Run(new Text("Valid 2")));
                paragraph.Append(validRun2);

                builder.Body.Append(paragraph);
            });

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                idManager.Initialize(commentsPart, body);
                Assert.Equal(11, idManager.GetNextRevisionId()); // Should be max(10) + 1, ignoring "invalid"
            });
        }

        [Fact]
        public void Initialize_WithMultipleCallsOnSameDocument_ShouldReinitializeCorrectly()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var filePath = CreateTestDocument(builder =>
                builder.WithComments("1", "3")
                       .WithInsertedRuns("2", "5"));

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                // First initialization
                idManager.Initialize(commentsPart, body);
                var firstCommentId = idManager.GetNextCommentId(); // Should be 4
                var firstRevisionId = idManager.GetNextRevisionId(); // Should be 6

                // Second initialization (simulating reprocessing)
                idManager.Initialize(commentsPart, body);
                var secondCommentId = idManager.GetNextCommentId(); // Should be 4 again
                var secondRevisionId = idManager.GetNextRevisionId(); // Should be 6 again

                Assert.Equal(4, firstCommentId);
                Assert.Equal(6, firstRevisionId);
                Assert.Equal(4, secondCommentId);
                Assert.Equal(6, secondRevisionId);
            });
        }

        #endregion

        #region Performance Tests

        [Fact]
        public void Initialize_WithLargeNumberOfRevisionsAndComments_ShouldPerformEfficiently()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Create arrays with many IDs
            var commentIds = Enumerable.Range(1, 1000).Select(i => i.ToString()).ToArray();
            var revisionIds = Enumerable.Range(1, 1000).Select(i => i.ToString()).ToArray();

            var filePath = CreateTestDocument(builder =>
                builder.WithComments(commentIds)
                       .WithInsertedRuns(revisionIds));

            // Act & Assert
            WithTestDocument(filePath, (commentsPart, body) =>
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                idManager.Initialize(commentsPart, body);
                stopwatch.Stop();

                Assert.Equal(1001, idManager.GetNextCommentId());
                Assert.Equal(1001, idManager.GetNextRevisionId());
                Assert.True(stopwatch.ElapsedMilliseconds < 1000, $"Initialization took too long: {stopwatch.ElapsedMilliseconds}ms");
            });
        }

        #endregion

        #region Paragraph ID Generation Tests

        [Fact]
        public void GenerateParaId_ShouldReturnNonEmptyString()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var paraId = idManager.GenerateParaId();

            // Assert
            Assert.NotNull(paraId);
            Assert.NotEmpty(paraId);
        }

        [Fact]
        public void GenerateParaId_ShouldReturnUniqueIds()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var paraId1 = idManager.GenerateParaId();
            var paraId2 = idManager.GenerateParaId();
            var paraId3 = idManager.GenerateParaId();

            // Assert
            Assert.NotEqual(paraId1, paraId2);
            Assert.NotEqual(paraId2, paraId3);
            Assert.NotEqual(paraId1, paraId3);
        }

        [Fact]
        public void GenerateParaId_ShouldReturnUppercaseHexString()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var paraId = idManager.GenerateParaId();

            // Assert
            Assert.Equal(8, paraId.Length); // GUID without hyphens is 32 characters
            Assert.True(paraId.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F')),
                "ParaId should contain only uppercase hexadecimal characters");
        }

        [Fact]
        public void GenerateParaId_MultipleCallsShouldGenerateUniqueIds()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var generatedIds = new HashSet<string>();

            // Act & Assert
            for (int i = 0; i < 100; i++)
            {
                var paraId = idManager.GenerateParaId();
                Assert.True(generatedIds.Add(paraId), $"Duplicate ParaId generated: {paraId}");
            }
        }

        #endregion

        #region Durable ID Generation Tests

        [Fact]
        public void GenerateDurableId_ShouldReturnNonEmptyString()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var durableId = idManager.GenerateDurableId();

            // Assert
            Assert.NotNull(durableId);
            Assert.NotEmpty(durableId);
        }

        [Fact]
        public void GenerateDurableId_ShouldReturn8CharacterString()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var durableId = idManager.GenerateDurableId();

            // Assert
            Assert.Equal(8, durableId.Length);
        }

        [Fact]
        public void GenerateDurableId_ShouldReturnUppercaseHexString()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var durableId = idManager.GenerateDurableId();

            // Assert
            Assert.True(durableId.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F')),
                "DurableId should contain only uppercase hexadecimal characters");
        }

        [Fact]
        public void GenerateDurableId_ShouldReturnUniqueIds()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var durableId1 = idManager.GenerateDurableId();
            var durableId2 = idManager.GenerateDurableId();
            var durableId3 = idManager.GenerateDurableId();

            // Assert
            Assert.NotEqual(durableId1, durableId2);
            Assert.NotEqual(durableId2, durableId3);
            Assert.NotEqual(durableId1, durableId3);
        }

        [Fact]
        public void GenerateDurableId_MultipleCallsShouldGenerateUniqueIds()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);
            var generatedIds = new HashSet<string>();

            // Act & Assert
            for (int i = 0; i < 1000; i++)
            {
                var durableId = idManager.GenerateDurableId();
                Assert.True(generatedIds.Add(durableId), $"Duplicate DurableId generated: {durableId}");
            }
        }

        [Fact]
        public void GenerateDurableId_ShouldMatchExpectedFormat()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var durableId = idManager.GenerateDurableId();

            // Assert
            // Should match pattern like "650706C9" - 8 uppercase hex characters
            Assert.Matches(@"^[0-9A-F]{8}$", durableId);
        }

        #endregion

        #region Integration Tests for ID Generation

        [Fact]
        public void IdGeneration_ParaIdAndDurableId_ShouldBeDifferentFormats()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Act
            var paraId = idManager.GenerateParaId();
            var durableId = idManager.GenerateDurableId();

            // Assert
            Assert.Equal(8, paraId.Length);
            Assert.Equal(8, durableId.Length);
            Assert.NotEqual(paraId.Substring(0, 8), durableId); // They should be different even if we compare first 8 chars
        }

        [Fact]
        public void IdGeneration_ShouldNotAffectExistingCounters()
        {
            // Arrange
            var idManager = new IdManager(_mockContext.Object);

            // Get initial counter values
            var initialCommentId = idManager.GetNextCommentId();
            var initialRevisionId = idManager.GetNextRevisionId();

            // Act - Generate some IDs
            var paraId1 = idManager.GenerateParaId();
            var durableId1 = idManager.GenerateDurableId();
            var paraId2 = idManager.GenerateParaId();
            var durableId2 = idManager.GenerateDurableId();

            // Assert - Counters should continue from where they left off
            Assert.Equal(initialCommentId + 1, idManager.GetNextCommentId());
            Assert.Equal(initialRevisionId + 1, idManager.GetNextRevisionId());

            // Generated IDs should be valid
            Assert.NotNull(paraId1);
            Assert.NotNull(durableId1);
            Assert.NotNull(paraId2);
            Assert.NotNull(durableId2);
        }

        #endregion

        #region 生成合法的 durableId

        /// <summary>
        /// Test that durableId generation follows Word compatibility rules (0-7 range)
        /// 测试durableId生成遵循Word兼容性规则（0-7范围）
        /// </summary>
        [Fact]
        public void DurableId_Generation_ShouldStartWith0To7()
        {
            // Arrange - 准备
            var elementFactory = new ApplyRevision.Factory.ElementFactory();
            var idManager = new ApplyRevision.Service.IdManager(_mockContext.Object);

            // Act - 执行
            // Generate multiple durableIds to test the pattern
            // 生成多个durableId来测试模式
            var durableIds = new List<string>();
            for (int i = 0; i < 50; i++) // Increased count to better test the distribution
            {
                var durableId = idManager.GenerateDurableId();
                durableIds.Add(durableId);
                Console.WriteLine($"Generated durableId: {durableId}");
            }

            // Assert - 验证
            foreach (var durableId in durableIds)
            {
                // Verify length is 8 characters
                // 验证长度为8个字符
                Assert.Equal(8, durableId.Length);

                // Verify all characters are hexadecimal
                // 验证所有字符都是十六进制
                Assert.True(durableId.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F')),
                    $"DurableId {durableId} contains non-hexadecimal characters");

                // CRITICAL: verify first character is in 0-7 range (Word requirement)
                // 关键：验证第一个字符在0-7范围内（Word要求）
                Assert.True(durableId[0] >= '0' && durableId[0] <= '7',
                    $"DurableId {durableId} does not start with 0-7. First character: {durableId[0]}");
            }

            // Check distribution of first characters
            // 检查首字符的分布
            var firstCharCounts = durableIds.GroupBy(id => id[0])
                .ToDictionary(g => g.Key, g => g.Count());

            Console.WriteLine("First character distribution:");
            Console.WriteLine("首字符分布：");
            foreach (var kvp in firstCharCounts.OrderBy(x => x.Key))
            {
                Console.WriteLine($"  '{kvp.Key}': {kvp.Value} times");
            }

            Console.WriteLine($"SUCCESS: All {durableIds.Count} generated durableIds start with 0-7");
            Console.WriteLine($"成功：所有{durableIds.Count}个生成的durableId都以0-7开头");
        }

        /// <summary>
        /// Test that paraId generation also follows the same rules
        /// 测试paraId生成也遵循相同规则
        /// </summary>
        [Fact]
        public void ParaId_Generation_ShouldStartWithDigit()
        {
            // Arrange - 准备
            var elementFactory = new ApplyRevision.Factory.ElementFactory();
            var idManager = new ApplyRevision.Service.IdManager(_mockContext.Object);

            // Act - 执行
            // Generate multiple paraIds to test the pattern
            // 生成多个paraId来测试模式
            var paraIds = new List<string>();
            for (int i = 0; i < 20; i++)
            {
                var paraId = idManager.GenerateParaId();
                paraIds.Add(paraId);
                Console.WriteLine($"Generated paraId: {paraId}");
            }

            // Assert - 验证
            foreach (var paraId in paraIds)
            {
                // Verify length is 8 characters
                // 验证长度为8个字符
                Assert.Equal(8, paraId.Length);

                // Verify all characters are hexadecimal
                // 验证所有字符都是十六进制
                Assert.True(paraId.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F')),
                    $"ParaId {paraId} contains non-hexadecimal characters");

                // Verify first character is a digit (0-9) for better Word compatibility
                // 验证第一个字符是数字(0-9)以获得更好的Word兼容性
                // Note: paraId might not have the same strict requirement as durableId,
                // but we're applying the same rule for consistency
                // 注意：paraId可能没有与durableId相同的严格要求，
                // 但我们为了一致性应用相同的规则
                Console.WriteLine($"ParaId {paraId} first character: {paraId[0]} (IsDigit: {char.IsDigit(paraId[0])})");
            }

            Console.WriteLine($"Generated {paraIds.Count} paraIds for testing");
            Console.WriteLine($"生成了{paraIds.Count}个paraId用于测试");
        }
        #endregion
    }
}