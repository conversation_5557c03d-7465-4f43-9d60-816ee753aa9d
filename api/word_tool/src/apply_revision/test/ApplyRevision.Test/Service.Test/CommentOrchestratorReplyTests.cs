using Xunit;
using Moq;
using Amazon.Lambda.Core;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Service;
using ApplyRevision.Factory;
using ApplyRevision.Component;
using ApplyRevision.Exceptions;
using System.IO;
using System;
using System.Linq;
using Comment = DocumentFormat.OpenXml.Wordprocessing.Comment;

namespace ApplyRevision.Tests.Service.Test
{
    /// <summary>
    /// CommentOrchestrator 评论回复功能的专门测试
    /// Dedicated tests for CommentOrchestrator comment reply functionality
    /// </summary>
    public class CommentOrchestratorReplyTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentReplyExtensionService> _mockCommentReplyExtensionService;
        private readonly CommentXmlService _commentXmlService;
        private readonly DocumentManipulationService _documentManipulationService;
        private readonly ValidationService _validationService;
        private readonly CommentOrchestrator _commentOrchestrator;

        public CommentOrchestratorReplyTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentReplyExtensionService = new Mock<ICommentReplyExtensionService>();

            _mockContext.Setup(x => x.Logger).Returns(_mockLogger.Object);

            // 创建真实的服务实例 / Create real service instances
            _commentXmlService = new CommentXmlService(_mockContext.Object, _mockElementFactory.Object, _mockIdManager.Object);
            _documentManipulationService = new DocumentManipulationService(_mockContext.Object, _mockElementFactory.Object);
            _validationService = new ValidationService(_mockContext.Object);

            _commentOrchestrator = new CommentOrchestrator(
                _mockContext.Object,
                _commentXmlService,
                _documentManipulationService,
                _mockCommentReplyExtensionService.Object,
                _validationService,
                _mockIdManager.Object,
                _mockElementFactory.Object);
        }

        #region ProcessCommentReply Tests

        [Fact]
        public void ProcessCommentReply_WithNonexistentParentId_ShouldReturnFalse()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act
                var result = _commentOrchestrator.ProcessCommentReply("nonexistent", "Reply Author", "2024-01-15", "Reply text");

                // Assert
                Assert.False(result);
                // 验证错误日志 / Verify error logging
                _mockLogger.Verify(x => x.LogError(It.Is<string>(s => s.Contains("Failed to process comment reply"))), Times.Once);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void ProcessCommentReply_WithNullText_ShouldReturnFalse()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act
                var result = _commentOrchestrator.ProcessCommentReply("1", "Reply Author", "2024-01-15", null!);

                // Assert
                Assert.False(result);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void ProcessCommentReply_WithEmptyText_ShouldReturnFalse()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act
                var result = _commentOrchestrator.ProcessCommentReply("1", "Reply Author", "2024-01-15", "");

                // Assert
                Assert.False(result);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void ProcessCommentReply_WithNullAuthor_ShouldReturnFalse()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act
                var result = _commentOrchestrator.ProcessCommentReply("1", null!, "2024-01-15", "Reply text");

                // Assert
                Assert.False(result);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void ProcessCommentReply_WithEmptyAuthor_ShouldReturnFalse()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act
                var result = _commentOrchestrator.ProcessCommentReply("1", "", "2024-01-15", "Reply text");

                // Assert
                Assert.False(result);
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void ProcessCommentReply_WhenNotInitialized_ShouldReturnFalse()
        {
            // Arrange, Act & Assert
            var result = _commentOrchestrator.ProcessCommentReply("1", "Reply Author", "2024-01-15", "Reply text");

            Assert.False(result);
            _mockLogger.Verify(x => x.LogError(It.Is<string>(s => s.Contains("Failed to process comment reply"))), Times.Once);
        }

        #endregion

        #region CreateCommentReplyComponent Tests

        [Fact]
        public void CreateCommentReplyComponent_WithNonexistentParentId_ShouldThrowArgumentException()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act & Assert - 由于ID格式验证在前，会抛出ArgumentException而不是CommentNotFoundException
                // Since ID format validation comes first, it throws ArgumentException instead of CommentNotFoundException
                Assert.Throws<ArgumentException>(() =>
                    _commentOrchestrator.CreateCommentReplyComponent("nonexistent", "Reply Author", "2024-01-15", "Reply text"));
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateCommentReplyComponent_WithValidIdButNonexistentComment_ShouldThrowCommentNotFoundException()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act & Assert - 使用有效的ID格式但不存在的评论
                // Use valid ID format but non-existent comment
                Assert.Throws<CommentNotFoundException>(() =>
                    _commentOrchestrator.CreateCommentReplyComponent("999", "Reply Author", "2024-01-15", "Reply text"));
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateCommentReplyComponent_WithNullText_ShouldThrowArgumentException()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act & Assert
                Assert.Throws<ArgumentException>(() =>
                    _commentOrchestrator.CreateCommentReplyComponent("1", "Reply Author", "2024-01-15", null!));
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateCommentReplyComponent_WithEmptyText_ShouldThrowArgumentException()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act & Assert
                Assert.Throws<ArgumentException>(() =>
                    _commentOrchestrator.CreateCommentReplyComponent("1", "Reply Author", "2024-01-15", ""));
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateCommentReplyComponent_WithNullAuthor_ShouldThrowArgumentException()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act & Assert
                Assert.Throws<ArgumentException>(() =>
                    _commentOrchestrator.CreateCommentReplyComponent("1", null!, "2024-01-15", "Reply text"));
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        [Fact]
        public void CreateCommentReplyComponent_WithEmptyAuthor_ShouldThrowArgumentException()
        {
            // Arrange
            var tempFile = CreateTestDocument();

            try
            {
                using var document = WordprocessingDocument.Open(tempFile, true);
                var mainPart = document.MainDocumentPart!;
                _commentOrchestrator.Initialize(mainPart);

                // Act & Assert
                Assert.Throws<ArgumentException>(() =>
                    _commentOrchestrator.CreateCommentReplyComponent("1", "", "2024-01-15", "Reply text"));
            }
            finally
            {
                File.Delete(tempFile);
            }
        }

        #endregion

        private static string CreateTestDocument()
        {
            var tempFile = Path.GetTempFileName();
            using var document = WordprocessingDocument.Create(tempFile, WordprocessingDocumentType.Document);

            var mainPart = document.AddMainDocumentPart();
            mainPart.Document = new Document(new Body(new Paragraph(new Run(new Text("Test document")))));

            return tempFile;
        }

        /// <summary>
        /// 复制生成的测试文件到TestResults目录以供检查
        /// Copy generated test files to the TestResults directory for inspection
        /// </summary>
        /// <param name="sourceFileName">当前目录中源文件的名称 / Name of the source file in current directory</param>
        /// <param name="testName">测试名称（用于组织文件）/ Name of the test (used for organizing files)</param>
        private void CopyTestFileToResults(string sourceFileName, string testName)
        {
            try
            {
                // 创建TestResults目录结构 / Create TestResults directory structure
                var testResultsDir = Path.Combine("..", "..", "..", "TestResults");
                var commentTestsDir = Path.Combine(testResultsDir, "CommentOrchestratorReplyTests");
                Directory.CreateDirectory(commentTestsDir);

                var destinationPath = Path.Combine(commentTestsDir, $"{testName}_{sourceFileName}");
                File.Copy(sourceFileName, destinationPath, true);
            }
            catch (Exception ex)
            {
                // 测试文件复制失败不应影响测试结果 / Test file copy failure should not affect test results
                _mockLogger.Object.LogWarning($"Failed to copy test file: {ex.Message}");
            }
        }

        public void Dispose()
        {
            // 清理资源 / Cleanup resources
            GC.SuppressFinalize(this);
        }
    }
}
