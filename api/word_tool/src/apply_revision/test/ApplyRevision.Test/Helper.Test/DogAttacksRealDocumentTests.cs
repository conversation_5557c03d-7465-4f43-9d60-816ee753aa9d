using System;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Amazon.Lambda.Core;
using Moq;
using Xunit;
using DocumentFormat.OpenXml.Packaging;
using ApplyRevision.Helper;
using ApplyRevision.Model;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// Real document integration test for Dog attacks document with its corresponding patch operations.
    /// 真实文档集成测试，测试Dog attacks文档及其对应的补丁操作。
    /// </summary>
    public class DogAttacksRealDocumentTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;
        private readonly string _originalDocumentPath;
        private readonly string _patchFilePath;

        public DogAttacksRealDocumentTests()
        {
            // Setup mock objects / 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // Create test results directory / 创建测试结果目录
            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "TestResults", "DogAttacksRealDocument");
            Directory.CreateDirectory(_testResultsDir);

            // Set file paths / 设置文件路径
            _originalDocumentPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "Dog attacks.docx");
            _patchFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "Dog_patchs.json");

            // Verify test files exist / 验证测试文件存在
            Assert.True(File.Exists(_originalDocumentPath),
                $"Original document not found: {_originalDocumentPath}. " +
                "Ensure the 'Dog attacks.docx' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");

            Assert.True(File.Exists(_patchFilePath),
                $"Patch file not found: {_patchFilePath}. " +
                "Ensure the 'Dog_patchs.json' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");
        }

        /// <summary>
        /// Tests applying the real Dog patches to the Dog attacks document.
        /// 测试将真实的Dog补丁应用到Dog attacks文档。
        /// </summary>
        [Fact]
        public void ApplyDogPatches_ToRealDocument_ShouldSucceed()
        {
            // Arrange / 准备
            var modifiedDocumentPath = Path.Combine(_testResultsDir, "Dog_attacks_Modified.docx");
            var beforeCopyPath = Path.Combine(_testResultsDir, "Dog_attacks_Before.docx");

            // Copy original document to preserve it and create a working copy / 复制原始文档以保留它并创建工作副本
            File.Copy(_originalDocumentPath, beforeCopyPath, true);
            File.Copy(_originalDocumentPath, modifiedDocumentPath, true);

            // Load patch from JSON file / 从JSON文件加载补丁
            var patchJson = File.ReadAllText(_patchFilePath);
            var patch = JsonConvert.DeserializeObject<WordTextPatch>(patchJson);

            Assert.NotNull(patch);
            Assert.NotNull(patch.Operations);
            Assert.NotEmpty(patch.Operations);

            // Create the patch applier / 创建补丁应用程序
            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Act / 执行
            var result = applier.ApplyPatch(modifiedDocumentPath, patch);

            // Log the result for debugging / 记录结果用于调试
            Console.WriteLine($"Patch application result: {result}");
            Console.WriteLine($"Total operations to apply: {patch.Operations.Count}");

            // Group operations by type for diagnosis / 按类型分组操作以便诊断
            var operationGroups = patch.Operations.GroupBy(op => op.Op).ToList();
            foreach (var group in operationGroups)
            {
                Console.WriteLine($"Operation type {group.Key}: {group.Count()} operations");
            }

            // Assert / 验证
            Assert.True(result, "Patch application should succeed. Check console output for operation details.");

            // Verify the modified document exists and is valid / 验证修改后的文档存在且有效
            Assert.True(File.Exists(modifiedDocumentPath), $"Modified document not found: {modifiedDocumentPath}");

            // Validate document structure integrity / 验证文档结构完整性
            using (var doc = WordprocessingDocument.Open(modifiedDocumentPath, false))
            {
                Assert.NotNull(doc.MainDocumentPart);
                Assert.NotNull(doc.MainDocumentPart.Document);
                Assert.NotNull(doc.MainDocumentPart.Document.Body);
            }

            // Log test results for manual verification / 记录测试结果以供手动验证
            Console.WriteLine($"Test completed successfully!");
            Console.WriteLine($"Original document: {beforeCopyPath}");
            Console.WriteLine($"Modified document: {modifiedDocumentPath}");
            Console.WriteLine($"Total operations applied: {patch.Operations.Count}");
        }

        public void Dispose()
        {
            // Cleanup is optional since test results should be preserved for manual verification
            // 清理是可选的，因为测试结果应该被保留以供手动验证
            GC.SuppressFinalize(this);
        }
    }
}