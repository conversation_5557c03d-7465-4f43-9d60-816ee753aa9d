using System;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// Test cases for InsertOperationHandler class
    /// </summary>
    public class InsertOperationHandlerTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly InsertOperationHandler _handler;
        private readonly Revision _testRevision;

        public InsertOperationHandlerTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();

            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _mockIdManager.Setup(m => m.GetNextRevisionId()).Returns(12345);

            _handler = new InsertOperationHandler(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object);

            _testRevision = new Revision
            {
                Author = "Test Author",
                Date = DateTime.Parse("2024-01-01T10:00:00")
            };
        }

        [Fact]
        public void Constructor_WithValidParameters_ShouldCreateInstance()
        {
            // Act & Assert
            var handler = new InsertOperationHandler(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object);

            Assert.NotNull(handler);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new InsertOperationHandler(
                null,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object));
        }

        [Fact]
        public void Constructor_WithNullElementFactory_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new InsertOperationHandler(
                _mockContext.Object,
                null,
                _mockRevisionFactory.Object,
                _mockIdManager.Object));
        }

        [Fact]
        public void Constructor_WithNullRevisionFactory_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new InsertOperationHandler(
                _mockContext.Object,
                _mockElementFactory.Object,
                null,
                _mockIdManager.Object));
        }

        [Fact]
        public void Constructor_WithNullIdManager_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new InsertOperationHandler(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                null));
        }

        [Fact]
        public void InsertBeforeText_WithValidParameters_ShouldInsertInsertedRunBeforeRun()
        {
            // Arrange
            var paragraph = new Paragraph();
            var run = new Run(new Text("original text"));
            paragraph.Append(run);

            var text = run.GetFirstChild<Text>();
            var insertedRun = new InsertedRun();

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()))
                .Returns(insertedRun);

            // Act
            _handler.InsertBeforeText(run, text, "new text", _testRevision);

            // Assert
            Assert.Equal(2, paragraph.ChildElements.Count);
            Assert.Equal(insertedRun, paragraph.ChildElements[0]);
            Assert.Equal(run, paragraph.ChildElements[1]);
            _mockRevisionFactory.Verify(f => f.CreateInsertedRunElement(
                "new text",
                "12345",
                "Test Author",
                "2024-01-01T10:00:00",
                run), Times.Once);
        }

        [Fact]
        public void InsertBeforeText_WithNullParent_ShouldNotThrow()
        {
            // Arrange
            var run = new Run(new Text("original text"));
            var text = run.GetFirstChild<Text>();

            // Act & Assert - Should not throw
            _handler.InsertBeforeText(run, text, "new text", _testRevision);
        }

        [Fact]
        public void InsertAfterText_WithValidParameters_ShouldInsertInsertedRunAfterRun()
        {
            // Arrange
            var paragraph = new Paragraph();
            var run = new Run(new Text("original text"));
            paragraph.Append(run);

            var text = run.GetFirstChild<Text>();
            var insertedRun = new InsertedRun();

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()))
                .Returns(insertedRun);

            // Act
            _handler.InsertAfterText(run, text, "new text", _testRevision);

            // Assert
            Assert.Equal(2, paragraph.ChildElements.Count);
            Assert.Equal(run, paragraph.ChildElements[0]);
            Assert.Equal(insertedRun, paragraph.ChildElements[1]);
        }

        [Fact]
        public void InsertAfterText_WithNullParent_ShouldNotThrow()
        {
            // Arrange
            var run = new Run(new Text("original text"));
            var text = run.GetFirstChild<Text>();

            // Act & Assert - Should not throw
            _handler.InsertAfterText(run, text, "new text", _testRevision);
        }

        [Fact]
        public void SplitTextAndInsert_WithValidOffset_ShouldSplitTextAndInsertCorrectly()
        {
            // Arrange
            var paragraph = new Paragraph();
            var run = new Run(new Text("Hello World"));
            paragraph.Append(run);

            var text = run.GetFirstChild<Text>();
            var insertedRun = new InsertedRun();
            var clonedRun = new Run(new Text());

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()))
                .Returns(insertedRun);

            _mockElementFactory.Setup(f => f.CloneElement(run, true))
                .Returns(clonedRun);

            // Act
            _handler.SplitTextAndInsert(run, text, 5, " NEW", _testRevision);

            // Assert
            Assert.Equal("Hello", text.Text);
            Assert.Equal(3, paragraph.ChildElements.Count);
            Assert.Equal(run, paragraph.ChildElements[0]);
            Assert.Equal(insertedRun, paragraph.ChildElements[1]);
            Assert.Equal(clonedRun, paragraph.ChildElements[2]);

            var afterText = clonedRun.GetFirstChild<Text>();
            Assert.NotNull(afterText);
            Assert.Equal(" World", afterText.Text);
            Assert.Equal(SpaceProcessingModeValues.Preserve, afterText.Space.Value);
        }

        [Fact]
        public void SplitTextAndInsert_WithOffsetAtEnd_ShouldNotCreateAfterRun()
        {
            // Arrange
            var paragraph = new Paragraph();
            var run = new Run(new Text("Hello"));
            paragraph.Append(run);

            var text = run.GetFirstChild<Text>();
            var insertedRun = new InsertedRun();

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()))
                .Returns(insertedRun);

            // Act
            _handler.SplitTextAndInsert(run, text, 5, " World", _testRevision);

            // Assert
            Assert.Equal("Hello", text.Text);
            Assert.Equal(2, paragraph.ChildElements.Count);
            Assert.Equal(run, paragraph.ChildElements[0]);
            Assert.Equal(insertedRun, paragraph.ChildElements[1]);
        }

        [Fact]
        public void SplitTextAndInsert_WithOffsetAtBeginning_ShouldCreateEmptyBeforeText()
        {
            // Arrange
            var paragraph = new Paragraph();
            var run = new Run(new Text("Hello"));
            paragraph.Append(run);

            var text = run.GetFirstChild<Text>();
            var insertedRun = new InsertedRun();
            var clonedRun = new Run(new Text());

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()))
                .Returns(insertedRun);

            _mockElementFactory.Setup(f => f.CloneElement(run, true))
                .Returns(clonedRun);

            // Act
            _handler.SplitTextAndInsert(run, text, 0, "NEW ", _testRevision);

            // Assert
            Assert.Equal("", text.Text);
            Assert.Equal(3, paragraph.ChildElements.Count);

            var afterText = clonedRun.GetFirstChild<Text>();
            Assert.NotNull(afterText);
            Assert.Equal("Hello", afterText.Text);
        }

        [Fact]
        public void CreateInsertedRun_WithOriginalRun_ShouldCallFactoryWithRun()
        {
            // Arrange
            var originalRun = new Run(new Text("original"));
            var expectedInsertedRun = new InsertedRun();

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                "test text",
                "12345",
                "Test Author",
                "2024-01-01T10:00:00",
                originalRun))
                .Returns(expectedInsertedRun);

            // Act
            var result = _handler.CreateInsertedRun("test text", _testRevision, originalRun);

            // Assert
            Assert.Equal(expectedInsertedRun, result);
            _mockRevisionFactory.Verify(f => f.CreateInsertedRunElement(
                "test text",
                "12345",
                "Test Author",
                "2024-01-01T10:00:00",
                originalRun), Times.Once);
        }

        [Fact]
        public void CreateInsertedRun_WithoutOriginalRun_ShouldCallFactoryWithoutRun()
        {
            // Arrange
            var expectedInsertedRun = new InsertedRun();

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                "test text",
                "12345",
                "Test Author",
                "2024-01-01T10:00:00"))
                .Returns(expectedInsertedRun);

            // Act
            var result = _handler.CreateInsertedRun("test text", _testRevision);

            // Assert
            Assert.Equal(expectedInsertedRun, result);
            _mockRevisionFactory.Verify(f => f.CreateInsertedRunElement(
                "test text",
                "12345",
                "Test Author",
                "2024-01-01T10:00:00"), Times.Once);
        }

        [Fact]
        public void CreateInsertedRun_WhenFactoryReturnsNull_ShouldCreateManuallyWithOriginalRun()
        {
            // Arrange
            var originalRun = new Run(
                new RunProperties(new Bold()),
                new Text("original"));

            var clonedRun = new Run(
                new RunProperties(new Bold()),
                new Text("old content"));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<Run>()))
                .Returns((InsertedRun)null);

            _mockElementFactory.Setup(f => f.CloneElement(originalRun, true))
                .Returns(clonedRun);

            // Act
            var result = _handler.CreateInsertedRun("test text", _testRevision, originalRun);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("12345", result.Id);
            Assert.Equal("Test Author", result.Author);
            Assert.Equal(new DateTimeValue(_testRevision.Date), result.Date);

            var textRun = result.GetFirstChild<Run>();
            Assert.NotNull(textRun);

            var textElement = textRun.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal("test text", textElement.Text);
            Assert.Equal(SpaceProcessingModeValues.Preserve, textElement.Space.Value);
        }

        [Fact]
        public void CreateInsertedRun_WhenFactoryReturnsNull_ShouldCreateManuallyWithoutOriginalRun()
        {
            // Arrange
            var basicTextRun = new Run(new Text("test text"));

            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()))
                .Returns((InsertedRun)null);

            _mockElementFactory.Setup(f => f.CreateTextRun("test text", true))
                .Returns(basicTextRun);

            // Act
            var result = _handler.CreateInsertedRun("test text", _testRevision);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("12345", result.Id);
            Assert.Equal("Test Author", result.Author);
            Assert.Equal(new DateTimeValue(_testRevision.Date), result.Date);

            var textRun = result.GetFirstChild<Run>();
            Assert.NotNull(textRun);
            Assert.Equal("test text", textRun.InnerText);
        }



        [Fact]
        public void CreateInsertedRun_ShouldUseNextRevisionId()
        {
            // Arrange
            _mockIdManager.Setup(m => m.GetNextRevisionId()).Returns(99999);

            var expectedInsertedRun = new InsertedRun();
            _mockRevisionFactory.Setup(f => f.CreateInsertedRunElement(
                "test text",
                "99999",
                "Test Author",
                "2024-01-01T10:00:00"))
                .Returns(expectedInsertedRun);

            // Act
            var result = _handler.CreateInsertedRun("test text", _testRevision);

            // Assert
            _mockIdManager.Verify(m => m.GetNextRevisionId(), Times.Once);
            _mockRevisionFactory.Verify(f => f.CreateInsertedRunElement(
                "test text",
                "99999",
                "Test Author",
                "2024-01-01T10:00:00"), Times.Once);
        }
    }
}