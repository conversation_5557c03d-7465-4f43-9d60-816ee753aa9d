using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Tests.Attributes;
using ApplyRevision.Tests.Infrastructure;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// Tests for WordTextPatchApplier with QuotationSeparateStrategy integration.
    /// Focuses on testing quotation separation in combination with other operations
    /// such as insert, delete, replace, and format operations before and after line breaks.
    ///
    /// This class demonstrates the use of SaveWordDocumentAttribute for automatic
    /// document preservation during testing, similar to Python decorators or Java annotations.
    /// </summary>
    public class WordTextPatchApplierQuotationTests : BaseWordDocumentTest
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;

        public WordTextPatchApplierQuotationTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // Reset RunLocator singleton instance for test isolation
            // 重置 RunLocator 单例实例以确保测试隔离
            RunLocator.ResetInstanceForTesting();
        }

        #region Mixed Operations with Quotation Separation Tests

        [Fact]
        public void ApplyPatch_InsertOperationBeforeQuotationSeparation_ShouldApplyBothOperations()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-1",
                Operations = new List<Operation>
                {
                    // First: Insert text at the beginning of paragraph
                    new Operation
                    {
                        Id = "insert-before-split",
                        Op = OperationType.Insert,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                        Text = "Inserted text: ",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Split the paragraph after the insertion
                    new Operation
                    {
                        Id = "quotation-split-after-insert",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 20, End = 20 }), // After "Inserted text: Hell"
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify that insertion was applied before split
            var firstParagraphText = GetParagraphText(paragraphs[0]);
            Assert.StartsWith("Inserted text:", firstParagraphText);
        }

        [Fact]
        public void ApplyPatch_DeleteOperationBeforeQuotationSeparation_ShouldApplyBothOperations()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-2",
                Operations = new List<Operation>
                {
                    // First: Delete some text from the beginning
                    new Operation
                    {
                        Id = "delete-before-split",
                        Op = OperationType.Delete,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 6 }), // Delete "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Split the paragraph after deletion
                    new Operation
                    {
                        Id = "quotation-split-after-delete",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "World!"
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify that deletion was applied before split
            var firstParagraphText = GetParagraphText(paragraphs[0]);
            Assert.DoesNotContain("Hello ", firstParagraphText);
        }

        [Fact]
        public void ApplyPatch_ReplaceOperationBeforeQuotationSeparation_ShouldApplyBothOperations()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-3",
                Operations = new List<Operation>
                {
                    // First: Replace text in the paragraph
                    new Operation
                    {
                        Id = "replace-before-split",
                        Op = OperationType.Replace,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 5 }), // Replace "Hello"
                        Text = "Greetings",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Split the paragraph after replacement
                    new Operation
                    {
                        Id = "quotation-split-after-replace",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 15, End = 15 }), // After "Greetings World"
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify that replacement was applied before split
            var firstParagraphText = GetParagraphText(paragraphs[0]);
            Assert.Contains("Greetings", firstParagraphText);
            Assert.DoesNotContain("Hello", firstParagraphText);
        }

        [Fact]
        [SaveWordDocument("multiple-operations-before-quotation-separation",
            SaveBefore = true,
            SaveAfter = true,
            IncludeTimestamp = true,
            Metadata = "Testing multiple operations before quotation separation")]
        public void ApplyPatch_MultipleOperationsBeforeQuotationSeparation_ShouldApplyAllOperations()
        {
            ExecuteWithDocumentCreationAndSaving(
                () => CreateTestDocxWithMultipleParagraphs(TempFilePath),
                () =>
                {
                    var applier = new WordTextPatchApplier(_mockContext.Object);
                    var patch = new WordTextPatch
                    {
                        DocumentId = "test-doc-4",
                        Operations = new List<Operation>
                        {
                            // First: Insert text
                            new Operation
                            {
                                Id = "insert-prefix",
                                Op = OperationType.Insert,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                                Text = "Prefix: ",
                                Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                            },
                            // Second: Replace middle text
                            new Operation
                            {
                                Id = "replace-middle",
                                Op = OperationType.Replace,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 8, End = 13 }), // Replace "Hello"
                                Text = "Modified",
                                Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                            },
                            // Third: Insert suffix
                            new Operation
                            {
                                Id = "insert-suffix",
                                Op = OperationType.Insert,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 20, End = 20 }),
                                Text = " (added)",
                                Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                            },
                            // Fourth: Split the paragraph
                            new Operation
                            {
                                Id = "quotation-split-final",
                                Op = OperationType.QuotationSeparate,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 25, End = 25 }),
                                Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                            }
                        }
                    };

                    // Act
                    var result = applier.ApplyPatch(TempFilePath, patch);

                    // Assert
                    Assert.True(result);

                    // Verify document structure
                    using var doc = WordprocessingDocument.Open(TempFilePath, false);
                    var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
                    Assert.NotNull(paragraphs);
                    Assert.True(paragraphs.Count == 3, "Document should have exactly 3 paragraphs after split");

                    var firstParagraphText = GetParagraphText(paragraphs[0]);
                    Assert.NotNull(firstParagraphText);
                    var secondParagraphText = GetParagraphText(paragraphs[1]);
                    Assert.NotNull(secondParagraphText);

                    // Verify all operations were applied
                    Assert.Contains("Prefix:", firstParagraphText);
                    Assert.Contains("Modified", firstParagraphText);
                    Assert.Contains("(added)", secondParagraphText);
                    Assert.DoesNotContain("Hello", secondParagraphText);
                }
            );

        }

        [Fact]
        public void ApplyPatch_FormatOperationBeforeQuotationSeparation_ShouldPreserveFormatting()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-5",
                Operations = new List<Operation>
                {
                    // First: Apply formatting
                    new Operation
                    {
                        Id = "format-before-split",
                        Op = OperationType.Format,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 5 }), // Format "Hello"
                        Props = new Dictionary<string, object> { { "bold", true } },
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-after-format",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify formatting is preserved in first paragraph
            var firstParagraph = paragraphs[0];
            var boldRuns = firstParagraph.Descendants<Run>()
                .Where(r => r.RunProperties?.Bold != null)
                .ToList();
            Assert.NotEmpty(boldRuns);
        }

        [Fact]
        public void ApplyPatch_CommentOperationBeforeQuotationSeparation_ShouldApplyBothOperations()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-6",
                Operations = new List<Operation>
                {
                    // First: Add comment
                    new Operation
                    {
                        Id = "comment-before-split",
                        Op = OperationType.CommentAdd,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 5 }), // Comment on "Hello"
                        Comment = new ApplyRevision.Model.Comment { Text = "This is a test comment" }
                    },
                    // Second: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-after-comment",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }) // After "Hello "
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");
        }

        [Fact]
        public void ApplyPatch_OperationsAfterQuotationSeparation_ShouldNotAffectSeparation()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-7",
                Operations = new List<Operation>
                {
                    // First: Insert text that won't affect the split position
                    new Operation
                    {
                        Id = "insert-second-paragraph",
                        Op = OperationType.Insert,
                        Target = new Target("P002-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                        Text = "Added to second paragraph: ",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Split the first paragraph (should not be affected)
                    new Operation
                    {
                        Id = "quotation-split-first-paragraph",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 3, "Document should have at least 3 paragraphs after operations");

            // Verify second paragraph was modified
            var secondParagraphText = GetParagraphText(paragraphs[2]); // Index 2 because first paragraph was split
            Assert.StartsWith("Added to second paragraph:", secondParagraphText);
        }

        [Fact]
        public void ApplyPatch_MultipleQuotationSeparations_ShouldHandleAllSeparations()
        {
            // Arrange
            CreateTestDocxWithLongParagraph(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-8",
                Operations = new List<Operation>
                {
                    // First: Split at first position
                    new Operation
                    {
                        Id = "first-quotation-split",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 10, End = 10 })
                    },
                    // Second: Split at second position (in original paragraph)
                    new Operation
                    {
                        Id = "second-quotation-split",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 20, End = 20 })
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have multiple paragraphs after splits");
        }

        #endregion

        #region Operations After Quotation Separation Tests

        [Fact]
        public void ApplyPatch_InsertOperationAfterQuotationSeparation_ShouldApplyToCorrectSegment()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-1",
                Operations = new List<Operation>
                {
                    // First: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-first",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Insert text in the remaining part of the original paragraph
                    new Operation
                    {
                        Id = "insert-after-split",
                        Op = OperationType.Insert,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // At "World!" position in original segment
                        Text = "Beautiful ",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify that insertion was applied to the correct segment after split
            var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
            Assert.Contains("Beautiful", allText);
        }

        [Fact]
        public void ApplyPatch_DeleteOperationAfterQuotationSeparation_ShouldDeleteFromCorrectSegment()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-2",
                Operations = new List<Operation>
                {
                    // First: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-before-delete",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Delete text from the remaining part
                    new Operation
                    {
                        Id = "delete-after-split",
                        Op = OperationType.Delete,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 12 }), // Delete "World!"
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify that deletion was applied correctly
            var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
            Assert.DoesNotContain("World!", allText);
        }

        [Fact]
        public void ApplyPatch_ReplaceOperationAfterQuotationSeparation_ShouldReplaceInCorrectSegment()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-3",
                Operations = new List<Operation>
                {
                    // First: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-before-replace",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Replace text in the remaining part
                    new Operation
                    {
                        Id = "replace-after-split",
                        Op = OperationType.Replace,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 11 }), // Replace "World"
                        Text = "Universe",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify that replacement was applied correctly
            var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
            Assert.Contains("Universe", allText);
            Assert.DoesNotContain("World", allText);
        }

        [Fact]
        public void ApplyPatch_MultipleOperationsAfterQuotationSeparation_ShouldApplyAllCorrectly()
        {
            // Arrange
            CreateTestDocxWithLongParagraph(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-4",
                Operations = new List<Operation>
                {
                    // First: Split the paragraph at position 20
                    new Operation
                    {
                        Id = "quotation-split-middle",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 20, End = 20 }),
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Insert text after the split point
                    new Operation
                    {
                        Id = "insert-after-split-1",
                        Op = OperationType.Insert,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 25, End = 25 }),
                        Text = " [INSERTED] ",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Third: Replace text further down
                    new Operation
                    {
                        Id = "replace-after-split-1",
                        Op = OperationType.Replace,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 50, End = 60 }),
                        Text = "REPLACED",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Fourth: Delete text near the end
                    new Operation
                    {
                        Id = "delete-after-split-1",
                        Op = OperationType.Delete,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 100, End = 120 }),
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify all operations were applied
            var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
            Assert.Contains("[INSERTED]", allText);
            Assert.Contains("REPLACED", allText);
        }

        [Fact]
        public void ApplyPatch_FormatOperationAfterQuotationSeparation_ShouldApplyFormattingCorrectly()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-5",
                Operations = new List<Operation>
                {
                    // First: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-before-format",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Apply formatting to text after split
                    new Operation
                    {
                        Id = "format-after-split",
                        Op = OperationType.Format,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 11 }), // Format "World"
                        Props = new Dictionary<string, object> { { "italic", true } },
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            // Verify formatting is applied in the correct paragraph
            var italicRuns = paragraphs.SelectMany(p => p.Descendants<Run>())
                .Where(r => r.RunProperties?.Italic != null)
                .ToList();
            Assert.NotEmpty(italicRuns);
        }

        [Fact]
        public void ApplyPatch_CommentOperationAfterQuotationSeparation_ShouldAddCommentCorrectly()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-6",
                Operations = new List<Operation>
                {
                    // First: Split the paragraph
                    new Operation
                    {
                        Id = "quotation-split-before-comment",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Add comment to text after split
                    new Operation
                    {
                        Id = "comment-after-split",
                        Op = OperationType.CommentAdd,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 11 }), // Comment on "World"
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment added after quotation separation" }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");
        }

        [Fact]
        public void ApplyPatch_OperationsOnDifferentSegmentsAfterQuotationSeparation_ShouldHandleIndependently()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-after-8",
                Operations = new List<Operation>
                {
                    // First: Split first paragraph
                    new Operation
                    {
                        Id = "split-first-paragraph",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }), // After "Hello "
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Second: Modify first paragraph after split
                    new Operation
                    {
                        Id = "modify-first-after-split",
                        Op = OperationType.Insert,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 12, End = 12 }),
                        Text = " (modified)",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    },
                    // Third: Modify second paragraph (should be independent)
                    new Operation
                    {
                        Id = "modify-second-paragraph",
                        Op = OperationType.Insert,
                        Target = new Target("P002-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                        Text = "PREFIX: ",
                        Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 3, "Document should have at least 3 paragraphs after operations");

            // Verify both modifications were applied independently
            var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
            Assert.Contains("(modified)", allText);
            Assert.Contains("PREFIX:", allText);
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        [SaveWordDocument("invalid-operation-before-quotation-separation",
            SaveBefore = true,
            SaveAfter = true,
            IncludeTimestamp = true,
            Metadata = "Testing invalid operation before quotation separation")]
        public void ApplyPatch_InvalidOperationBeforeQuotationSeparation_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            ExecuteWithDocumentCreationAndSaving(
                () => CreateTestDocxWithMultipleParagraphs(TempFilePath),
                () =>
                {
                    var applier = new WordTextPatchApplier(_mockContext.Object);
                    var patch = new WordTextPatch
                    {
                        DocumentId = "test-doc-9",
                        Operations = new List<Operation>
                        {
                            // First: Invalid operation (non-existent segment)
                            new Operation
                            {
                                Id = "invalid-operation",
                                Op = OperationType.Insert,
                                Target = new Target("INVALID-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                                Text = "This should fail",
                                Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                            },
                            // Second: Valid quotation separation
                            new Operation
                            {
                                Id = "valid-quotation-split",
                                Op = OperationType.QuotationSeparate,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 }),
                                Revision = new Revision { Author = "John Doe", Date = DateTime.Now }
                            }
                        }
                    };

                    // Act
                    var result = applier.ApplyPatch(TempFilePath, patch);

                    // Assert
                    Assert.True(result); // Should fail due to invalid operation
                }
            );
        }

        [Fact]
        public void ApplyPatch_OnlyQuotationSeparationOperations_ShouldProcessCorrectly()
        {
            // Arrange
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            var patch = new WordTextPatch
            {
                DocumentId = "test-doc-10",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Id = "single-quotation-split",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 6, End = 6 })
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(TempFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document structure
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count == 3, "Document should have exactly 3 paragraphs after split");
        }

        /// <summary>
        /// 演示手动控制文档保存的用法
        /// Manual document saving control demonstration
        /// </summary>
        [Fact]
        [SaveWordDocument("manual-control-demo",
            SaveBefore = true,
            SaveAfter = true,
            SaveIntermediateSteps = true,
            IncludeTimestamp = true,
            Metadata = "Manual control of document saving at specific points")]
        public void ApplyPatch_ManualDocumentSavingControl_ShouldSaveAtSpecificPoints()
        {
            // Arrange - 先创建文档
            CreateTestDocxWithMultipleParagraphs(TempFilePath);
            SaveDocumentBefore(); // 手动保存初始状态（现在文档已创建）

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Step 1: Insert operation
            var patch1 = new WordTextPatch
            {
                DocumentId = "manual-demo-1",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Id = "manual-insert",
                        Op = OperationType.Insert,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                        Text = "MANUAL: ",
                        Revision = new Revision { Author = "Manual User", Date = DateTime.Now }
                    }
                }
            };

            // Act & Save intermediate step
            var result1 = applier.ApplyPatch(TempFilePath, patch1);
            Assert.True(result1);
            SaveIntermediateDocument("after-manual-insert"); // 保存中间步骤

            // Step 2: Quotation separation
            var patch2 = new WordTextPatch
            {
                DocumentId = "manual-demo-2",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Id = "manual-quotation-split",
                        Op = OperationType.QuotationSeparate,
                        Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 12, End = 12 }),
                        Revision = new Revision { Author = "Manual User", Date = DateTime.Now }
                    }
                }
            };

            var result2 = applier.ApplyPatch(TempFilePath, patch2);
            Assert.True(result2);
            SaveIntermediateDocument("after-quotation-split"); // 保存另一个中间步骤

            // Final verification and save
            using var doc = WordprocessingDocument.Open(TempFilePath, false);
            var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
            Assert.NotNull(paragraphs);
            Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

            var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
            Assert.Contains("MANUAL:", allText);

            SaveDocumentAfter(); // 手动保存最终状态
        }

        /// <summary>
        /// 演示所有文档保存注解的用法模式
        /// Demonstrates all usage patterns for document saving annotations
        /// </summary>
        [Fact]
        [SaveWordDocument("usage-demo",
            SaveBefore = true,
            SaveAfter = true,
            SaveIntermediateSteps = true,
            IncludeTimestamp = true,
            OutputDirectory = "TestResults/UsageDemo",
            Metadata = "Complete demonstration of SaveWordDocumentAttribute usage patterns")]
        public void ApplyPatch_SaveWordDocumentUsageDemo_ShouldDemonstrateAllPatterns()
        {
            // 模式1: 使用 ExecuteWithDocumentCreationAndSaving (推荐)
            // Pattern 1: Using ExecuteWithDocumentCreationAndSaving (Recommended)
            ExecuteWithDocumentCreationAndSaving(
                // 文档创建阶段 - Document creation phase
                () => CreateTestDocxWithMultipleParagraphs(TempFilePath),
                // 测试执行阶段 - Test execution phase
                () =>
                {
                    var applier = new WordTextPatchApplier(_mockContext.Object);

                    // Step 1: Insert operation
                    var patch1 = new WordTextPatch
                    {
                        DocumentId = "usage-demo-1",
                        Operations = new List<Operation>
                        {
                            new Operation
                            {
                                Id = "demo-insert",
                                Op = OperationType.Insert,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 0, End = 0 }),
                                Text = "DEMO: ",
                                Revision = new Revision { Author = "Demo User", Date = DateTime.Now }
                            }
                        }
                    };

                    var result1 = applier.ApplyPatch(TempFilePath, patch1);
                    Assert.True(result1);

                    // 保存中间步骤 - Save intermediate step
                    SaveIntermediateDocument("after-insert");

                    // Step 2: Quotation separation
                    var patch2 = new WordTextPatch
                    {
                        DocumentId = "usage-demo-2",
                        Operations = new List<Operation>
                        {
                            new Operation
                            {
                                Id = "demo-quotation-split",
                                Op = OperationType.QuotationSeparate,
                                Target = new Target("P001-0", new ApplyRevision.Model.Range { Start = 10, End = 10 }),
                                Revision = new Revision { Author = "Demo User", Date = DateTime.Now }
                            }
                        }
                    };

                    var result2 = applier.ApplyPatch(TempFilePath, patch2);
                    Assert.True(result2);

                    // 保存另一个中间步骤 - Save another intermediate step
                    SaveIntermediateDocument("after-quotation-split");

                    // Verify final result
                    using var doc = WordprocessingDocument.Open(TempFilePath, false);
                    var paragraphs = doc.MainDocumentPart?.Document?.Body?.Elements<Paragraph>().ToList();
                    Assert.NotNull(paragraphs);
                    Assert.True(paragraphs.Count >= 2, "Document should have at least 2 paragraphs after split");

                    var allText = string.Join(" ", paragraphs.Select(GetParagraphText));
                    Assert.Contains("DEMO:", allText);
                });
        }

        #endregion

        #region Helper Methods

        private static void CreateTestDocxWithMultipleParagraphs(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // First paragraph
            var paragraph1 = new Paragraph();
            paragraph1.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "P001"));
            var run1 = new Run(new Text("Hello World! This is the first paragraph for testing."));
            paragraph1.AppendChild(run1);
            mainPart.Document.Body.AppendChild(paragraph1);

            // Second paragraph
            var paragraph2 = new Paragraph();
            paragraph2.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "P002"));
            var run2 = new Run(new Text("This is the second paragraph for additional testing scenarios."));
            paragraph2.AppendChild(run2);
            mainPart.Document.Body.AppendChild(paragraph2);

            mainPart.Document.Save();
        }

        private static void CreateTestDocxWithLongParagraph(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // Long paragraph for multiple splits
            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "P001"));
            var run = new Run(new Text("This is a very long paragraph that contains multiple sentences and can be split at various positions to test quotation separation functionality with multiple split operations."));
            paragraph.AppendChild(run);
            mainPart.Document.Body.AppendChild(paragraph);

            mainPart.Document.Save();
        }

        private static string GetParagraphText(Paragraph paragraph)
        {
            return string.Concat(paragraph.Descendants<Text>().Select(t => t.Text));
        }

        #endregion

        #region IDisposable Implementation

        public override void Dispose()
        {
            base.Dispose();
        }

        #endregion
    }
}