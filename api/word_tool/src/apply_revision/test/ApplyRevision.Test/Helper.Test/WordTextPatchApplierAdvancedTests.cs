using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordComment = DocumentFormat.OpenXml.Wordprocessing.Comment;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// WordTextPatchApplier advanced test scenarios
    /// </summary>
    public class WordTextPatchApplierAdvancedTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;

        public WordTextPatchApplierAdvancedTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Lo<PERSON>).Returns(_mockLogger.Object);

            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "AdvancedTestResults");
            Directory.CreateDirectory(_testResultsDir);

            // Reset RunLocator singleton instance for test isolation
            // 重置 RunLocator 单例实例以确保测试隔离
#if DEBUG
            RunLocator.ResetInstanceForTesting();
#endif
        }

        #region Performance Tests

        [Fact]
        public void PerformanceTest_LargeNumberOfOperations_ShouldCompleteInReasonableTime()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "PerformanceTest_LargeOperations.DOCX");
            CreateLargeDocumentForPerformanceTest(testFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateLargeOperationsPatch();

            var startTime = DateTime.Now;

            // Act
            var result = applier.ApplyPatch(testFilePath, patch);

            // Assert
            var endTime = DateTime.Now;
            var duration = endTime - startTime;

            Assert.True(result);
            Assert.True(duration.TotalSeconds < 30, $"Operation took too long: {duration.TotalSeconds} seconds");

            // Verify some operations were applied
            using var doc = WordprocessingDocument.Open(testFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.NotEmpty(insertedRuns);
        }

        [Fact]
        public void PerformanceTest_VeryLargeDocument_ShouldHandleEfficiently()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "PerformanceTest_VeryLargeDocument.DOCX");
            CreateVeryLargeDocument(testFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateSimpleInsertPatch();

            var startTime = DateTime.Now;

            // Act
            var result = applier.ApplyPatch(testFilePath, patch);

            // Assert
            var endTime = DateTime.Now;
            var duration = endTime - startTime;

            Assert.True(result);
            Assert.True(duration.TotalSeconds < 60, $"Operation took too long: {duration.TotalSeconds} seconds");
        }

        #endregion

        #region Error Recovery Tests

        [Fact]
        public void ErrorRecovery_CorruptedDocument_ShouldHandleGracefully()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "ErrorRecovery_CorruptedDocument.DOCX");
            CreateCorruptedDocument(testFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateSimpleInsertPatch();

            // Act
            var result = applier.ApplyPatch(testFilePath, patch);

            // Assert
            Assert.False(result);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>()), Times.AtLeastOnce);
        }

        [Fact]
        public void ErrorRecovery_InvalidOperationRange_ShouldHandleGracefully()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "ErrorRecovery_InvalidRange.DOCX");
            CreateSimpleDocument(testFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateInvalidRangePatch();

            // Act
            var result = applier.ApplyPatch(testFilePath, patch);

            // Assert
            // Should handle gracefully without crashing
            Assert.True(result || !result); // Either succeeds or fails gracefully
        }

        #endregion

        #region Edge Case Tests

        [Fact]
        public void EdgeCase_UnicodeCharacters_ShouldHandleCorrectly()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "EdgeCase_Unicode.DOCX");
            CreateUnicodeDocument(testFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateUnicodeOperationsPatch();

            // Act
            var result = applier.ApplyPatch(testFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify Unicode characters are preserved
            using var doc = WordprocessingDocument.Open(testFilePath, false);
            var textContent = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("🚀", textContent);
            Assert.Contains("中文", textContent);
        }

        #endregion

        #region Helper Methods for Document Creation

        private void CreateLargeDocumentForPerformanceTest(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var body = new Body();

            // Create 50 paragraphs with different content
            for (int i = 1; i <= 50; i++)
            {
                var paragraph = new Paragraph(
                    new ParagraphProperties(
                        new ParagraphStyleId() { Val = "Normal" }
                    ),
                    new Run(
                        new RunProperties(
                            new FontSize() { Val = "24" },
                            new RunFonts() { Ascii = "Times New Roman" }
                        ),
                        new Text($"Paragraph {i}: This is test content for performance testing.")
                    )
                )
                {
                    ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue(i.ToString())
                };
                body.Append(paragraph);
            }

            mainPart.Document = new Document(body);
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateVeryLargeDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var largeText = string.Join(" ", Enumerable.Repeat("Lorem ipsum dolor sit amet.", 1000));

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text(largeText)
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateCorruptedDocument(string filePath)
        {
            // Create a file that looks like a DOCX but is corrupted
            File.WriteAllText(filePath, "This is not a valid DOCX file content");
        }

        private void CreateSimpleDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Simple test document content.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateUnicodeDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Unicode test: 中文 العربية Русский 日本語")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        #endregion

        #region Helper Methods for Patch Creation

        private WordTextPatch CreateLargeOperationsPatch()
        {
            var operations = new List<Operation>();

            // Create 50 insert operations
            for (int i = 1; i <= 50; i++)
            {
                operations.Add(new Operation
                {
                    Op = OperationType.Insert,
                    Target = new Target($"{i}-0"),
                    Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                    Text = $"Insert {i}: ",
                    Revision = new Revision
                    {
                        Author = $"Author {i % 5}",
                        Date = DateTime.Now.AddMinutes(-i)
                    }
                });
            }

            return new WordTextPatch
            {
                DocumentId = "large-operations-test",
                Operations = operations
            };
        }

        private WordTextPatch CreateSimpleInsertPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "simple-insert-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new Target("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Inserted: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateInvalidRangePatch()
        {
            return new WordTextPatch
            {
                DocumentId = "invalid-range-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new Target("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 1000, End = 2000 }, // Invalid range
                        Text = "Invalid range insert",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateUnicodeOperationsPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "unicode-ops-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new Target("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "🚀 Emoji and symbols: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        #endregion

        public void Dispose()
        {
            // Clean up test results directory if needed
            // Keeping files for inspection during development
        }
    }
}