using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using Amazon.Lambda.Core;
using Moq;
using Xunit;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Helper;
using ApplyRevision.Model;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// Custom JSON converter to handle ParaId field that can be either string or number in JSON
    /// </summary>
    public class ParaIdConverter : JsonConverter<string>
    {
        public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.String)
            {
                return reader.GetString() ?? string.Empty;
            }
            else if (reader.TokenType == JsonTokenType.Number)
            {
                return reader.GetInt64().ToString();
            }
            throw new JsonException($"Cannot convert {reader.TokenType} to string for ParaId");
        }

        public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value);
        }
    }

    /// <summary>
    /// Custom DocumentChange class with ParaId converter for testing
    /// </summary>
    public class DocumentChangeWithConverter
    {
        [JsonConverter(typeof(ParaIdConverter))]
        public required string ParaId { get; set; }
        public string Text { get; set; } = "";
        public List<RevisionInfo> ChangeText { get; set; } = [];
    }

    /// <summary>
    /// Tests for general document change application functionality.
    /// </summary>
    public class GeneralChangeApplicationTests : IDisposable
    {
        private readonly List<string> _temporaryFiles = [];
        private const string TestJsonFileName = "Penalties for breaching the Spam Act 2003 (Cth).json"; // Referenced from Files folder in output directory
        private const string TestDocxFileName = "Penalties for breaching the Spam Act 2003 (Cth).DOCX"; // Referenced from Files folder in output directory

        /// <summary>
        /// Tests that ApplyChanges correctly processes changes from an external JSON file
        /// and generates a DOCX document.
        /// </summary>
        [Fact]
        public void ApplyChanges_ShouldGenerateDocument_FromExternalJson()
        {
            // Arrange
            var documentPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", TestDocxFileName);
            // Assumes "Files" directory is copied to the test execution directory (e.g., bin/Debug/net8.0/Files/)
            var jsonFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", TestJsonFileName);

            // It's crucial for the test setup that this file exists.
            // Ensure the test_changes1.json file's "Copy to Output Directory" property is set in the .csproj file.
            Assert.True(File.Exists(jsonFilePath), $"Test JSON file not found: {jsonFilePath}. " +
                                                  "Ensure 'Copy to Output Directory' is set for the JSON file in the test project.");

            var documentChanges = LoadDocumentChangesFromJson(jsonFilePath);

            var mockContext = CreateMockLambdaContext();
            var changeApplier = new DocxChangeApplier(mockContext.Object);

            // Act
            changeApplier.ApplyChanges(documentPath, documentChanges);

            // Assert
            AssertDocumentGeneratedAndValid(documentPath);
        }

        /// <summary>
        /// Tests that JSON deserialization works correctly with mixed ParaId types (string and number)
        /// </summary>
        [Fact]
        public void LoadDocumentChangesFromJson_ShouldHandleMixedParaIdTypes()
        {
            // Arrange
            var jsonFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", TestJsonFileName);

            // Ensure the test file exists
            Assert.True(File.Exists(jsonFilePath), $"Test JSON file not found: {jsonFilePath}");

            // Act & Assert - should not throw exception
            var documentChanges = LoadDocumentChangesFromJson(jsonFilePath);

            // Verify we got some changes
            Assert.NotNull(documentChanges);
            Assert.NotEmpty(documentChanges);

            // Verify that all ParaId values are strings (even those that were numbers in JSON)
            foreach (var change in documentChanges)
            {
                Assert.NotNull(change.ParaId);
                Assert.IsType<string>(change.ParaId);
            }
        }

        /// <summary>
        /// Loads and deserializes document changes from the specified JSON file.
        /// </summary>
        /// <param name="jsonPath">Path to the JSON file.</param>
        /// <returns>A list of DocumentChange objects.</returns>
        private static List<DocumentChange> LoadDocumentChangesFromJson(string jsonPath)
        {
            var jsonString = File.ReadAllText(jsonPath);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                NumberHandling = JsonNumberHandling.AllowReadingFromString | JsonNumberHandling.WriteAsString,
                // Add custom converter to handle ParaId field type conversion
                Converters = {
                    new ParaIdConverter(),
                    new JsonStringEnumConverter() // Add enum converter for EditType and RevisionOperatorType
                }
            };

            // First deserialize to our custom class with converter
            var changesWithConverter = JsonSerializer.Deserialize<List<DocumentChangeWithConverter>>(jsonString, options);

            Assert.NotNull(changesWithConverter); // Ensure deserialization was successful
            Assert.NotEmpty(changesWithConverter); // Ensure the JSON file was not empty or malformed to produce no changes

            // Convert to the actual DocumentChange objects
            var changes = changesWithConverter.Select(c => new DocumentChange
            {
                ParaId = c.ParaId,
                Text = c.Text,
                ChangeText = c.ChangeText
            }).ToList();

            return changes;
        }

        /// <summary>
        /// Creates a mock Lambda context for testing.
        /// </summary>
        /// <returns>A mock ILambdaContext object.</returns>
        private static Mock<ILambdaContext> CreateMockLambdaContext()
        {
            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);
            return mockContext;
        }

        /// <summary>
        /// Asserts that the document was generated and is a valid Word document.
        /// </summary>
        /// <param name="documentPath">Path to the document to validate.</param>
        private static void AssertDocumentGeneratedAndValid(string documentPath)
        {
            Assert.True(File.Exists(documentPath), $"Document was not generated at path: {documentPath}");

            // Attempt to open the document to verify its integrity
            try
            {
                using var document = WordprocessingDocument.Open(documentPath, false);
                Assert.NotNull(document.MainDocumentPart); // Basic check for a valid main document part
            }
            catch (Exception ex)
            {
                Assert.True(false, $"The generated document at {documentPath} could not be opened or is invalid. Error: {ex.Message}");
            }
        }

        /// <summary>
        /// Cleans up temporary files created during testing.
        /// </summary>
        public void Dispose()
        {
            foreach (var tempFile in _temporaryFiles)
            {
                if (File.Exists(tempFile))
                {
                    try
                    {
                        // File.Delete(tempFile); // As per existing tests, deletion is commented out.
                    }
                    catch
                    {
                        // Ignore errors during cleanup in tests
                    }
                }
            }
            GC.SuppressFinalize(this); // Standard dispose pattern
        }
    }
}