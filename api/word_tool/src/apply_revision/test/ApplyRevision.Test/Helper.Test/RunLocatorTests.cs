using System;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using Common.Tests.Attributes;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    public class RunLocatorTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _tempFilePath;

        public RunLocatorTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);
            _tempFilePath = Path.GetTempFileName();

            // Reset RunLocator singleton instance for test isolation
            // 重置 RunLocator 单例实例以确保测试隔离
#if DEBUG
            RunLocator.ResetInstanceForTesting();
#endif
        }

        public void Dispose()
        {
            // Clean up temp file if it exists
            // 清理临时文件（如果存在）
            if (File.Exists(_tempFilePath))
            {
                File.Delete(_tempFilePath);
            }
            GC.SuppressFinalize(this);
        }

        [Fact]
        public void FindRun_WithValidSegId_ShouldReturnCorrectRun()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Act
            var run = runLocator.FindRun(doc, "12345-0");

            // Assert
            Assert.NotNull(run);
            Assert.Equal("Original text", run.InnerText);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void FindRun_WithInvalidSegIdFormat_ShouldReturnNull()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Act
            var run = runLocator.FindRun(doc, "invalid-format-without-dash");

            // Assert
            Assert.Null(run);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void FindRun_WithInvalidRunIndex_ShouldReturnNull()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Act
            var run = runLocator.FindRun(doc, "12345-abc");

            // Assert
            Assert.Null(run);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void FindRun_WithNonExistentParagraphId_ShouldReturnNull()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Act
            var run = runLocator.FindRun(doc, "nonexistent-0");

            // Assert
            Assert.Null(run);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void FindRun_WithOutOfRangeRunIndex_ShouldReturnNull()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Act
            var run = runLocator.FindRun(doc, "12345-99");

            // Assert
            Assert.Null(run);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void FindRun_WithNullDocumentBody_ShouldReturnNull()
        {
            // Arrange
            var tempFilePath = Path.GetTempFileName();

            // 创建一个没有Body的文档
            using (var doc = WordprocessingDocument.Create(tempFilePath, WordprocessingDocumentType.Document))
            {
                var mainPart = doc.AddMainDocumentPart();
                mainPart.Document = new Document(); // 创建Document但不添加Body
            }

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            // Act
            using (var doc = WordprocessingDocument.Open(tempFilePath, false))
            {
                var run = runLocator.FindRun(doc, "12345-0");

                // Assert
                Assert.Null(run);
            }

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void FindRun_ShouldUseCacheForRepeatedParagraphLookups()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Act - First call should find paragraph and cache it
            var run1 = runLocator.FindRun(doc, "12345-0");

            // Modify the document to remove the paragraph (to verify cache is used)
            doc.MainDocumentPart.Document.Body.RemoveAllChildren();

            // Act - Second call should use cached paragraph
            var run2 = runLocator.FindRun(doc, "12345-0");

            // Assert
            Assert.NotNull(run1);
            Assert.NotNull(run2);
            Assert.Equal("Original text", run1.InnerText);
            Assert.Equal("Original text", run2.InnerText);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void ClearCache_ShouldRemoveCachedParagraphs()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // Cache a paragraph
            var run1 = runLocator.FindRun(doc, "12345-0");
            Assert.NotNull(run1);

            // Modify the document to remove the paragraph
            doc.MainDocumentPart.Document.Body.RemoveAllChildren();

            // Clear the cache
            runLocator.ClearCache();

            // Try to find the run again (should fail now)
            var run2 = runLocator.FindRun(doc, "12345-0");

            // Assert
            Assert.Null(run2);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void LocatePosition_WithValidPosition_ShouldReturnCorrectTextAndOffset()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var run = doc.MainDocumentPart.Document.Body.Descendants<Run>().First();

            // Act
            var (text, offset) = runLocator.LocatePosition(run, 5);

            // Assert
            Assert.NotNull(text);
            Assert.Equal("Original text", text.Text);
            Assert.Equal(5, offset);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void LocatePosition_WithPositionAtEnd_ShouldReturnLastTextAndCorrectOffset()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var run = doc.MainDocumentPart.Document.Body.Descendants<Run>().First();

            // Act
            var (text, offset) = runLocator.LocatePosition(run, 13); // "Original text" length

            // Assert
            Assert.NotNull(text);
            Assert.Equal("Original text", text.Text);
            Assert.Equal(13, offset);

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void LocatePosition_WithPositionBeyondEnd_ShouldReturnLastTextAndItsLength()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var run = doc.MainDocumentPart.Document.Body.Descendants<Run>().First();

            // Act
            var (text, offset) = runLocator.LocatePosition(run, 100); // Beyond text length

            // Assert
            Assert.NotNull(text);
            Assert.Equal("Original text", text.Text);
            Assert.Equal(13, offset); // Length of "Original text"

            // Cleanup
            File.Delete(_tempFilePath);
        }

        [Fact]
        public void LocatePosition_WithMultipleTextElements_ShouldReturnCorrectTextAndOffset()
        {
            // Arrange
            var tempFilePath = Path.GetTempFileName();
            using (var doc = WordprocessingDocument.Create(tempFilePath, WordprocessingDocumentType.Document))
            {
                var mainPart = doc.AddMainDocumentPart();
                mainPart.Document = new Document(new Body());

                var paragraph = new Paragraph();
                var run = new Run();

                // Add multiple Text elements to the Run
                run.AppendChild(new Text("First"));
                run.AppendChild(new Text("Second"));
                run.AppendChild(new Text("Third"));

                paragraph.AppendChild(run);
                mainPart.Document.Body.AppendChild(paragraph);
                mainPart.Document.Save();
            }

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var docToTest = WordprocessingDocument.Open(tempFilePath, false);
            var runToTest = docToTest.MainDocumentPart.Document.Body.Descendants<Run>().First();

            // Act - Position in the second Text element
            var (text, offset) = runLocator.LocatePosition(runToTest, 7); // "First" + 2 chars into "Second"

            // Assert
            Assert.NotNull(text);
            Assert.Equal("Second", text.Text);
            Assert.Equal(2, offset);

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void LocatePosition_WithEmptyRun_ShouldReturnNullAndZero()
        {
            // Arrange
            var tempFilePath = Path.GetTempFileName();
            using (var doc = WordprocessingDocument.Create(tempFilePath, WordprocessingDocumentType.Document))
            {
                var mainPart = doc.AddMainDocumentPart();
                mainPart.Document = new Document(new Body());

                var paragraph = new Paragraph();
                var run = new Run(); // Empty run with no Text elements

                paragraph.AppendChild(run);
                mainPart.Document.Body.AppendChild(paragraph);
                mainPart.Document.Save();
            }

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var docToTest = WordprocessingDocument.Open(tempFilePath, false);
            var runToTest = docToTest.MainDocumentPart.Document.Body.Descendants<Run>().First();

            // Act
            var (text, offset) = runLocator.LocatePosition(runToTest, 0);

            // Assert
            Assert.Null(text);
            Assert.Equal(0, offset);

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void FindRun_WithInsertedRun_ShouldLocateRunInRevisionElement()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var paragraph = doc.MainDocumentPart!.Document!.Body!.Elements<Paragraph>().First();

            // Add an InsertedRun with a Run inside
            var insertedRun = new InsertedRun
            {
                Id = "1",
                Author = "Test Author",
                Date = DateTime.Now
            };
            var innerRun = new Run(new Text("Inserted text"));
            insertedRun.Append(innerRun);
            paragraph.Append(insertedRun);

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            // Act - Try to find the Run inside InsertedRun (should be index 1, after the original Run)
            var result = runLocator.FindRun(doc, "12345-1");

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Inserted text", result.Elements<Text>().First().Text);
            Assert.Same(innerRun, result);
        }

        [UnsupportedFeatureFact("DeleteRun is not included in convert transformation rules")]
        public void FindRun_WithDeletedRun_ShouldLocateRunInRevisionElement()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var paragraph = doc.MainDocumentPart!.Document!.Body!.Elements<Paragraph>().First();

            // Add a DeletedRun with a Run inside
            var deletedRun = new DeletedRun
            {
                Id = "2",
                Author = "Test Author",
                Date = DateTime.Now
            };
            var innerRun = new Run(new DeletedText("Deleted text"));
            deletedRun.Append(innerRun);
            paragraph.Append(deletedRun);

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            // Act - Try to find the Run inside DeletedRun (should be index 1, after the original Run)
            var result = runLocator.FindRun(doc, "12345-1");

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Deleted text", result.Elements<DeletedText>().First().Text);
            Assert.Same(innerRun, result);
        }

        [UnsupportedFeatureFact("DeleteRun is not included in convert transformation rules")]
        public void FindRun_WithMixedRevisionElements_ShouldLocateCorrectRun()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var paragraph = doc.MainDocumentPart!.Document!.Body!.Elements<Paragraph>().First();

            // Add multiple revision elements
            var insertedRun = new InsertedRun { Id = "1", Author = "Author1", Date = DateTime.Now };
            insertedRun.Append(new Run(new Text("First inserted")));
            paragraph.Append(insertedRun);

            var deletedRun = new DeletedRun { Id = "2", Author = "Author2", Date = DateTime.Now };
            deletedRun.Append(new Run(new DeletedText("Deleted content")));
            paragraph.Append(deletedRun);

            var moveToRun = new MoveToRun { Id = "3", Author = "Author3", Date = DateTime.Now };
            moveToRun.Append(new Run(new Text("Moved content")));
            paragraph.Append(moveToRun);

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            // Act & Assert - Test finding different runs by index
            // Index 0: Original run
            var originalRun = runLocator.FindRun(doc, "12345-0");
            Assert.NotNull(originalRun);
            Assert.Equal("Original text", originalRun.Elements<Text>().First().Text);

            // Index 1: Run inside InsertedRun
            var insertedRunElement = runLocator.FindRun(doc, "12345-1");
            Assert.NotNull(insertedRunElement);
            Assert.Equal("First inserted", insertedRunElement.Elements<Text>().First().Text);

            // Index 2: Run inside DeletedRun
            var deletedRunElement = runLocator.FindRun(doc, "12345-2");
            Assert.NotNull(deletedRunElement);
            Assert.Equal("Deleted content", deletedRunElement.Elements<DeletedText>().First().Text);

            // Index 3: Run inside MoveToRun
            var moveToRunElement = runLocator.FindRun(doc, "12345-3");
            Assert.NotNull(moveToRunElement);
            Assert.Equal("Moved content", moveToRunElement.Elements<Text>().First().Text);
        }

        [Fact]
        public void FindRunEnhanced_WithRevisionElements_ShouldHandleComplexStructure()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var paragraph = doc.MainDocumentPart!.Document!.Body!.Elements<Paragraph>().First();

            // Create a complex structure with nested revision elements
            var insertedRun = new InsertedRun { Id = "1", Author = "Author1", Date = DateTime.Now };
            var innerRun1 = new Run(new Text("Part 1"));
            var innerRun2 = new Run(new Text(" Part 2"));
            insertedRun.Append(innerRun1);
            insertedRun.Append(innerRun2);
            paragraph.Append(insertedRun);

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            // Act - Test the enhanced method directly
            var firstInnerRun = runLocator.FindRunEnhanced(doc, "12345-1");
            var secondInnerRun = runLocator.FindRunEnhanced(doc, "12345-2");

            // Assert
            Assert.NotNull(firstInnerRun);
            Assert.Equal("Part 1", firstInnerRun.Elements<Text>().First().Text);
            Assert.Same(innerRun1, firstInnerRun);

            Assert.NotNull(secondInnerRun);
            Assert.Equal(" Part 2", secondInnerRun.Elements<Text>().First().Text);
            Assert.Same(innerRun2, secondInnerRun);
        }

        [Fact]
        public void FindRunWithTextForOperation_WithInsertedRun_ShouldCalculateCorrectPositions()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var paragraph = doc.MainDocumentPart!.Document!.Body!.Elements<Paragraph>().First();

            // Add text before and after the insertion point
            var originalRun = paragraph.Elements<Run>().First();
            originalRun.Elements<Text>().First().Text = "Start ";

            var insertedRun = new InsertedRun { Id = "1", Author = "Author", Date = DateTime.Now };
            insertedRun.Append(new Run(new Text("INSERTED ")));
            paragraph.Append(insertedRun);

            var endRun = new Run(new Text("End"));
            paragraph.Append(endRun);

            var operation = new Operation
            {
                Op = OperationType.Insert,
                Target = new("12345-1"),
                Range = new ApplyRevision.Model.Range { Start = 6, End = 6 }, // Position at start of "INSERTED "
                Text = "NEW "
            };

            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            // Act
            var result = runLocator.FindRunWithTextForOperation(doc, operation, out int relativeStart, out int relativeEnd);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(6, relativeStart); // Should be at start of "INSERTED " text
            Assert.Equal(6, relativeEnd);
            Assert.Equal("Start ", result.Elements<Text>().First().Text);
        }

        [Fact]
        public void GetRangeRunBySegmentId_WithSplitRuns_ShouldFindCorrectStartAndEndRuns()
        {
            // Arrange - Create a scenario where original run "hello world" was split into multiple runs
            // after delete+insert operations: "he" + "abc" + " world"
            var tempFilePath = Path.GetTempFileName();
            CreateSplitRunTestDocx(tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(tempFilePath, false);

            // Act - CommentAdd with range [0, 5] should span "he" (2 chars) + "abc" (3 chars)
            var result = runLocator.GetRangeRunBySegmentId(doc, "12345-0", 0, 5);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.StartRun);
            Assert.NotNull(result.EndRun);
            
            // StartRun should be "he" with relative position 0
            Assert.Equal("he", RunLocator.GetRunText(result.StartRun));
            Assert.Equal(0, result.RelativeStartPosition);
            
            // EndRun should be "abc" with relative position 3 (end of "abc")
            Assert.Equal("abc", RunLocator.GetRunText(result.EndRun));
            Assert.Equal(3, result.RelativeEndPosition);
            
            // Should span multiple runs
            Assert.True(result.SpansMultipleRuns);

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void GetRangeRunBySegmentId_WithSplitRuns_SingleRunRange_ShouldFindSameStartAndEndRun()
        {
            // Arrange - Test range within a single split run
            var tempFilePath = Path.GetTempFileName();
            CreateSplitRunTestDocx(tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(tempFilePath, false);

            // Act - CommentAdd with range [0, 2] should be entirely within "he" run
            var result = runLocator.GetRangeRunBySegmentId(doc, "12345-0", 0, 2);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.StartRun);
            Assert.NotNull(result.EndRun);
            
            // Both should be the "he" run
            Assert.Equal("he", RunLocator.GetRunText(result.StartRun));
            Assert.Equal("he", RunLocator.GetRunText(result.EndRun));
            Assert.Equal(0, result.RelativeStartPosition);
            Assert.Equal(2, result.RelativeEndPosition);
            
            // Should NOT span multiple runs
            Assert.False(result.SpansMultipleRuns);

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void GetRangeRunBySegmentId_WithSplitRuns_CrossingDeletedRun_ShouldSkipDeletedContent()
        {
            // Arrange - Test range that would cross deleted content
            var tempFilePath = Path.GetTempFileName();
            CreateSplitRunWithDeletedTestDocx(tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(tempFilePath, false);

            // Act - CommentAdd with range [0, 5] should span "he" + "abc", skipping deleted "llo"
            var result = runLocator.GetRangeRunBySegmentId(doc, "12345-0", 0, 5);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.StartRun);
            Assert.NotNull(result.EndRun);
            
            // StartRun should be "he"
            Assert.Equal("he", RunLocator.GetRunText(result.StartRun));
            Assert.Equal(0, result.RelativeStartPosition);
            
            // EndRun should be "abc" (skipping the deleted "llo" run)
            Assert.Equal("abc", RunLocator.GetRunText(result.EndRun));
            Assert.Equal(3, result.RelativeEndPosition);
            
            // Should span multiple runs
            Assert.True(result.SpansMultipleRuns);

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void GetRangeRunBySegmentId_WithFinegrainedSplit_ShouldFindCorrectEndRun()
        {
            // Arrange - Test the specific scenario: 'hello' -> 'h' + 'e' + 'llo', finding 'ello' [1,5]
            var tempFilePath = Path.GetTempFileName();
            CreateFinegrainedSplitTestDocx(tempFilePath);
            var runLocator = RunLocator.GetInstance(_mockContext.Object);

            using var doc = WordprocessingDocument.Open(tempFilePath, false);

            // Act - Find 'ello' in the split 'hello' with range [1, 5]
            var result = runLocator.GetRangeRunBySegmentId(doc, "12345-0", 1, 5);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.StartRun);
            Assert.NotNull(result.EndRun);
            
            // StartRun should be "e" with relative position 0
            Assert.Equal("e", RunLocator.GetRunText(result.StartRun));
            Assert.Equal(0, result.RelativeStartPosition);
            
            // EndRun should be "llo" with relative position 3 (end of "llo")
            Assert.Equal("llo", RunLocator.GetRunText(result.EndRun));
            Assert.Equal(3, result.RelativeEndPosition);
            
            // Should span multiple runs
            Assert.True(result.SpansMultipleRuns);

            // Cleanup
            File.Delete(tempFilePath);
        }

        private void CreateSplitRunTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // Simulate the result after delete+insert operations on "hello world"
            // Original: "hello world" -> After operations: "he" + "abc" + " world"
            var run1 = new Run(new Text("he"));              // Original run (index 0)
            var run2 = new Run(new Text("abc"));             // Inserted run (index 1) 
            var run3 = new Run(new Text(" world"));          // Remaining text (index 2)

            paragraph.AppendChild(run1);
            paragraph.AppendChild(run2);
            paragraph.AppendChild(run3);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateSplitRunWithDeletedTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // Simulate: "hello world" -> "he" + <deleted>"llo"</deleted> + "abc" + " world"
            var run1 = new Run(new Text("he"));              // Original run (index 0)
            
            // Add deleted run (should be skipped by GetRunsByConvertRules)
            var deletedRun = new DeletedRun { Id = "del1", Author = "Test", Date = DateTime.Now };
            deletedRun.Append(new Run(new DeletedText("llo")));
            
            var run2 = new Run(new Text("abc"));             // Inserted run (index 1)
            var run3 = new Run(new Text(" world"));          // Remaining text (index 2)

            paragraph.AppendChild(run1);
            paragraph.AppendChild(deletedRun);
            paragraph.AppendChild(run2);
            paragraph.AppendChild(run3);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateFinegrainedSplitTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // Simulate: "hello" -> "h" + "e" + "llo"
            // Position mapping: h(0-0), e(1-1), llo(2-4)
            // Range [1,5] should span e(0-0) + llo(0-2)
            var run1 = new Run(new Text("h"));               // Position 0
            var run2 = new Run(new Text("e"));               // Position 1  
            var run3 = new Run(new Text("llo"));             // Position 2-4

            paragraph.AppendChild(run1);
            paragraph.AppendChild(run2);
            paragraph.AppendChild(run3);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Original text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }
    }
}