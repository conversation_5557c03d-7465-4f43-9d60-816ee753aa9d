using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordComment = DocumentFormat.OpenXml.Wordprocessing.Comment;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// 诊断偏移跟踪问题的简单测试
    /// </summary>
    public class OffsetTrackerDiagnosticTest : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;

        public OffsetTrackerDiagnosticTest()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "DiagnosticResults");
            Directory.CreateDirectory(_testResultsDir);
        }

        [Fact]
        public void Test_SimpleTwoInsertsAtPosition0_ShouldWork()
        {
            // Arrange
            var filePath = Path.Combine(_testResultsDir, "TwoInsertsTest.docx");
            var modifiedPath = Path.Combine(_testResultsDir, "TwoInsertsTest_Modified.docx");

            CreateSimpleDocument(filePath, "Hello World");
            File.Copy(filePath, modifiedPath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Create a simple patch with only 2 operations
            var patch = new WordTextPatch
            {
                DocumentId = "two-inserts-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "First ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "1",
                            Text = "First Comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Second ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "2",
                            Text = "Second Comment"
                        }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(modifiedPath, patch);

            // Assert
            Assert.True(result, "Patch application should succeed");

            // Verify document content
            using var doc = WordprocessingDocument.Open(modifiedPath, false);
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            // Log the actual results for debugging
            _mockLogger.Object.LogInformation($"Found {comments?.Count ?? 0} comments");
            if (comments != null)
            {
                foreach (var comment in comments)
                {
                    _mockLogger.Object.LogInformation($"Comment: {comment.InnerText}");
                }
            }

            // Check text content
            var textContent = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            _mockLogger.Object.LogInformation($"Final text content: '{textContent}'");

            // We expect 2 comments
            Assert.NotNull(comments);
            Assert.Equal(2, comments.Count);

            // Check if text contains both inserts
            Assert.Contains("First", textContent);
            Assert.Contains("Second", textContent);
        }

        [Fact]
        public void Test_OffsetTrackerLogic_Standalone()
        {
            // Test the offset tracker logic in isolation
            var tracker = new PatchOffsetTracker(_mockContext.Object);

            // Simulate three operations at position 0
            // Original position: 0, Actual position after adjustment: 0
            tracker.RecordOffset("1-0", 0, 0, 5); // Insert "First" (5 chars)

            // Second operation: original pos 0, should be adjusted to 5
            var adjustedPos1 = tracker.AdjustPosition("1-0", 0);
            tracker.RecordOffset("1-0", 0, adjustedPos1, 6); // Insert "Second" (6 chars)

            // Third operation: original pos 0, should be adjusted to 11
            var adjustedPos2 = tracker.AdjustPosition("1-0", 0);

            // Log the results
            _mockLogger.Object.LogInformation($"First adjustment: 0 → {adjustedPos1}");
            _mockLogger.Object.LogInformation($"Second adjustment: 0 → {adjustedPos2}");

            // Verify the logic
            Assert.Equal(5, adjustedPos1); // Should be moved by first insert
            Assert.Equal(11, adjustedPos2); // Should be moved by both inserts
        }

        private void CreateSimpleDocument(string filePath, string content)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text(content)
                )
            );

            // 正确设置段落ID - 使用Word 2010命名空间
            paragraph.SetAttribute(new DocumentFormat.OpenXml.OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "1"));

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        public void Dispose()
        {
            // 保留测试结果文件用于调试
        }
    }
}