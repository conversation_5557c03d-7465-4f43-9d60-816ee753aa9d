using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;
using ApplyRevision.Helper;

namespace ApplyRevision.Tests.Helper.Test
{
    public class DocxChangeApplierTests
    {
        [Fact]
        public void ApplyChanges_ShouldUpdateParagraphsCorrectly()
        {
            // Arrange
            var tempFilePath = Path.GetTempFileName();
            CreateTestDocx(tempFilePath);

            var docChanges = new List<DocumentChange>
            {
                new DocumentChange
                {
                    ParaId = "12345",
                    ChangeText = new List<RevisionInfo>
                    {
                        new RevisionInfo
                        {
                            RevId = "2",
                            EditType = EditType.inplace,
                            Ops = new List<RevisionOperator> {
                                new RevisionOperator {
                                    Op = RevisionOperatorType.ins,
                                    Text = "Test",
                                    Author = "AI",
                                    CommentStart = true,
                                    CommentEnd = true
                                }
                            }
                        },
                        new RevisionInfo
                        {
                            RevId = "3",
                            EditType = EditType.suffix,
                            Ops = new List<RevisionOperator> {
                                new RevisionOperator {
                                    Op = RevisionOperatorType.ins,
                                    Text = "Test",
                                    Author = "AI",
                                    CommentStart = true,
                                    CommentEnd = true
                                }
                            }
                        },
                        new RevisionInfo
                        {
                            RevId = "4",
                            EditType = EditType.prefix,
                            Ops = new List<RevisionOperator> {
                                new RevisionOperator {
                                    Op = RevisionOperatorType.ins,
                                    Text = "Test",
                                    Author = "styling_agent",
                                    CommentStart = true,
                                    CommentEnd = true
                                }
                            }
                        },
                    }
                }
            };

            var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            mockContext.Setup(c => c.Logger).Returns(mockLogger.Object);

            var applier = new DocxChangeApplier(mockContext.Object);

            // Act
            applier.ApplyChanges(tempFilePath, docChanges);

            // Assert
            using var doc = WordprocessingDocument.Open(tempFilePath, false);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().FirstOrDefault();
            Assert.NotNull(paragraph);
            var revison = paragraph.Elements<OpenXmlElement>().Where(item => item is InsertedRun).FirstOrDefault();
            Assert.NotNull(revison);
            Assert.Equal("Test", revison.InnerText);

            // Cleanup
            File.Delete(tempFilePath);
        }

        [Fact]
        public void ApplyChanges_ShouldConvertNewlinesToBreaks()
        {
            var tempFilePath = Path.GetTempFileName();
            CreateTestDocx(tempFilePath);

            var docChanges = new List<DocumentChange>
            {
                new DocumentChange
                {
                    ParaId = "12345",
                    ChangeText = new List<RevisionInfo>
                    {
                        new RevisionInfo
                        {
                            RevId = "2",
                            EditType = EditType.inplace,
                            Ops = new List<RevisionOperator>
                            {
                                new RevisionOperator
                                {
                                    Op = RevisionOperatorType.ins,
                                    Text = "foo\n\nbar",
                                    Author = "AI",
                                    CommentStart = false,
                                    CommentEnd = false
                                }
                            }
                        }
                    }
                }
            };

            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);
            var applier = new DocxChangeApplier(mockContext.Object);

            // Act
            applier.ApplyChanges(tempFilePath, docChanges);

            // Assert
            using var doc = WordprocessingDocument.Open(tempFilePath, false);
            var insertedRun = doc
                .MainDocumentPart
                .Document
                .Body
                .Descendants<InsertedRun>()
                .FirstOrDefault();
            Assert.NotNull(insertedRun);

            var run = insertedRun.GetFirstChild<Run>();
            var children = run.Elements().ToList();

            // should be Text("foo"), Break, Text("bar")
            Assert.IsType<Text>(children[0]);
            Assert.Equal("foo", ((Text)children[0]).Text);

            Assert.IsType<Break>(children[1]);

            Assert.IsType<Text>(children[2]);
            Assert.Equal("bar", ((Text)children[2]).Text);

            Assert.Equal(3, children.Count);

            // Cleanup
            File.Delete(tempFilePath);
        }

        private InsertedRun GetInsertRevison(string id)
        {
            var text = new Text("Test") { Space = SpaceProcessingModeValues.Preserve };
            var run = new Run();
            run.AddChild(text);

            var insertedRun = new InsertedRun()
            {
                Id = id,
                Author = "Test"
            };

            insertedRun.Append(run);
            return insertedRun;
        }


        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body(new Paragraph(new DocumentFormat.OpenXml.Wordprocessing.Run(new Text("Original text")))));
            var paragraph = mainPart.Document.Body.Elements<Paragraph>().First();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var insertedRun2 = GetInsertRevison("2");
            var insertedRun3 = GetInsertRevison("3");
            var insertedRun4 = GetInsertRevison("4");
            paragraph.Append(insertedRun2);
            paragraph.Append(insertedRun3);
            paragraph.Append(insertedRun4);
            mainPart.Document.Save();
        }
    }
}
