using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordComment = DocumentFormat.OpenXml.Wordprocessing.Comment;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    public class WordTextPatchApplierWithTrackingTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;
        private readonly WordTextPatchApplier _applier;

        public WordTextPatchApplierWithTrackingTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "TestResults", "TrackingApplier");
            Directory.CreateDirectory(_testResultsDir);

            _applier = new WordTextPatchApplier(_mockContext.Object);
        }

        [Fact]
        public void ApplyPatch_ConsecutiveInsertsWithoutComments_ShouldSucceed()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "ConsecutiveInsertsNoComments.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "ConsecutiveInsertsNoComments_Modified.DOCX");

            CreateTestDocument(testFilePath, "Hello World");
            File.Copy(testFilePath, modifiedFilePath, true);

            var patch = new WordTextPatch
            {
                DocumentId = "consecutive-inserts-tracking",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "First ",
                        Revision = new Revision { Author = "Author1", Date = DateTime.Now }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Second ",
                        Revision = new Revision { Author = "Author2", Date = DateTime.Now.AddMinutes(1) }
                    }
                }
            };

            // Act
            var result = _applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Equal(2, insertedRuns.Count);

            var textContent = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("First", textContent);
            Assert.Contains("Second", textContent);
            Assert.Contains("Hello World", textContent);
        }

        [Fact]
        public void ApplyPatch_ConsecutiveInsertsWithComments_ShouldSucceed()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "ConsecutiveInsertsWithComments.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "ConsecutiveInsertsWithComments_Modified.DOCX");

            CreateTestDocument(testFilePath, "Hello World");
            File.Copy(testFilePath, modifiedFilePath, true);

            var patch = new WordTextPatch
            {
                DocumentId = "consecutive-inserts-comments-tracking",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "First ",
                        Revision = new Revision { Author = "Author1", Date = DateTime.Now },
                        Comment = new ApplyRevision.Model.Comment { CommentId = "c1", Text = "First comment" }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Second ",
                        Revision = new Revision { Author = "Author2", Date = DateTime.Now.AddMinutes(1) },
                        Comment = new ApplyRevision.Model.Comment { CommentId = "c2", Text = "Second comment" }
                    }
                }
            };

            // Act
            var result = _applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.Equal(2, insertedRuns.Count);
            Assert.NotNull(comments);
            Assert.Equal(2, comments.Count);

            var textContent = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("First", textContent);
            Assert.Contains("Second", textContent);
            Assert.Contains("Hello World", textContent);
        }

        [Fact]
        public void ApplyPatch_MultipleOperationsOnSameSegment_ShouldSucceed()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "MultipleOperationsSameSegment.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "MultipleOperationsSameSegment_Modified.DOCX");

            CreateTestDocument(testFilePath, "The quick brown fox");
            File.Copy(testFilePath, modifiedFilePath, true);

            var patch = new WordTextPatch
            {
                DocumentId = "multiple-ops-tracking",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "FIRST ",
                        Revision = new Revision { Author = "Author1", Date = DateTime.Now },
                        Comment = new ApplyRevision.Model.Comment { CommentId = "c1", Text = "First insertion" }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 4, End = 4 },
                        Text = " VERY",
                        Revision = new Revision { Author = "Author2", Date = DateTime.Now.AddMinutes(1) },
                        Comment = new ApplyRevision.Model.Comment { CommentId = "c2", Text = "Second insertion" }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 16, End = 16 },
                        Text = " JUMPS",
                        Revision = new Revision { Author = "Author3", Date = DateTime.Now.AddMinutes(2) },
                        Comment = new ApplyRevision.Model.Comment { CommentId = "c3", Text = "Third insertion" }
                    }
                }
            };

            // Act
            var result = _applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.Equal(3, insertedRuns.Count);
            Assert.NotNull(comments);
            Assert.Equal(3, comments.Count);

            var textContent = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("FIRST", textContent);
            Assert.Contains("VERY", textContent);
            Assert.Contains("JUMPS", textContent);
            Assert.Contains("FIRST The  VERYquick brown  JUMPSfox", textContent);
        }

        [Fact]
        public void ApplyPatch_NoTrackingMarkersLeftAfterProcessing_ShouldBeClean()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "CleanupTest.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "CleanupTest_Modified.DOCX");

            CreateTestDocument(testFilePath, "Test Document");
            File.Copy(testFilePath, modifiedFilePath, true);

            var patch = new WordTextPatch
            {
                DocumentId = "cleanup-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "New ",
                        Revision = new Revision { Author = "Author1", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = _applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify no tracking attributes remain
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var allRuns = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            foreach (var run in allRuns)
            {
                var trackingAttributes = run.GetAttributes()
                    .Where(attr => attr.LocalName == "runId" && attr.NamespaceUri == "http://schemas.lexis.com/word/2024/tracking")
                    .ToList();

                Assert.Empty(trackingAttributes);
            }
        }

        [Fact]
        public void ApplyPatch_WithEmptyPatch_ShouldReturnTrue()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "EmptyPatchTest.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "EmptyPatchTest_Modified.DOCX");

            CreateTestDocument(testFilePath, "Test Document");
            File.Copy(testFilePath, modifiedFilePath, true);

            var patch = new WordTextPatch
            {
                DocumentId = "empty-test",
                Operations = new List<Operation>()
            };

            // Act
            var result = _applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ApplyPatch_WithInvalidFile_ShouldReturnFalse()
        {
            // Arrange
            var patch = new WordTextPatch { DocumentId = "test", Operations = new List<Operation>() };

            // Act
            var result = _applier.ApplyPatch("nonexistent.docx", patch);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ApplyPatch_InsertWithComment_ShouldCreateCommentAndRevision()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "InsertWithComment_Test.DOCX");
            CreateSimpleTestDocument(testFilePath);

            var patch = new WordTextPatch
            {
                DocumentId = "test-insert-with-comment",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "New ",
                        Revision = new Revision
                        {
                            Author = "Test User",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment_test",
                            Text = "This is a test comment for the insert operation"
                        }
                    }
                }
            };

            // Act
            var result = _applier.ApplyPatch(testFilePath, patch);

            // Assert
            Assert.True(result, "Patch application should succeed");

            // Verify document structure
            using var doc = WordprocessingDocument.Open(testFilePath, false);

            // Check for InsertedRun
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Contains("New", insertedRuns[0].InnerText);

            // Check for comment components
            var commentRangeStarts = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = doc.MainDocumentPart.Document.Body.Descendants<CommentReference>().ToList();

            Assert.Single(commentRangeStarts);
            Assert.Single(commentRangeEnds);
            Assert.Single(commentReferences);

            // Verify comment range IDs match
            Assert.Equal(commentRangeStarts[0].Id.Value, commentRangeEnds[0].Id.Value);
            Assert.Equal(commentRangeStarts[0].Id.Value, commentReferences[0].Id.Value);

            // Check for comment in comments part
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.Single(comments);
            Assert.Contains("This is a test comment", comments[0].InnerText);

            // Verify final text
            var allText = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.StartsWith("New Hello World", allText);

            _mockLogger.Verify(l => l.LogInformation(It.Is<string>(s => s.Contains("Successfully applied insert operation with comment"))), Times.Once);
        }

        [Fact]
        public void ApplyPatch_MultipleInsertsAtSamePosition_ShouldHandleCorrectly()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "MultipleInsertsAtSamePosition_Tracking.DOCX");
            CreateSimpleTestDocument(testFilePath);

            var patch = new WordTextPatch
            {
                DocumentId = "test-multiple-inserts-same-position",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "First ",
                        Revision = new Revision
                        {
                            Author = "Author 1",
                            Date = DateTime.Now.AddMinutes(-10)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment1",
                            Text = "First insert comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Second ",
                        Revision = new Revision
                        {
                            Author = "Author 2",
                            Date = DateTime.Now.AddMinutes(-5)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment2",
                            Text = "Second insert comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Third ",
                        Revision = new Revision
                        {
                            Author = "Author 3",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment3",
                            Text = "Third insert comment"
                        }
                    }
                }
            };

            // Act
            var result = _applier.ApplyPatch(testFilePath, patch);

            // Assert
            Assert.True(result, "Patch application should succeed");

            // Verify document structure
            using var doc = WordprocessingDocument.Open(testFilePath, false);

            // Check for InsertedRuns - should have 3
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Equal(3, insertedRuns.Count);

            // Check for comments - should have 3
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.Equal(3, comments.Count);

            // Check for comment components - should have matching counts
            var commentRangeStarts = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = doc.MainDocumentPart.Document.Body.Descendants<CommentReference>().ToList();

            Assert.Equal(3, commentRangeStarts.Count);
            Assert.Equal(3, commentRangeEnds.Count);
            Assert.Equal(3, commentReferences.Count);

            // Verify final text contains all inserted text
            var allText = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("First", allText);
            Assert.Contains("Second", allText);
            Assert.Contains("Third", allText);
            Assert.Contains("Hello World", allText);

            // Due to tracking-based processing, all operations are applied successfully
            // The exact order may vary based on implementation, but all content should be present
            _mockLogger.Verify(l => l.LogInformation(It.Is<string>(s => s.Contains("Patch application completed successfully"))), Times.Once);
        }

        private void CreateTestDocument(string filePath, string content)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text(content)
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateSimpleTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Hello World")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        public void Dispose()
        {
            // Keep test files for inspection if needed
        }
    }
}