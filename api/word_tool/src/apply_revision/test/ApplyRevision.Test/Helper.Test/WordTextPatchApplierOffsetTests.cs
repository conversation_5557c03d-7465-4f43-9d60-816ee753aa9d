using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordComment = DocumentFormat.OpenXml.Wordprocessing.Comment;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// 专门测试 WordTextPatchApplier 中的偏移跟踪问题
    /// </summary>
    public class WordTextPatchApplierOffsetTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;

        public WordTextPatchApplierOffsetTests()
        {
            // 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // 创建测试结果目录
            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "OffsetTestResults");
            Directory.CreateDirectory(_testResultsDir);
        }

        [Fact]
        public void Test_MultipleInsertsAtSamePosition_ShouldHandleOffsetsCorrectly()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "MultipleInsertsTest.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "MultipleInsertsTest_Modified.DOCX");

            CreateSimpleTestDocument(testFilePath, "Hello World");
            File.Copy(testFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMultipleInsertsAtSamePositionPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList() ?? new List<WordComment>();

            // 应该有3个评论
            Assert.Equal(3, comments.Count);

            // 验证文档文本内容是否正确
            var body = doc.MainDocumentPart.Document.Body;
            var allText = string.Join("", body.Descendants<Text>().Select(t => t.Text));

            // 预期结果：在位置0插入了三个文本，应该是 "ThirdSecondFirstHello World"
            _mockLogger.Object.LogInformation($"Final document text: '{allText}'");

            // 验证包含所有插入的文本
            Assert.Contains("First", allText);
            Assert.Contains("Second", allText);
            Assert.Contains("Third", allText);
            Assert.Contains("Hello World", allText);
        }

        [Fact]
        public void Test_MixedOperationsWithOffsets_ShouldHandleCorrectly()
        {
            // Arrange
            var testFilePath = Path.Combine(_testResultsDir, "MixedOperationsTest.DOCX");
            var modifiedFilePath = Path.Combine(_testResultsDir, "MixedOperationsTest_Modified.DOCX");

            CreateSimpleTestDocument(testFilePath, "The quick brown fox jumps");
            File.Copy(testFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMixedOperationsPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList() ?? new List<WordComment>();

            // 应该有预期数量的评论
            Assert.True(comments.Count >= 2);

            // 验证操作是否正确应用
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            Assert.NotEmpty(insertedRuns);
            Assert.NotEmpty(deletedRuns);
        }

        private void CreateSimpleTestDocument(string filePath, string text)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text(text)
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private WordTextPatch CreateMultipleInsertsAtSamePositionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "offset-test-multiple-inserts",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "First",
                        Revision = new Revision
                        {
                            Author = "Author 1",
                            Date = DateTime.Now.AddMinutes(-10)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment1",
                            Text = "First insert comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Second",
                        Revision = new Revision
                        {
                            Author = "Author 2",
                            Date = DateTime.Now.AddMinutes(-5)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment2",
                            Text = "Second insert comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Third",
                        Revision = new Revision
                        {
                            Author = "Author 3",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment3",
                            Text = "Third insert comment"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateMixedOperationsPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "offset-test-mixed-operations",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "NEW: ",
                        Revision = new Revision
                        {
                            Author = "Insert Author",
                            Date = DateTime.Now.AddMinutes(-10)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "insert_comment",
                            Text = "Inserted at beginning"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 10, End = 15 }, // Delete "brown"
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Delete Author",
                            Date = DateTime.Now.AddMinutes(-5)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "delete_comment",
                            Text = "Deleted brown"
                        }
                    }
                }
            };
        }

        public void Dispose()
        {
            // 保留测试结果以便检查
        }
    }
}