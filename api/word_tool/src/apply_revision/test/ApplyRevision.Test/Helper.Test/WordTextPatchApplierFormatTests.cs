using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using Common.Tests.Attributes;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// WordTextPatchApplier类的Format操作测试
    /// Tests for Format operations in WordTextPatchApplier class
    /// </summary>
    public class WordTextPatchApplierFormatTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _tempFilePath;
        private readonly string _tempDirPath;

        public WordTextPatchApplierFormatTests()
        {
            // 设置模拟对象
            // Setup mock objects
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // 创建临时目录和文件
            // Create temporary directory and files
            _tempDirPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(_tempDirPath);
            _tempFilePath = Path.Combine(_tempDirPath, "test.docx");
        }

        #region Format操作测试 - 基本格式化 / Basic Format Operation Tests

        [Fact]
        public void ApplyOperation_WithFormatBold_ShouldApplyBoldFormatting()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithBoldFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            // 查找应用了格式的Run
            // Find the Run with applied formatting
            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Bold != null);

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatItalic_ShouldApplyItalicFormatting()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithItalicFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Italic != null);

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatUnderline_ShouldApplyUnderlineFormatting()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithUnderlineFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Underline?.Val?.Value == UnderlineValues.Single);

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatColor_ShouldApplyColorFormatting()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithColorFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Color?.Val?.Value == "FF0000");

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatFontSize_ShouldApplyFontSizeFormatting()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFontSizeFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            // NOTICE: Word uses half-points for font size, so 24pt is represented as 48
            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.FontSize?.Val?.Value == "48");

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatFontFamily_ShouldApplyFontFamilyFormatting()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFontFamilyFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.RunFonts?.Ascii?.Value == "Arial");

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region Format操作测试 - 组合格式化 / Combined Format Operation Tests

        [Fact]
        public void ApplyOperation_WithMultipleFormats_ShouldApplyAllFormats()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithMultipleFormats();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Bold != null &&
                r.RunProperties?.Italic != null &&
                r.RunProperties?.Color?.Val?.Value == "FF0000");

            Assert.NotNull(formattedRun);
            Assert.Equal("Original", formattedRun.InnerText);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatOnPartialText_ShouldApplyFormatToRange()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithPartialTextFormat();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            // 应该有多个Run，其中一个应用了格式
            // Should have multiple Runs, one with applied formatting
            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Bold != null);

            Assert.NotNull(formattedRun);
            Assert.Equal("inal", formattedRun.InnerText); // "Original text"中的"inal"部分

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region Format操作测试 - 特殊情况 / Special Cases Format Operation Tests

        [Fact]
        public void ApplyOperation_WithFormatOnEmptyRange_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatOnEmptyRange();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatOnInvalidRange_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatOnInvalidRange();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            _mockLogger.Verify(l => l.LogWarning(It.IsAny<string>()), Times.AtLeastOnce);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatOnNonExistentSegment_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatOnNonExistentSegment();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            _mockLogger.Verify(l => l.LogWarning(It.IsAny<string>()), Times.AtLeastOnce);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithFormatRemoval_ShouldRemoveFormatting()
        {
            // Arrange
            CreateFormattedDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatRemoval();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var runs = doc.MainDocumentPart.Document.Body.Descendants<Run>().ToList();

            // 应该没有粗体格式
            // Should have no bold formatting
            var boldRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Bold != null);

            Assert.Null(boldRun);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region Format操作测试 - 修订标记中的格式化 / Format Operations within Revision Marks

        [Fact]
        public void ApplyOperation_WithFormatInInsertedRun_ShouldApplyFormatCorrectly()
        {
            // Arrange
            CreateDocumentWithInsertedRun(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatInInsertedRun();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();

            Assert.Single(insertedRuns);
            var runs = insertedRuns[0].Descendants<Run>().ToList();
            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Bold != null);

            Assert.NotNull(formattedRun);

            // Cleanup
            CleanupTempFiles();
        }

        [UnsupportedFeatureFact("无法对deleteRun内的文本进行格式化")]
        public void ApplyOperation_WithFormatInDeletedRun_ShouldApplyFormatCorrectly()
        {
            // Arrange
            CreateDocumentWithDeletedRun(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatInDeletedRun();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            Assert.Single(deletedRuns);
            var runs = deletedRuns[0].Descendants<Run>().ToList();
            var formattedRun = runs.FirstOrDefault(r =>
                r.RunProperties?.Bold != null);

            Assert.NotNull(formattedRun);

            // Cleanup
            CleanupTempFiles();
        }

        [UnsupportedFeatureFact("无法对deleteRun内的文本进行格式化")]
        public void ApplyOperation_WithFormatCrossDeletedRun_ShouldApplyFormatCorrectly()
        {
            // Arrange
            CreateDocumentWithCrossDeletedRuns(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormatCrossDeletedRun();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            // Verify document content
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // 验证第一个普通Run (12345-0) 有格式
            // Verify first normal run (12345-0) has formatting
            var firstRun = doc.MainDocumentPart.Document.Body.Descendants<Run>()
                .Where(r => r.Parent is Paragraph && r.RunProperties?.Bold != null)
                .FirstOrDefault();
            Assert.NotNull(firstRun);
            Assert.Equal("Before ", firstRun.InnerText);
            Assert.NotNull(firstRun.RunProperties?.Bold);
            Assert.Equal("0000FF", firstRun.RunProperties?.Color?.Val?.Value);

            // 验证DeletedRun内的Run (12345-1) 有格式
            // Verify run inside DeletedRun (12345-1) has formatting
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
            var deletedRunInner = deletedRuns[0].Descendants<Run>().FirstOrDefault();
            Assert.NotNull(deletedRunInner);
            Assert.Equal("deleted ", deletedRunInner.InnerText);
            Assert.NotNull(deletedRunInner.RunProperties?.Bold);
            Assert.Equal("0000FF", deletedRunInner.RunProperties?.Color?.Val?.Value);

            // 验证第二个普通Run (12345-2) 有格式
            // Verify second normal run (12345-2) has formatting
            var allNormalRuns = doc.MainDocumentPart.Document.Body.Descendants<Run>()
                .Where(r => r.Parent is Paragraph && r.RunProperties?.Bold != null).ToList();
            Assert.True(allNormalRuns.Count >= 2);
            var secondRun = allNormalRuns.LastOrDefault();
            Assert.NotNull(secondRun);
            Assert.Equal("after", secondRun.InnerText);
            Assert.NotNull(secondRun.RunProperties?.Bold);
            Assert.Equal("0000FF", secondRun.RunProperties?.Color?.Val?.Value);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region Format操作测试 - 错误处理 / Error Handling Format Operation Tests

        [Fact]
        public void ApplyOperation_WithInvalidFormatProperties_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithInvalidFormatProperties();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            _mockLogger.Verify(l => l.LogWarning(It.IsAny<string>()), Times.AtLeastOnce);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithNullFormatProperties_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithNullFormatProperties();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region 辅助方法 - 创建测试文档 / Helper Methods - Create Test Documents

        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Original text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateFormattedDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run();
            var runProps = new RunProperties();
            runProps.AppendChild(new Bold() { Val = OnOffValue.FromBoolean(true) });
            runProps.AppendChild(new Italic() { Val = OnOffValue.FromBoolean(true) });
            run.AppendChild(runProps);
            run.AppendChild(new Text("Formatted text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateDocumentWithInsertedRun(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var insertedRun = new InsertedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            var run = new Run(new Text("Inserted Text"));
            insertedRun.AppendChild(run);
            paragraph.AppendChild(insertedRun);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateDocumentWithDeletedRun(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var deletedRun = new DeletedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            var run = new Run(new DeletedText("Deleted Text"));
            deletedRun.AppendChild(run);
            paragraph.AppendChild(deletedRun);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        /// <summary>
        /// 创建包含多个Run的复杂文档结构: run1 + delrun + run2
        /// Creates a complex document structure with multiple runs: run1 + delrun + run2
        /// </summary>
        private void CreateDocumentWithCrossDeletedRuns(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // Run1: 第一个普通Run
            // Run1: First normal run
            var run1 = new Run(new Text("Before "));
            paragraph.AppendChild(run1);

            // DeletedRun: 中间的已删除Run  
            // DeletedRun: Middle deleted run
            var deletedRun = new DeletedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };
            var deletedRunInner = new Run(new DeletedText("deleted "));
            deletedRun.AppendChild(deletedRunInner);
            paragraph.AppendChild(deletedRun);

            // Run2: 第二个普通Run
            // Run2: Second normal run  
            var run2 = new Run(new Text("after"));
            paragraph.AppendChild(run2);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CleanupTempFiles()
        {
            try
            {
                if (File.Exists(_tempFilePath))
                {
                    File.Delete(_tempFilePath);
                }

                if (Directory.Exists(_tempDirPath))
                {
                    Directory.Delete(_tempDirPath, true);
                }
            }
            catch
            {
                // 忽略清理过程中的异常
                // Ignore exceptions during cleanup
            }
        }

        #endregion

        #region 辅助方法 - 创建测试补丁 / Helper Methods - Create Test Patches

        private WordTextPatch CreatePatchWithBoldFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithItalicFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "italic", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithUnderlineFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "underline", "single" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithColorFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "color", "FF0000" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFontSizeFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "fontSize", "24" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFontFamilyFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "fontFamily", "Arial" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithMultipleFormats()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true },
                            { "italic", true },
                            { "color", "FF0000" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithPartialTextFormat()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 4, End = 8 }, // "inal" in "Original text"
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormatOnEmptyRange()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 5, End = 5 }, // Empty range
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormatOnInvalidRange()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 50, End = 100 }, // Invalid range
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormatOnNonExistentSegment()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("99999-0"), // Non-existent segment
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 5 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormatRemoval()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 14 },
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", false }, // Remove bold formatting
                            { "italic", false } // Remove italic formatting
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormatInInsertedRun()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormatInDeletedRun()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 7 },
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        /// <summary>
        /// 创建跨越已删除Run的格式化补丁
        /// Creates a format patch that crosses deleted runs
        /// 测试场景: run1(Before ) + delrun(deleted ) + run2(after) 
        /// 格式化范围: 从位置0到12，跨越所有三个run
        /// Test scenario: run1(Before ) + delrun(deleted ) + run2(after)
        /// Format range: from position 0 to 12, spanning all three runs
        /// </summary>
        private WordTextPatch CreatePatchWithFormatCrossDeletedRun()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 12 }, // 跨越整个文本范围 / Spans entire text range  
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true },
                            { "color", "0000FF" } // 蓝色 / Blue color
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithInvalidFormatProperties()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Props = new Dictionary<string, object>
                        {
                            { "invalidProperty", "invalidValue" },
                            { "color", "INVALID_COLOR" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithNullFormatProperties()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Format,
                        Target = new("12345-0"),
                        Text = "", // Format操作不需要文本内容 / Format operations don't need text content
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Props = null, // Null properties
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        #endregion
    }
}