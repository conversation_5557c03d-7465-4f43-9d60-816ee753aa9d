using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using Amazon.Lambda.Core;
using Moq;
using Xunit;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Helper;
using ApplyRevision.Model;

namespace ApplyRevision.Tests.Helper
{
    public class HyperlinkInsertionTests
    {
        [Fact]
        public void ApplyChanges_ShouldInsertHyperlinkField_ForInplaceRevision()
        {
            // ───── Arrange ─────
            string tmp = Path.GetTempFileName();
            CreateDocxWithSingleRevision(tmp, out string paraId, out string revId);

            var docChanges = new List<DocumentChange>
            {
                new DocumentChange
                {
                    ParaId = paraId,
                    ChangeText = new List<RevisionInfo>
                    {
                        new RevisionInfo
                        {
                            RevId = revId,
                            EditType = EditType.inplace,
                            Ops = new List<RevisionOperator>
                            {
                                // new RevisionOperator
                                // {
                                //     Op   = RevisionOperatorType.ins,
                                //     Text = " xxx",
                                //     Author = "styling_agent",
                                //     Comment = "xxx",
                                //     CommentStart = true,
                                //     CommentEnd = true,
                                // },
                                new RevisionOperator
                                {
                                    Op   = RevisionOperatorType.ins,
                                    Text = "Australian Competition and Consumer",
                                    Author = "original_author"
                                },
                                new RevisionOperator
                                {
                                    Op   = RevisionOperatorType.del,
                                    Text = " Law",          // ← 空格 + Law
                                    Author = "styling_agent",
                                    Comment = "xxx",
                                    CommentStart = true,
                                    CommentEnd = true,
                                }
                            },
                            Link = new List<RevisionLink>
                            {
                                new RevisionLink
                                {
                                    // DisplayText = "Australian Competition and Consumer Law",
                                    DisplayText = " Law",
                                    Comment = "xxx",
                                    Uri = "https://example.com/test"
                                }
                            }
                        }
                    }
                }
            };

            var ctx = new Mock<ILambdaContext>();
            // var mockContext = new Mock<ILambdaContext>();
            var mockLogger = new Mock<ILambdaLogger>();
            ctx.Setup(c => c.Logger).Returns(mockLogger.Object);
            new DocxChangeApplier(ctx.Object).ApplyChanges(tmp, docChanges);

            // ───── Assert ─────
            using var doc = WordprocessingDocument.Open(tmp, false);
            var body = doc.MainDocumentPart.Document.Body;

            // 1) FieldChar begin / end should be paired
            int begins = body.Descendants<FieldChar>()
                            .Count(f => f.FieldCharType == FieldCharValues.Begin);
            int ends = body.Descendants<FieldChar>()
                            .Count(f => f.FieldCharType == FieldCharValues.End);
            Assert.False(begins > 0);
            Assert.Equal(begins, ends);

            // 2) instrText includes HYPERLINK
            var instr = body.Descendants<FieldCode>()
                            .FirstOrDefault(i => i.Text.Contains("HYPERLINK"));
            Assert.NotNull(instr);
            Assert.Contains("https://example.com/test", instr!.Text);

            // 3) display text is correct
            string mergedText = string.Concat(
                body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("Australian Competition and Consumer Law", mergedText);

            File.Delete(tmp);
        }

        // ───────── helper: generate docx ─────────
        private static void CreateDocxWithSingleRevision(
                string path,
                out string paraId,
                out string revId
            )
        {
            paraId = "P12345";
            revId = "7";

            using var doc =
                WordprocessingDocument.Create(path,
                    WordprocessingDocumentType.Document);
            var main = doc.AddMainDocumentPart();
            var para = new Paragraph(
                        new Run(new Text("Australian Competition and Consumer Law")
                            )
                            );
            para.SetAttribute(new OpenXmlAttribute("w14", "paraId",
                "http://schemas.microsoft.com/office/word/2010/wordml", paraId));

            // 包进一个 InsertedRun，模拟 in-place 新修订
            var insRun = new InsertedRun
            {
                Id = revId,
                Author = "original_author"
            };
            insRun.Append(
                new Run(new Text(
                    "Australian Competition and Consumer Law")));
            para.Append(insRun);

            main.Document = new Document(new Body(para));
            main.Document.Save();
        }
    }
}
