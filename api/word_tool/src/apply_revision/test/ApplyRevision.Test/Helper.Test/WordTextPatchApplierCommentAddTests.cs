using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordComment = DocumentFormat.OpenXml.Wordprocessing.Comment;
using Moq;
using Xunit;
using Common.Tests.Attributes;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// Integration tests for WordTextPatchApplier CommentAdd operations
    /// Tests the complete flow from patch application to document modification
    /// Saves before/after files to TestResults directory for comparison
    /// </summary>
    public class WordTextPatchApplierCommentAddTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;

        public WordTextPatchApplierCommentAddTests()
        {
            // Setup mock objects
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // Create test results directory
            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "TestResults", "CommentAdd");
            Directory.CreateDirectory(_testResultsDir);
        }

        [Fact]
        public void ApplyPatch_WithSingleCommentAdd_ShouldAddCommentSuccessfully()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "SingleCommentAdd_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "SingleCommentAdd_Modified.docx");

            CreateSimpleTestDocument(originalFilePath, "This is a test document for comment addition.");
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateSingleCommentAddPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content and comments
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.NotNull(comments);
            Assert.Single(comments);
            Assert.Contains(comments, c => c.InnerText.Contains("This is a test comment"));

            // Verify comment range markers exist in document
            var paragraph = doc.MainDocumentPart!.Document.Body!.Elements<Paragraph>().First();
            var commentRangeStarts = paragraph.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = paragraph.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = paragraph.Descendants<CommentReference>().ToList();

            Assert.NotEmpty(commentRangeStarts);
            Assert.NotEmpty(commentRangeEnds);
            Assert.NotEmpty(commentReferences);
            Assert.Equal(commentRangeStarts.Count, commentRangeEnds.Count);
            Assert.Equal(commentRangeStarts.Count, commentReferences.Count);
        }

        [Fact]
        public void ApplyPatch_WithMultipleCommentAdd_ShouldAddAllCommentsSuccessfully()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "MultipleCommentAdd_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "MultipleCommentAdd_Modified.docx");

            CreateMultiParagraphTestDocument(originalFilePath);
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMultipleCommentAddPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content and comments
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.NotNull(comments);
            Assert.Equal(3, comments.Count);

            // Verify specific comment contents
            Assert.Contains(comments, c => c.InnerText.Contains("First paragraph comment"));
            Assert.Contains(comments, c => c.InnerText.Contains("Second paragraph comment"));
            Assert.Contains(comments, c => c.InnerText.Contains("Third paragraph comment"));

            // Verify comment range markers exist for all paragraphs
            var paragraphs = doc.MainDocumentPart!.Document.Body!.Elements<Paragraph>().ToList();
            var totalCommentRangeStarts = paragraphs.SelectMany(p => p.Descendants<CommentRangeStart>()).Count();
            var totalCommentRangeEnds = paragraphs.SelectMany(p => p.Descendants<CommentRangeEnd>()).Count();
            var totalCommentReferences = paragraphs.SelectMany(p => p.Descendants<CommentReference>()).Count();

            Assert.Equal(3, totalCommentRangeStarts);
            Assert.Equal(3, totalCommentRangeEnds);
            Assert.Equal(3, totalCommentReferences);
        }

        [UnsupportedFeatureFact("CommentAdd on specific text range")]
        public void ApplyPatch_WithCommentAddOnSpecificTextRange_ShouldAddCommentToCorrectPosition()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "CommentAddTextRange_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "CommentAddTextRange_Modified.docx");

            CreateSimpleTestDocument(originalFilePath, "The quick brown fox jumps over the lazy dog.");
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateTextRangeCommentAddPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content and comments
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.NotNull(comments);
            Assert.Single(comments);
            Assert.Contains(comments, c => c.InnerText.Contains("Comment on 'brown fox'"));

            // Verify comment positioning - should be around "brown fox" text
            var paragraph = doc.MainDocumentPart!.Document.Body!.Elements<Paragraph>().First();
            var commentRangeStart = paragraph.Descendants<CommentRangeStart>().First();
            var commentRangeEnd = paragraph.Descendants<CommentRangeEnd>().First();

            Assert.NotNull(commentRangeStart);
            Assert.NotNull(commentRangeEnd);
            Assert.Equal(commentRangeStart.Id?.Value, commentRangeEnd.Id?.Value);
        }

        [Fact]
        public void ApplyPatch_WithCommentAddAndRevisionInfo_ShouldPreserveRevisionMetadata()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "CommentAddRevision_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "CommentAddRevision_Modified.docx");

            CreateSimpleTestDocument(originalFilePath, "Document with revision tracking enabled.");
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateCommentAddWithRevisionPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify document content and comments with revision info
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.NotNull(comments);
            Assert.Single(comments);

            var comment = comments.First();
            Assert.Equal("Test Reviewer", comment.Author?.Value);
            Assert.NotNull(comment.Date?.Value);
            Assert.Contains("This is a comment with revision metadata", comment.InnerText);
        }

        [Fact]
        public void ApplyPatch_WithMultipleRunsInParagraph_ShouldAddCommentsToSpecificRuns()
        {
            // Arrange - Create a document with a paragraph containing multiple runs
            var originalFilePath = Path.Combine(_testResultsDir, "MultipleRuns_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "MultipleRuns_Modified.docx");

            CreateMultipleRunsTestDocument(originalFilePath);
            File.Copy(originalFilePath, modifiedFilePath, true);

            // Debug: Verify RunLocator can find all runs before applying patch
            using (var debugDoc = WordprocessingDocument.Open(modifiedFilePath, false))
            {
                var runLocator = RunLocator.GetInstance(_mockContext.Object);
                var run1 = runLocator.FindRun(debugDoc, "1-0");
                var run2 = runLocator.FindRun(debugDoc, "1-1");
                var run3 = runLocator.FindRun(debugDoc, "1-2");

                Assert.NotNull(run1);
                Assert.NotNull(run2);
                Assert.NotNull(run3);

                // Verify the runs contain expected text
                Assert.Contains("first run", run1.InnerText);
                Assert.Contains("second run", run2.InnerText);
                Assert.Contains("third run", run3.InnerText);
            }

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMultipleRunsCommentPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify comments were added correctly
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            Assert.NotNull(comments);
            Assert.Equal(3, comments.Count); // Should have 3 comments for 3 different runs

            // Verify comment content and authors
            var firstComment = comments.FirstOrDefault(c => c.InnerText.Contains("Comment on first run"));
            var secondComment = comments.FirstOrDefault(c => c.InnerText.Contains("Comment on second run"));
            var thirdComment = comments.FirstOrDefault(c => c.InnerText.Contains("Comment on third run"));

            Assert.NotNull(firstComment);
            Assert.NotNull(secondComment);
            Assert.NotNull(thirdComment);

            Assert.Equal("Reviewer A", firstComment.Author?.Value);
            Assert.Equal("Reviewer B", secondComment.Author?.Value);
            Assert.Equal("Reviewer C", thirdComment.Author?.Value);

            // Verify comment references were added to the document
            var commentRangeStarts = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = doc.MainDocumentPart.Document.Body.Descendants<CommentReference>().ToList();

            Assert.Equal(3, commentRangeStarts.Count);
            Assert.Equal(3, commentRangeEnds.Count);
            Assert.Equal(3, commentReferences.Count);

            // Verify that each comment has matching IDs across start, end, and reference
            foreach (var comment in comments)
            {
                var commentId = comment.Id?.Value;
                Assert.NotNull(commentId);

                var matchingStart = commentRangeStarts.FirstOrDefault(crs => crs.Id?.Value == commentId);
                var matchingEnd = commentRangeEnds.FirstOrDefault(cre => cre.Id?.Value == commentId);
                var matchingRef = commentReferences.FirstOrDefault(cr => cr.Id?.Value == commentId);

                Assert.NotNull(matchingStart);
                Assert.NotNull(matchingEnd);
                Assert.NotNull(matchingRef);
            }
        }

        [Fact]
        public void ApplyPatch_WithEmptyCommentText_ShouldFailGracefully()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "EmptyCommentText_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "EmptyCommentText_Modified.docx");

            CreateSimpleTestDocument(originalFilePath, "Test document for empty comment validation.");
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateEmptyCommentTextPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.False(result); // Should fail due to empty comment text

            // Verify no comments were added
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();

            // Should be empty or null since operation failed
            Assert.True(comments == null || !comments.Any());
        }

        [Fact]
        public void ApplyPatch_WithCommentAddFollowedByTextOperations_ShouldAdjustPositionsCorrectly()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "CommentAddWithTextOps_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "CommentAddWithTextOps_Modified.docx");

            // Create document with text: "The quick brown fox jumps over the lazy dog."
            CreateSimpleTestDocument(originalFilePath, "The quick brown fox jumps over the lazy dog.");
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateCommentAddWithSubsequentTextOperationsPatch();

            // Debug: Check Target.Count
            var firstOp = patch.Operations.First();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            if (!result)
            {
                // Log failure details for debugging
                _mockContext.Object.Logger.LogError("Patch application failed - check logs for details");
            }
            Assert.True(result);

            // Verify the final document content and structure
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);

            // Verify comment was added
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.Single(comments);
            Assert.Contains("Comment on 'quick'", comments.First().InnerText);

            // Verify text operations were applied correctly after comment offset adjustment
            var paragraph = doc.MainDocumentPart!.Document.Body!.Elements<Paragraph>().First();
            var allText = string.Join("", paragraph.Descendants<Text>().Select(t => t.Text));

            // Expected final text after all operations:
            // 1. Original: "The quick brown fox jumps over the lazy dog."
            // 2. CommentAdd at [4,9] on "quick" (adds comment reference run, offset +1)
            // 3. Insert "very " at [10,10] -> adjusted to [11,11] due to comment offset -> "The quick very brown fox jumps over the lazy dog."
            // 4. Delete "fox " at [21,25] -> adjusted to [22,26] due to previous operations -> "The quick very brown jumps over the lazy dog."
            // 5. Replace "lazy" at [35,39] -> adjusted position -> "The quick very brown jumps over the sleepy dog."

            // Note: The exact text verification depends on how comment reference runs are inserted
            // We focus on verifying that operations were applied in correct positions
            Assert.Contains("very", allText); // Insert operation succeeded
            Assert.DoesNotContain("fox", allText); // Delete operation succeeded
            Assert.Contains("sleepy", allText); // Replace operation succeeded
            Assert.DoesNotContain("lazy", allText); // Original "lazy" was replaced

            // Verify comment structure
            var commentRangeStarts = paragraph.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = paragraph.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = paragraph.Descendants<CommentReference>().ToList();

            Assert.Single(commentRangeStarts);
            Assert.Single(commentRangeEnds);
            Assert.Single(commentReferences);
        }

        [Fact]
        public void ApplyPatch_WithMultipleCommentAddsAndTextOperations_ShouldHandleComplexOffsetCalculations()
        {
            // Arrange
            var originalFilePath = Path.Combine(_testResultsDir, "MultipleCommentsWithTextOps_Original.docx");
            var modifiedFilePath = Path.Combine(_testResultsDir, "MultipleCommentsWithTextOps_Modified.docx");

            // Create document with longer text for complex operations
            CreateSimpleTestDocument(originalFilePath, "First sentence here. Second sentence follows. Third sentence ends.");
            File.Copy(originalFilePath, modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMultipleCommentsWithTextOperationsPatch();

            // Act
            var result = applier.ApplyPatch(modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // Verify the final document content and structure
            using var doc = WordprocessingDocument.Open(modifiedFilePath, false);

            // Verify all comments were added
            var comments = doc.MainDocumentPart?.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.Equal(3, comments.Count);

            // Verify comment contents
            Assert.Contains(comments, c => c.InnerText.Contains("Comment on First"));
            Assert.Contains(comments, c => c.InnerText.Contains("Comment on Second"));
            Assert.Contains(comments, c => c.InnerText.Contains("Comment on Third"));

            // Verify text operations were applied correctly with proper offset adjustments
            var paragraph = doc.MainDocumentPart!.Document.Body!.Elements<Paragraph>().First();
            var allText = string.Join("", paragraph.Descendants<Text>().Select(t => t.Text));

            // Expected transformations (positions adjusted for comment offsets):
            // 1. Original: "First sentence here. Second sentence follows. Third sentence ends."
            // 2. CommentAdd at [0,5] on "First" (offset +1)
            // 3. CommentAdd at [20,26] -> adjusted to [21,27] on "Second" (offset +1)
            // 4. CommentAdd at [45,50] -> adjusted to [47,52] on "Third" (offset +1)
            // 5. Insert "very " at [6,6] -> adjusted to [9,9] -> "First very sentence here. Second sentence follows. Third sentence ends."
            // 6. Delete "here. " at [19,25] -> adjusted position -> "First very sentence Second sentence follows. Third sentence ends."
            // 7. Replace "follows" at [43,50] -> adjusted position -> "First very sentence Second sentence continues. Third sentence ends."

            Assert.Contains("very", allText); // Insert succeeded
            Assert.DoesNotContain("here.", allText); // Delete succeeded
            Assert.Contains("continues", allText); // Replace succeeded
            Assert.DoesNotContain("follows", allText); // Original text was replaced

            // Verify comment structure - should have 3 sets of comment markers
            var commentRangeStarts = paragraph.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = paragraph.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = paragraph.Descendants<CommentReference>().ToList();

            Assert.Equal(3, commentRangeStarts.Count);
            Assert.Equal(3, commentRangeEnds.Count);
            Assert.Equal(3, commentReferences.Count);
        }

        #region Helper Methods - Create Test Documents

        private void CreateSimpleTestDocument(string filePath, string text)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text(text)
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateMultiParagraphTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph1 = new Paragraph(
                new ParagraphProperties(new ParagraphStyleId() { Val = "Normal" }),
                new Run(new RunProperties(new FontSize() { Val = "24" }), new Text("First paragraph content."))
            )
            { ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1") };

            var paragraph2 = new Paragraph(
                new ParagraphProperties(new ParagraphStyleId() { Val = "Normal" }),
                new Run(new RunProperties(new FontSize() { Val = "24" }), new Text("Second paragraph content."))
            )
            { ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("2") };

            var paragraph3 = new Paragraph(
                new ParagraphProperties(new ParagraphStyleId() { Val = "Normal" }),
                new Run(new RunProperties(new FontSize() { Val = "24" }), new Text("Third paragraph content."))
            )
            { ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("3") };

            mainPart.Document = new Document(new Body(paragraph1, paragraph2, paragraph3));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        /// <summary>
        /// Create a test document with a single paragraph containing multiple runs
        /// 创建包含单个段落但有多个run的测试文档
        /// </summary>
        /// <param name="filePath">File path to create the document</param>
        private void CreateMultipleRunsTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            // Create a paragraph with multiple runs
            var paragraph = new Paragraph(
                new ParagraphProperties(new ParagraphStyleId() { Val = "Normal" })
            )
            { ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1") };

            // Add first run with bold text
            var run1 = new Run(
                new RunProperties(
                    new FontSize() { Val = "24" },
                    new Bold()
                ),
                new Text("This is the first run. ")
            );

            // Add second run with italic text
            var run2 = new Run(
                new RunProperties(
                    new FontSize() { Val = "24" },
                    new Italic()
                ),
                new Text("This is the second run. ")
            );

            // Add third run with underlined text
            var run3 = new Run(
                new RunProperties(
                    new FontSize() { Val = "24" },
                    new Underline() { Val = UnderlineValues.Single }
                ),
                new Text("This is the third run.")
            );

            // Add all runs to the paragraph
            paragraph.Append(run1);
            paragraph.Append(run2);
            paragraph.Append(run3);

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        #endregion

        #region Helper Methods - Create Test Patches

        private WordTextPatch CreateSingleCommentAddPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "single-comment-add-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "This is a test comment" },
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Create a patch that adds comments to multiple runs within the same paragraph
        /// </summary>
        /// <returns>WordTextPatch with operations for multiple runs</returns>
        private WordTextPatch CreateMultipleRunsCommentPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "multiple-runs-comment-test",
                Operations =
                [
                    // Comment on first run (1-0)
                    new() {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 }, // "This is th"
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on first run - bold text" },
                        Revision = new Revision
                        {
                            Author = "Reviewer A",
                            Date = DateTime.Now
                        }
                    },
                    // Comment on second run (1-1)
                    new() {
                        Op = OperationType.CommentAdd,
                        Target = new("1-1"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 }, // "This is th"
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on second run - italic text" },
                        Revision = new Revision
                        {
                            Author = "Reviewer B",
                            Date = DateTime.Now
                        }
                    },
                    // Comment on third run (1-2)
                    new() {
                        Op = OperationType.CommentAdd,
                        Target = new("1-2"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 }, // "This is th"
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on third run - underlined text" },
                        Revision = new Revision
                        {
                            Author = "Reviewer C",
                            Date = DateTime.Now
                        }
                    }
                ]
            };
        }

        /// <summary>
        /// Creates a patch with a single operation that adds a comment to multiple SegIds
        /// 创建包含单个操作的补丁，该操作向多个SegId添加评论
        /// </summary>
        /// <returns>WordTextPatch with single operation targeting multiple SegIds</returns>
        // private WordTextPatch CreateSingleOperationMultipleSegIdsPatch()
        // {
        //     return new WordTextPatch
        //     {
        //         DocumentId = "single-operation-multiple-segids-test",
        //         Operations = new List<Operation>
        //         {
        //             new Operation
        //             {
        //                 Op = OperationType.CommentAdd,
        //                 Target = new Target
        //                 {
        //                     Segments = new List<string> { "1-0", "1-1", "1-2" }
        //                 },
        //                 Comment = new ApplyRevision.Model.Comment { Text = "Shared comment across multiple runs" },
        //                 Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
        //                 Revision = new Revision
        //                 {
        //                     Author = "Multi-Reviewer",
        //                     Date = DateTime.Now
        //                 }
        //             }
        //         }
        //     };
        // }

        private WordTextPatch CreateMultipleCommentAddPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "multiple-comment-add-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "First paragraph comment",
                        Revision = new Revision
                        {
                            Author = "Reviewer 1",
                            Date = DateTime.Now
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("2-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Second paragraph comment",
                        Revision = new Revision
                        {
                            Author = "Reviewer 2",
                            Date = DateTime.Now
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("3-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Third paragraph comment",
                        Revision = new Revision
                        {
                            Author = "Reviewer 3",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateTextRangeCommentAddPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "text-range-comment-add-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 10, End = 19 }, // "brown fox"
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on 'brown fox'" },
                        Revision = new Revision
                        {
                            Author = "Range Reviewer",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateCommentAddWithRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "comment-add-revision-test",
                Operations =
                [
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            Text = "This is a comment with revision metadata"
                        },
                        Revision = new Revision
                        {
                            Author = "Test Reviewer",
                            Date = new DateTime(2024, 1, 15, 10, 30, 0)
                        }
                    }
                ]
            };
        }

        private WordTextPatch CreateEmptyCommentTextPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "empty-comment-text-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "", // Empty comment text should cause failure
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Creates a patch with CommentAdd followed by Insert, Delete, and Replace operations
        /// to test offset calculation and position adjustment
        /// </summary>
        private WordTextPatch CreateCommentAddWithSubsequentTextOperationsPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "comment-add-with-text-ops-test",
                Operations = new List<Operation>
                {
                    // 1. Add comment on "quick" at position [4,9]
                    new Operation
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 4, End = 9 }, // "quick"
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on 'quick'" },
                        Revision = new Revision
                        {
                            Author = "Reviewer",
                            Date = DateTime.Now
                        }
                    },
                    // 2. Insert "very " at position [10,10] (should be adjusted to [11,11] due to comment offset)
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 10, End = 10 },
                        Text = "very ",
                        Revision = new Revision
                        {
                            Author = "Editor",
                            Date = DateTime.Now
                        }
                    },
                    // 3. Delete "fox " at position [21,25] (should be adjusted due to previous operations)
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 21, End = 25 },
                        Text = "fox ",
                        Revision = new Revision
                        {
                            Author = "Editor",
                            Date = DateTime.Now
                        }
                    },
                    // 4. Replace "lazy" with "sleepy" at position [35,39] (should be adjusted due to previous operations)
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 35, End = 39 },
                        Text = "sleepy",
                        Revision = new Revision
                        {
                            Author = "Editor",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Creates a patch with multiple CommentAdd operations followed by text operations
        /// to test complex offset calculations
        /// </summary>
        private WordTextPatch CreateMultipleCommentsWithTextOperationsPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "multiple-comments-with-text-ops-test",
                Operations = new List<Operation>
                {
                    // 1. Add comment on "First" at position [0,5]
                    new() {
                        Op = OperationType.CommentAdd,
                        Target = new( "1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 5 }, // "First"
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on First" },
                        Revision = new Revision
                        {
                            Author = "Reviewer A",
                            Date = DateTime.Now
                        }
                    },
                    // 2. Add comment on "Second" at position [20,26] (will be adjusted due to first comment)
                    new() {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 20, End = 26 }, // "Second"
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on Second" },
                        Revision = new Revision
                        {
                            Author = "Reviewer B",
                            Date = DateTime.Now
                        }
                    },
                    // 3. Add comment on "Third" at position [45,50] (will be adjusted due to previous comments)
                    new()
                    {
                        Op = OperationType.CommentAdd,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 45, End = 50 }, // "Third"
                        Text = "",
                        Comment = new ApplyRevision.Model.Comment { Text = "Comment on Third" },
                        Revision = new Revision
                        {
                            Author = "Reviewer C",
                            Date = DateTime.Now
                        }
                    },
                    // 4. Insert "very " at position [6,6] (will be adjusted due to comment offsets)
                    new()
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 6, End = 6 },
                        Text = "very ",
                        Revision = new Revision
                        {
                            Author = "Editor",
                            Date = DateTime.Now
                        }
                    },
                    // 5. Delete "here. " at position [19,25] (will be adjusted due to previous operations)
                    new()
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 19, End = 25 },
                        Text = "here. ",
                        Revision = new Revision
                        {
                            Author = "Editor",
                            Date = DateTime.Now
                        }
                    },
                    // 6. Replace "follows" with "continues" at position [43,50] (will be adjusted due to previous operations)
                    new()
                    {
                        Op = OperationType.Replace,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 43, End = 50 },
                        Text = "continues",
                        Revision = new Revision
                        {
                            Author = "Editor",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        #endregion

        public void Dispose()
        {
            // Cleanup is handled by the test framework
            // Files in TestResults directory are kept for manual inspection
        }
    }
}
