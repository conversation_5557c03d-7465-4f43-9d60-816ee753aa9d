using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using ApplyRevision.Service;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    /// <summary>
    /// WordTextPatchApplier类的测试
    /// </summary>
    public class WordTextPatchApplierTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _tempFilePath;
        private readonly string _tempDirPath;

        public WordTextPatchApplierTests()
        {
            // 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // 创建临时目录和文件
            _tempDirPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
            Directory.CreateDirectory(_tempDirPath);
            _tempFilePath = Path.Combine(_tempDirPath, "test.docx");
        }

        #region 构造函数测试

        [Fact]
        public void Constructor_ShouldInitializeAllDependencies()
        {
            // Arrange & Act
            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Assert - 如果构造函数没有正确初始化依赖项，会抛出异常
            Assert.NotNull(applier);
        }

        [Fact]
        public void Constructor_WithNullContext_ShouldThrowArgumentNullException()
        {
            // Arrange & Act & Assert
            var exception = Assert.Throws<ArgumentNullException>(() => new WordTextPatchApplier(null));
            Assert.Equal("context", exception.ParamName);
        }

        #endregion

        #region ApplyPatch方法测试 - 正常情况

        [Fact]
        public void ApplyPatch_WithValidPatch_ShouldReturnTrue()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateValidPatch();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>()), Times.Never);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithMultipleOperations_ShouldApplyAllOperations()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithMultipleOperations();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().First();

            // 验证插入操作
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "Inserted Text");

            // 验证删除操作 - 添加调试输出
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // 添加调试输出
            Console.WriteLine($"DeletedRuns count: {deletedRuns.Count}");
            foreach (var run in deletedRuns)
            {
                Console.WriteLine($"DeletedRun InnerText: '{run.InnerText}'");
                Console.WriteLine($"DeletedRun XML: {run.OuterXml}");
            }

            // 验证删除操作删除的是原始文档中的"Original"文本
            Assert.Single(deletedRuns);
            Assert.Contains(deletedRuns, r => r.InnerText.Contains("Original"));

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithDifferentOperationTypes_ShouldApplyAllOperations()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithDifferentOperationTypes();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);

            // 验证插入操作
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "Inserted Text");

            // 验证删除操作
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText.Contains("Original"));

            // 验证替换操作
            Assert.Contains(insertedRuns, r => r.InnerText == "Replaced Text");

            // Cleanup
            CleanupTempFiles();
        }

        #endregion
        #region ApplyPatch方法测试 - 边界情况

        [Fact]
        public void ApplyPatch_WithEmptyDocument_ShouldHandleGracefully()
        {
            // Arrange
            CreateEmptyDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateValidPatch();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            Assert.NotNull(doc.MainDocumentPart.Document.Body);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithLargeDocument_ShouldHandleGracefully()
        {
            // Arrange
            CreateLargeDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateValidPatch();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithComplexPatch_ShouldHandleGracefully()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithDifferentOperationTypes(); // 使用包含多种操作类型的复杂补丁

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region ApplyOperation方法测试 - Insert操作

        [Fact]
        public void ApplyOperation_WithInsertAtBeginning_ShouldInsertCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithInsertAtBeginning();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "Beginning ");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithInsertInMiddle_ShouldInsertCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithInsertInMiddle();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == " Middle ");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithInsertAtEnd_ShouldInsertCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithInsertAtEnd();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == " End");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithInsertFormattedText_ShouldInsertCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithFormattedInsert();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "Formatted Text");

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region ApplyOperation方法测试 - Delete操作

        [Fact]
        public void ApplyOperation_WithDeleteFromBeginning_ShouldDeleteCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithDeleteFromBeginning();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "Original");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithDeleteFromMiddle_ShouldDeleteCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithDeleteFromMiddle();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "inal ");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithDeleteFromEnd_ShouldDeleteCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithDeleteFromEnd();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "text");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithDeleteEntireText_ShouldDeleteCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithDeleteEntireText();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "Original text");

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region ApplyOperation方法测试 - Replace操作

        [Fact]
        public void ApplyOperation_WithReplaceAtBeginning_ShouldReplaceCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithReplaceAtBeginning();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "Original");

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "New");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithReplaceInMiddle_ShouldReplaceCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithReplaceInMiddle();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // 添加调试输出
            Console.WriteLine($"DeletedRuns count: {deletedRuns.Count}");
            foreach (var run in deletedRuns)
            {
                Console.WriteLine($"DeletedRun InnerText: '{run.InnerText}'");
                Console.WriteLine($"DeletedRun XML: {run.OuterXml}");
            }

            Assert.Contains(deletedRuns, r => r.InnerText == "inal ");

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "modified t");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithReplaceAtEnd_ShouldReplaceCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithReplaceAtEnd();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "text");

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "content");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithReplaceEntireText_ShouldReplaceCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithReplaceEntireText();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "Original text");

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "Completely new text");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithReplaceLongerText_ShouldReplaceCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithReplaceLongerText();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "Original text");

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "This is a much longer replacement text that should work correctly");

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithReplaceShorterText_ShouldReplaceCorrectly()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreatePatchWithReplaceShorterText();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_tempFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Contains(deletedRuns, r => r.InnerText == "Original text");

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Contains(insertedRuns, r => r.InnerText == "Short");

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region ApplyPatch方法测试 - 错误处理

        [Fact]
        public void ApplyPatch_WithNonExistentFile_ShouldReturnFalse()
        {
            // Arrange
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateValidPatch();
            var nonExistentFilePath = Path.Combine(_tempDirPath, "nonexistent.docx");

            // Act
            var result = applier.ApplyPatch(nonExistentFilePath, patch);

            // Assert
            Assert.False(result);
            _mockLogger.Verify(l => l.LogError(It.Is<string>(s => s.Contains("Document file not found"))), Times.Once);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithNullPatch_ShouldReturnTrue()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Act
            var result = applier.ApplyPatch(_tempFilePath, null);

            // Assert
            Assert.True(result);
            _mockLogger.Verify(l => l.LogWarning(It.Is<string>(s => s.Contains("No operations to apply"))), Times.Once);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithEmptyOperations_ShouldReturnTrue()
        {
            // Arrange
            CreateTestDocx(_tempFilePath);
            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>()
            };

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            _mockLogger.Verify(l => l.LogWarning(It.Is<string>(s => s.Contains("No operations to apply"))), Times.Once);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyPatch_WithInvalidDocument_ShouldReturnFalse()
        {
            // Arrange
            // 创建一个无效的文档（没有MainDocumentPart）
            File.WriteAllText(_tempFilePath, "This is not a valid DOCX file");

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateValidPatch();

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.False(result);
            _mockLogger.Verify(l => l.LogError(It.IsAny<string>()), Times.Once);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion

        #region 辅助方法 - 创建测试文档

        private void CreateTestDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            var run = new Run(new Text("Original text"));
            paragraph.AppendChild(run);

            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void CreateEmptyDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());
            mainPart.Document.Save();
        }

        private void CreateLargeDocx(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // 创建多个段落
            for (int i = 0; i < 100; i++)
            {
                var paragraph = new Paragraph();
                paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", $"para{i}"));

                var run = new Run(new Text($"Paragraph {i} with some text content for testing large documents."));
                paragraph.AppendChild(run);

                mainPart.Document.Body.AppendChild(paragraph);
            }

            mainPart.Document.Save();
        }

        private void CleanupTempFiles()
        {
            try
            {
                if (File.Exists(_tempFilePath))
                {
                    File.Delete(_tempFilePath);
                }

                if (Directory.Exists(_tempDirPath))
                {
                    Directory.Delete(_tempDirPath, true);
                }
            }
            catch
            {
                // 忽略清理过程中的异常
            }
        }

        private void CreateDocumentWithDeletedRun(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // 创建一个普通的Run元素，这样"12345-0"会指向这个Run
            var emptyRun = new Run(new Text(""));
            paragraph.AppendChild(emptyRun);

            // 创建一个DeletedRun元素
            var deletedRun = new DeletedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            var run = new Run();
            var runProps = new RunProperties();
            run.AppendChild(runProps);
            run.AppendChild(new DeletedText("Deleted Text"));
            deletedRun.AppendChild(run);

            paragraph.AppendChild(deletedRun);
            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void VerifyDocumentHasInsertedAndDeletedText(string filePath)
        {
            // 等待文件系统确保所有句柄都已释放
            System.Threading.Thread.Sleep(300);

            using var doc = WordprocessingDocument.Open(filePath, false);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().First();

            // 验证插入操作
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Single(insertedRuns);
            Assert.Equal("Inserted Before Deleted", insertedRuns[0].InnerText);

            // 验证DeletedRun仍然存在且内容未变
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
            Assert.Equal("Deleted Text", deletedRuns[0].InnerText);
        }

        private void CreateDocumentWithNestedRevisions(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            var paragraph = new Paragraph();
            paragraph.SetAttribute(new OpenXmlAttribute("w14", "paraId", "http://schemas.microsoft.com/office/word/2010/wordml", "12345"));

            // 先添加一个普通的Run，这样"12345-0"会指向这个Run
            var emptyRun = new Run(new Text(""));
            paragraph.AppendChild(emptyRun);

            // 创建嵌套的修订标记：DeletedRun内包含InsertedRun
            var deletedRun = new DeletedRun()
            {
                Id = "1",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            var insertedRun = new InsertedRun()
            {
                Id = "2",
                Author = "Test Author",
                Date = new DateTimeValue(DateTime.Now)
            };

            var run = new Run();
            run.AppendChild(new Text("Nested Text"));
            insertedRun.AppendChild(run);
            deletedRun.AppendChild(insertedRun);

            paragraph.AppendChild(deletedRun);
            mainPart.Document.Body.AppendChild(paragraph);
            mainPart.Document.Save();
        }

        private void VerifyDocumentHasNestedRevisionsAndInsert(string filePath)
        {
            // 等待文件系统确保所有句柄都已释放
            System.Threading.Thread.Sleep(300);

            using var doc = WordprocessingDocument.Open(filePath, false);

            // 验证新的InsertedRun已添加
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.Equal(2, insertedRuns.Count); // 原始的一个 + 新添加的一个
            Assert.Contains(insertedRuns, r => r.InnerText == "Inserted Before Nested");

            // 验证原始的DeletedRun仍然存在
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.Single(deletedRuns);
        }

        #endregion

        #region 辅助方法 - 创建测试补丁

        private WordTextPatch CreateValidPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Inserted Text",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithMultipleOperations()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Inserted Text",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    },
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithDifferentOperationTypes()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Inserted Text",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    },
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    },
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 9, End = 13 },
                        Text = "Replaced Text",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithInsertAtBeginning()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Beginning ",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithInsertInMiddle()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 8, End = 8 },
                        Text = " Middle ",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithInsertAtEnd()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 13, End = 13 },
                        Text = " End",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithFormattedInsert()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Formatted Text",
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true },
                            { "italic", true },
                            { "color", "FF0000" }
                        },
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithDeleteFromBeginning()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithDeleteFromMiddle()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 4, End = 9 },
                        Text = "",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithDeleteFromEnd()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 9, End = 13 },
                        Text = "",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithDeleteEntireText()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 13 },
                        Text = "",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithReplaceAtBeginning()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 8 },
                        Text = "New",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithReplaceInMiddle()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 4, End = 9 },
                        Text = "modified t",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithReplaceAtEnd()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 9, End = 13 },
                        Text = "content",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithReplaceEntireText()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 13 },
                        Text = "Completely new text",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithReplaceLongerText()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 13 },
                        Text = "This is a much longer replacement text that should work correctly",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithReplaceShorterText()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 13 },
                        Text = "Short",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        private WordTextPatch CreatePatchWithUnsupportedOperationType()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = (OperationType)999, // 不支持的操作类型
                        Target = new("12345-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Test",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };
        }

        #endregion

        #region 修订标记内插入测试

        [Fact]
        public void ApplyOperation_WithInsertWithinDeletedRun_ShouldInsertCorrectly()
        {
            // Arrange
            CreateDocumentWithDeletedRun(_tempFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"), // 指向第一个Run
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Inserted Before Deleted",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            VerifyDocumentHasInsertedAndDeletedText(_tempFilePath);

            // Cleanup
            CleanupTempFiles();
        }

        [Fact]
        public void ApplyOperation_WithInsertWithinNestedRevisions_ShouldInsertCorrectly()
        {
            // Arrange
            CreateDocumentWithNestedRevisions(_tempFilePath);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("12345-0"), // 指向第一个Run
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Inserted Before Nested",
                        Revision = new Revision { Author = "Test Author", Date = DateTime.Now }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(_tempFilePath, patch);

            // Assert
            Assert.True(result);
            VerifyDocumentHasNestedRevisionsAndInsert(_tempFilePath);

            // Cleanup
            CleanupTempFiles();
        }

        #endregion
    }
}