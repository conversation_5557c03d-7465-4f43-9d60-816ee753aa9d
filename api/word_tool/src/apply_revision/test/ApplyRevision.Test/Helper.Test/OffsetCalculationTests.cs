// using Xunit;
// using Moq;
// using Amazon.Lambda.Core;
// using ApplyRevision.Strategy;
// using ApplyRevision.Model;
// using DocumentFormat.OpenXml.Wordprocessing;
// using DocumentFormat.OpenXml.Packaging;
// using System.IO;
// using ApplyRevision.Service;
// using ApplyRevision.Factory;
// using System;

// namespace ApplyRevision.Test.Helper.Test
// {
//     /// <summary>
//     /// Tests for verifying correct offset calculation in various operations
//     /// 验证各种操作中正确偏移量计算的测试
//     /// </summary>
//     public class OffsetCalculationTests : IDisposable
//     {
//         private readonly Mock<ILambdaContext> _mockContext;
//         private readonly Mock<ICommentOrchestrator> _mockOrchestrator;
//         private readonly Mock<IElementFactory> _mockElementFactory;
//         private readonly string _tempFilePath;

//         public OffsetCalculationTests()
//         {
//             _mockContext = new Mock<ILambdaContext>();
//             _mockOrchestrator = new Mock<ICommentOrchestrator>();
//             _mockElementFactory = new Mock<IElementFactory>();
//             _tempFilePath = Path.GetTempFileName() + ".docx";
//         }

//         [Fact]
//         public void InsertStrategy_WithComment_ShouldReturnTextLengthPlusOne()
//         {
//             // Arrange
//             CreateSimpleTestDocument(_tempFilePath);
//             var strategy = new InsertStrategy(_mockContext.Object, _mockElementFactory.Object, null);

//             var operation = new Operation
//             {
//                 Op = OperationType.Insert,
//                 Text = "Hello",
//                 Comment = new Comment { Text = "Test comment" },
//                 Revision = new Revision { Author = "Test", Date = System.DateTime.Now }
//             };

//             // Act
//             int offset;
//             using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
//             {
//                 var targetRun = doc.MainDocumentPart.Document.Body.Descendants<Run>().First();
//                 offset = strategy.ExecuteWithRelativePosition(doc, operation, targetRun, 0, 0);
//             }

//             // Assert
//             // Should return text length (5) + comment reference run (1) = 6
//             Assert.Equal(6, offset);
//         }

//         [Fact]
//         public void InsertStrategy_WithoutComment_ShouldReturnTextLength()
//         {
//             // Arrange
//             CreateSimpleTestDocument(_tempFilePath);
//             var strategy = new InsertStrategy(_mockContext.Object, _mockElementFactory.Object, null);

//             var operation = new Operation
//             {
//                 Op = OperationType.Insert,
//                 Text = "Hello",
//                 Comment = null,
//                 Revision = new Revision { Author = "Test", Date = System.DateTime.Now }
//             };

//             // Act
//             int offset;
//             using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
//             {
//                 var targetRun = doc.MainDocumentPart.Document.Body.Descendants<Run>().First();
//                 offset = strategy.ExecuteWithRelativePosition(doc, operation, targetRun, 0, 0);
//             }

//             // Assert
//             // Should return only text length (5)
//             Assert.Equal(5, offset);
//         }

//         [Fact]
//         public void DeleteStrategy_WithComment_ShouldReturnNegativeTextLengthPlusOne()
//         {
//             // Arrange
//             CreateSimpleTestDocument(_tempFilePath);
//             var strategy = new DeleteStrategy(_mockContext.Object, _mockElementFactory.Object, null);

//             var operation = new Operation
//             {
//                 Op = OperationType.Delete,
//                 Comment = new Comment { Text = "Test comment" },
//                 Revision = new Revision { Author = "Test", Date = System.DateTime.Now }
//             };

//             // Act
//             int offset;
//             using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
//             {
//                 var targetRun = doc.MainDocumentPart.Document.Body.Descendants<Run>().First();
//                 offset = strategy.ExecuteWithRelativePosition(doc, operation, targetRun, 0, 3);
//             }

//             // Assert
//             // Should return -3 (deleted text) + 1 (comment reference run) = -2
//             Assert.Equal(-2, offset);
//         }

//         [Fact]
//         public void CommentAddStrategy_WithMultipleSegIds_ShouldReturnSegIdCount()
//         {
//             // Arrange
//             var strategy = new CommentAddStrategy(_mockContext.Object, _mockOrchestrator.Object);

//             var operation = new Operation
//             {
//                 Op = OperationType.CommentAdd,
//                 Target = new Target(new[] { "seg1", "seg2", "seg3" }),
//                 Comment = new Comment { Text = "Test comment" },
//                 Revision = new Revision { Author = "Test", Date = System.DateTime.Now }
//             };

//             _mockOrchestrator.Setup(x => x.ProcessCommentAddMultiple(It.IsAny<System.Collections.Generic.IEnumerable<string>>(),
//                 It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
//                 .Returns(true);
//             _mockOrchestrator.Setup(x => x.Save()).Returns(true);

//             // Act
//             int offset;
//             using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
//             {
//                 offset = strategy.ExecuteWithRelativePosition(doc, operation, null, 0, 0);
//             }

//             // Assert
//             // Should return 3 (one comment reference run per SegId)
//             Assert.Equal(3, offset);
//         }

//         [Fact]
//         public void CommentReplyStrategy_ShouldReturnOne()
//         {
//             // Arrange
//             var strategy = new CommentReplyStrategy(_mockContext.Object, _mockOrchestrator.Object);

//             var operation = new Operation
//             {
//                 Op = OperationType.CommentReply,
//                 Comment = new Comment { Text = "Reply text", ParentCommentId = "1" },
//                 Revision = new Revision { Author = "Test", Date = System.DateTime.Now }
//             };

//             _mockOrchestrator.Setup(x => x.ProcessCommentReply(It.IsAny<string>(),
//                 It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()))
//                 .Returns(true);

//             // Act
//             int offset;
//             using (var doc = WordprocessingDocument.Open(_tempFilePath, true))
//             {
//                 offset = strategy.ExecuteWithRelativePosition(doc, operation, null, 0, 0);
//             }

//             // Assert
//             // Should return 1 (one comment reference run for the reply)
//             Assert.Equal(1, offset);
//         }

//         private void CreateSimpleTestDocument(string filePath)
//         {
//             using var doc = WordprocessingDocument.Create(filePath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document);
//             var mainPart = doc.AddMainDocumentPart();
//             mainPart.Document = new Document();
//             var body = mainPart.Document.AppendChild(new Body());
//             var paragraph = body.AppendChild(new Paragraph());
//             var run = paragraph.AppendChild(new Run());
//             run.AppendChild(new Text("Sample text"));
//         }

//         public void Dispose()
//         {
//             if (File.Exists(_tempFilePath))
//             {
//                 File.Delete(_tempFilePath);
//             }
//         }
//     }
// }
