using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordComment = DocumentFormat.OpenXml.Wordprocessing.Comment;
using Moq;
using Xunit;
using Common.Tests.Attributes;

namespace ApplyRevision.Tests.Helper.Test
{
    public class WordTextPatchApplierWithTrackingIntegrationTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;
        private readonly string _originalFilePath;
        private readonly string _modifiedFilePath;
        private readonly string _insertMiddleFilePath;
        private readonly string _modifiedInsertMiddleFilePath;

        public WordTextPatchApplierWithTrackingIntegrationTests()
        {
            // 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // 创建测试结果目录
            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "TestResults");
            Directory.CreateDirectory(_testResultsDir);

            // 生成简单测试文档
            _originalFilePath = Path.Combine(_testResultsDir, "CreateDocumentWithComplexCommentStructure.DOCX");
            CreateSimpleDocument(_originalFilePath);

            // 设置修改文件路径
            _modifiedFilePath = Path.Combine(_testResultsDir, "CreateDocumentWithComplexCommentStructure_Modified.DOCX");

            // 生成用于中间插入测试的文档
            _insertMiddleFilePath = Path.Combine(_testResultsDir, "InsertTextInMiddleOfDocument.DOCX");
            CreateInsertMiddleTestDocument(_insertMiddleFilePath);

            // 设置中间插入测试的修改文件路径
            _modifiedInsertMiddleFilePath = Path.Combine(_testResultsDir, "InsertTextInMiddleOfDocument_Modified.DOCX");
        }

        private void CreateSimpleDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" },
                    new Justification() { Val = JustificationValues.Left },
                    new SpacingBetweenLines()
                    {
                        After = "0",
                        Line = "240",
                        LineRule = LineSpacingRuleValues.Auto
                    },
                    new Indentation()
                    {
                        Left = "0",
                        Right = "0",
                        FirstLine = "0"
                    }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "48" }, // 12pt font (24 half-points)
                        new RunFonts() { Ascii = "Times New Roman" },
                        new Color() { Val = "333333" }
                    ),
                    new Text("Test Document Content")
                )
            )
            {
                // Set paragraph ID using the ParagraphId property (Office 2010+ feature)
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(
                new Body(paragraph)
            );

            // 添加评论部分
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateInsertMiddleTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" },
                    new Justification() { Val = JustificationValues.Left },
                    new SpacingBetweenLines()
                    {
                        After = "0",
                        Line = "240",
                        LineRule = LineSpacingRuleValues.Auto
                    },
                    new Indentation()
                    {
                        Left = "0",
                        Right = "0",
                        FirstLine = "0"
                    }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "48" }, // 12pt font (24 half-points)
                        new RunFonts() { Ascii = "Times New Roman" },
                        new Color() { Val = "333333" }
                    ),
                    new Text("The quick brown fox jumps over the lazy dog.")
                )
            )
            {
                // Set paragraph ID using the ParagraphId property (Office 2010+ feature)
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(
                new Body(paragraph)
            );

            // 添加评论部分
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        [Fact]
        public void IntegrationTest_CreateDocumentWithComplexCommentStructure_ShouldGenerateDocx()
        {
            // Arrange
            // 复制原始文件到测试结果目录
            File.Copy(_originalFilePath, _modifiedFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateComplexCommentPatch();

            // Act
            var result = applier.ApplyPatch(_modifiedFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_modifiedFilePath, false);
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart.Comments.Elements<WordComment>().ToList();
            Assert.Equal(3, comments.Count);

            // 验证嵌套评论
            var parentComment = comments.First(c => c.InnerText.Contains("Parent Comment"));
            var childComment = comments.First(c => c.InnerText.Contains("Child Comment"));
            var replyComment = comments.First(c => c.InnerText.Contains("Reply Comment"));

            // 验证评论内容
            Assert.NotNull(parentComment);
            Assert.NotNull(childComment);
            Assert.NotNull(replyComment);
        }

        [Fact]
        public void IntegrationTest_InsertTextInMiddleOfDocument_ShouldInsertCorrectly()
        {
            // Arrange
            // 复制原始文件到测试结果目录
            File.Copy(_insertMiddleFilePath, _modifiedInsertMiddleFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateInsertMiddlePatch();

            // Act
            var result = applier.ApplyPatch(_modifiedInsertMiddleFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(_modifiedInsertMiddleFilePath, false);
            var body = doc.MainDocumentPart.Document.Body;
            var paragraph = body.Elements<Paragraph>().First();
            var textContent = string.Join("", paragraph.Descendants<Text>().Select(t => t.Text));

            // 验证插入的文本是否在正确位置
            // 原文本: "The quick brown fox jumps over the lazy dog."
            // 在位置20 ("fox jumps" 之间) 插入 " very fast"
            // 期望结果: "The quick brown fox very fast jumps over the lazy dog."
            Assert.Contains("very fast", textContent);

            // 验证完整文本内容包含插入的内容
            var expectedPattern = "The quick brown fox.*very fast.*jumps over the lazy dog";
            Assert.Matches(expectedPattern, textContent);
        }

        [Fact]
        public void IntegrationTest_DeleteTextFromDocument_ShouldDeleteCorrectly()
        {
            // Arrange
            var deleteTestFilePath = Path.Combine(_testResultsDir, "DeleteTextTest.DOCX");
            var modifiedDeleteFilePath = Path.Combine(_testResultsDir, "DeleteTextTest_Modified.DOCX");
            CreateDeleteTestDocument(deleteTestFilePath);
            File.Copy(deleteTestFilePath, modifiedDeleteFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateDeletePatch();

            // Act
            var result = applier.ApplyPatch(modifiedDeleteFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedDeleteFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            Assert.NotEmpty(deletedRuns);
            Assert.Contains(deletedRuns, r => r.InnerText.Contains("brown"));
        }

        [Fact]
        public void IntegrationTest_DeleteEntireRunContent_ShouldReplaceWithDeletedRun()
        {
            // Arrange
            var deleteEntireRunTestFilePath = Path.Combine(_testResultsDir, "DeleteEntireRunTest.DOCX");
            var modifiedDeleteEntireRunFilePath = Path.Combine(_testResultsDir, "DeleteEntireRunTest_Modified.DOCX");

            // 创建包含短文本的测试文档
            using (var doc_ = WordprocessingDocument.Create(deleteEntireRunTestFilePath, WordprocessingDocumentType.Document))
            {
                var mainPart = doc_.AddMainDocumentPart();
                var paragraph_ = new Paragraph(
                    new ParagraphProperties(
                        new ParagraphStyleId() { Val = "Normal" }
                    ),
                    new Run(
                        new RunProperties(
                            new FontSize() { Val = "24" },
                            new RunFonts() { Ascii = "Times New Roman" }
                        ),
                        new Text("Short Text") // 10个字符的短文本
                    )
                )
                {
                    ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
                };

                mainPart.Document = new Document(new Body(paragraph_));
                var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
                commentsPart.Comments = new Comments();
            }

            File.Copy(deleteEntireRunTestFilePath, modifiedDeleteEntireRunFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // 创建删除整个文本的patch
            var patch = new WordTextPatch
            {
                DocumentId = "delete-entire-run-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 10 }, // 删除整个"Short Text"
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(modifiedDeleteEntireRunFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedDeleteEntireRunFilePath, false);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().First();
            var runs = paragraph.Elements<Run>().ToList();
            var deletedRuns = paragraph.Elements<DeletedRun>().ToList();

            // 验证原始Run已被删除，并被DeletedRun替换
            Assert.NotEmpty(deletedRuns);
            Assert.Contains(deletedRuns, r => r.InnerText.Contains("Short Text"));

            // 验证原始Run不再存在或者内容为空
            var textRuns = runs.Where(r => r.Descendants<Text>().Any(t => !string.IsNullOrEmpty(t.Text))).ToList();
            Assert.Empty(textRuns.Where(r => r.Descendants<Text>().Any(t => t.Text.Contains("Short Text"))));
        }

        [Fact]
        public void IntegrationTest_DeleteEntireRunContentWithComment_ShouldReplaceWithDeletedRunAndComment()
        {
            // Arrange
            var deleteEntireRunWithCommentTestFilePath = Path.Combine(_testResultsDir, "DeleteEntireRunWithCommentTest.DOCX");
            var modifiedDeleteEntireRunWithCommentFilePath = Path.Combine(_testResultsDir, "DeleteEntireRunWithCommentTest_Modified.DOCX");

            // 创建包含短文本的测试文档
            using (var doc_ = WordprocessingDocument.Create(deleteEntireRunWithCommentTestFilePath, WordprocessingDocumentType.Document))
            {
                var mainPart = doc_.AddMainDocumentPart();
                var paragraph_ = new Paragraph(
                    new ParagraphProperties(
                        new ParagraphStyleId() { Val = "Normal" }
                    ),
                    new Run(
                        new RunProperties(
                            new FontSize() { Val = "24" },
                            new RunFonts() { Ascii = "Times New Roman" }
                        ),
                        new Text("Short Text") // 10个字符的短文本
                    )
                )
                {
                    ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
                };

                mainPart.Document = new Document(new Body(paragraph_));
                var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
                commentsPart.Comments = new Comments();
            }

            File.Copy(deleteEntireRunWithCommentTestFilePath, modifiedDeleteEntireRunWithCommentFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // 创建删除整个文本的patch，包含评论
            var patch = new WordTextPatch
            {
                DocumentId = "delete-entire-run-with-comment-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 10 }, // 删除整个"Short Text"
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "delete_entire_run_comment",
                            Text = "Deleted entire run content"
                        }
                    }
                }
            };

            // Act
            var result = applier.ApplyPatch(modifiedDeleteEntireRunWithCommentFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedDeleteEntireRunWithCommentFilePath, false);
            var paragraph = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().First();
            var deletedRuns = paragraph.Elements<DeletedRun>().ToList();

            // 验证DeletedRun存在
            Assert.NotEmpty(deletedRuns);
            Assert.Contains(deletedRuns, r => r.InnerText.Contains("Short Text"));

            // 验证评论存在
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.NotEmpty(comments);
            Assert.Contains(comments, c => c.InnerText.Contains("Deleted entire run content"));

            // 验证评论范围标记存在
            var commentRangeStarts = paragraph.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = paragraph.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = paragraph.Descendants<CommentReference>().ToList();

            Assert.NotEmpty(commentRangeStarts);
            Assert.NotEmpty(commentRangeEnds);
            Assert.NotEmpty(commentReferences);
            Assert.Equal(commentRangeStarts.Count, commentRangeEnds.Count);
            Assert.Equal(commentRangeStarts.Count, commentReferences.Count);
        }

        [Fact]
        public void IntegrationTest_ReplaceTextInDocument_ShouldReplaceCorrectly()
        {
            // Arrange
            var replaceTestFilePath = Path.Combine(_testResultsDir, "ReplaceTextTest.DOCX");
            var modifiedReplaceFilePath = Path.Combine(_testResultsDir, "ReplaceTextTest_Modified.DOCX");
            CreateReplaceTestDocument(replaceTestFilePath);
            File.Copy(replaceTestFilePath, modifiedReplaceFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateReplacePatch();

            // Act
            var result = applier.ApplyPatch(modifiedReplaceFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedReplaceFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();

            Assert.NotEmpty(deletedRuns);
            Assert.NotEmpty(insertedRuns);
            Assert.Contains(deletedRuns, r => r.InnerText.Contains("fox"));
            Assert.Contains(insertedRuns, r => r.InnerText.Contains("cat"));
        }

        [Fact]
        public void IntegrationTest_MultipleOperationsOnSameDocument_ShouldApplyAllCorrectly()
        {
            // Arrange 
            var multiOpTestFilePath = Path.Combine(_testResultsDir, "MultipleOperationsTest.DOCX");
            var modifiedMultiOpFilePath = Path.Combine(_testResultsDir, "MultipleOperationsTest_Modified.DOCX");
            CreateMultipleOperationsTestDocument(multiOpTestFilePath);
            File.Copy(multiOpTestFilePath, modifiedMultiOpFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMultipleOperationsPatch();

            // Act
            var result = applier.ApplyPatch(modifiedMultiOpFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedMultiOpFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // 验证插入操作
            Assert.Contains(insertedRuns, r => r.InnerText.Contains("NEW"));
            // 验证删除操作 - 检查是否有删除的内容
            Assert.NotEmpty(deletedRuns);
            // 验证替换操作（包含删除和插入）
            Assert.Contains(insertedRuns, r => r.InnerText.Contains("content"));
        }

        [Fact]
        public void IntegrationTest_InsertWithCommentAndRevision_ShouldCreateCommentCorrectly()
        {
            // Arrange
            var commentTestFilePath = Path.Combine(_testResultsDir, "CommentTest.DOCX");
            var modifiedCommentFilePath = Path.Combine(_testResultsDir, "CommentTest_Modified.DOCX");
            CreateCommentTestDocument(commentTestFilePath);
            File.Copy(commentTestFilePath, modifiedCommentFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateCommentPatch();

            // Act
            var result = applier.ApplyPatch(modifiedCommentFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容和评论
            using var doc = WordprocessingDocument.Open(modifiedCommentFilePath, false);
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.NotEmpty(comments);

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.NotEmpty(insertedRuns);
        }

        [Fact]
        public void IntegrationTest_OperationsOnMultipleParagraphs_ShouldHandleCorrectly()
        {
            // Arrange
            var multiParaTestFilePath = Path.Combine(_testResultsDir, "MultiParagraphTest.DOCX");
            var modifiedMultiParaFilePath = Path.Combine(_testResultsDir, "MultiParagraphTest_Modified.DOCX");
            CreateMultiParagraphTestDocument(multiParaTestFilePath);
            File.Copy(multiParaTestFilePath, modifiedMultiParaFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateMultiParagraphPatch();

            // Act
            var result = applier.ApplyPatch(modifiedMultiParaFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedMultiParaFilePath, false);
            var paragraphs = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().ToList();
            Assert.True(paragraphs.Count >= 2);

            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.NotEmpty(insertedRuns);
        }

        [Fact]
        public void IntegrationTest_EmptyDocumentWithOperations_ShouldHandleGracefully()
        {
            // Arrange
            var emptyTestFilePath = Path.Combine(_testResultsDir, "EmptyDocumentTest.DOCX");
            var modifiedEmptyFilePath = Path.Combine(_testResultsDir, "EmptyDocumentTest_Modified.DOCX");
            CreateEmptyTestDocument(emptyTestFilePath);
            File.Copy(emptyTestFilePath, modifiedEmptyFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateEmptyDocumentPatch();

            // Act
            var result = applier.ApplyPatch(modifiedEmptyFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档结构仍然有效
            using var doc = WordprocessingDocument.Open(modifiedEmptyFilePath, false);
            Assert.NotNull(doc.MainDocumentPart.Document.Body);
        }

        [Fact]
        public void IntegrationTest_LargeTextOperations_ShouldHandleEfficiently()
        {
            // Arrange
            var largeTextFilePath = Path.Combine(_testResultsDir, "LargeTextTest.DOCX");
            var modifiedLargeTextFilePath = Path.Combine(_testResultsDir, "LargeTextTest_Modified.DOCX");
            CreateLargeTextTestDocument(largeTextFilePath);
            File.Copy(largeTextFilePath, modifiedLargeTextFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateLargeTextPatch();

            // Act
            var result = applier.ApplyPatch(modifiedLargeTextFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedLargeTextFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.NotEmpty(insertedRuns);
        }

        [Fact]
        public void IntegrationTest_SpecialCharactersInOperations_ShouldHandleCorrectly()
        {
            // Arrange
            var specialCharFilePath = Path.Combine(_testResultsDir, "SpecialCharactersTest.DOCX");
            var modifiedSpecialCharFilePath = Path.Combine(_testResultsDir, "SpecialCharactersTest_Modified.DOCX");
            CreateSpecialCharactersTestDocument(specialCharFilePath);
            File.Copy(specialCharFilePath, modifiedSpecialCharFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);
            var patch = CreateSpecialCharactersPatch();

            // Act
            var result = applier.ApplyPatch(modifiedSpecialCharFilePath, patch);

            // Assert
            Assert.True(result);

            // 验证文档内容
            using var doc = WordprocessingDocument.Open(modifiedSpecialCharFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.NotEmpty(insertedRuns);

            // 验证特殊字符是否正确处理
            var textContent = string.Join("", doc.MainDocumentPart.Document.Body.Descendants<Text>().Select(t => t.Text));
            Assert.Contains("©", textContent);
            Assert.Contains("™", textContent);
            Assert.Contains("®", textContent);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_InsertOnInsertedText_ShouldCreateNestedRevisions()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Insert.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Insert_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // First apply an insert operation
            var firstPatch = CreateTwoRevisionPatch();
            var firstResult = applier.ApplyPatch(modifiedRevisionFilePath, firstPatch);
            Assert.True(firstResult);

            // Then apply another insert operation on the previously inserted text
            var secondPatch = CreateSecondRevisionInsertPatch();
            var secondResult = applier.ApplyPatch(modifiedRevisionFilePath, secondPatch);

            // Assert
            Assert.True(secondResult);

            // Verify document content and nested revisions
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();

            // Should have multiple inserted runs from different revisions
            Assert.True(insertedRuns.Count >= 2);

            // Verify different authors/dates for different revisions
            var firstRevisionRuns = insertedRuns.Where(r => r.Author?.Value == "First Author").ToList();
            var secondRevisionRuns = insertedRuns.Where(r => r.Author?.Value == "Second Author").ToList();

            Assert.NotEmpty(firstRevisionRuns);
            Assert.NotEmpty(secondRevisionRuns);

            // Verify comments are properly associated
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 2);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_DeleteInsertedText_ShouldCreateDeletedRevision()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Delete.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Delete_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // First apply an insert operation
            var firstPatch = CreateTwoRevisionPatch();
            var firstResult = applier.ApplyPatch(modifiedRevisionFilePath, firstPatch);
            Assert.True(firstResult);

            // Then delete part of the previously inserted text
            var deletePatch = CreateDeleteInsertedTextPatch();
            var deleteResult = applier.ApplyPatch(modifiedRevisionFilePath, deletePatch);

            // Assert
            Assert.True(deleteResult);

            // Verify document content
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // Should have both inserted and deleted runs
            Assert.NotEmpty(insertedRuns);
            Assert.NotEmpty(deletedRuns);

            // Verify the deleted run contains text that was previously inserted
            Assert.Contains(deletedRuns, r => r.Author?.Value == "Delete Author");

            // Verify comments for both operations
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 2);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_ReplaceDeletedText_ShouldCreateComplexRevisionStructure()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Replace.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Replace_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // First apply a delete operation
            var deletePatch = CreateFirstDeleteRevisionPatch();
            var deleteResult = applier.ApplyPatch(modifiedRevisionFilePath, deletePatch);
            Assert.True(deleteResult);

            // Then replace the deleted text with new content
            var replacePatch = CreateReplaceDeletedTextPatch();
            var replaceResult = applier.ApplyPatch(modifiedRevisionFilePath, replacePatch);

            // Assert
            Assert.True(replaceResult);

            // Verify document content
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();

            // Should have both deleted and inserted runs from different operations
            Assert.NotEmpty(deletedRuns);
            Assert.NotEmpty(insertedRuns);

            // Verify different authors for different operations
            var firstDeleteRuns = deletedRuns.Where(r => r.Author?.Value == "Delete Author").ToList();
            var replaceInsertRuns = insertedRuns.Where(r => r.Author?.Value == "Replace Author").ToList();

            Assert.NotEmpty(firstDeleteRuns);
            Assert.NotEmpty(replaceInsertRuns);

            // Verify comments structure
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 2);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_MultipleAuthorsSequentialEdits_ShouldMaintainRevisionHistory()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_MultipleAuthors.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_MultipleAuthors_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply a sequence of operations by different authors
            var patches = CreateMultipleAuthorRevisionSequence();

            foreach (var patch in patches)
            {
                var result = applier.ApplyPatch(modifiedRevisionFilePath, patch);
                Assert.True(result);
            }

            // Assert
            // Verify document content and revision history
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // Should have revisions from multiple authors
            var authors = insertedRuns.Select(r => r.Author?.Value).Concat(deletedRuns.Select(r => r.Author?.Value)).Distinct().Where(a => !string.IsNullOrEmpty(a)).ToList();
            Assert.True(authors.Count >= 3); // At least 3 different authors

            // Verify chronological order of revisions
            var allRevisions = insertedRuns.Cast<OpenXmlElement>().Concat(deletedRuns.Cast<OpenXmlElement>()).ToList();
            var revisionDates = allRevisions.Select(r =>
            {
                if (r is InsertedRun ir && ir.Date?.Value != null && DateTime.TryParse(ir.Date.Value.ToString(), out var insertDate))
                    return insertDate;
                if (r is DeletedRun dr && dr.Date?.Value != null && DateTime.TryParse(dr.Date.Value.ToString(), out var deleteDate))
                    return deleteDate;
                return DateTime.MinValue;
            }).Where(d => d != DateTime.MinValue).ToList();

            // Verify dates are in chronological order (allowing for some time variance)
            for (int i = 1; i < revisionDates.Count; i++)
            {
                Assert.True(revisionDates[i] >= revisionDates[i - 1].AddSeconds(-1)); // Allow 1 second variance
            }

            // Verify comments for all operations
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 3);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_OverlappingRanges_ShouldHandleCorrectly()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_OverlappingRanges.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_OverlappingRanges_Modified.DOCX");
            CreateDocumentWithLongerText(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply overlapping operations
            var overlappingPatches = CreateOverlappingRevisionPatches();

            foreach (var patch in overlappingPatches)
            {
                var result = applier.ApplyPatch(modifiedRevisionFilePath, patch);
                Assert.True(result);
            }

            // Assert
            // Verify document structure is maintained
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var body = doc.MainDocumentPart.Document.Body;
            Assert.NotNull(body);

            // Verify revisions are properly nested/structured
            var insertedRuns = body.Descendants<InsertedRun>().ToList();
            var deletedRuns = body.Descendants<DeletedRun>().ToList();

            Assert.NotEmpty(insertedRuns);
            Assert.NotEmpty(deletedRuns);

            // Verify document is still valid and readable
            var allText = string.Join("", body.Descendants<Text>().Select(t => t.Text));
            Assert.NotEmpty(allText);

            // Verify comments are properly associated
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.NotEmpty(comments);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_AcceptRejectScenario_ShouldMaintainIntegrity()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_AcceptReject.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_AcceptReject_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply initial revision
            var initialPatch = CreateTwoRevisionPatch();
            var initialResult = applier.ApplyPatch(modifiedRevisionFilePath, initialPatch);
            Assert.True(initialResult, "Initial patch application failed");

            // Apply revision to the revision (simulating further editing before accept/reject)
            var secondPatch = CreateRevisionOnRevisionPatch();
            var secondResult = applier.ApplyPatch(modifiedRevisionFilePath, secondPatch);
            Assert.True(secondResult, "Second patch application failed");

            // Apply final revision (simulating reviewer's additional changes)
            var finalPatch = CreateFinalRevisionPatch();
            var finalResult = applier.ApplyPatch(modifiedRevisionFilePath, finalPatch);

            // Assert
            Assert.True(finalResult, "Final patch application failed");

            // Verify complex revision structure
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // Should have multiple layers of revisions
            Assert.True(insertedRuns.Count >= 2);

            // Verify different revision IDs
            var revisionIds = insertedRuns.Select(r => r.Id?.Value).Concat(deletedRuns.Select(r => r.Id?.Value)).Distinct().Where(id => !string.IsNullOrEmpty(id)).ToList();
            Assert.True(revisionIds.Count >= 2);

            // Verify comments maintain proper relationships
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 2);

            // Verify comment range markers are properly structured
            var commentRangeStarts = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeEnd>().ToList();

            Assert.Equal(commentRangeStarts.Count, commentRangeEnds.Count);
            Assert.True(commentRangeStarts.Count >= 2);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_FormattingChangesOnRevisions_ShouldPreserveFormatting()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Formatting.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Formatting_Modified.DOCX");
            CreateDocumentWithFormattedText(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply initial revision with formatting
            var formattedPatch = CreateFormattedRevisionPatch();
            var formattedResult = applier.ApplyPatch(modifiedRevisionFilePath, formattedPatch);
            Assert.True(formattedResult);

            // Apply another revision that modifies the formatted text
            var modifyFormattedPatch = CreateModifyFormattedRevisionPatch();
            var modifyResult = applier.ApplyPatch(modifiedRevisionFilePath, modifyFormattedPatch);

            // Assert
            Assert.True(modifyResult);

            // Verify document content and formatting preservation
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            Assert.NotEmpty(insertedRuns);

            // Verify formatting properties are preserved in revisions
            var formattedInsertedRuns = insertedRuns.Where(r => r.Descendants<RunProperties>().Any()).ToList();
            Assert.NotEmpty(formattedInsertedRuns);

            // Verify comments for formatting operations
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 2);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_NestedComments_ShouldCreateHierarchicalStructure()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_NestedComments.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_NestedComments_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply revisions with nested comment structure
            var nestedCommentPatches = CreateNestedCommentRevisionPatches();

            foreach (var patch in nestedCommentPatches)
            {
                var result = applier.ApplyPatch(modifiedRevisionFilePath, patch);
                Assert.True(result);
            }

            // Assert
            // Verify nested comment structure
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 3);

            // Verify comment range markers are properly nested
            var commentRangeStarts = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeStart>().ToList();
            var commentRangeEnds = doc.MainDocumentPart.Document.Body.Descendants<CommentRangeEnd>().ToList();
            var commentReferences = doc.MainDocumentPart.Document.Body.Descendants<CommentReference>().ToList();

            Assert.Equal(commentRangeStarts.Count, commentRangeEnds.Count);
            Assert.Equal(commentRangeStarts.Count, commentReferences.Count);
            Assert.True(commentRangeStarts.Count >= 3);

            // Verify revisions are properly associated with comments
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            Assert.True(insertedRuns.Count >= 3);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_ConflictingOperations_ShouldHandleGracefully()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Conflicts.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_Conflicts_Modified.DOCX");
            CreateDocumentWithExistingRevisions(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply conflicting operations (operations that target the same text range)
            var conflictingPatches = CreateConflictingRevisionPatches();

            var results = new List<bool>();
            foreach (var patch in conflictingPatches)
            {
                var result = applier.ApplyPatch(modifiedRevisionFilePath, patch);
                results.Add(result);
            }

            // Assert
            // At least some operations should succeed (graceful handling)
            Assert.Contains(true, results);

            // Verify document structure is maintained despite conflicts
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var body = doc.MainDocumentPart.Document.Body;
            Assert.NotNull(body);

            // Verify document is still readable
            var allText = string.Join("", body.Descendants<Text>().Select(t => t.Text));
            Assert.NotEmpty(allText);

            // Verify some revisions were applied
            var insertedRuns = body.Descendants<InsertedRun>().ToList();
            var deletedRuns = body.Descendants<DeletedRun>().ToList();
            Assert.True(insertedRuns.Count > 0 || deletedRuns.Count > 0);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_LargeScaleOperations_ShouldMaintainPerformance()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_LargeScale.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_LargeScale_Modified.DOCX");
            CreateLargeDocumentForRevisionTesting(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply many sequential revisions
            var largeScalePatches = CreateLargeScaleRevisionPatches();

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            foreach (var patch in largeScalePatches)
            {
                var result = applier.ApplyPatch(modifiedRevisionFilePath, patch);
                Assert.True(result);
            }

            stopwatch.Stop();

            // Assert
            // Performance check - should complete within reasonable time (adjust as needed)
            Assert.True(stopwatch.ElapsedMilliseconds < 30000); // 30 seconds max

            // Verify all revisions were applied correctly
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var insertedRuns = doc.MainDocumentPart.Document.Body.Descendants<InsertedRun>().ToList();
            var deletedRuns = doc.MainDocumentPart.Document.Body.Descendants<DeletedRun>().ToList();

            // Should have many revisions
            Assert.True(insertedRuns.Count >= 10);

            // Verify comments were created for all operations
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 10);
        }

        [UnsupportedFeatureFact("Multiple patch application on same document")]
        public void IntegrationTest_ReviseExistingRevision_CrossParagraphOperations_ShouldHandleCorrectly()
        {
            // Arrange
            var revisionTestFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_CrossParagraph.DOCX");
            var modifiedRevisionFilePath = Path.Combine(_testResultsDir, "ReviseExistingRevision_CrossParagraph_Modified.DOCX");
            CreateMultiParagraphDocumentForRevisionTesting(revisionTestFilePath);
            File.Copy(revisionTestFilePath, modifiedRevisionFilePath, true);

            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Apply revisions across multiple paragraphs
            var crossParagraphPatches = CreateCrossParagraphRevisionPatches();

            foreach (var patch in crossParagraphPatches)
            {
                var result = applier.ApplyPatch(modifiedRevisionFilePath, patch);
                Assert.True(result);
            }

            // Assert
            // Verify revisions span multiple paragraphs correctly
            using var doc = WordprocessingDocument.Open(modifiedRevisionFilePath, false);
            var paragraphs = doc.MainDocumentPart.Document.Body.Elements<Paragraph>().ToList();
            Assert.True(paragraphs.Count >= 3);

            // Verify revisions exist in multiple paragraphs
            var paragraphsWithRevisions = paragraphs.Where(p =>
                p.Descendants<InsertedRun>().Any() || p.Descendants<DeletedRun>().Any()).ToList();
            Assert.True(paragraphsWithRevisions.Count >= 2);

            // Verify comments are properly distributed
            var comments = doc.MainDocumentPart.WordprocessingCommentsPart?.Comments?.Elements<WordComment>().ToList();
            Assert.NotNull(comments);
            Assert.True(comments.Count >= 3);
        }

        #region Additional Helper Methods for Complex Revision Tests

        private void CreateDocumentWithExistingRevisions(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("This is the original text for revision testing.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateDocumentWithLongerText(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("This is a longer text document that will be used for testing overlapping revision ranges and complex editing scenarios.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private WordTextPatch CreateTwoRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "first-revision-test",
                Operations =
                [
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "[FIRST] ",
                        Revision = new Revision
                        {
                            Author = "First Author",
                            Date = DateTime.Now.AddMinutes(-30)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "first_revision",
                            Text = "First revision comment"
                        }
                    }
                ]
            };
        }

        private WordTextPatch CreateSecondRevisionInsertPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "second-revision-insert-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 10, End = 10 },
                        Text = "[SECOND] ",
                        Revision = new Revision
                        {
                            Author = "Second Author",
                            Date = DateTime.Now.AddMinutes(-20)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "second_revision",
                            Text = "Second revision on first revision"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateDeleteInsertedTextPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "delete-inserted-text-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 5, End = 15 },
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Delete Author",
                            Date = DateTime.Now.AddMinutes(-15)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "delete_revision",
                            Text = "Deleting previously inserted text"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateFirstDeleteRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "first-delete-revision-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 10, End = 20 },
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Delete Author",
                            Date = DateTime.Now.AddMinutes(-25)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "first_delete",
                            Text = "First delete operation"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateReplaceDeletedTextPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "replace-deleted-text-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 15, End = 15 },
                        Text = "[REPLACEMENT TEXT]",
                        Revision = new Revision
                        {
                            Author = "Replace Author",
                            Date = DateTime.Now.AddMinutes(-10)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "replace_revision",
                            Text = "Replacing deleted text"
                        }
                    }
                }
            };
        }

        private List<WordTextPatch> CreateMultipleAuthorRevisionSequence()
        {
            return new List<WordTextPatch>
            {
                new WordTextPatch
                {
                    DocumentId = "multi-author-1",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                            Text = "[AUTHOR1] ",
                            Revision = new Revision
                            {
                                Author = "Author One",
                                Date = DateTime.Now.AddMinutes(-40)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "author1_comment",
                                Text = "First author's revision"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "multi-author-2",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 10, End = 10 },
                            Text = "[AUTHOR2] ",
                            Revision = new Revision
                            {
                                Author = "Author Two",
                                Date = DateTime.Now.AddMinutes(-30)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "author2_comment",
                                Text = "Second author's revision"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "multi-author-3",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Delete,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 20, End = 25 },
                            Text = "",
                            Revision = new Revision
                            {
                                Author = "Author Three",
                                Date = DateTime.Now.AddMinutes(-20)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "author3_comment",
                                Text = "Third author's deletion"
                            }
                        }
                    }
                }
            };
        }

        private List<WordTextPatch> CreateOverlappingRevisionPatches()
        {
            return new List<WordTextPatch>
            {
                new WordTextPatch
                {
                    DocumentId = "overlap-1",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 10, End = 10 },
                            Text = "[OVERLAP1] ",
                            Revision = new Revision
                            {
                                Author = "Overlap Author 1",
                                Date = DateTime.Now.AddMinutes(-18)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "overlap1",
                                Text = "First overlapping revision"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "overlap-2",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Delete,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 15, End = 25 },
                            Text = "",
                            Revision = new Revision
                            {
                                Author = "Overlap Author 2",
                                Date = DateTime.Now.AddMinutes(-16)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "overlap2",
                                Text = "Second overlapping revision"
                            }
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateRevisionOnRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "revision-on-revision-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 15, End = 15 },
                        Text = "[SECOND] ",
                        Revision = new Revision
                        {
                            Author = "Nested Author",
                            Date = DateTime.Now.AddMinutes(-12)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "nested_revision",
                            Text = "Revision on existing revision"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateFinalRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "final-revision-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 30, End = 30 },
                        Text = "[THIRD] ",
                        Revision = new Revision
                        {
                            Author = "Final Author",
                            Date = DateTime.Now.AddMinutes(-5)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "final_revision",
                            Text = "Final revision in sequence"
                        }
                    }
                }
            };
        }

        private void CreateDocumentWithFormattedText(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" },
                        new Bold(),
                        new Italic(),
                        new Color() { Val = "FF0000" }
                    ),
                    new Text("This is formatted text for revision testing.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateLargeDocumentForRevisionTesting(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var body = new Body();

            // Create multiple paragraphs with substantial content
            for (int i = 1; i <= 5; i++)
            {
                var paragraph = new Paragraph(
                    new ParagraphProperties(
                        new ParagraphStyleId() { Val = "Normal" }
                    ),
                    new Run(
                        new RunProperties(
                            new FontSize() { Val = "24" },
                            new RunFonts() { Ascii = "Times New Roman" }
                        ),
                        new Text($"Paragraph {i}: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.")
                    )
                )
                {
                    ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue(i.ToString())
                };
                body.Append(paragraph);
            }

            mainPart.Document = new Document(body);
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateMultiParagraphDocumentForRevisionTesting(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph1 = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("First paragraph for cross-paragraph revision testing.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            var paragraph2 = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Second paragraph with additional content for testing.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("2")
            };

            var paragraph3 = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Third paragraph to complete the multi-paragraph structure.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("3")
            };

            mainPart.Document = new Document(new Body(paragraph1, paragraph2, paragraph3));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private WordTextPatch CreateFormattedRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "formatted-revision-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "[FORMATTED] ",
                        Props = new Dictionary<string, object>
                        {
                            { "bold", true },
                            { "italic", true },
                            { "color", "0000FF" }
                        },
                        Revision = new Revision
                        {
                            Author = "Format Author",
                            Date = DateTime.Now.AddMinutes(-12)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "format1",
                            Text = "Added formatted text"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateModifyFormattedRevisionPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "modify-formatted-revision-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 5, End = 15 },
                        Text = "[MODIFIED]",
                        Props = new Dictionary<string, object>
                        {
                            { "bold", false },
                            { "underline", true },
                            { "color", "00FF00" }
                        },
                        Revision = new Revision
                        {
                            Author = "Modify Author",
                            Date = DateTime.Now.AddMinutes(-6)
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "format2",
                            Text = "Modified formatting of existing revision"
                        }
                    }
                }
            };
        }

        private List<WordTextPatch> CreateNestedCommentRevisionPatches()
        {
            return new List<WordTextPatch>
            {
                new WordTextPatch
                {
                    DocumentId = "nested-comment-1",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                            Text = "[PARENT] ",
                            Revision = new Revision
                            {
                                Author = "Parent Author",
                                Date = DateTime.Now.AddMinutes(-15)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "parent_comment",
                                Text = "Parent comment for nested structure"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "nested-comment-2",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 5, End = 5 },
                            Text = "[CHILD] ",
                            Revision = new Revision
                            {
                                Author = "Child Author",
                                Date = DateTime.Now.AddMinutes(-10)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "child_comment",
                                Text = "Child comment nested within parent"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "nested-comment-3",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 10, End = 10 },
                            Text = "[GRANDCHILD] ",
                            Revision = new Revision
                            {
                                Author = "Grandchild Author",
                                Date = DateTime.Now.AddMinutes(-5)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "grandchild_comment",
                                Text = "Grandchild comment in deep nesting"
                            }
                        }
                    }
                }
            };
        }

        private List<WordTextPatch> CreateConflictingRevisionPatches()
        {
            return new List<WordTextPatch>
            {
                new WordTextPatch
                {
                    DocumentId = "conflict-1",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Delete,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 10, End = 20 },
                            Text = "",
                            Revision = new Revision
                            {
                                Author = "Conflict Author 1",
                                Date = DateTime.Now.AddMinutes(-8)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "conflict1",
                                Text = "First conflicting operation"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "conflict-2",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Replace,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 15, End = 25 }, // Overlapping range
                            Text = "[CONFLICTING REPLACEMENT]",
                            Revision = new Revision
                            {
                                Author = "Conflict Author 2",
                                Date = DateTime.Now.AddMinutes(-7)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "conflict2",
                                Text = "Second conflicting operation"
                            }
                        }
                    }
                }
            };
        }

        private List<WordTextPatch> CreateLargeScaleRevisionPatches()
        {
            var patches = new List<WordTextPatch>();
            var baseTime = DateTime.Now.AddMinutes(-60);

            for (int i = 1; i <= 15; i++)
            {
                patches.Add(new WordTextPatch
                {
                    DocumentId = $"large-scale-{i}",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = i % 3 == 0 ? OperationType.Delete : (i % 2 == 0 ? OperationType.Replace : OperationType.Insert),
                            Target = new Target(((i % 5) + 1).ToString() + "-0" ),
                            Range = new ApplyRevision.Model.Range
                            {
                                Start = (i * 5) % 50,
                                End = i % 3 == 0 ? ((i * 5) % 50) + 10 : (i % 2 == 0 ? ((i * 5) % 50) + 5 : (i * 5) % 50)
                            },
                            Text = i % 3 == 0 ? "" : $"[REVISION-{i}] ",
                            Revision = new Revision
                            {
                                Author = $"Author {i % 3 + 1}",
                                Date = baseTime.AddMinutes(i * 2)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = $"large_scale_{i}",
                                Text = $"Large scale revision operation {i}"
                            }
                        }
                    }
                });
            }

            return patches;
        }

        private List<WordTextPatch> CreateCrossParagraphRevisionPatches()
        {
            return new List<WordTextPatch>
            {
                new WordTextPatch
                {
                    DocumentId = "cross-para-1",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("1-0"),
                            Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                            Text = "[PARA1] ",
                            Revision = new Revision
                            {
                                Author = "Cross Para Author 1",
                                Date = DateTime.Now.AddMinutes(-20)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "cross1",
                                Text = "First paragraph revision"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "cross-para-2",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Insert,
                            Target = new("2-0"),
                            Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                            Text = "[PARA2] ",
                            Revision = new Revision
                            {
                                Author = "Cross Para Author 2",
                                Date = DateTime.Now.AddMinutes(-15)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "cross2",
                                Text = "Second paragraph revision"
                            }
                        }
                    }
                },
                new WordTextPatch
                {
                    DocumentId = "cross-para-3",
                    Operations = new List<Operation>
                    {
                        new Operation
                        {
                            Op = OperationType.Delete,
                            Target = new("3-0"),
                            Range = new ApplyRevision.Model.Range { Start = 10, End = 20 },
                            Text = "",
                            Revision = new Revision
                            {
                                Author = "Cross Para Author 3",
                                Date = DateTime.Now.AddMinutes(-10)
                            },
                            Comment = new ApplyRevision.Model.Comment
                            {
                                CommentId = "cross3",
                                Text = "Third paragraph deletion"
                            }
                        }
                    }
                }
            };
        }

        #endregion

        private WordTextPatch CreateComplexCommentPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Parent Comment",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "1",
                            Text = "Parent Comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Child Comment",
                        Revision = new Revision
                        {
                            Author = "Test Author111",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "2",
                            Text = "Child Comment"
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Reply Comment",
                        Revision = new Revision
                        {
                            Author = "Test Author222",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "3",
                            Text = "Reply Comment"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateInsertMiddlePatch()
        {
            return new WordTextPatch
            {
                DocumentId = "insert-middle-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 20, End = 20 }, // 在 "fox" 和 "jumps" 之间插入
                        Text = " very fast",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private void CreateDeleteTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("The quick brown fox jumps over the lazy dog.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateReplaceTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("The quick brown fox jumps over the lazy dog.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateMultipleOperationsTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("This is old text that needs modification.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateCommentTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("This text will have a comment.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateMultiParagraphTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph1 = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("First paragraph content.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            var paragraph2 = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Second paragraph content.")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("2")
            };

            mainPart.Document = new Document(new Body(paragraph1, paragraph2));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateEmptyTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateLargeTextTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var largeText = string.Join(" ", Enumerable.Repeat("Lorem ipsum dolor sit amet, consectetur adipiscing elit.", 100));

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text(largeText)
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private void CreateSpecialCharactersTestDocument(string filePath)
        {
            using var doc = WordprocessingDocument.Create(filePath, WordprocessingDocumentType.Document);
            var mainPart = doc.AddMainDocumentPart();

            var paragraph = new Paragraph(
                new ParagraphProperties(
                    new ParagraphStyleId() { Val = "Normal" }
                ),
                new Run(
                    new RunProperties(
                        new FontSize() { Val = "24" },
                        new RunFonts() { Ascii = "Times New Roman" }
                    ),
                    new Text("Text with special characters: àáâãäåæçèéêë")
                )
            )
            {
                ParagraphId = new DocumentFormat.OpenXml.HexBinaryValue("1")
            };

            mainPart.Document = new Document(new Body(paragraph));
            var commentsPart = mainPart.AddNewPart<WordprocessingCommentsPart>();
            commentsPart.Comments = new Comments();
        }

        private WordTextPatch CreateDeletePatch()
        {
            return new WordTextPatch
            {
                DocumentId = "delete-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 10, End = 15 }, // Delete "brown"
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateReplacePatch()
        {
            return new WordTextPatch
            {
                DocumentId = "replace-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 16, End = 19 }, // Replace "fox" with "cat"
                        Text = "cat",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateMultipleOperationsPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "multi-op-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "NEW: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Delete,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 8, End = 11 }, // Delete "old"
                        Text = "",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Replace,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 30, End = 34 }, // Replace "text" at the end
                        Text = "content",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateCommentPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "comment-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Commented text: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        },
                        Comment = new ApplyRevision.Model.Comment
                        {
                            CommentId = "comment1",
                            Text = "This is a test comment"
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateMultiParagraphPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "multi-para-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Modified: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    },
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("2-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Also modified: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateEmptyDocumentPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "empty-doc-test",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "Added to empty document",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateLargeTextPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "large-text-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 100, End = 100 },
                        Text = " [INSERTED IN LARGE TEXT] ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        private WordTextPatch CreateSpecialCharactersPatch()
        {
            return new WordTextPatch
            {
                DocumentId = "special-char-test-doc",
                Operations = new List<Operation>
                {
                    new Operation
                    {
                        Op = OperationType.Insert,
                        Target = new("1-0"),
                        Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                        Text = "© ™ ® symbols: ",
                        Revision = new Revision
                        {
                            Author = "Test Author",
                            Date = DateTime.Now
                        }
                    }
                }
            };
        }

        public void Dispose()
        {
            // 不再自动清理测试结果目录，以便检查测试结果
            // 手动清理可以通过删除 TestResults 目录来完成
        }
    }
}