using System;
using System.IO;
using System.Linq;
using Amazon.Lambda.Core;
using ApplyRevision.Helper;
using ApplyRevision.Model;
using DocumentFormat.OpenXml.Packaging;
using Moq;
using Newtonsoft.Json;
using Xunit;

namespace ApplyRevision.Tests.Helper.Test
{
    public class RealDocumentTests : IDisposable
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<ILambdaLogger> _mockLogger;
        private readonly string _testResultsDir;

        public RealDocumentTests()
        {
            // Setup mock objects / 设置模拟对象
            _mockContext = new Mock<ILambdaContext>();
            _mockLogger = new Mock<ILambdaLogger>();
            _mockContext.Setup(c => c.Logger).Returns(_mockLogger.Object);

            // Setup logger to output to console / 设置日志记录器输出到控制台
            _mockLogger.Setup(x => x.LogInformation(It.IsAny<string>()))
                      .Callback<string>(message => Console.WriteLine($"[INFO] {message}"));
            _mockLogger.Setup(x => x.Log<PERSON>rror(It.IsAny<string>()))
                      .Callback<string>(message => Console.WriteLine($"[ERROR] {message}"));
            _mockLogger.Setup(x => x.LogWarning(It.IsAny<string>()))
                      .Callback<string>(message => Console.WriteLine($"[WARNING] {message}"));

            // Create test results directory / 创建测试结果目录
            _testResultsDir = Path.Combine(Directory.GetCurrentDirectory(), "TestResults", "RealDocument");
            Directory.CreateDirectory(_testResultsDir);
        }

        /// <summary>
        /// Tests applying the real Dog patches to the Dog attacks document.
        /// 测试将真实的Dog补丁应用到Dog attacks文档。
        /// </summary>
        [Fact]
        public void ApplyDogPatches_ToRealDocument_ShouldSucceed()
        {
            // Arrange / 准备
            var modifiedDocumentPath = Path.Combine(_testResultsDir, "Dog_attacks_Modified.docx");
            var beforeCopyPath = Path.Combine(_testResultsDir, "Dog_attacks_Before.docx");

            // Set file paths / 设置文件路径
            var _originalDocumentPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "Dog attacks.docx");
            var _patchFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "Dog_patchs-simple.json");

            // Verify test files exist / 验证测试文件存在
            Assert.True(File.Exists(_originalDocumentPath),
                $"Original document not found: {_originalDocumentPath}. " +
                "Ensure the 'Dog attacks.docx' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");

            Assert.True(File.Exists(_patchFilePath),
                $"Patch file not found: {_patchFilePath}. " +
                "Ensure the 'Dog_patchs.json' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");


            // Copy original document to preserve it and create a working copy / 复制原始文档以保留它并创建工作副本
            File.Copy(_originalDocumentPath, beforeCopyPath, true);
            File.Copy(_originalDocumentPath, modifiedDocumentPath, true);

            // Load patch from JSON file / 从JSON文件加载补丁
            var patchJson = File.ReadAllText(_patchFilePath);
            var patch = JsonConvert.DeserializeObject<WordTextPatch>(patchJson);

            Assert.NotNull(patch);
            Assert.NotNull(patch.Operations);
            Assert.NotEmpty(patch.Operations);

            // Create the patch applier / 创建补丁应用程序
            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Act / 执行
            var result = applier.ApplyPatch(modifiedDocumentPath, patch);

            // Log the result for debugging / 记录结果用于调试
            Console.WriteLine($"Patch application result: {result}");
            Console.WriteLine($"Total operations to apply: {patch.Operations.Count}");

            // Group operations by type for diagnosis / 按类型分组操作以便诊断
            var operationGroups = patch.Operations.GroupBy(op => op.Op).ToList();
            foreach (var group in operationGroups)
            {
                Console.WriteLine($"Operation type {group.Key}: {group.Count()} operations");
            }

            // Assert / 验证
            Assert.True(result, "Patch application should succeed. Check console output for operation details.");

            // Verify the modified document exists and is valid / 验证修改后的文档存在且有效
            Assert.True(File.Exists(modifiedDocumentPath), $"Modified document not found: {modifiedDocumentPath}");

            // Validate document structure integrity / 验证文档结构完整性
            using (var doc = WordprocessingDocument.Open(modifiedDocumentPath, false))
            {
                Assert.NotNull(doc.MainDocumentPart);
                Assert.NotNull(doc.MainDocumentPart.Document);
                Assert.NotNull(doc.MainDocumentPart.Document.Body);
            }

            // Log test results for manual verification / 记录测试结果以供手动验证
            Console.WriteLine($"Test completed successfully!");
            Console.WriteLine($"Original document: {beforeCopyPath}");
            Console.WriteLine($"Modified document: {modifiedDocumentPath}");
            Console.WriteLine($"Total operations applied: {patch.Operations.Count}");
        }

        [Fact]

        public void ApplyGN13Patches_ToRealDocument_ShouldSucceed()
        {
            // Arrange / 准备
            var modifiedDocumentPath = Path.Combine(_testResultsDir, "gn13_Modified.docx");
            var beforeCopyPath = Path.Combine(_testResultsDir, "gn13_Before.docx");

            // Set file paths / 设置文件路径
            var _originalDocumentPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "gn13.docx");
            var _patchFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "gn13_patchs.json");

            // Verify test files exist / 验证测试文件存在
            Assert.True(File.Exists(_originalDocumentPath),
                $"Original document not found: {_originalDocumentPath}. " +
                "Ensure the 'Dog attacks.docx' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");

            Assert.True(File.Exists(_patchFilePath),
                $"Patch file not found: {_patchFilePath}. " +
                "Ensure the 'Dog_patchs.json' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");

            // Copy original document to preserve it and create a working copy / 复制原始文档以保留它并创建工作副本
            File.Copy(_originalDocumentPath, beforeCopyPath, true);
            File.Copy(_originalDocumentPath, modifiedDocumentPath, true);

            // Load patch from JSON file / 从JSON文件加载补丁
            var patchJson = File.ReadAllText(_patchFilePath);
            var patch = JsonConvert.DeserializeObject<WordTextPatch>(patchJson);

            Assert.NotNull(patch);
            Assert.NotNull(patch.Operations);
            Assert.NotEmpty(patch.Operations);

            // Create the patch applier / 创建补丁应用程序
            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Act / 执行
            var result = applier.ApplyPatch(modifiedDocumentPath, patch);

            // Log the result for debugging / 记录结果用于调试
            Console.WriteLine($"Patch application result: {result}");
            Console.WriteLine($"Total operations to apply: {patch.Operations.Count}");

            // Group operations by type for diagnosis / 按类型分组操作以便诊断
            var operationGroups = patch.Operations.GroupBy(op => op.Op).ToList();
            foreach (var group in operationGroups)
            {
                Console.WriteLine($"Operation type {group.Key}: {group.Count()} operations");
            }

            // Assert / 验证
            Assert.True(result, "Patch application should succeed. Check console output for operation details.");

            // Verify the modified document exists and is valid / 验证修改后的文档存在且有效
            Assert.True(File.Exists(modifiedDocumentPath), $"Modified document not found: {modifiedDocumentPath}");

            // Validate document structure integrity / 验证文档结构完整性
            using (var doc = WordprocessingDocument.Open(modifiedDocumentPath, false))
            {
                Assert.NotNull(doc.MainDocumentPart);
                Assert.NotNull(doc.MainDocumentPart.Document);
                Assert.NotNull(doc.MainDocumentPart.Document.Body);
            }

            // Log test results for manual verification / 记录测试结果以供手动验证
            Console.WriteLine($"Test completed successfully!");
            Console.WriteLine($"Original document: {beforeCopyPath}");
            Console.WriteLine($"Modified document: {modifiedDocumentPath}");
            Console.WriteLine($"Total operations applied: {patch.Operations.Count}");
        }

        [Fact]
        public void _3ConPatches_ShouldSucceed()
        {
            // Arrange / 准备
            var modifiedDocumentPath = Path.Combine(_testResultsDir, "3con_Modified.docx");
            var beforeCopyPath = Path.Combine(_testResultsDir, "3con_Before.docx");

            // Set file paths / 设置文件路径
            var _originalDocumentPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "3con.docx");
            var _patchFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Files", "3con_patchs.json");

            // Verify test files exist / 验证测试文件存在
            Assert.True(File.Exists(_originalDocumentPath),
                $"Original document not found: {_originalDocumentPath}. " +
                "Ensure the 'Dog attacks.docx' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");

            Assert.True(File.Exists(_patchFilePath),
                $"Patch file not found: {_patchFilePath}. " +
                "Ensure the 'Dog_patchs.json' file is in the Files directory with 'Copy to Output Directory' set to 'Copy always'.");

            // Copy original document to preserve it and create a working copy / 复制原始文档以保留它并创建工作副本
            File.Copy(_originalDocumentPath, beforeCopyPath, true);
            File.Copy(_originalDocumentPath, modifiedDocumentPath, true);

            // Load patch from JSON file / 从JSON文件加载补丁
            var patchJson = File.ReadAllText(_patchFilePath);
            var patch = JsonConvert.DeserializeObject<WordTextPatch>(patchJson);

            Assert.NotNull(patch);
            Assert.NotNull(patch.Operations);
            Assert.NotEmpty(patch.Operations);

            // Create the patch applier / 创建补丁应用程序
            var applier = new WordTextPatchApplier(_mockContext.Object);

            // Act / 执行
            var result = applier.ApplyPatch(modifiedDocumentPath, patch);

            // Log the result for debugging / 记录结果用于调试
            Console.WriteLine($"Patch application result: {result}");
            Console.WriteLine($"Total operations to apply: {patch.Operations.Count}");

            // Group operations by type for diagnosis / 按类型分组操作以便诊断
            var operationGroups = patch.Operations.GroupBy(op => op.Op).ToList();
            foreach (var group in operationGroups)
            {
                Console.WriteLine($"Operation type {group.Key}: {group.Count()} operations");
            }
        }

        public void Dispose()
        {
            // Cleanup is optional since test results should be preserved for manual verification
            // 清理是可选的，因为测试结果应该被保留以供手动验证
            GC.SuppressFinalize(this);
        }
    }
}