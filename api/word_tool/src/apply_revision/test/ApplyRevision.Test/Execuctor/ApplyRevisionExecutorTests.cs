using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Amazon.Lambda.Core;
using Amazon.S3.Model;
using ApplyRevision.Executor;
using ApplyRevision.Model;
using Common.Abstract;
using Common.Model;
using Moq;
using Xunit;

namespace ApplyRevision.Tests
{
    public class ApplyRevisionExecutorTests
    {
        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenEventIsNull()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);
            ApplyRevisionEvent applyRevisionEvent = null;

            var executor = new ApplyRevisionExecutor(applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Event is empty.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenDocumentIsInvalid()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);
            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = null,
                Revision = new S3Request { Bucket = "test-bucket", Key = "test-key" }
            };

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("The document bucket or key is empty.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenRevisionIsInvalid()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);
            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = new S3Request { Bucket = "test-bucket", Key = "test-key" },
                Revision = null
            };

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("The revision bucket or key is empty.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenS3ObjectsAreEmpty()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);

            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = new S3Request { Bucket = "test-bucket", Key = "test-key" },
                Revision = new S3Request { Bucket = "test-bucket", Key = "test-key" }
            };

            mockS3Service.Setup(s => s.GetObjectAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new GetObjectResponse { ContentLength = 0 });

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("The document is empty.", result.Message);
        }


        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenDocumentAreEmpty() {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);

            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = new S3Request { Bucket = "test-bucket", Key = "test-key" },
                Revision = new S3Request { Bucket = "test-bucket", Key = "test-key" }
            };

            mockS3Service.Setup(s => s.GetObjectAsync(It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((GetObjectResponse)null);

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Request Document or Revision failed.", result.Message);
        }

        [Fact]
        public async Task Run_ShouldReturnErrorResponse_WhenErrorHappened()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);

            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = new S3Request { Bucket = "test-bucket", Key = "test-key.docx" },
                Revision = new S3Request { Bucket = "test-bucket", Key = "test-revision.json" }
            };

            var mockDocumentResponse = new GetObjectResponse
            {
                ContentLength = 1000,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                ResponseStream = GetTestFileStream("Files/test.docx")
            };

            var mockRevisionResponse = new GetObjectResponse
            {
                ContentLength = 1000,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                ResponseStream = GetTestFileStream("Files/test_changes.json")
            };

            mockS3Service.Setup(s => s.GetObjectAsync("test-bucket", "test-key.docx"))
                .ReturnsAsync(mockDocumentResponse);

            mockS3Service.Setup(s => s.GetObjectAsync("test-bucket", "test-revision.json"))
                .ReturnsAsync(mockRevisionResponse);

            mockS3Service.Setup(s => s.PutObjectAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Error testing"));

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(400, result.Code);
            Assert.Equal("Apply revision changes failed.", result.Message);
        }

        public MemoryStream GetTestFileStream(string filePath)
        {
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var memoryStream = new MemoryStream();
            fileStream.CopyTo(memoryStream);
            memoryStream.Position = 0; // Reset the position to the beginning of the stream  
            return memoryStream;
        }

        [Fact]
        public async Task Run_ShouldReturnSuccessResponse_WhenExecutionIsSuccessful()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);

            var applyRevisionEvent = new ApplyRevisionEvent
            {
                Document = new S3Request { Bucket = "test-bucket", Key = "test-key.docx" },
                Revision = new S3Request { Bucket = "test-bucket", Key = "test-revision.json" }
            };

            var mockDocumentResponse = new GetObjectResponse
            {
                ContentLength = 1000,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                ResponseStream = GetTestFileStream("Files/test.docx")
            };

            var mockRevisionResponse = new GetObjectResponse
            {
                ContentLength = 1000,
                HttpStatusCode = System.Net.HttpStatusCode.OK,
                ResponseStream = GetTestFileStream("Files/test_changes.json")
            };

            mockS3Service.Setup(s => s.GetObjectAsync("test-bucket", "test-key.docx"))
                .ReturnsAsync(mockDocumentResponse);

            mockS3Service.Setup(s => s.GetObjectAsync("test-bucket", "test-revision.json"))
                .ReturnsAsync(mockRevisionResponse);

            mockS3Service.Setup(s => s.PutObjectAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(true);

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.Code);
            Assert.NotNull(result.Data);

            var data = result.Data as S3Request;
            Assert.Equal("test-bucket", data.Bucket);
            Assert.StartsWith("test-key", data.Key);
        }


        [Fact]
        public async Task Run_ReturnsOriginalDocument_WhenDocRevisionChangesIsNullOrEmpty()
        {
            // Arrange
            var mockS3Service = new Mock<IS3Service>();
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger).Returns(new Mock<ILambdaLogger>().Object);

            var document = new S3Request { Bucket = "bucket", Key = "doc.docx" };
            var revision = new S3Request { Bucket = "bucket", Key = "rev.json" };
            var applyRevisionEvent = new ApplyRevisionEvent { Document = document, Revision = revision };

            // Mock S3 GetObjectAsync to return a non-empty document and a revision with empty changes
            var docResponse = new GetObjectResponse
            {
                ContentLength = 1,
                ResponseStream = new MemoryStream(new byte[] { 1, 2, 3 })
            };
            var revisionResponse = new GetObjectResponse
            {
                ContentLength = 1,
                ResponseStream = new MemoryStream(Encoding.UTF8.GetBytes("[]"))
            };
            mockS3Service.Setup(s => s.GetObjectAsync(document.Bucket, document.Key)).ReturnsAsync(docResponse);
            mockS3Service.Setup(s => s.GetObjectAsync(revision.Bucket, revision.Key)).ReturnsAsync(revisionResponse);

            var executor = new ApplyRevisionExecutor(mockS3Service.Object, applyRevisionEvent, mockContext.Object);

            // Act
            var result = await executor.Run();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.Code);
            Assert.IsType<S3Request>(result.Data);
            var returnedDoc = (S3Request)result.Data;
            Assert.Equal(document.Bucket, returnedDoc.Bucket);
            Assert.Equal(document.Key, returnedDoc.Key);
        }
    }
}
