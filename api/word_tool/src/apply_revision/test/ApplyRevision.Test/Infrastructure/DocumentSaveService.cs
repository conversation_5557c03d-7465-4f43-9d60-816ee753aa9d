using System;
using System.IO;
using System.Linq;
using System.Reflection;
using ApplyRevision.Tests.Attributes;

namespace ApplyRevision.Tests.Infrastructure
{
    /// <summary>
    /// Service for automatically saving Word documents based on SaveWordDocumentAttribute.
    /// Provides functionality similar to Python decorators or Java annotations for test methods.
    /// </summary>
    public static class DocumentSaveService
    {
        private static readonly string DefaultOutputDirectory = Path.Combine(
            Directory.GetCurrentDirectory(), 
            "TestResults", 
            "SavedDocuments"
        );

        /// <summary>
        /// Saves a Word document before test execution if the method has SaveWordDocumentAttribute.
        /// </summary>
        /// <param name="testMethod">The test method being executed</param>
        /// <param name="documentPath">Path to the document to save</param>
        /// <returns>The path where the document was saved, or null if not saved</returns>
        public static string? SaveDocumentBefore(MethodInfo testMethod, string documentPath)
        {
            var attribute = testMethod.GetCustomAttribute<SaveWordDocumentAttribute>();
            if (attribute == null || !attribute.SaveBefore || !File.Exists(documentPath))
                return null;

            return SaveDocument(documentPath, attribute, "before");
        }

        /// <summary>
        /// Saves a Word document after test execution if the method has SaveWordDocumentAttribute.
        /// </summary>
        /// <param name="testMethod">The test method being executed</param>
        /// <param name="documentPath">Path to the document to save</param>
        /// <returns>The path where the document was saved, or null if not saved</returns>
        public static string? SaveDocumentAfter(MethodInfo testMethod, string documentPath)
        {
            var attribute = testMethod.GetCustomAttribute<SaveWordDocumentAttribute>();
            if (attribute == null || !attribute.SaveAfter || !File.Exists(documentPath))
                return null;

            return SaveDocument(documentPath, attribute, "after");
        }

        /// <summary>
        /// Saves an intermediate document during test execution if SaveIntermediateSteps is enabled.
        /// </summary>
        /// <param name="testMethod">The test method being executed</param>
        /// <param name="documentPath">Path to the document to save</param>
        /// <param name="stepName">Name of the intermediate step</param>
        /// <returns>The path where the document was saved, or null if not saved</returns>
        public static string? SaveIntermediateDocument(MethodInfo testMethod, string documentPath, string stepName)
        {
            var attribute = testMethod.GetCustomAttribute<SaveWordDocumentAttribute>();
            if (attribute == null || !attribute.SaveIntermediateSteps || !File.Exists(documentPath))
                return null;

            return SaveDocument(documentPath, attribute, $"step_{stepName}");
        }

        /// <summary>
        /// Gets the SaveWordDocumentAttribute from a test method.
        /// </summary>
        /// <param name="testMethod">The test method to check</param>
        /// <returns>The attribute if present, otherwise null</returns>
        public static SaveWordDocumentAttribute? GetSaveAttribute(MethodInfo testMethod)
        {
            return testMethod.GetCustomAttribute<SaveWordDocumentAttribute>();
        }

        /// <summary>
        /// Checks if a test method has the SaveWordDocumentAttribute.
        /// </summary>
        /// <param name="testMethod">The test method to check</param>
        /// <returns>True if the attribute is present, otherwise false</returns>
        public static bool ShouldSaveDocuments(MethodInfo testMethod)
        {
            return testMethod.GetCustomAttribute<SaveWordDocumentAttribute>() != null;
        }

        /// <summary>
        /// Creates a metadata file alongside the saved document with test information.
        /// </summary>
        /// <param name="documentPath">Path to the saved document</param>
        /// <param name="testMethod">The test method information</param>
        /// <param name="attribute">The SaveWordDocumentAttribute</param>
        /// <param name="stage">The stage (before/after/step_name)</param>
        private static void CreateMetadataFile(string documentPath, MethodInfo testMethod, 
            SaveWordDocumentAttribute attribute, string stage)
        {
            var metadataPath = Path.ChangeExtension(documentPath, ".txt");
            var metadata = $"""
                Test Method: {testMethod.DeclaringType?.Name}.{testMethod.Name}
                Scenario: {attribute.ScenarioName}
                Stage: {stage}
                Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
                Original Document: {documentPath}
                Custom Metadata: {attribute.Metadata ?? "None"}
                """;

            File.WriteAllText(metadataPath, metadata);
        }

        /// <summary>
        /// Internal method to save a document with the specified parameters.
        /// </summary>
        private static string SaveDocument(string sourcePath, SaveWordDocumentAttribute attribute, string stage)
        {
            try
            {
                // Determine output directory
                var outputDir = string.IsNullOrWhiteSpace(attribute.OutputDirectory) 
                    ? DefaultOutputDirectory 
                    : attribute.OutputDirectory;

                // Ensure directory exists
                Directory.CreateDirectory(outputDir);

                // Generate filename
                var timestamp = attribute.IncludeTimestamp 
                    ? $"_{DateTime.Now:yyyyMMdd_HHmmss}" 
                    : "";
                
                var fileName = $"{attribute.ScenarioName}_{stage}{timestamp}.docx";
                var destinationPath = Path.Combine(outputDir, fileName);

                // Copy the document
                File.Copy(sourcePath, destinationPath, overwrite: true);

                // Create metadata file if requested
                if (!string.IsNullOrWhiteSpace(attribute.Metadata))
                {
                    var testMethod = new System.Diagnostics.StackTrace()
                        .GetFrames()
                        ?.FirstOrDefault(f => f.GetMethod()?.GetCustomAttribute<SaveWordDocumentAttribute>() != null)
                        ?.GetMethod();

                    if (testMethod != null)
                    {
                        CreateMetadataFile(destinationPath, (MethodInfo)testMethod, attribute, stage);
                    }
                }

                Console.WriteLine($"Document saved: {destinationPath}");
                return destinationPath;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to save document: {ex.Message}");
                return null;
            }
        }
    }
}
