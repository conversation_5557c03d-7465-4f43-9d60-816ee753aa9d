using System;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using ApplyRevision.Tests.Attributes;
using ApplyRevision.Tests.Infrastructure;

namespace ApplyRevision.Tests.Infrastructure
{
    /// <summary>
    /// Base class for Word document tests that provides automatic document saving functionality.
    /// Tests inheriting from this class can use the SaveWordDocumentAttribute to automatically
    /// save documents before and after test execution, similar to Python decorators or Java annotations.
    /// </summary>
    public abstract class BaseWordDocumentTest : IDisposable
    {
        private readonly string _tempFilePath;
        private string? _savedBeforePath;
        private string? _savedAfterPath;
        private bool _disposed = false;

        /// <summary>
        /// Gets the path to the temporary test file.
        /// </summary>
        protected string TempFilePath => _tempFilePath;

        /// <summary>
        /// Gets the path where the document was saved before test execution, if any.
        /// </summary>
        protected string? SavedBeforePath => _savedBeforePath;

        /// <summary>
        /// Gets the path where the document was saved after test execution, if any.
        /// </summary>
        protected string? SavedAfterPath => _savedAfterPath;

        /// <summary>
        /// Initializes a new instance of the BaseWordDocumentTest class.
        /// </summary>
        protected BaseWordDocumentTest()
        {
            _tempFilePath = Path.GetTempFileName();
        }

        /// <summary>
        /// Saves the document before test execution if the calling method has SaveWordDocumentAttribute.
        /// This method should be called at the beginning of test methods that want to use the save functionality.
        /// </summary>
        /// <param name="callerName">The name of the calling method (automatically filled)</param>
        protected void SaveDocumentBefore([CallerMemberName] string callerName = "")
        {
            var testMethod = GetTestMethod(callerName);
            if (testMethod != null)
            {
                _savedBeforePath = DocumentSaveService.SaveDocumentBefore(testMethod, _tempFilePath);
            }
        }

        /// <summary>
        /// Saves the document after test execution if the calling method has SaveWordDocumentAttribute.
        /// This method should be called at the end of test methods that want to use the save functionality.
        /// </summary>
        /// <param name="callerName">The name of the calling method (automatically filled)</param>
        protected void SaveDocumentAfter([CallerMemberName] string callerName = "")
        {
            var testMethod = GetTestMethod(callerName);
            if (testMethod != null)
            {
                _savedAfterPath = DocumentSaveService.SaveDocumentAfter(testMethod, _tempFilePath);
            }
        }

        /// <summary>
        /// Saves an intermediate document during test execution if SaveIntermediateSteps is enabled.
        /// </summary>
        /// <param name="stepName">Name of the intermediate step</param>
        /// <param name="callerName">The name of the calling method (automatically filled)</param>
        /// <returns>The path where the document was saved, or null if not saved</returns>
        protected string? SaveIntermediateDocument(string stepName, [CallerMemberName] string callerName = "")
        {
            var testMethod = GetTestMethod(callerName);
            if (testMethod != null)
            {
                return DocumentSaveService.SaveIntermediateDocument(testMethod, _tempFilePath, stepName);
            }
            return null;
        }

        /// <summary>
        /// Creates a document and saves it as the "before" state if SaveWordDocumentAttribute is present.
        /// This should be called after creating the initial document content.
        /// </summary>
        /// <param name="documentCreationAction">Action that creates the document content</param>
        /// <param name="callerName">The name of the calling method (automatically filled)</param>
        protected void CreateDocumentAndSaveBefore(Action documentCreationAction, [CallerMemberName] string callerName = "")
        {
            // First create the document
            documentCreationAction();

            // Then save the before state if requested
            SaveDocumentBefore(callerName);
        }

        /// <summary>
        /// Executes a test with automatic document saving based on the SaveWordDocumentAttribute.
        /// This is a helper method that handles the before/after saving automatically.
        /// Note: The document should already be created before calling this method if SaveBefore is true.
        /// </summary>
        /// <param name="testAction">The test action to execute</param>
        /// <param name="callerName">The name of the calling method (automatically filled)</param>
        protected void ExecuteWithDocumentSaving(Action testAction, [CallerMemberName] string callerName = "")
        {
            var testMethod = GetTestMethod(callerName);
            if (testMethod == null)
            {
                testAction();
                return;
            }

            var attribute = DocumentSaveService.GetSaveAttribute(testMethod);
            if (attribute == null)
            {
                testAction();
                return;
            }

            try
            {
                // Note: SaveBefore should be called manually after document creation
                // or use ExecuteWithDocumentCreationAndSaving method instead

                // Execute the test
                testAction();

                // Save after if requested
                if (attribute.SaveAfter)
                {
                    _savedAfterPath = DocumentSaveService.SaveDocumentAfter(testMethod, _tempFilePath);
                }
            }
            catch (Exception)
            {
                // Still try to save after even if test failed, for debugging purposes
                if (attribute.SaveAfter)
                {
                    try
                    {
                        _savedAfterPath = DocumentSaveService.SaveDocumentAfter(testMethod, _tempFilePath);
                    }
                    catch
                    {
                        // Ignore save errors during exception handling
                    }
                }
                throw;
            }
        }

        /// <summary>
        /// Executes a complete test workflow with document creation and automatic saving.
        /// This method handles the entire flow: create document -> save before -> execute test -> save after.
        /// </summary>
        /// <param name="documentCreationAction">Action that creates the initial document</param>
        /// <param name="testAction">The test action to execute</param>
        /// <param name="callerName">The name of the calling method (automatically filled)</param>
        protected void ExecuteWithDocumentCreationAndSaving(Action documentCreationAction, Action testAction, [CallerMemberName] string callerName = "")
        {
            var testMethod = GetTestMethod(callerName);
            if (testMethod == null)
            {
                documentCreationAction();
                testAction();
                return;
            }

            var attribute = DocumentSaveService.GetSaveAttribute(testMethod);
            if (attribute == null)
            {
                documentCreationAction();
                testAction();
                return;
            }

            try
            {
                // Create the document first
                documentCreationAction();

                // Save before if requested
                if (attribute.SaveBefore)
                {
                    _savedBeforePath = DocumentSaveService.SaveDocumentBefore(testMethod, _tempFilePath);
                }

                // Execute the test
                testAction();

                // Save after if requested
                if (attribute.SaveAfter)
                {
                    _savedAfterPath = DocumentSaveService.SaveDocumentAfter(testMethod, _tempFilePath);
                }
            }
            catch (Exception)
            {
                // Still try to save after even if test failed, for debugging purposes
                if (attribute.SaveAfter)
                {
                    try
                    {
                        _savedAfterPath = DocumentSaveService.SaveDocumentAfter(testMethod, _tempFilePath);
                    }
                    catch
                    {
                        // Ignore save errors during exception handling
                    }
                }
                throw;
            }
        }

        /// <summary>
        /// Gets the MethodInfo for the specified test method name.
        /// </summary>
        /// <param name="methodName">The name of the method</param>
        /// <returns>The MethodInfo if found, otherwise null</returns>
        private MethodInfo? GetTestMethod(string methodName)
        {
            return GetType().GetMethod(methodName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        }

        /// <summary>
        /// Disposes of the temporary file and any other resources.
        /// </summary>
        public virtual void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    if (File.Exists(_tempFilePath))
                    {
                        File.Delete(_tempFilePath);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deleting temporary file: {ex.Message}");
                }

                _disposed = true;
            }
            GC.SuppressFinalize(this);
        }
    }
}
