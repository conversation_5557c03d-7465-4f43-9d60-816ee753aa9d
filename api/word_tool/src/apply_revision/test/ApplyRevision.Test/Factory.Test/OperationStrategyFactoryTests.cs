using System;
using Amazon.Lambda.Core;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using ApplyRevision.Service;
using ApplyRevision.Strategy;
using Moq;
using Xunit;

namespace ApplyRevision.Tests.Factory.Test
{
    /// <summary>
    /// Unit tests for OperationStrategyFactory
    /// OperationStrategyFactory 的单元测试
    /// </summary>
    public class OperationStrategyFactoryTests
    {
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly Mock<IElementFactory> _mockElementFactory;
        private readonly Mock<IRevisionElementFactory> _mockRevisionFactory;
        private readonly Mock<IIdManager> _mockIdManager;
        private readonly Mock<ICommentManager> _mockCommentManager;
        private readonly OperationStrategyFactory _factory;

        public OperationStrategyFactoryTests()
        {
            _mockContext = new Mock<ILambdaContext>();
            _mockElementFactory = new Mock<IElementFactory>();
            _mockRevisionFactory = new Mock<IRevisionElementFactory>();
            _mockIdManager = new Mock<IIdManager>();
            _mockCommentManager = new Mock<ICommentManager>();

            _factory = new OperationStrategyFactory(
                _mockContext.Object,
                _mockElementFactory.Object,
                _mockRevisionFactory.Object,
                _mockIdManager.Object,
                _mockCommentManager.Object
            );
        }

        #region CreateStrategy with string parameter tests

        [Fact]
        public void CreateStrategy_WithInsertString_ShouldReturnInsertStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("insert");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<InsertStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithDeleteString_ShouldReturnDeleteStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("delete");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<DeleteStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithReplaceString_ShouldReturnReplaceStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("replace");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<ReplaceStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithFormatString_ShouldReturnFormatStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("format");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<FormatStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithCommentReplyString_ShouldReturnCommentReplyStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("commentreply");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<CommentReplyStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithUpperCaseString_ShouldReturnCorrectStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("COMMENTREPLY");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<CommentReplyStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithMixedCaseString_ShouldReturnCorrectStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy("CommentReply");

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<CommentReplyStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithUnsupportedString_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => _factory.CreateStrategy("unsupported"));
            Assert.Contains("Unsupported operation type: unsupported", exception.Message);
        }

        [Fact]
        public void CreateStrategy_WithNullString_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => _factory.CreateStrategy((string)null));
            Assert.Contains("Unsupported operation type:", exception.Message);
        }

        #endregion

        #region CreateStrategy with OperationType enum tests

        [Fact]
        public void CreateStrategy_WithInsertEnum_ShouldReturnInsertStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy(OperationType.Insert);

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<InsertStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithDeleteEnum_ShouldReturnDeleteStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy(OperationType.Delete);

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<DeleteStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithReplaceEnum_ShouldReturnReplaceStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy(OperationType.Replace);

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<ReplaceStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithFormatEnum_ShouldReturnFormatStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy(OperationType.Format);

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<FormatStrategy>(strategy);
        }

        [Fact]
        public void CreateStrategy_WithCommentReplyEnum_ShouldReturnCommentReplyStrategy()
        {
            // Act
            var strategy = _factory.CreateStrategy(OperationType.CommentReply);

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<CommentReplyStrategy>(strategy);
        }

        #endregion

        #region CreateStrategy with Operation object tests

        [Fact]
        public void CreateStrategy_WithOperationObject_ShouldReturnCorrectStrategy()
        {
            // Arrange
            var operation = new Operation
            {
                Op = OperationType.CommentReply,
                Target = new("test"),
                Range = new ApplyRevision.Model.Range { Start = 0, End = 0 },
                Text = "Test reply"
            };

            // Act
            var strategy = _factory.CreateStrategy(operation);

            // Assert
            Assert.NotNull(strategy);
            Assert.IsType<CommentReplyStrategy>(strategy);
        }

        #endregion

        #region Integration tests

        [Fact]
        public void CreateStrategy_AllSupportedTypes_ShouldCreateDifferentInstances()
        {
            // Act
            var insertStrategy = _factory.CreateStrategy(OperationType.Insert);
            var deleteStrategy = _factory.CreateStrategy(OperationType.Delete);
            var replaceStrategy = _factory.CreateStrategy(OperationType.Replace);
            var formatStrategy = _factory.CreateStrategy(OperationType.Format);
            var commentReplyStrategy = _factory.CreateStrategy(OperationType.CommentReply);

            // Assert
            Assert.NotNull(insertStrategy);
            Assert.NotNull(deleteStrategy);
            Assert.NotNull(replaceStrategy);
            Assert.NotNull(formatStrategy);
            Assert.NotNull(commentReplyStrategy);

            // Verify they are different instances
            Assert.NotSame(insertStrategy, deleteStrategy);
            Assert.NotSame(insertStrategy, replaceStrategy);
            Assert.NotSame(insertStrategy, formatStrategy);
            Assert.NotSame(insertStrategy, commentReplyStrategy);
            Assert.NotSame(deleteStrategy, replaceStrategy);
            Assert.NotSame(deleteStrategy, formatStrategy);
            Assert.NotSame(deleteStrategy, commentReplyStrategy);
            Assert.NotSame(replaceStrategy, formatStrategy);
            Assert.NotSame(replaceStrategy, commentReplyStrategy);
            Assert.NotSame(formatStrategy, commentReplyStrategy);

            // Verify correct types
            Assert.IsType<InsertStrategy>(insertStrategy);
            Assert.IsType<DeleteStrategy>(deleteStrategy);
            Assert.IsType<ReplaceStrategy>(replaceStrategy);
            Assert.IsType<FormatStrategy>(formatStrategy);
            Assert.IsType<CommentReplyStrategy>(commentReplyStrategy);
        }

        #endregion
    }
}
