using System;
using System.Collections.Generic;
using System.Linq;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using Xunit;

namespace ApplyRevision.Tests.Factory.Test
{
    public class ElementFactoryTests
    {
        private readonly ElementFactory _factory;

        public ElementFactoryTests()
        {
            _factory = new ElementFactory();
        }

        #region CreateRunElement Tests

        [Fact]
        public void CreateRunElement_WithSimpleInsertText_ShouldCreateTextElement()
        {
            // Arrange
            string text = "Hello World";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var textElement = result.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
            Assert.Equal(SpaceProcessingModeValues.Preserve, textElement.Space.Value);
        }

        [Fact]
        public void CreateRunElement_WithSimpleDeleteText_ShouldCreateDeletedTextElement()
        {
            // Arrange
            string text = "Deleted Text";
            var opType = RevisionOperatorType.del;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var deletedTextElement = result.GetFirstChild<DeletedText>();
            Assert.NotNull(deletedTextElement);
            Assert.Equal(text, deletedTextElement.Text);
            Assert.Equal(SpaceProcessingModeValues.Preserve, deletedTextElement.Space.Value);
        }

        [Fact]
        public void CreateRunElement_WithSingleNewline_ShouldCreateTextAndBreak()
        {
            // Arrange
            string text = "Line 1\nLine 2";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var children = result.Elements().ToList();

            // Should have Text("Line 1"), Break, Text("Line 2")
            Assert.Equal(3, children.Count);
            Assert.IsType<Text>(children[0]);
            Assert.Equal("Line 1", ((Text)children[0]).Text);
            Assert.IsType<Break>(children[1]);
            Assert.IsType<Text>(children[2]);
            Assert.Equal("Line 2", ((Text)children[2]).Text);
        }

        [Fact]
        public void CreateRunElement_WithMultipleNewlines_ShouldNormalizeToSingleNewline()
        {
            // Arrange
            string text = "Line 1\n\n\nLine 2";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var children = result.Elements().ToList();

            // Multiple newlines should be normalized to single newline
            Assert.Equal(3, children.Count);
            Assert.IsType<Text>(children[0]);
            Assert.Equal("Line 1", ((Text)children[0]).Text);
            Assert.IsType<Break>(children[1]);
            Assert.IsType<Text>(children[2]);
            Assert.Equal("Line 2", ((Text)children[2]).Text);
        }

        [Fact]
        public void CreateRunElement_WithEmptyString_ShouldCreateEmptyRun()
        {
            // Arrange
            string text = "";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements());
        }

        [Fact]
        public void CreateRunElement_WithOnlyNewlines_ShouldCreateEmptyRun()
        {
            // Arrange
            string text = "\n\n\n";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements());
        }

        [Fact]
        public void CreateRunElement_WithTrailingNewline_ShouldNotCreateExtraBreak()
        {
            // Arrange
            string text = "Single line\n";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var children = result.Elements().ToList();

            // Should only have Text("Single line")
            Assert.Single(children);
            Assert.IsType<Text>(children[0]);
            Assert.Equal("Single line", ((Text)children[0]).Text);
        }

        [Fact]
        public void CreateRunElement_WithLeadingNewline_ShouldCreateBreakFirst()
        {
            // Arrange
            string text = "\nSingle line";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var children = result.Elements().ToList();

            // Should have Break, Text("Single line")
            Assert.Equal(2, children.Count);
            Assert.IsType<Break>(children[0]);
            Assert.IsType<Text>(children[1]);
            Assert.Equal("Single line", ((Text)children[1]).Text);
        }

        [Fact]
        public void CreateRunElement_WithWhitespaceOnly_ShouldCreateTextElement()
        {
            // Arrange
            string text = "   ";
            var opType = RevisionOperatorType.ins;

            // Act
            var result = _factory.CreateRunElement(text, opType);

            // Assert
            Assert.NotNull(result);
            var textElement = result.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
            Assert.Equal(SpaceProcessingModeValues.Preserve, textElement.Space.Value);
        }

        #endregion

        #region CreateCommentElement Tests

        [Fact]
        public void CreateCommentElement_WithValidParameters_ShouldCreateCommentWithCorrectProperties()
        {
            // Arrange
            int commentId = 1;
            string author = "John Doe";
            string date = "2024-01-15T10:30:00";
            string message = "This is a test comment";

            // Act
            var result = _factory.CreateCommentElement(commentId, author, date, message);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(commentId.ToString(), result.Id.Value);
            Assert.Equal(author, result.Author.Value);
            Assert.Equal("JD", result.Initials.Value);
            Assert.NotNull(result.Date);

            // Check the paragraph content
            var paragraph = result.GetFirstChild<Paragraph>();
            Assert.NotNull(paragraph);
            var run = paragraph.GetFirstChild<Run>();
            Assert.NotNull(run);
            var text = run.GetFirstChild<Text>();
            Assert.NotNull(text);
            Assert.Equal(message, text.Text);
        }

        [Fact]
        public void CreateCommentElement_WithEmptyAuthor_ShouldUseAIInitials()
        {
            // Arrange
            int commentId = 2;
            string author = "";
            string date = "2024-01-15";
            string message = "AI comment";

            // Act
            var result = _factory.CreateCommentElement(commentId, author, date, message);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("", result.Author.Value);
            Assert.Equal("AI", result.Initials.Value);
        }

        [Fact]
        public void CreateCommentElement_WithInvalidDate_ShouldUseCurrentDate()
        {
            // Arrange
            int commentId = 3;
            string author = "Test User";
            string date = "invalid-date";
            string message = "Test message";
            var beforeCall = DateTime.Now;

            // Act
            var result = _factory.CreateCommentElement(commentId, author, date, message);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Date);
            var commentDate = result.Date.Value;
            var afterCall = DateTime.Now;
            Assert.True(commentDate >= beforeCall && commentDate <= afterCall);
        }

        [Fact]
        public void CreateCommentElement_WithMultilineMessage_ShouldHandleNewlines()
        {
            // Arrange
            int commentId = 4;
            string author = "Test User";
            string date = "2024-01-15";
            string message = "Line 1\nLine 2\nLine 3";

            // Act
            var result = _factory.CreateCommentElement(commentId, author, date, message);

            // Assert
            Assert.NotNull(result);
            var paragraph = result.GetFirstChild<Paragraph>();
            Assert.NotNull(paragraph);
            var run = paragraph.GetFirstChild<Run>();
            Assert.NotNull(run);

            // Should have proper text and break elements
            var children = run.Elements().ToList();
            Assert.Equal(5, children.Count); // Text, Break, Text, Break, Text
            Assert.IsType<Text>(children[0]);
            Assert.Equal("Line 1", ((Text)children[0]).Text);
            Assert.IsType<Break>(children[1]);
            Assert.IsType<Text>(children[2]);
            Assert.Equal("Line 2", ((Text)children[2]).Text);
            Assert.IsType<Break>(children[3]);
            Assert.IsType<Text>(children[4]);
            Assert.Equal("Line 3", ((Text)children[4]).Text);
        }

        [Fact]
        public void CreateCommentElement_WithStylingAgent_ShouldSetCorrectInitials()
        {
            // Arrange
            int commentId = 5;
            string author = "styling_agent";
            string date = "2024-01-15";
            string message = "Automated styling comment";

            // Act
            var result = _factory.CreateCommentElement(commentId, author, date, message);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("S", result.Initials.Value);
        }

        #endregion

        #region CreateCommentReference Tests

        [Fact]
        public void CreateCommentReference_WithStringValue_ShouldCreateValidReference()
        {
            // Arrange
            StringValue commentId = "1";

            // Act
            var result = _factory.CreateCommentReference(commentId);

            // Assert
            Assert.NotNull(result);
            var commentRef = result.GetFirstChild<CommentReference>();
            Assert.NotNull(commentRef);
            Assert.Equal(commentId.Value, commentRef.Id.Value);

            // Check run properties
            var runProps = result.RunProperties;
            Assert.NotNull(runProps);
            var runStyle = runProps.GetFirstChild<RunStyle>();
            Assert.NotNull(runStyle);
            Assert.Equal("CommentReference", runStyle.Val.Value);
        }

        [Fact]
        public void CreateCommentReference_WithNumericString_ShouldWork()
        {
            // Arrange
            StringValue commentId = "999";

            // Act
            var result = _factory.CreateCommentReference(commentId);

            // Assert
            Assert.NotNull(result);
            var commentRef = result.GetFirstChild<CommentReference>();
            Assert.NotNull(commentRef);
            Assert.Equal("999", commentRef.Id.Value);
        }

        [Fact]
        public void CreateCommentReference_WithImplicitStringConversion_ShouldWork()
        {
            // Arrange
            string commentId = "42";

            // Act
            var result = _factory.CreateCommentReference(commentId);

            // Assert
            Assert.NotNull(result);
            var commentRef = result.GetFirstChild<CommentReference>();
            Assert.NotNull(commentRef);
            Assert.Equal("42", commentRef.Id.Value);
        }

        #endregion

        #region GetCommentInitials Tests

        [Fact]
        public void GetCommentInitials_WithTwoNames_ShouldReturnTwoInitials()
        {
            // Arrange
            string author = "John Doe";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("JD", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithThreeNames_ShouldReturnThreeInitials()
        {
            // Arrange
            string author = "Mary Jane Smith";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("MJS", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithSingleName_ShouldReturnSingleInitial()
        {
            // Arrange
            string author = "SingleName";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("S", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithEmptyString_ShouldReturnAI()
        {
            // Arrange
            string author = "";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("AI", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithWhitespace_ShouldReturnAI()
        {
            // Arrange
            string author = "   ";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("AI", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithNull_ShouldReturnAI()
        {
            // Arrange
            string author = null;

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("AI", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithCommaDelimitedNames_ShouldReturnCorrectInitials()
        {
            // Arrange
            string author = "Doe, John";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("DJ", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithMixedDelimiters_ShouldReturnCorrectInitials()
        {
            // Arrange
            string author = "Jane, Mary Smith";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("JMS", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithLowercaseNames_ShouldReturnUppercaseInitials()
        {
            // Arrange
            string author = "john doe";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("JD", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithExtraSpaces_ShouldIgnoreEmptyParts()
        {
            // Arrange
            string author = "John  Doe";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("JD", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithSpecialCharacters_ShouldOnlyProcessAlphabetic()
        {
            // Arrange
            string author = "John-Paul Van-Der-Berg";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("JV", result.Value);
        }

        [Fact]
        public void GetCommentInitials_WithLongNameList_ShouldReturnAllInitials()
        {
            // Arrange
            string author = "Anna Bob Charlie David Edward Francis George";

            // Act
            var result = _factory.GetCommentInitials(author);

            // Assert
            Assert.Equal("ABCDEFG", result.Value);
        }

        #endregion

        #region CreateTextRun Tests

        [Fact]
        public void CreateTextRun_WithValidText_ShouldCreateRunWithText()
        {
            // Arrange
            string text = "Sample text";

            // Act
            var result = _factory.CreateTextRun(text);

            // Assert
            Assert.NotNull(result);
            var textElement = result.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
            Assert.Equal(SpaceProcessingModeValues.Preserve, textElement.Space.Value);
        }

        [Fact]
        public void CreateTextRun_WithPreserveSpaceFalse_ShouldNotSetSpaceAttribute()
        {
            // Arrange
            string text = "Sample text";

            // Act
            var result = _factory.CreateTextRun(text, false);

            // Assert
            Assert.NotNull(result);
            var textElement = result.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
            Assert.Null(textElement.Space);
        }

        [Fact]
        public void CreateTextRun_WithEmptyText_ShouldCreateEmptyRun()
        {
            // Arrange
            string text = "";

            // Act
            var result = _factory.CreateTextRun(text);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements());
        }

        [Fact]
        public void CreateTextRun_WithNullText_ShouldCreateEmptyRun()
        {
            // Arrange
            string text = null;

            // Act
            var result = _factory.CreateTextRun(text);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements());
        }

        #endregion

        #region CreateParagraph Tests

        [Fact]
        public void CreateParagraph_WithoutText_ShouldCreateEmptyParagraph()
        {
            // Act
            var result = _factory.CreateParagraph();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements<Run>());
        }

        [Fact]
        public void CreateParagraph_WithText_ShouldCreateParagraphWithRun()
        {
            // Arrange
            string text = "Paragraph text";

            // Act
            var result = _factory.CreateParagraph(text);

            // Assert
            Assert.NotNull(result);
            var run = result.GetFirstChild<Run>();
            Assert.NotNull(run);
            var textElement = run.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
        }

        [Fact]
        public void CreateParagraph_WithEmptyText_ShouldCreateEmptyParagraph()
        {
            // Arrange
            string text = "";

            // Act
            var result = _factory.CreateParagraph(text);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements<Run>());
        }

        [Fact]
        public void CreateParagraph_WithNullText_ShouldCreateEmptyParagraph()
        {
            // Arrange
            string text = null;

            // Act
            var result = _factory.CreateParagraph(text);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.Elements<Run>());
        }

        #endregion

        #region CreateBreak Tests

        [Fact]
        public void CreateBreak_WithoutType_ShouldCreateDefaultBreak()
        {
            // Act
            var result = _factory.CreateBreak();

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.Type);
        }

        [Fact]
        public void CreateBreak_WithLineBreak_ShouldCreateLineBreak()
        {
            // Act
            var result = _factory.CreateBreak(BreakValues.TextWrapping);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(BreakValues.TextWrapping, result.Type.Value);
        }

        [Fact]
        public void CreateBreak_WithPageBreak_ShouldCreatePageBreak()
        {
            // Act
            var result = _factory.CreateBreak(BreakValues.Page);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(BreakValues.Page, result.Type.Value);
        }

        [Fact]
        public void CreateBreak_WithColumnBreak_ShouldCreateColumnBreak()
        {
            // Act
            var result = _factory.CreateBreak(BreakValues.Column);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(BreakValues.Column, result.Type.Value);
        }

        #endregion

        #region CreateFormattedRun Tests

        [Fact]
        public void CreateFormattedRun_WithBoldText_ShouldCreateBoldRun()
        {
            // Arrange
            string text = "Bold text";

            // Act
            var result = _factory.CreateFormattedRun(text, isBold: true);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);
            Assert.NotNull(runProperties.GetFirstChild<Bold>());

            var textElement = result.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
        }

        [Fact]
        public void CreateFormattedRun_WithItalicText_ShouldCreateItalicRun()
        {
            // Arrange
            string text = "Italic text";

            // Act
            var result = _factory.CreateFormattedRun(text, isItalic: true);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);
            Assert.NotNull(runProperties.GetFirstChild<Italic>());
        }

        [Fact]
        public void CreateFormattedRun_WithUnderlineText_ShouldCreateUnderlineRun()
        {
            // Arrange
            string text = "Underline text";

            // Act
            var result = _factory.CreateFormattedRun(text, isUnderline: true);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);
            var underline = runProperties.GetFirstChild<Underline>();
            Assert.NotNull(underline);
            Assert.Equal(UnderlineValues.Single, underline.Val.Value);
        }

        [Fact]
        public void CreateFormattedRun_WithFontSize_ShouldSetFontSize()
        {
            // Arrange
            string text = "Sized text";
            int fontSize = 24; // 12pt

            // Act
            var result = _factory.CreateFormattedRun(text, fontSize: fontSize);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);
            var fontSizeElement = runProperties.GetFirstChild<FontSize>();
            Assert.NotNull(fontSizeElement);
            Assert.Equal(fontSize.ToString(), fontSizeElement.Val.Value);
        }

        [Fact]
        public void CreateFormattedRun_WithFontName_ShouldSetFontName()
        {
            // Arrange
            string text = "Font text";
            string fontName = "Arial";

            // Act
            var result = _factory.CreateFormattedRun(text, fontName: fontName);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);
            var runFonts = runProperties.GetFirstChild<RunFonts>();
            Assert.NotNull(runFonts);
            Assert.Equal(fontName, runFonts.Ascii.Value);
            Assert.Equal(fontName, runFonts.HighAnsi.Value);
        }

        [Fact]
        public void CreateFormattedRun_WithAllFormatting_ShouldApplyAllFormats()
        {
            // Arrange
            string text = "Fully formatted text";
            int fontSize = 28;
            string fontName = "Times New Roman";

            // Act
            var result = _factory.CreateFormattedRun(text, true, true, true, fontSize, fontName);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);

            Assert.NotNull(runProperties.GetFirstChild<Bold>());
            Assert.NotNull(runProperties.GetFirstChild<Italic>());
            Assert.NotNull(runProperties.GetFirstChild<Underline>());

            var fontSizeElement = runProperties.GetFirstChild<FontSize>();
            Assert.NotNull(fontSizeElement);
            Assert.Equal(fontSize.ToString(), fontSizeElement.Val.Value);

            var runFonts = runProperties.GetFirstChild<RunFonts>();
            Assert.NotNull(runFonts);
            Assert.Equal(fontName, runFonts.Ascii.Value);
        }

        [Fact]
        public void CreateFormattedRun_WithNoFormatting_ShouldNotHaveRunProperties()
        {
            // Arrange
            string text = "Plain text";

            // Act
            var result = _factory.CreateFormattedRun(text);

            // Assert
            Assert.NotNull(result);
            var runProperties = result.GetFirstChild<RunProperties>();
            Assert.Null(runProperties);
        }

        #endregion

        #region CreateRunProperties Tests

        [Fact]
        public void CreateRunProperties_WithNoFormatting_ShouldCreateEmptyProperties()
        {
            // Act
            var result = _factory.CreateRunProperties();

            // Assert
            Assert.NotNull(result);
            Assert.False(result.HasChildren);
        }

        [Fact]
        public void CreateRunProperties_WithBold_ShouldContainBold()
        {
            // Act
            var result = _factory.CreateRunProperties(isBold: true);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.GetFirstChild<Bold>());
        }

        [Fact]
        public void CreateRunProperties_WithAllFormats_ShouldContainAllElements()
        {
            // Arrange
            int fontSize = 20;
            string fontName = "Calibri";

            // Act
            var result = _factory.CreateRunProperties(true, true, true, fontSize, fontName);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.GetFirstChild<Bold>());
            Assert.NotNull(result.GetFirstChild<Italic>());
            Assert.NotNull(result.GetFirstChild<Underline>());
            Assert.NotNull(result.GetFirstChild<FontSize>());
            Assert.NotNull(result.GetFirstChild<RunFonts>());
        }

        #endregion

        #region CreateParagraphProperties Tests

        [Fact]
        public void CreateParagraphProperties_WithNoFormatting_ShouldCreateEmptyProperties()
        {
            // Act
            var result = _factory.CreateParagraphProperties();

            // Assert
            Assert.NotNull(result);
            Assert.False(result.HasChildren);
        }

        [Fact]
        public void CreateParagraphProperties_WithAlignment_ShouldSetJustification()
        {
            // Act
            var result = _factory.CreateParagraphProperties(alignment: JustificationValues.Center);

            // Assert
            Assert.NotNull(result);
            var justification = result.GetFirstChild<Justification>();
            Assert.NotNull(justification);
            Assert.Equal(JustificationValues.Center, justification.Val.Value);
        }

        [Fact]
        public void CreateParagraphProperties_WithIndentation_ShouldSetIndentation()
        {
            // Arrange
            int indentValue = 720; // 0.5 inch in twips

            // Act
            var result = _factory.CreateParagraphProperties(indentation: indentValue);

            // Assert
            Assert.NotNull(result);
            var indentation = result.GetFirstChild<Indentation>();
            Assert.NotNull(indentation);
            Assert.Equal(indentValue.ToString(), indentation.Left.Value);
        }

        [Fact]
        public void CreateParagraphProperties_WithSpacing_ShouldSetSpacing()
        {
            // Arrange
            int spaceBefore = 240;
            int spaceAfter = 120;

            // Act
            var result = _factory.CreateParagraphProperties(spacingBefore: spaceBefore, spacingAfter: spaceAfter);

            // Assert
            Assert.NotNull(result);
            var spacing = result.GetFirstChild<SpacingBetweenLines>();
            Assert.NotNull(spacing);
            Assert.Equal(spaceBefore.ToString(), spacing.Before.Value);
            Assert.Equal(spaceAfter.ToString(), spacing.After.Value);
        }

        [Fact]
        public void CreateParagraphProperties_WithAllProperties_ShouldSetAllElements()
        {
            // Arrange
            var alignment = JustificationValues.Right;
            int indentation = 360;
            int spaceBefore = 120;
            int spaceAfter = 240;

            // Act
            var result = _factory.CreateParagraphProperties(alignment, indentation, spaceBefore, spaceAfter);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.GetFirstChild<Justification>());
            Assert.NotNull(result.GetFirstChild<Indentation>());
            Assert.NotNull(result.GetFirstChild<SpacingBetweenLines>());
        }

        #endregion

        #region CreateTable Tests

        [Fact]
        public void CreateTable_WithValidDimensions_ShouldCreateTableWithCorrectStructure()
        {
            // Arrange
            int rows = 3;
            int columns = 4;

            // Act
            var result = _factory.CreateTable(rows, columns);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.GetFirstChild<TableProperties>());

            var tableRows = result.Elements<TableRow>().ToList();
            Assert.Equal(rows, tableRows.Count);

            foreach (var row in tableRows)
            {
                var cells = row.Elements<TableCell>().ToList();
                Assert.Equal(columns, cells.Count);
            }
        }

        [Fact]
        public void CreateTable_WithHeader_ShouldCreateHeaderRow()
        {
            // Arrange
            int rows = 2;
            int columns = 3;

            // Act
            var result = _factory.CreateTable(rows, columns, hasHeader: true);

            // Assert
            Assert.NotNull(result);
            var tableRows = result.Elements<TableRow>().ToList();
            Assert.Equal(rows, tableRows.Count);

            // First row should be header
            var headerRow = tableRows[0];
            var headerRowProperties = headerRow.GetFirstChild<TableRowProperties>();
            Assert.NotNull(headerRowProperties);
            Assert.NotNull(headerRowProperties.GetFirstChild<TableHeader>());

            // Other rows should not be headers
            for (int i = 1; i < tableRows.Count; i++)
            {
                var normalRow = tableRows[i];
                var normalRowProperties = normalRow.GetFirstChild<TableRowProperties>();
                Assert.Null(normalRowProperties);
            }
        }

        [Fact]
        public void CreateTable_WithInvalidDimensions_ShouldThrowException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _factory.CreateTable(0, 3));
            Assert.Throws<ArgumentException>(() => _factory.CreateTable(3, 0));
            Assert.Throws<ArgumentException>(() => _factory.CreateTable(-1, 3));
            Assert.Throws<ArgumentException>(() => _factory.CreateTable(3, -1));
        }

        [Fact]
        public void CreateTable_ShouldHaveTableBorders()
        {
            // Act
            var result = _factory.CreateTable(2, 2);

            // Assert
            Assert.NotNull(result);
            var tableProperties = result.GetFirstChild<TableProperties>();
            Assert.NotNull(tableProperties);
            var tableBorders = tableProperties.GetFirstChild<TableBorders>();
            Assert.NotNull(tableBorders);

            Assert.NotNull(tableBorders.GetFirstChild<TopBorder>());
            Assert.NotNull(tableBorders.GetFirstChild<BottomBorder>());
            Assert.NotNull(tableBorders.GetFirstChild<LeftBorder>());
            Assert.NotNull(tableBorders.GetFirstChild<RightBorder>());
            Assert.NotNull(tableBorders.GetFirstChild<InsideHorizontalBorder>());
            Assert.NotNull(tableBorders.GetFirstChild<InsideVerticalBorder>());
        }

        #endregion

        #region CreateTableRow Tests

        [Fact]
        public void CreateTableRow_WithValidCellCount_ShouldCreateRowWithCells()
        {
            // Arrange
            int cellCount = 5;

            // Act
            var result = _factory.CreateTableRow(cellCount);

            // Assert
            Assert.NotNull(result);
            var cells = result.Elements<TableCell>().ToList();
            Assert.Equal(cellCount, cells.Count);

            // Should not be header by default
            var rowProperties = result.GetFirstChild<TableRowProperties>();
            Assert.Null(rowProperties);
        }

        [Fact]
        public void CreateTableRow_WithHeaderFlag_ShouldCreateHeaderRow()
        {
            // Arrange
            int cellCount = 3;

            // Act
            var result = _factory.CreateTableRow(cellCount, isHeader: true);

            // Assert
            Assert.NotNull(result);
            var rowProperties = result.GetFirstChild<TableRowProperties>();
            Assert.NotNull(rowProperties);
            var tableHeader = rowProperties.GetFirstChild<TableHeader>();
            Assert.NotNull(tableHeader);
            Assert.Equal(OnOffOnlyValues.On, tableHeader.Val.Value);
        }

        [Fact]
        public void CreateTableRow_WithInvalidCellCount_ShouldThrowException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() => _factory.CreateTableRow(0));
            Assert.Throws<ArgumentException>(() => _factory.CreateTableRow(-1));
        }

        #endregion

        #region CreateTableCell Tests

        [Fact]
        public void CreateTableCell_WithoutText_ShouldCreateEmptyCell()
        {
            // Act
            var result = _factory.CreateTableCell();

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.GetFirstChild<TableCellProperties>());

            var paragraph = result.GetFirstChild<Paragraph>();
            Assert.NotNull(paragraph);
            Assert.Empty(paragraph.Elements<Run>());
        }

        [Fact]
        public void CreateTableCell_WithText_ShouldCreateCellWithText()
        {
            // Arrange
            string text = "Cell content";

            // Act
            var result = _factory.CreateTableCell(text);

            // Assert
            Assert.NotNull(result);
            var paragraph = result.GetFirstChild<Paragraph>();
            Assert.NotNull(paragraph);

            var run = paragraph.GetFirstChild<Run>();
            Assert.NotNull(run);
            var textElement = run.GetFirstChild<Text>();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
        }

        [Fact]
        public void CreateTableCell_WithHeaderFlag_ShouldCreateHeaderCell()
        {
            // Arrange
            string text = "Header cell";

            // Act
            var result = _factory.CreateTableCell(text, isHeader: true);

            // Assert
            Assert.NotNull(result);
            var cellProperties = result.GetFirstChild<TableCellProperties>();
            Assert.NotNull(cellProperties);
            var shading = cellProperties.GetFirstChild<Shading>();
            Assert.NotNull(shading);
            Assert.Equal("D9D9D9", shading.Fill.Value);

            // Header text should be bold
            var paragraph = result.GetFirstChild<Paragraph>();
            var run = paragraph.GetFirstChild<Run>();
            var runProperties = run.GetFirstChild<RunProperties>();
            Assert.NotNull(runProperties);
            Assert.NotNull(runProperties.GetFirstChild<Bold>());
        }

        [Fact]
        public void CreateTableCell_WithEmptyText_ShouldCreateEmptyCell()
        {
            // Act
            var result = _factory.CreateTableCell("");

            // Assert
            Assert.NotNull(result);
            var paragraph = result.GetFirstChild<Paragraph>();
            Assert.NotNull(paragraph);
            Assert.Empty(paragraph.Elements<Run>());
        }

        #endregion

        #region ValidateElement Tests

        [Fact]
        public void ValidateElement_WithNullElement_ShouldReturnFalse()
        {
            // Act
            var result = _factory.ValidateElement(null);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateElement_WithValidRun_ShouldReturnTrue()
        {
            // Arrange
            var run = _factory.CreateTextRun("Valid text");

            // Act
            var result = _factory.ValidateElement(run);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateElement_WithEmptyRun_ShouldReturnFalse()
        {
            // Arrange
            var run = new Run();

            // Act
            var result = _factory.ValidateElement(run);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateElement_WithValidParagraph_ShouldReturnTrue()
        {
            // Arrange
            var paragraph = _factory.CreateParagraph("Valid paragraph");

            // Act
            var result = _factory.ValidateElement(paragraph);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateElement_WithEmptyParagraph_ShouldReturnTrue()
        {
            // Arrange
            var paragraph = new Paragraph();

            // Act
            var result = _factory.ValidateElement(paragraph);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateElement_WithValidTable_ShouldReturnTrue()
        {
            // Arrange
            var table = _factory.CreateTable(2, 2);

            // Act
            var result = _factory.ValidateElement(table);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void ValidateElement_WithEmptyTable_ShouldReturnFalse()
        {
            // Arrange
            var table = new Table();

            // Act
            var result = _factory.ValidateElement(table);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateElement_WithValidComment_ShouldReturnTrue()
        {
            // Arrange
            var comment = _factory.CreateCommentElement(1, "Author", "2024-01-01", "Comment text");

            // Act
            var result = _factory.ValidateElement(comment);

            // Assert
            Assert.True(result);
        }

        #endregion

        #region CloneElement Tests

        [Fact]
        public void CloneElement_WithValidElement_ShouldCreateDeepCopy()
        {
            // Arrange
            var originalRun = _factory.CreateFormattedRun("Original text", isBold: true);

            // Act
            var clonedRun = _factory.CloneElement(originalRun, deepClone: true);

            // Assert
            Assert.NotNull(clonedRun);
            Assert.NotSame(originalRun, clonedRun);

            // Verify content is the same
            var originalText = originalRun.GetFirstChild<Text>();
            var clonedText = clonedRun.GetFirstChild<Text>();
            Assert.Equal(originalText.Text, clonedText.Text);

            // Verify formatting is preserved
            Assert.NotNull(clonedRun.GetFirstChild<RunProperties>());
            Assert.NotNull(clonedRun.GetFirstChild<RunProperties>().GetFirstChild<Bold>());
        }

        [Fact]
        public void CloneElement_WithShallowClone_ShouldCreateShallowCopy()
        {
            // Arrange
            var originalRun = _factory.CreateFormattedRun("Original text", isBold: true);

            // Act
            var clonedRun = _factory.CloneElement(originalRun, deepClone: false);

            // Assert
            Assert.NotNull(clonedRun);
            Assert.NotSame(originalRun, clonedRun);

            // Shallow clone should not have children
            Assert.False(clonedRun.HasChildren);
        }

        [Fact]
        public void CloneElement_WithNullElement_ShouldThrowException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _factory.CloneElement<Run>(null));
        }

        #endregion

        #region CreateTextRuns Tests

        [Fact]
        public void CreateTextRuns_WithValidSegments_ShouldCreateRunsForEachSegment()
        {
            // Arrange
            var segments = new List<string> { "First", "Second", "Third" };

            // Act
            var result = _factory.CreateTextRuns(segments);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(segments.Count, result.Count);

            for (int i = 0; i < segments.Count; i++)
            {
                var run = result[i];
                var text = run.GetFirstChild<Text>();
                Assert.NotNull(text);
                Assert.Equal(segments[i], text.Text);
                Assert.Equal(SpaceProcessingModeValues.Preserve, text.Space.Value);
            }
        }

        [Fact]
        public void CreateTextRuns_WithEmptySegments_ShouldSkipEmptySegments()
        {
            // Arrange
            var segments = new List<string> { "First", "", "Third", null, "Fifth" };

            // Act
            var result = _factory.CreateTextRuns(segments);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count); // Only non-empty segments

            var texts = result.Select(r => r.GetFirstChild<Text>().Text).ToList();
            Assert.Contains("First", texts);
            Assert.Contains("Third", texts);
            Assert.Contains("Fifth", texts);
        }

        [Fact]
        public void CreateTextRuns_WithNullList_ShouldReturnEmptyList()
        {
            // Act
            var result = _factory.CreateTextRuns(null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void CreateTextRuns_WithPreserveSpaceFalse_ShouldNotSetSpaceAttribute()
        {
            // Arrange
            var segments = new List<string> { "First", "Second" };

            // Act
            var result = _factory.CreateTextRuns(segments, preserveSpace: false);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(segments.Count, result.Count);

            foreach (var run in result)
            {
                var text = run.GetFirstChild<Text>();
                Assert.NotNull(text);
                Assert.Null(text.Space);
            }
        }

        #endregion

        #region Integration Tests

        [Fact]
        public void Factory_IntegrationTest_CreateCompleteCommentWorkflow()
        {
            // Arrange
            int commentId = 100;
            string author = "Integration Test User";
            string date = "2024-01-15T10:30:00Z";
            string message = "This is an integration test comment";

            // Act
            var comment = _factory.CreateCommentElement(commentId, author, date, message);
            var commentRef = _factory.CreateCommentReference(commentId.ToString());

            // Assert
            Assert.NotNull(comment);
            Assert.NotNull(commentRef);

            // Verify comment properties
            Assert.Equal(commentId.ToString(), comment.Id.Value);
            Assert.Equal(author, comment.Author.Value);
            Assert.Equal("ITU", comment.Initials.Value);

            // Verify comment reference
            var refElement = commentRef.GetFirstChild<CommentReference>();
            Assert.NotNull(refElement);
            Assert.Equal(commentId.ToString(), refElement.Id.Value);
        }

        [Fact]
        public void Factory_IntegrationTest_CreateRunElementsWithAllOperatorTypes()
        {
            // Arrange
            string text = "Sample text for testing";

            // Act
            var insertRun = _factory.CreateRunElement(text, RevisionOperatorType.ins);
            var deleteRun = _factory.CreateRunElement(text, RevisionOperatorType.del);

            // Assert
            Assert.NotNull(insertRun);
            Assert.NotNull(deleteRun);

            // Insert run should have Text element
            var insertText = insertRun.GetFirstChild<Text>();
            Assert.NotNull(insertText);
            Assert.Equal(text, insertText.Text);

            // Delete run should have DeletedText element
            var deleteText = deleteRun.GetFirstChild<DeletedText>();
            Assert.NotNull(deleteText);
            Assert.Equal(text, deleteText.Text);
        }

        [Fact]
        public void Factory_IntegrationTest_CreateCompleteDocumentStructure()
        {
            // Arrange & Act - Create a complete document structure using the factory
            var title = _factory.CreateFormattedRun("Document Title", isBold: true, fontSize: 32);
            var titleParagraph = _factory.CreateParagraph();
            titleParagraph.Append(title);
            titleParagraph.PrependChild(_factory.CreateParagraphProperties(JustificationValues.Center));

            var subtitle = _factory.CreateFormattedRun("Subtitle", isItalic: true, fontSize: 24);
            var subtitleParagraph = _factory.CreateParagraph();
            subtitleParagraph.Append(subtitle);
            subtitleParagraph.PrependChild(_factory.CreateParagraphProperties(JustificationValues.Center));

            var bodyText = _factory.CreateTextRun("This is the body text of the document.");
            var bodyParagraph = _factory.CreateParagraph();
            bodyParagraph.Append(bodyText);

            var table = _factory.CreateTable(3, 2, hasHeader: true);
            var headerCells = table.Elements<TableRow>().First().Elements<TableCell>().ToList();
            headerCells[0].RemoveAllChildren<Paragraph>();
            headerCells[0].Append(_factory.CreateParagraph("Header 1"));
            headerCells[1].RemoveAllChildren<Paragraph>();
            headerCells[1].Append(_factory.CreateParagraph("Header 2"));

            // Assert - Verify the structure is valid
            Assert.True(_factory.ValidateElement(titleParagraph));
            Assert.True(_factory.ValidateElement(subtitleParagraph));
            Assert.True(_factory.ValidateElement(bodyParagraph));
            Assert.True(_factory.ValidateElement(table));

            // Verify title formatting
            var titleRun = titleParagraph.Elements<Run>().First();
            var titleProps = titleRun.GetFirstChild<RunProperties>();
            Assert.NotNull(titleProps.GetFirstChild<Bold>());
            Assert.Equal("32", titleProps.GetFirstChild<FontSize>().Val.Value);

            // Verify table structure
            var tableRows = table.Elements<TableRow>().ToList();
            Assert.Equal(3, tableRows.Count);
            Assert.NotNull(tableRows[0].GetFirstChild<TableRowProperties>().GetFirstChild<TableHeader>());
        }

        [Fact]
        public void Factory_IntegrationTest_CreateFormattedTextWithBreaks()
        {
            // Arrange
            string multilineText = "First line\nSecond line\nThird line";

            // Act
            var run = _factory.CreateRunElement(multilineText, RevisionOperatorType.ins);
            var formattedRun = _factory.CreateFormattedRun("Formatted text", isBold: true, isItalic: true);
            var paragraph = _factory.CreateParagraph();
            paragraph.Append(run);
            paragraph.Append(formattedRun);

            // Assert
            Assert.True(_factory.ValidateElement(paragraph));

            // Verify multiline text has breaks
            var textElements = run.Elements<Text>().ToList();
            var breakElements = run.Elements<Break>().ToList();
            Assert.Equal(3, textElements.Count); // Three text segments
            Assert.Equal(2, breakElements.Count); // Two breaks between them

            // Verify formatted run has both bold and italic
            var runProps = formattedRun.GetFirstChild<RunProperties>();
            Assert.NotNull(runProps.GetFirstChild<Bold>());
            Assert.NotNull(runProps.GetFirstChild<Italic>());
        }

        [Fact]
        public void Factory_IntegrationTest_CreateComplexTableWithFormatting()
        {
            // Arrange & Act
            var table = _factory.CreateTable(2, 3, hasHeader: true);
            var rows = table.Elements<TableRow>().ToList();

            // Customize header row
            var headerCells = rows[0].Elements<TableCell>().ToList();
            for (int i = 0; i < headerCells.Count; i++)
            {
                headerCells[i].RemoveAllChildren<Paragraph>();
                var headerText = _factory.CreateFormattedRun($"Header {i + 1}", isBold: true);
                var headerParagraph = _factory.CreateParagraph();
                headerParagraph.Append(headerText);
                headerParagraph.PrependChild(_factory.CreateParagraphProperties(JustificationValues.Center));
                headerCells[i].Append(headerParagraph);
            }

            // Customize data row
            var dataCells = rows[1].Elements<TableCell>().ToList();
            for (int i = 0; i < dataCells.Count; i++)
            {
                dataCells[i].RemoveAllChildren<Paragraph>();
                var dataText = _factory.CreateTextRun($"Data {i + 1}");
                var dataParagraph = _factory.CreateParagraph();
                dataParagraph.Append(dataText);
                dataCells[i].Append(dataParagraph);
            }

            // Assert
            Assert.True(_factory.ValidateElement(table));

            // Verify header formatting
            foreach (var headerCell in headerCells)
            {
                var paragraph = headerCell.GetFirstChild<Paragraph>();
                var run = paragraph.GetFirstChild<Run>();
                var runProps = run.GetFirstChild<RunProperties>();
                Assert.NotNull(runProps.GetFirstChild<Bold>());

                var paragraphProps = paragraph.GetFirstChild<ParagraphProperties>();
                var justification = paragraphProps.GetFirstChild<Justification>();
                Assert.Equal(JustificationValues.Center, justification.Val.Value);
            }

            // Verify data cells have content
            foreach (var dataCell in dataCells)
            {
                var paragraph = dataCell.GetFirstChild<Paragraph>();
                var run = paragraph.GetFirstChild<Run>();
                var text = run.GetFirstChild<Text>();
                Assert.NotNull(text);
                Assert.StartsWith("Data", text.Text);
            }
        }

        [Fact]
        public void Factory_IntegrationTest_CreateCommentWithComplexContent()
        {
            // Arrange
            string complexMessage = "This is a comment\nwith multiple lines\nand formatting.";

            // Act
            var comment = _factory.CreateCommentElement(123, "John Doe", "2024-01-15T10:30:00", complexMessage);
            var commentReference = _factory.CreateCommentReference("123");

            // Assert
            Assert.True(_factory.ValidateElement(comment));
            Assert.True(_factory.ValidateElement(commentReference));

            // Verify comment properties
            Assert.Equal("123", comment.Id.Value);
            Assert.Equal("John Doe", comment.Author.Value);
            Assert.Equal("JD", comment.Initials.Value);

            // Verify comment content handles multiline text
            var paragraph = comment.GetFirstChild<Paragraph>();
            var run = paragraph.GetFirstChild<Run>();
            var textElements = run.Elements<Text>().ToList();
            var breakElements = run.Elements<Break>().ToList();

            Assert.True(textElements.Count > 1); // Multiple text segments
            Assert.True(breakElements.Count > 0); // Line breaks present

            // Verify comment reference
            var reference = commentReference.GetFirstChild<CommentReference>();
            Assert.Equal("123", reference.Id.Value);
            var runStyle = commentReference.GetFirstChild<RunProperties>().GetFirstChild<RunStyle>();
            Assert.Equal("CommentReference", runStyle.Val.Value);
        }

        [Fact]
        public void Factory_IntegrationTest_CloneAndModifyElements()
        {
            // Arrange
            var originalRun = _factory.CreateFormattedRun("Original text", isBold: true, fontSize: 24);
            var originalTable = _factory.CreateTable(2, 2);

            // Act
            var clonedRun = _factory.CloneElement(originalRun);
            var clonedTable = _factory.CloneElement(originalTable);

            // Modify cloned elements
            var clonedText = clonedRun.GetFirstChild<Text>();
            clonedText.Text = "Modified text";

            // Assert
            Assert.True(_factory.ValidateElement(clonedRun));
            Assert.True(_factory.ValidateElement(clonedTable));

            // Verify original is unchanged
            var originalText = originalRun.GetFirstChild<Text>();
            Assert.Equal("Original text", originalText.Text);
            Assert.Equal("Modified text", clonedText.Text);

            // Verify both have same formatting
            var originalProps = originalRun.GetFirstChild<RunProperties>();
            var clonedProps = clonedRun.GetFirstChild<RunProperties>();
            Assert.NotNull(originalProps.GetFirstChild<Bold>());
            Assert.NotNull(clonedProps.GetFirstChild<Bold>());
            Assert.Equal("24", originalProps.GetFirstChild<FontSize>().Val.Value);
            Assert.Equal("24", clonedProps.GetFirstChild<FontSize>().Val.Value);
        }

        [Fact]
        public void Factory_IntegrationTest_CreateTextRunsFromSegments()
        {
            // Arrange
            var segments = new List<string>
            {
                "First segment",
                "Second segment with formatting",
                "Third segment"
            };

            // Act
            var runs = _factory.CreateTextRuns(segments);
            var paragraph = _factory.CreateParagraph();

            // Add runs to paragraph with different formatting
            for (int i = 0; i < runs.Count; i++)
            {
                if (i == 1) // Format the second run
                {
                    var boldRun = _factory.CreateFormattedRun(segments[i], isBold: true);
                    paragraph.Append(boldRun);
                }
                else
                {
                    paragraph.Append(runs[i]);
                }

                if (i < runs.Count - 1)
                {
                    paragraph.Append(_factory.CreateTextRun(" ")); // Add space between segments
                }
            }

            // Assert
            Assert.True(_factory.ValidateElement(paragraph));

            var paragraphRuns = paragraph.Elements<Run>().ToList();
            Assert.True(paragraphRuns.Count >= segments.Count); // At least as many runs as segments (plus spaces)

            // Verify the formatted run has bold formatting
            var formattedRun = paragraphRuns.FirstOrDefault(r =>
                r.GetFirstChild<Text>()?.Text == "Second segment with formatting");
            Assert.NotNull(formattedRun);
            var runProps = formattedRun.GetFirstChild<RunProperties>();
            Assert.NotNull(runProps?.GetFirstChild<Bold>());
        }

        #endregion
    }
}