using Xunit;
using DocumentFormat.OpenXml.Wordprocessing;
using ApplyRevision.Factory;
using ApplyRevision.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using DocumentFormat.OpenXml.Packaging;
using System.IO;

namespace ApplyRevision.Tests.Factory
{
    public class RevisionElementFactoryTests
    {
        private readonly RevisionElementFactory _factory;

        public RevisionElementFactoryTests()
        {
            _factory = new RevisionElementFactory();
        }

        [Fact]
        public void CreateInsertedRunElement_ShouldCreateCorrectElement()
        {
            // Arrange
            string text = "Sample text";
            string revisionId = "1";
            string author = "Test Author";
            string date = "2024-01-15";

            // Act
            var result = _factory.CreateInsertedRunElement(text, revisionId, author, date);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(revisionId, result.Id);
            Assert.Equal(author, result.Author);
            Assert.Equal(DateTime.Parse(date), result.Date.Value);

            var run = result.Elements<Run>().FirstOrDefault();
            Assert.NotNull(run);

            var textElement = run.Elements<Text>().FirstOrDefault();
            Assert.NotNull(textElement);
            Assert.Equal(text, textElement.Text);
        }

        [Fact]
        public void CreateDeletedRunElement_ShouldCreateCorrectElement()
        {
            // Arrange
            string text = "Deleted text";
            string revisionId = "2";
            string author = "Test Author";
            string date = "2024-01-15";

            // Act
            var result = _factory.CreateDeletedRunElement(text, revisionId, author, date);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(revisionId, result.Id);
            Assert.Equal(author, result.Author);
            Assert.Equal(DateTime.Parse(date), result.Date.Value);

            var run = result.Elements<Run>().FirstOrDefault();
            Assert.NotNull(run);

            var deletedText = run.Elements<DeletedText>().FirstOrDefault();
            Assert.NotNull(deletedText);
            Assert.Equal(text, deletedText.Text);
        }

        [Fact]
        public void CreateRevisionElement_WithInsertOperation_ShouldReturnInsertedRun()
        {
            // Arrange
            var revisionOperator = new RevisionOperator
            {
                Op = RevisionOperatorType.ins,
                Text = "Test content",
                Author = "Test Author",
                Date = "2024-01-15",
                Scope = RevisionScope.Run
            };
            string revisionId = "3";

            // Act
            var result = _factory.CreateRevisionElement(revisionOperator, revisionId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<InsertedRun>(result);

            var insertedRun = (InsertedRun)result;
            Assert.Equal(revisionId, insertedRun.Id);
            Assert.Equal(revisionOperator.Author, insertedRun.Author);
        }

        [Fact]
        public void CreateRevisionElement_WithDeleteOperation_ShouldReturnDeletedRun()
        {
            // Arrange
            var revisionOperator = new RevisionOperator
            {
                Op = RevisionOperatorType.del,
                Text = "Deleted content",
                Author = "Test Author",
                Date = "2024-01-15",
                Scope = RevisionScope.Run
            };
            string revisionId = "4";

            // Act
            var result = _factory.CreateRevisionElement(revisionOperator, revisionId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<DeletedRun>(result);

            var deletedRun = (DeletedRun)result;
            Assert.Equal(revisionId, deletedRun.Id);
            Assert.Equal(revisionOperator.Author, deletedRun.Author);
        }

        [Fact]
        public void CreateRevisionElement_WithMoveFromOperation_ShouldReturnMoveFromRun()
        {
            // Arrange
            var revisionOperator = new RevisionOperator
            {
                Op = RevisionOperatorType.moveFrom,
                Text = "Moved content",
                Author = "Test Author",
                Date = "2024-01-15",
                Scope = RevisionScope.Run
            };
            string revisionId = "5";

            // Act
            var result = _factory.CreateRevisionElement(revisionOperator, revisionId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<MoveFromRun>(result);

            var moveFromRun = (MoveFromRun)result;
            Assert.Equal(revisionId, moveFromRun.Id);
            Assert.Equal(revisionOperator.Author, moveFromRun.Author);
        }

        [Fact]
        public void CreateRevisionElement_WithBlockInsOperation_ShouldReturnInserted()
        {
            // Arrange
            var revisionOperator = new RevisionOperator
            {
                Op = RevisionOperatorType.blockIns,
                Author = "Test Author",
                Date = "2024-01-15",
                Scope = RevisionScope.Paragraph
            };
            string revisionId = "6";

            // Act
            var result = _factory.CreateRevisionElement(revisionOperator, revisionId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<Inserted>(result);

            var inserted = (Inserted)result;
            Assert.Equal(revisionId, inserted.Id);
            Assert.Equal(revisionOperator.Author, inserted.Author);
        }

        [Fact]
        public void CreateRevisionElements_ShouldCreateElementsAndComments()
        {
            // Arrange
            var revisionOperators = new List<RevisionOperator>
            {
                new RevisionOperator
                {
                    Op = RevisionOperatorType.ins,
                    Text = "Inserted text",
                    Author = "Author1",
                    Date = "2024-01-15",
                    Scope = RevisionScope.Run
                },
                new RevisionOperator
                {
                    Op = RevisionOperatorType.del,
                    Text = "Deleted text",
                    Author = "Author2",
                    Date = "2024-01-15",
                    Scope = RevisionScope.Run
                }
            };

            // Act
            var (revisionElements, comments) = _factory.CreateRevisionElements(
                revisionOperators, EditType.inplace, 1, 1);

            // Assert
            Assert.Equal(2, revisionElements.Count);
            Assert.IsType<InsertedRun>(revisionElements[0]);
            Assert.IsType<DeletedRun>(revisionElements[1]);

            // Should create comment for delete operation only
            Assert.Single(comments);
            Assert.Equal("1", comments[0].Id);
        }

        [Fact]
        public void CreateCellMerge_ShouldCreateCorrectElement()
        {
            // Arrange
            string revisionId = "7";
            string author = "Test Author";
            string date = "2024-01-15";
            var vMerge = MergedCellValues.Restart;

            // Act
            var result = _factory.CreateCellMerge(revisionId, author, date, vMerge);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(revisionId, result.Id);
            Assert.Equal(author, result.Author);
            Assert.Equal(DateTime.Parse(date), result.Date.Value);
        }

        [Fact]
        public void CreateCommentRangeStart_ShouldCreateCorrectElement()
        {
            // Arrange
            string commentId = "comment1";

            // Act
            var result = _factory.CreateCommentRangeStart(commentId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(commentId, result.Id);
        }

        [Fact]
        public void CreateCommentRangeEnd_ShouldCreateCorrectElement()
        {
            // Arrange
            string commentId = "comment1";

            // Act
            var result = _factory.CreateCommentRangeEnd(commentId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(commentId, result.Id);
        }

        [Fact]
        public void GenerateDocxWithRevisions_ShouldCreateDocumentWithAllRevisionTypes()
        {
            // Arrange
            var outputPath = Path.Combine(Directory.GetCurrentDirectory(), "RevisionElementsDemo.docx");

            // Create a new Word document
            using (var wordDocument = WordprocessingDocument.Create(outputPath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document))
            {
                // Add a main document part
                var mainPart = wordDocument.AddMainDocumentPart();
                mainPart.Document = new Document();
                var body = mainPart.Document.AppendChild(new Body());

                // Add title
                var titleParagraph = new Paragraph();
                var titleRun = new Run();
                var titleRunProperties = new RunProperties();
                titleRunProperties.Append(new Bold());
                titleRunProperties.Append(new FontSize() { Val = "28" });
                titleRun.Append(titleRunProperties);
                titleRun.Append(new Text("Word Document Revision Elements Demo"));
                titleParagraph.Append(titleRun);
                body.Append(titleParagraph);

                // Add empty line
                body.Append(new Paragraph());

                // Example 1: Inserted text
                var insertedParagraph = new Paragraph();
                var beforeRun1 = new Run(new Text("Normal text before "));
                insertedParagraph.Append(beforeRun1);

                var insertedRun = _factory.CreateInsertedRunElement("inserted text", "1", "John Doe", "2024-01-15");
                insertedParagraph.Append(insertedRun);

                var afterRun1 = new Run(new Text(" normal text after."));
                insertedParagraph.Append(afterRun1);
                body.Append(insertedParagraph);

                // Example 2: Deleted text
                var deletedParagraph = new Paragraph();
                var beforeRun2 = new Run(new Text("Normal text before "));
                deletedParagraph.Append(beforeRun2);

                var deletedRun = _factory.CreateDeletedRunElement("deleted text", "2", "Jane Smith", "2024-01-15");
                deletedParagraph.Append(deletedRun);

                var afterRun2 = new Run(new Text(" normal text after."));
                deletedParagraph.Append(afterRun2);
                body.Append(deletedParagraph);

                // Example 3: Move From
                var moveFromParagraph = new Paragraph();
                var beforeRun3 = new Run(new Text("Text before "));
                moveFromParagraph.Append(beforeRun3);

                var moveFromRun = _factory.CreateMoveFromRunElement("moved from here", "3", "Editor", "2024-01-15");
                moveFromParagraph.Append(moveFromRun);

                var afterRun3 = new Run(new Text(" text after."));
                moveFromParagraph.Append(afterRun3);
                body.Append(moveFromParagraph);

                // Example 4: Move To  
                var moveToParagraph = new Paragraph();
                var beforeRun4 = new Run(new Text("Text before "));
                moveToParagraph.Append(beforeRun4);

                var moveToRun = _factory.CreateMoveToRunElement("moved to here", "4", "Editor", "2024-01-15");
                moveToParagraph.Append(moveToRun);

                var afterRun4 = new Run(new Text(" text after."));
                moveToParagraph.Append(afterRun4);
                body.Append(moveToParagraph);

                // Example 5: Complex paragraph with multiple revisions
                var complexParagraph = new Paragraph();
                complexParagraph.Append(new Run(new Text("This paragraph has ")));

                var insertedRun2 = _factory.CreateInsertedRunElement("inserted", "5", "Author1", "2024-01-15");
                complexParagraph.Append(insertedRun2);

                complexParagraph.Append(new Run(new Text(" and ")));

                var deletedRun2 = _factory.CreateDeletedRunElement("deleted", "6", "Author2", "2024-01-15");
                complexParagraph.Append(deletedRun2);

                complexParagraph.Append(new Run(new Text(" text in the same paragraph.")));
                body.Append(complexParagraph);
            }

            // Assert that file was created
            Assert.True(File.Exists(outputPath), $"Document should be created at {outputPath}");

            // Log the output path for user reference
            System.Console.WriteLine($"Generated Word document with revision elements at: {outputPath}");
            System.Console.WriteLine("Open this file in Microsoft Word to see the track changes visualization.");
        }

        [Fact]
        public void GenerateComprehensiveDocxWithRevisions_ShouldCreateDocumentWithAdvancedRevisionTypes()
        {
            // Arrange
            var outputPath = Path.Combine(Directory.GetCurrentDirectory(), "ComprehensiveRevisionElementsDemo.docx");

            // Create a new Word document
            using (var wordDocument = WordprocessingDocument.Create(outputPath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document))
            {
                // Add a main document part
                var mainPart = wordDocument.AddMainDocumentPart();
                mainPart.Document = new Document();
                var body = mainPart.Document.AppendChild(new Body());

                // Add title
                var titleParagraph = new Paragraph();
                var titleRun = new Run();
                var titleRunProperties = new RunProperties();
                titleRunProperties.Append(new Bold());
                titleRunProperties.Append(new FontSize() { Val = "32" });
                titleRun.Append(titleRunProperties);
                titleRun.Append(new Text("Comprehensive Revision Elements Demo"));
                titleParagraph.Append(titleRun);
                body.Append(titleParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 1: Basic Text Revisions
                var section1Title = new Paragraph();
                var section1Run = new Run();
                var section1Props = new RunProperties();
                section1Props.Append(new Bold());
                section1Props.Append(new FontSize() { Val = "24" });
                section1Run.Append(section1Props);
                section1Run.Append(new Text("1. Basic Text Revisions"));
                section1Title.Append(section1Run);
                body.Append(section1Title);

                // Example 1: Basic insertions and deletions
                var basicRevParagraph = new Paragraph();
                basicRevParagraph.Append(new Run(new Text("This is normal text with ")));

                var insertedRun = _factory.CreateInsertedRunElement("inserted content", "1", "John Doe", "2024-01-15");
                basicRevParagraph.Append(insertedRun);

                basicRevParagraph.Append(new Run(new Text(" and ")));

                var deletedRun = _factory.CreateDeletedRunElement("deleted content", "2", "Jane Smith", "2024-01-15");
                basicRevParagraph.Append(deletedRun);

                basicRevParagraph.Append(new Run(new Text(" in the same paragraph.")));
                body.Append(basicRevParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 2: Move Operations
                var section2Title = new Paragraph();
                var section2Run = new Run();
                var section2Props = new RunProperties();
                section2Props.Append(new Bold());
                section2Props.Append(new FontSize() { Val = "24" });
                section2Run.Append(section2Props);
                section2Run.Append(new Text("2. Move Operations"));
                section2Title.Append(section2Run);
                body.Append(section2Title);

                // Move From example
                var moveFromParagraph = new Paragraph();
                moveFromParagraph.Append(new Run(new Text("Original location: ")));
                var moveFromRun = _factory.CreateMoveFromRunElement("This text was moved", "3", "Editor", "2024-01-15");
                moveFromParagraph.Append(moveFromRun);
                moveFromParagraph.Append(new Run(new Text(" (moved from here)")));
                body.Append(moveFromParagraph);

                // Move To example
                var moveToParagraph = new Paragraph();
                moveToParagraph.Append(new Run(new Text("New location: ")));
                var moveToRun = _factory.CreateMoveToRunElement("This text was moved", "4", "Editor", "2024-01-15");
                moveToParagraph.Append(moveToRun);
                moveToParagraph.Append(new Run(new Text(" (moved to here)")));
                body.Append(moveToParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 3: Simple Formatting Example
                var section3Title = new Paragraph();
                var section3Run = new Run();
                var section3Props = new RunProperties();
                section3Props.Append(new Bold());
                section3Props.Append(new FontSize() { Val = "24" });
                section3Run.Append(section3Props);
                section3Run.Append(new Text("3. Formatted Content"));
                section3Title.Append(section3Run);
                body.Append(section3Title);

                // Simple formatted text example
                var formattedParagraph = new Paragraph();
                formattedParagraph.Append(new Run(new Text("This paragraph shows ")));

                var boldRun = new Run();
                var boldProps = new RunProperties();
                boldProps.Append(new Bold());
                boldRun.Append(boldProps);
                boldRun.Append(new Text("bold text"));
                formattedParagraph.Append(boldRun);

                formattedParagraph.Append(new Run(new Text(" mixed with normal text.")));
                body.Append(formattedParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 4: Mixed Content Examples
                var section4Title = new Paragraph();
                var section4Run = new Run();
                var section4Props = new RunProperties();
                section4Props.Append(new Bold());
                section4Props.Append(new FontSize() { Val = "24" });
                section4Run.Append(section4Props);
                section4Run.Append(new Text("4. Mixed Content Examples"));
                section4Title.Append(section4Run);
                body.Append(section4Title);

                // Simple paragraph with different content
                var mixedParagraph = new Paragraph();
                mixedParagraph.Append(new Run(new Text("This paragraph shows various content types working together in the document.")));
                body.Append(mixedParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 5: Multiple Revisions in Complex Scenarios
                var section5Title = new Paragraph();
                var section5Run = new Run();
                var section5Props = new RunProperties();
                section5Props.Append(new Bold());
                section5Props.Append(new FontSize() { Val = "24" });
                section5Run.Append(section5Props);
                section5Run.Append(new Text("5. Complex Multi-Author Scenarios"));
                section5Title.Append(section5Run);
                body.Append(section5Title);

                // Complex paragraph with multiple authors and revision types
                var complexParagraph = new Paragraph();
                complexParagraph.Append(new Run(new Text("This sentence was written by the original author. ")));

                var editorInsert = _factory.CreateInsertedRunElement("The editor added this part. ", "7", "Editor", "2024-01-16");
                complexParagraph.Append(editorInsert);

                var reviewerDelete = _factory.CreateDeletedRunElement("The reviewer removed this. ", "8", "Reviewer", "2024-01-17");
                complexParagraph.Append(reviewerDelete);

                var finalEdit = _factory.CreateInsertedRunElement("Final author made this addition.", "9", "Final Author", "2024-01-18");
                complexParagraph.Append(finalEdit);

                body.Append(complexParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 6: Additional Revisions
                var section6Title = new Paragraph();
                var section6Run = new Run();
                var section6Props = new RunProperties();
                section6Props.Append(new Bold());
                section6Props.Append(new FontSize() { Val = "24" });
                section6Run.Append(section6Props);
                section6Run.Append(new Text("6. Additional Examples"));
                section6Title.Append(section6Run);
                body.Append(section6Title);

                // Simple example with comment ranges (without the complex range operations)
                var rangeParagraph = new Paragraph();
                rangeParagraph.Append(new Run(new Text("This paragraph demonstrates additional revision tracking capabilities for comprehensive testing.")));
                body.Append(rangeParagraph);

                body.Append(new Paragraph()); // Empty line

                // Add a final summary paragraph
                var summaryParagraph = new Paragraph();
                var summaryRun = new Run();
                var summaryProps = new RunProperties();
                summaryProps.Append(new Italic());
                summaryRun.Append(summaryProps);
                summaryRun.Append(new Text("This document demonstrates various types of revision tracking elements that can be created using the RevisionElementFactory. Open in Microsoft Word with track changes enabled to see the visual representation."));
                summaryParagraph.Append(summaryRun);
                body.Append(summaryParagraph);
            }

            // Assert that file was created
            Assert.True(File.Exists(outputPath), $"Document should be created at {outputPath}");

            // Log the output path for user reference
            System.Console.WriteLine($"Generated comprehensive Word document with revision elements at: {outputPath}");
            System.Console.WriteLine("Open this file in Microsoft Word to see all the track changes visualizations.");
        }

        [Fact]
        public void GenerateNestedRevisionsDocx_ShouldCreateDocumentWithComplexNestedRevisions()
        {
            // Arrange
            var outputPath = Path.Combine(Directory.GetCurrentDirectory(), "NestedRevisionsDemo.docx");

            // Create a new Word document
            using (var wordDocument = WordprocessingDocument.Create(outputPath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document))
            {
                // Add a main document part
                var mainPart = wordDocument.AddMainDocumentPart();
                mainPart.Document = new Document();
                var body = mainPart.Document.AppendChild(new Body());

                // Add title
                var titleParagraph = new Paragraph();
                var titleRun = new Run();
                var titleRunProperties = new RunProperties();
                titleRunProperties.Append(new Bold());
                titleRunProperties.Append(new FontSize() { Val = "36" });
                titleRun.Append(titleRunProperties);
                titleRun.Append(new Text("Nested Revisions Complex Scenarios Demo"));
                titleParagraph.Append(titleRun);
                body.Append(titleParagraph);

                body.Append(new Paragraph()); // Empty line

                // Scenario 1: Insert Text + Format Change
                var scenario1Title = new Paragraph();
                var scenario1Run = new Run();
                var scenario1Props = new RunProperties();
                scenario1Props.Append(new Bold());
                scenario1Props.Append(new FontSize() { Val = "24" });
                scenario1Run.Append(scenario1Props);
                scenario1Run.Append(new Text("Scenario 1: Insert Text → Format Change"));
                scenario1Title.Append(scenario1Run);
                body.Append(scenario1Title);

                // Original text
                var para1 = new Paragraph();
                para1.Append(new Run(new Text("Original text. ")));

                // Step 1: John inserts text
                var insertedByJohn = _factory.CreateInsertedRunElement("This text was inserted by John", "1", "John Doe", "2024-01-15T10:00:00");
                para1.Append(insertedByJohn);

                // Step 2: Jane formats the inserted text (nested revision)
                var formattedInsertedRun = new Run();
                var formattedInsertedRunProps = new RunProperties();
                formattedInsertedRunProps.Append(new Bold());
                formattedInsertedRunProps.Append(new Italic());

                // Add property change revision to track the formatting change
                var formatChange = _factory.CreateRunPropertiesChange("2", "Jane Smith", "2024-01-15T14:30:00");
                formattedInsertedRunProps.Append(formatChange);

                formattedInsertedRun.Append(formattedInsertedRunProps);
                formattedInsertedRun.Append(new Text(" and then formatted by Jane"));

                // Wrap the formatted run in an inserted run to show it's also an insertion
                var nestedInsertedRun = _factory.CreateInsertedRunElement("", "3", "Jane Smith", "2024-01-15T14:30:00");
                nestedInsertedRun.Append(formattedInsertedRun);
                para1.Append(nestedInsertedRun);

                para1.Append(new Run(new Text(". More original text.")));
                body.Append(para1);

                body.Append(new Paragraph()); // Empty line

                // Scenario 2: Insert Text → Partial Deletion
                var scenario2Title = new Paragraph();
                var scenario2Run = new Run();
                var scenario2Props = new RunProperties();
                scenario2Props.Append(new Bold());
                scenario2Props.Append(new FontSize() { Val = "24" });
                scenario2Run.Append(scenario2Props);
                scenario2Run.Append(new Text("Scenario 2: Insert Text → Partial Deletion"));
                scenario2Title.Append(scenario2Run);
                body.Append(scenario2Title);

                var para2 = new Paragraph();
                para2.Append(new Run(new Text("Base text. ")));

                // Step 1: Alice inserts a longer text
                var aliceInsert = _factory.CreateInsertedRunElement("Alice inserted this longer piece of text that will be partially modified", "4", "Alice Johnson", "2024-01-16T09:00:00");
                para2.Append(aliceInsert);

                // Step 2: Bob deletes part of Alice's insertion
                para2.Append(new Run(new Text(" but ")));
                var bobDelete = _factory.CreateDeletedRunElement("Bob deleted this part", "5", "Bob Wilson", "2024-01-16T15:20:00");
                para2.Append(bobDelete);

                // Step 3: Bob also adds something
                var bobInsert = _factory.CreateInsertedRunElement("Bob added this instead", "6", "Bob Wilson", "2024-01-16T15:25:00");
                para2.Append(bobInsert);

                para2.Append(new Run(new Text(" continuing the text.")));
                body.Append(para2);

                body.Append(new Paragraph()); // Empty line

                // Scenario 3: Move Text → Format Change
                var scenario3Title = new Paragraph();
                var scenario3Run = new Run();
                var scenario3Props = new RunProperties();
                scenario3Props.Append(new Bold());
                scenario3Props.Append(new FontSize() { Val = "24" });
                scenario3Run.Append(scenario3Props);
                scenario3Run.Append(new Text("Scenario 3: Move Text → Format Change"));
                scenario3Title.Append(scenario3Run);
                body.Append(scenario3Title);

                // Original location (move from)
                var para3a = new Paragraph();
                para3a.Append(new Run(new Text("Original location: ")));
                var moveFromRun = _factory.CreateMoveFromRunElement("This text was moved from here", "7", "Charlie Brown", "2024-01-17T11:00:00");
                para3a.Append(moveFromRun);
                para3a.Append(new Run(new Text(" (original location)")));
                body.Append(para3a);

                // New location (move to) with formatting
                var para3b = new Paragraph();
                para3b.Append(new Run(new Text("New location: ")));

                // Create the moved text with new formatting
                var moveToRun = new Run();
                var moveToRunProps = new RunProperties();
                moveToRunProps.Append(new Bold());
                moveToRunProps.Append(new Underline() { Val = UnderlineValues.Single });

                // Add property change to track formatting applied after move
                var moveFormatChange = _factory.CreateRunPropertiesChange("8", "Diana Prince", "2024-01-17T16:45:00");
                moveToRunProps.Append(moveFormatChange);

                moveToRun.Append(moveToRunProps);
                moveToRun.Append(new Text("This text was moved from here"));

                // Wrap in MoveToRun
                var moveToElement = _factory.CreateMoveToRunElement("", "9", "Charlie Brown", "2024-01-17T11:00:00");
                moveToElement.Append(moveToRun);
                para3b.Append(moveToElement);

                para3b.Append(new Run(new Text(" (new location with formatting)")));
                body.Append(para3b);

                body.Append(new Paragraph()); // Empty line

                // Scenario 4: Multi-Author Cascade
                var scenario4Title = new Paragraph();
                var scenario4Run = new Run();
                var scenario4Props = new RunProperties();
                scenario4Props.Append(new Bold());
                scenario4Props.Append(new FontSize() { Val = "24" });
                scenario4Run.Append(scenario4Props);
                scenario4Run.Append(new Text("Scenario 4: Multi-Author Revision Cascade"));
                scenario4Title.Append(scenario4Run);
                body.Append(scenario4Title);

                var para4 = new Paragraph();
                para4.Append(new Run(new Text("Initial draft text. ")));

                // Step 1: Editor inserts content
                var editorInsert = _factory.CreateInsertedRunElement("Editor added this sentence", "10", "Chief Editor", "2024-01-18T09:00:00");
                para4.Append(editorInsert);

                para4.Append(new Run(new Text(". ")));

                // Step 2: Reviewer deletes part and suggests replacement
                var reviewerDelete = _factory.CreateDeletedRunElement("Reviewer removed this outdated info", "11", "Senior Reviewer", "2024-01-18T11:30:00");
                para4.Append(reviewerDelete);

                var reviewerInsert = _factory.CreateInsertedRunElement("Reviewer added updated information", "12", "Senior Reviewer", "2024-01-18T11:35:00");
                para4.Append(reviewerInsert);

                para4.Append(new Run(new Text(". ")));

                // Step 3: Proofreader makes final formatting adjustments
                var proofreaderRun = new Run();
                var proofreaderProps = new RunProperties();
                proofreaderProps.Append(new Italic());
                var proofreaderFormatChange = _factory.CreateRunPropertiesChange("13", "Final Proofreader", "2024-01-18T17:00:00");
                proofreaderProps.Append(proofreaderFormatChange);
                proofreaderRun.Append(proofreaderProps);
                proofreaderRun.Append(new Text("Proofreader added final touches with formatting"));

                var proofreaderInsert = _factory.CreateInsertedRunElement("", "14", "Final Proofreader", "2024-01-18T17:00:00");
                proofreaderInsert.Append(proofreaderRun);
                para4.Append(proofreaderInsert);

                para4.Append(new Run(new Text(". Document completed.")));
                body.Append(para4);

                body.Append(new Paragraph()); // Empty line

                // Scenario 5: Complex Nested Structure
                var scenario5Title = new Paragraph();
                var scenario5Run = new Run();
                var scenario5Props = new RunProperties();
                scenario5Props.Append(new Bold());
                scenario5Props.Append(new FontSize() { Val = "24" });
                scenario5Run.Append(scenario5Props);
                scenario5Run.Append(new Text("Scenario 5: Complex Nested Structure"));
                scenario5Title.Append(scenario5Run);
                body.Append(scenario5Title);

                var para5 = new Paragraph();
                para5.Append(new Run(new Text("Complex scenario: ")));

                // Multiple overlapping revisions with different timestamps
                var complexInsert1 = _factory.CreateInsertedRunElement("First insertion", "15", "Author A", "2024-01-19T08:00:00");
                para5.Append(complexInsert1);

                para5.Append(new Run(new Text(" with ")));

                // Nested formatting change
                var complexRun = new Run();
                var complexProps = new RunProperties();
                complexProps.Append(new Bold());
                complexProps.Append(new Color() { Val = "FF0000" }); // Red color
                var complexFormatChange = _factory.CreateRunPropertiesChange("16", "Designer", "2024-01-19T10:30:00");
                complexProps.Append(complexFormatChange);
                complexRun.Append(complexProps);
                complexRun.Append(new Text("styled content"));

                var complexInsert2 = _factory.CreateInsertedRunElement("", "17", "Designer", "2024-01-19T10:30:00");
                complexInsert2.Append(complexRun);
                para5.Append(complexInsert2);

                para5.Append(new Run(new Text(" and ")));

                // Deletion in the middle
                var complexDelete = _factory.CreateDeletedRunElement("removed content", "18", "Editor B", "2024-01-19T14:00:00");
                para5.Append(complexDelete);

                // Final addition
                var complexInsert3 = _factory.CreateInsertedRunElement("replacement content", "19", "Editor B", "2024-01-19T14:05:00");
                para5.Append(complexInsert3);

                para5.Append(new Run(new Text(".")));
                body.Append(para5);

                body.Append(new Paragraph()); // Empty line

                // Scenario 6: Timeline-based Revisions
                var scenario6Title = new Paragraph();
                var scenario6Run = new Run();
                var scenario6Props = new RunProperties();
                scenario6Props.Append(new Bold());
                scenario6Props.Append(new FontSize() { Val = "24" });
                scenario6Run.Append(scenario6Props);
                scenario6Run.Append(new Text("Scenario 6: Timeline-based Revision History"));
                scenario6Title.Append(scenario6Run);
                body.Append(scenario6Title);

                var para6 = new Paragraph();
                para6.Append(new Run(new Text("Version history: ")));

                // Day 1: Initial draft
                var day1Insert = _factory.CreateInsertedRunElement("Initial draft content", "20", "Original Author", "2024-01-20T09:00:00");
                para6.Append(day1Insert);

                para6.Append(new Run(new Text(". ")));

                // Day 2: First review
                var day2Delete = _factory.CreateDeletedRunElement("Day 2 - removed unclear text", "21", "First Reviewer", "2024-01-21T10:00:00");
                para6.Append(day2Delete);

                var day2Insert = _factory.CreateInsertedRunElement("Day 2 - added clarification", "22", "First Reviewer", "2024-01-21T10:05:00");
                para6.Append(day2Insert);

                para6.Append(new Run(new Text(". ")));

                // Day 3: Second review with formatting
                var day3Run = new Run();
                var day3Props = new RunProperties();
                day3Props.Append(new Bold());
                day3Props.Append(new Underline() { Val = UnderlineValues.Single });
                var day3FormatChange = _factory.CreateRunPropertiesChange("23", "Second Reviewer", "2024-01-22T15:30:00");
                day3Props.Append(day3FormatChange);
                day3Run.Append(day3Props);
                day3Run.Append(new Text("Day 3 - emphasized important text"));

                var day3Insert = _factory.CreateInsertedRunElement("", "24", "Second Reviewer", "2024-01-22T15:30:00");
                day3Insert.Append(day3Run);
                para6.Append(day3Insert);

                para6.Append(new Run(new Text(". ")));

                // Day 4: Final edits
                var day4Insert = _factory.CreateInsertedRunElement("Day 4 - final polish", "25", "Final Editor", "2024-01-23T16:00:00");
                para6.Append(day4Insert);

                para6.Append(new Run(new Text(".")));
                body.Append(para6);

                body.Append(new Paragraph()); // Empty line

                // Summary paragraph
                var summaryParagraph = new Paragraph();
                var summaryRun = new Run();
                var summaryProps = new RunProperties();
                summaryProps.Append(new Italic());
                summaryRun.Append(summaryProps);
                summaryRun.Append(new Text("This document demonstrates complex nested revision scenarios that occur in real-world collaborative document editing. Each scenario shows how multiple revision types can be layered and combined to create sophisticated change tracking patterns."));
                summaryParagraph.Append(summaryRun);
                body.Append(summaryParagraph);
            }

            // Assert that file was created
            Assert.True(File.Exists(outputPath), $"Document should be created at {outputPath}");

            // Copy file to current working directory (workspace root) for easy access
            var workspaceRoot = "/home/<USER>/projects/lexis_plus_ai_content_platform/api/word_tool/src";
            var destinationPath = Path.Combine(workspaceRoot, "NestedRevisionsDemo.docx");

            try
            {
                File.Copy(outputPath, destinationPath, overwrite: true);
                System.Console.WriteLine($"File copied to workspace root: {destinationPath}");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"Warning: Could not copy file to workspace root. Error: {ex.Message}");
            }

            // Log the output path for user reference
            System.Console.WriteLine($"Generated nested revisions document at: {outputPath}");
            System.Console.WriteLine("This document showcases complex nested revision scenarios:");
            System.Console.WriteLine("1. Insert Text → Format Change");
            System.Console.WriteLine("2. Insert Text → Partial Deletion");
            System.Console.WriteLine("3. Move Text → Format Change");
            System.Console.WriteLine("4. Multi-Author Revision Cascade");
            System.Console.WriteLine("5. Complex Nested Structure");
            System.Console.WriteLine("6. Timeline-based Revision History");
            System.Console.WriteLine("Open in Microsoft Word with track changes enabled to see the visualization.");
        }

        [Fact]
        public void GenerateCorrectMoveOperationsDocx_ShouldCreateDocumentWithProperMoveStructure()
        {
            // Arrange
            var outputPath = Path.Combine(Directory.GetCurrentDirectory(), "CorrectMoveOperationsDemo.docx");

            // Create a new Word document
            using (var wordDocument = WordprocessingDocument.Create(outputPath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document))
            {
                // Add a main document part
                var mainPart = wordDocument.AddMainDocumentPart();
                mainPart.Document = new Document();
                var body = mainPart.Document.AppendChild(new Body());

                // Add title
                var titleParagraph = new Paragraph();
                var titleRun = new Run();
                var titleRunProperties = new RunProperties();
                titleRunProperties.Append(new Bold());
                titleRunProperties.Append(new FontSize() { Val = "36" });
                titleRun.Append(titleRunProperties);
                titleRun.Append(new Text("Correct Move Operations Demo"));
                titleParagraph.Append(titleRun);
                body.Append(titleParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 1: Explanation
                var explanationTitle = new Paragraph();
                var explanationRun = new Run();
                var explanationProps = new RunProperties();
                explanationProps.Append(new Bold());
                explanationProps.Append(new FontSize() { Val = "24" });
                explanationRun.Append(explanationProps);
                explanationRun.Append(new Text("Understanding Move Operations"));
                explanationTitle.Append(explanationRun);
                body.Append(explanationTitle);

                var explanationParagraph = new Paragraph();
                explanationParagraph.Append(new Run(new Text("This document demonstrates the correct structure for move operations in Word documents. " +
                    "Unlike simple insert/delete operations, move operations consist of linked source and destination elements " +
                    "that share a common move name. When accepting or rejecting a move, both the source (moveFrom) and " +
                    "destination (moveTo) are affected simultaneously.")));
                body.Append(explanationParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 2: Complete Move Operation Example
                var moveTitle = new Paragraph();
                var moveRun = new Run();
                var moveProps = new RunProperties();
                moveProps.Append(new Bold());
                moveProps.Append(new FontSize() { Val = "24" });
                moveRun.Append(moveProps);
                moveRun.Append(new Text("Example: Complete Move Operation"));
                moveTitle.Append(moveRun);
                body.Append(moveTitle);

                // Paragraph with move from location
                var moveFromParagraph = new Paragraph();
                moveFromParagraph.Append(new Run(new Text("This is the original paragraph. ")));

                // Create move from operation wrapped with ranges
                var moveFromElements = _factory.CreateMoveFromWithRanges(
                    "This sentence was moved from here",
                    "102", // moveFromRangeId
                    "103", // moveFromRevisionId
                    "move195176064", // moveName (shared identifier)
                    "Thatcher, Fiona (LNG-SYD)",
                    "2025-04-10T11:14:00Z");

                // Add all move from elements to the paragraph
                foreach (var element in moveFromElements)
                {
                    moveFromParagraph.Append(element);
                }

                moveFromParagraph.Append(new Run(new Text(" and continues with more text.")));
                body.Append(moveFromParagraph);

                // Paragraph with move to location
                var moveToParagraph = new Paragraph();
                moveToParagraph.Append(new Run(new Text("In the new location, we have: ")));

                // Create move to operation wrapped with ranges with the same move name
                var moveToElements = _factory.CreateMoveToWithRanges(
                    "This sentence was moved from here",
                    "55", // moveToRangeId
                    "56", // moveToRevisionId
                    "move195176064", // moveName (same as move from - this links them)
                    "Thatcher, Fiona (LNG-SYD)",
                    "2025-04-10T11:14:00Z");

                // Add all move to elements to the paragraph
                foreach (var element in moveToElements)
                {
                    moveToParagraph.Append(element);
                }

                moveToParagraph.Append(new Run(new Text(" which now appears here instead.")));
                body.Append(moveToParagraph);

                body.Append(new Paragraph()); // Empty line

                // Section 3: Multiple Move Operations
                var multiMoveTitle = new Paragraph();
                var multiMoveRun = new Run();
                var multiMoveProps = new RunProperties();
                multiMoveProps.Append(new Bold());
                multiMoveProps.Append(new FontSize() { Val = "24" });
                multiMoveRun.Append(multiMoveProps);
                multiMoveRun.Append(new Text("Example: Multiple Move Operations"));
                multiMoveTitle.Append(multiMoveRun);
                body.Append(multiMoveTitle);

                // First move operation
                var multiMove1Para = new Paragraph();
                multiMove1Para.Append(new Run(new Text("First move example: ")));

                var move1FromElements = _factory.CreateMoveFromWithRanges(
                    "First moved text",
                    "201", "202", "move_operation_1",
                    "Editor A", "2025-04-10T12:00:00Z");

                foreach (var element in move1FromElements)
                {
                    multiMove1Para.Append(element);
                }

                multiMove1Para.Append(new Run(new Text(" (original location)")));
                body.Append(multiMove1Para);

                // Second move operation
                var multiMove2Para = new Paragraph();
                multiMove2Para.Append(new Run(new Text("Second move example: ")));

                var move2FromElements = _factory.CreateMoveFromWithRanges(
                    "Second moved text",
                    "301", "302", "move_operation_2",
                    "Editor B", "2025-04-10T13:00:00Z");

                foreach (var element in move2FromElements)
                {
                    multiMove2Para.Append(element);
                }

                multiMove2Para.Append(new Run(new Text(" (original location)")));
                body.Append(multiMove2Para);

                body.Append(new Paragraph()); // Empty line

                // Destination paragraph for multiple moves
                var multiMoveDestPara = new Paragraph();
                multiMoveDestPara.Append(new Run(new Text("New combined location: ")));

                // First move destination
                var move1ToElements = _factory.CreateMoveToWithRanges(
                    "First moved text",
                    "251", "252", "move_operation_1",
                    "Editor A", "2025-04-10T12:00:00Z");

                foreach (var element in move1ToElements)
                {
                    multiMoveDestPara.Append(element);
                }

                multiMoveDestPara.Append(new Run(new Text(" and ")));

                // Second move destination
                var move2ToElements = _factory.CreateMoveToWithRanges(
                    "Second moved text",
                    "351", "352", "move_operation_2",
                    "Editor B", "2025-04-10T13:00:00Z");

                foreach (var element in move2ToElements)
                {
                    multiMoveDestPara.Append(element);
                }

                multiMoveDestPara.Append(new Run(new Text(" are now together.")));
                body.Append(multiMoveDestPara);

                body.Append(new Paragraph()); // Empty line

                // Section 4: Using the Complete Move Operation Helper
                var helperTitle = new Paragraph();
                var helperRun = new Run();
                var helperProps = new RunProperties();
                helperProps.Append(new Bold());
                helperProps.Append(new FontSize() { Val = "24" });
                helperRun.Append(helperProps);
                helperRun.Append(new Text("Example: Using Complete Move Operation Helper"));
                helperTitle.Append(helperRun);
                body.Append(helperTitle);

                // Create a complete move operation using the helper method
                var (completeFromElements, completeToElements) = _factory.CreateLinkedMoveOperationWithRanges(
                    "Text created with helper method",
                    "401", "402", "451", "452",
                    "helper_move_example",
                    "Helper User", "2025-04-10T14:00:00Z");

                // Add move from paragraph
                var helperFromPara = new Paragraph();
                helperFromPara.Append(new Run(new Text("Helper method source: ")));
                foreach (var element in completeFromElements)
                {
                    helperFromPara.Append(element);
                }
                helperFromPara.Append(new Run(new Text(" (moved from here)")));
                body.Append(helperFromPara);

                // Add move to paragraph
                var helperToPara = new Paragraph();
                helperToPara.Append(new Run(new Text("Helper method destination: ")));
                foreach (var element in completeToElements)
                {
                    helperToPara.Append(element);
                }
                helperToPara.Append(new Run(new Text(" (moved to here)")));
                body.Append(helperToPara);

                body.Append(new Paragraph()); // Empty line

                // Summary
                var summaryParagraph = new Paragraph();
                var summaryRun = new Run();
                var summaryProps = new RunProperties();
                summaryProps.Append(new Italic());
                summaryRun.Append(summaryProps);
                summaryRun.Append(new Text("This document demonstrates the correct XML structure for move operations. " +
                    "Each move operation consists of: 1) MoveFromRangeStart/MoveFrom/MoveFromRangeEnd at the source, " +
                    "2) MoveToRangeStart/MoveTo/MoveToRangeEnd at the destination, and 3) both sharing the same move name " +
                    "for proper linking. This ensures that accepting or rejecting the move affects both locations simultaneously."));
                summaryParagraph.Append(summaryRun);
                body.Append(summaryParagraph);
            }

            // Assert that file was created
            Assert.True(File.Exists(outputPath), $"Document should be created at {outputPath}");

            // Copy file to current working directory (workspace root) for easy access
            var workspaceRoot = "/home/<USER>/projects/lexis_plus_ai_content_platform/api/word_tool/src";
            var destinationPath = Path.Combine(workspaceRoot, "CorrectMoveOperationsDemo.docx");

            try
            {
                File.Copy(outputPath, destinationPath, overwrite: true);
                System.Console.WriteLine($"File copied to workspace root: {destinationPath}");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"Warning: Could not copy file to workspace root. Error: {ex.Message}");
            }

            // Log the output path for user reference
            System.Console.WriteLine($"Generated correct move operations document at: {outputPath}");
            System.Console.WriteLine("This document showcases proper move operation structure:");
            System.Console.WriteLine("1. MoveFromRangeStart → MoveFrom → MoveFromRangeEnd (source)");
            System.Console.WriteLine("2. MoveToRangeStart → MoveTo → MoveToRangeEnd (destination)");
            System.Console.WriteLine("3. Shared move name for proper linking");
            System.Console.WriteLine("4. Single accept/reject affects both locations");
            System.Console.WriteLine("Open in Microsoft Word with track changes enabled to see the correct move visualization.");
        }

        [Fact]
        public void CreateCompleteMoveOperation_ShouldCreateLinkedMoveElements()
        {
            // Arrange
            string text = "Test move content";
            string moveFromRangeId = "100";
            string moveFromRevisionId = "101";
            string moveToRangeId = "200";
            string moveToRevisionId = "201";
            string moveName = "test_move_operation";
            string author = "Test Author";
            string date = "2024-01-15";

            // Act
            var (moveFromElements, moveToElements) = _factory.CreateLinkedMoveOperationWithRanges(
                text, moveFromRangeId, moveFromRevisionId, moveToRangeId, moveToRevisionId,
                moveName, author, date);

            // Assert
            Assert.Equal(3, moveFromElements.Count);
            Assert.Equal(3, moveToElements.Count);

            // Verify move from structure
            Assert.IsType<MoveFromRangeStart>(moveFromElements[0]);
            Assert.IsType<MoveFromRun>(moveFromElements[1]);
            Assert.IsType<MoveFromRangeEnd>(moveFromElements[2]);

            // Verify move to structure
            Assert.IsType<MoveToRangeStart>(moveToElements[0]);
            Assert.IsType<MoveToRun>(moveToElements[1]);
            Assert.IsType<MoveToRangeEnd>(moveToElements[2]);

            // Verify shared move name (linking)
            var moveFromRangeStart = (MoveFromRangeStart)moveFromElements[0];
            var moveToRangeStart = (MoveToRangeStart)moveToElements[0];
            Assert.Equal(moveName, moveFromRangeStart.Name);
            Assert.Equal(moveName, moveToRangeStart.Name);

            // Verify IDs
            Assert.Equal(moveFromRangeId, moveFromRangeStart.Id);
            Assert.Equal(moveToRangeId, moveToRangeStart.Id);

            var moveFromElement = (MoveFromRun)moveFromElements[1];
            var moveToElement = (MoveToRun)moveToElements[1];
            Assert.Equal(moveFromRevisionId, moveFromElement.Id);
            Assert.Equal(moveToRevisionId, moveToElement.Id);
        }
    }
}
