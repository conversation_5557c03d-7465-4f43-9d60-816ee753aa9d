using System;

namespace ApplyRevision.Tests.Attributes
{
    /// <summary>
    /// Attribute to automatically save Word documents before and after test execution.
    /// Similar to Python decorators or Java annotations, this attribute enables automatic
    /// document preservation for debugging and verification purposes.
    /// 
    /// Usage:
    /// [SaveWordDocument("test-scenario-name")]
    /// [SaveWordDocument("test-scenario-name", SaveBefore = true, SaveAfter = true)]
    /// [SaveWordDocument("test-scenario-name", OutputDirectory = "custom-path")]
    /// </summary>
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class SaveWordDocumentAttribute : Attribute
    {
        /// <summary>
        /// Gets the scenario name used for file naming.
        /// Files will be saved as: {ScenarioName}_before.docx and {ScenarioName}_after.docx
        /// </summary>
        public string ScenarioName { get; }

        /// <summary>
        /// Gets or sets whether to save the document before test execution.
        /// Default: true
        /// </summary>
        public bool SaveBefore { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to save the document after test execution.
        /// Default: true
        /// </summary>
        public bool SaveAfter { get; set; } = true;

        /// <summary>
        /// Gets or sets the output directory for saved documents.
        /// If null or empty, uses the default TestResults directory.
        /// </summary>
        public string? OutputDirectory { get; set; }

        /// <summary>
        /// Gets or sets whether to include timestamp in filename.
        /// Default: true
        /// </summary>
        public bool IncludeTimestamp { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to save intermediate documents during multi-step operations.
        /// Default: false
        /// </summary>
        public bool SaveIntermediateSteps { get; set; } = false;

        /// <summary>
        /// Gets or sets additional metadata to include in the saved file info.
        /// </summary>
        public string? Metadata { get; set; }

        /// <summary>
        /// Initializes a new instance of the SaveWordDocumentAttribute.
        /// </summary>
        /// <param name="scenarioName">The scenario name for file naming</param>
        public SaveWordDocumentAttribute(string scenarioName)
        {
            if (string.IsNullOrWhiteSpace(scenarioName))
                throw new ArgumentException("Scenario name cannot be null or empty", nameof(scenarioName));
            
            ScenarioName = scenarioName;
        }
    }
}
