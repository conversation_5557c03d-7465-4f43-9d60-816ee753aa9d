@echo off
setlocal enabledelayedexpansion

REM ApplyRevision Local Runner Script for Windows
REM This script helps run the ApplyRevision tool locally for development and testing

set "DOCUMENT="
set "REVISION="
set "OUTPUT="

REM Parse command line arguments
:parse_args
if "%~1"=="" goto validate_args
if "%~1"=="-d" (
    set "DOCUMENT=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--document" (
    set "DOCUMENT=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="-r" (
    set "REVISION=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--revision" (
    set "REVISION=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="-o" (
    set "OUTPUT=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--output" (
    set "OUTPUT=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help

echo ❌ Unknown option: %~1
goto show_help

:show_help
echo Usage: %~nx0 [OPTIONS]
echo.
echo Options:
echo   -d, --document PATH     Path to the Word document (.docx)
echo   -r, --revision PATH     Path to the revision file (.json)
echo   -o, --output PATH       Output path (optional)
echo   -h, --help             Show this help message
echo.
echo Examples:
echo   %~nx0 -d test.docx -r patch.json
echo   %~nx0 --document test.docx --revision patch.json --output modified.docx
echo.
echo Environment Variables:
echo   DOTNET_CLI_TELEMETRY_OPTOUT=1  Disable .NET telemetry
goto end

:validate_args
if "%DOCUMENT%"=="" (
    echo ❌ Document path is required
    goto show_help
)

if "%REVISION%"=="" (
    echo ❌ Revision path is required
    goto show_help
)

REM Check if files exist
if not exist "%DOCUMENT%" (
    echo ❌ Document file not found: %DOCUMENT%
    exit /b 1
)

if not exist "%REVISION%" (
    echo ❌ Revision file not found: %REVISION%
    exit /b 1
)

REM Capture the original working directory before changing directories
set "ORIGINAL_DIR=%CD%"

REM Get the directory of this script
set "SCRIPT_DIR=%~dp0"
set "PROJECT_DIR=%SCRIPT_DIR%src\ApplyRevision"

REM Check if project exists
if not exist "%PROJECT_DIR%\ApplyRevision.csproj" (
    echo ❌ ApplyRevision project not found at: %PROJECT_DIR%
    exit /b 1
)

echo ℹ️  Building ApplyRevision project...

REM Build the project
cd /d "%PROJECT_DIR%"
dotnet build --configuration Debug --verbosity quiet
if errorlevel 1 (
    echo ❌ Failed to build the project
    exit /b 1
)

echo ✅ Project built successfully

REM Convert relative paths to absolute paths based on the original working directory
call :get_absolute_path "%DOCUMENT%" ABSOLUTE_DOCUMENT
call :get_absolute_path "%REVISION%" ABSOLUTE_REVISION

echo ℹ️  Resolved paths:
echo ℹ️    Document: !ABSOLUTE_DOCUMENT!
echo ℹ️    Revision: !ABSOLUTE_REVISION!

REM Verify files exist before proceeding
if not exist "!ABSOLUTE_DOCUMENT!" (
    echo ❌ Document file not found: !ABSOLUTE_DOCUMENT!
    exit /b 1
)

if not exist "!ABSOLUTE_REVISION!" (
    echo ❌ Revision file not found: !ABSOLUTE_REVISION!
    exit /b 1
)

REM Prepare arguments for the application
set "ARGS=--document "!ABSOLUTE_DOCUMENT!" --revision "!ABSOLUTE_REVISION!""
if not "%OUTPUT%"=="" (
    call :get_absolute_path "%OUTPUT%" ABSOLUTE_OUTPUT
    set "ARGS=!ARGS! --output "!ABSOLUTE_OUTPUT!""
) else (
    REM Set default output to original directory with modified filename
    for %%f in ("!ABSOLUTE_DOCUMENT!") do set "FILENAME=%%~nf"
    set "ABSOLUTE_OUTPUT=%ORIGINAL_DIR%\!FILENAME!_modified.docx"
    set "ARGS=!ARGS! --output "!ABSOLUTE_OUTPUT!""
)

echo ℹ️  Running ApplyRevision locally...
echo ℹ️  Document: !ABSOLUTE_DOCUMENT!
echo ℹ️  Revision: !ABSOLUTE_REVISION!
echo ℹ️  Output: !ABSOLUTE_OUTPUT!

REM Disable .NET telemetry for cleaner output
set DOTNET_CLI_TELEMETRY_OPTOUT=1

REM Run the application
dotnet run --configuration Debug --no-build -- !ARGS!
if errorlevel 1 (
    echo ❌ ApplyRevision failed
    exit /b 1
)

echo ✅ ApplyRevision completed successfully
goto end

REM Function to convert relative path to absolute path
:get_absolute_path
set "input_path=%~1"
set "output_var=%~2"

REM Check if path is already absolute (has drive letter or starts with \)
echo "%input_path%" | findstr /R "^\"[A-Za-z]:" >nul
if not errorlevel 1 (
    REM Already absolute path
    set "%output_var%=%input_path%"
    goto :eof
)

echo "%input_path%" | findstr /R "^\\\\" >nul
if not errorlevel 1 (
    REM UNC path, already absolute
    set "%output_var%=%input_path%"
    goto :eof
)

REM Relative path, make it absolute based on original directory
if "%input_path:~0,2%"==".\" (
    set "%output_var%=%ORIGINAL_DIR%\%input_path:~2%"
) else if "%input_path:~0,1%"=="\" (
    REM Root relative path
    set "%output_var%=%ORIGINAL_DIR:~0,2%%input_path%"
) else (
    REM Simple relative path
    set "%output_var%=%ORIGINAL_DIR%\%input_path%"
)
goto :eof

:end
endlocal
