# Apply Revision Lambda targets
.PHONY: applier-build
applier-build:
	@echo "Building apply_revision lambda function..."
	cd apply_revision && sam build
	@echo "Packaging build output to ZIP file..."
	cd apply_revision/.aws-sam/build/ApplyRevisionFunction && zip -r ../../../../apply_revision_lambda.zip .
	@echo "Lambda package created: apply_revision_lambda.zip"

.PHONY: applier-clean
applier-clean:
	@echo "Cleaning apply_revision build artifacts..."
	cd apply_revision && rm -rf .aws-sam/
	cd apply_revision/src/ApplyRevision && dotnet clean
	cd apply_revision/test/ApplyRevision.Test && dotnet clean