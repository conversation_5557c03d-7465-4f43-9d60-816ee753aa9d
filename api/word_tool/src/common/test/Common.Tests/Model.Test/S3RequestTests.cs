using Common.Model;

namespace Common.Tests.Model
{
    public class S3RequestTests
    {
        [Fact]
        public void S3Request_Should_SetAndGetPropertiesCorrectly()
        {
            // Arrange
            var bucketName = "test-bucket";
            var keyName = "test-key";

            // Act
            var s3Request = new S3Request
            {
                Bucket = bucketName,
                Key = keyName
            };

            // Assert
            Assert.Equal(bucketName, s3Request.Bucket);
            Assert.Equal(keyName, s3Request.Key);
        }

        [Fact]
        public void S3Request_Should_AllowNullValues()
        {
            // Act
            var s3Request = new S3Request
            {
                Bucket = null,
                Key = null
            };

            // Assert
            Assert.Null(s3Request.Bucket);
            Assert.Null(s3Request.Key);
        }
    }
}
