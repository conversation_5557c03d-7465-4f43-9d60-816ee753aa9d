using Common.Model;

namespace Common.Tests.Model.Test
{
    public class ResponseFactoryTests
    {
        [Fact]
        public void Success_ShouldReturnSuccessResponse()
        {
            // Arrange
            var data = "Test Data";

            // Act
            var response = ResponseFactory<string>.success(data);

            // Assert
            Assert.NotNull(response);
            Assert.Equal(200, response.Code);
            Assert.Equal("success", response.Message);
            Assert.Equal(data, response.Data);
        }

        [Fact]
        public void Error_ShouldReturnErrorResponse_WithDefaultValues()
        {
            // Arrange
            var data = "Error Data";

            // Act
            var response = ResponseFactory<string>.error(data);

            // Assert
            Assert.NotNull(response);
            Assert.Equal(400, response.Code);
            Assert.Equal("failed", response.Message);
            Assert.Equal(data, response.Data);
        }

        [Fact]
        public void Error_ShouldReturnErrorResponse_WithCustomValues()
        {
            // Arrange
            var data = "Custom Error Data";
            var customCode = 500;
            var customMessage = "Internal Server Error";

            // Act
            var response = ResponseFactory<string>.error(data, customCode, customMessage);

            // Assert
            Assert.NotNull(response);
            Assert.Equal(customCode, response.Code);
            Assert.Equal(customMessage, response.Message);
            Assert.Equal(data, response.Data);
        }
    }
}
