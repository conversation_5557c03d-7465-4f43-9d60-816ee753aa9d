using Common.Model;

namespace Common.Test.Model
{
    public class RunTests
    {

        [Fact]
        public void RevisionRun_ShouldInitializePropertiesCorrectly()
        {
            // Arrange

            var textList = new List<string> { "Text1", "Text2" };

            // Act
            var revisionRun = new RevisionRun
            {
                Id = "123",
                Type = "Insert",
                Text = textList,
                Author = "AuthorName",
                Date = "2023-10-01"
            };

            // Assert
            Assert.Equal("123", revisionRun.Id);
            Assert.Equal("Insert", revisionRun.Type);
            Assert.Equal(textList, revisionRun.Text);
            Assert.Equal("AuthorName", revisionRun.Author);
            Assert.Equal("2023-10-01", revisionRun.Date);
        }
    }
}
