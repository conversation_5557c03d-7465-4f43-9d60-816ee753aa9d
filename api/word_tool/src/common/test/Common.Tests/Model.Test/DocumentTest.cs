using System.Text.Json;
using Common.DocItemModel;
using Xunit;

namespace Common.Tests.Model
{
    public class DocumentTests
    {
        [Fact]
        public void ParagraphElement_ShouldInitializePropertiesCorrectly()
        {
            // Arrange & Act
            var paragraph = new ParagraphElement
            {
                ElementType = "Paragraph",
                ElementId = "para-001",
                PlainText = "This is a test paragraph.",
                Properties = new ParagraphProperty
                {
                    Style = "Heading1"
                },
                Segments = new List<Segment>
                {
                    new Segment
                    {
                        SegmentId = "seg-001",
                        SegmentText = "This is",
                        Start = 0,
                        End = 7,
                        Properties = new RunProperty
                        {
                            FontFamily = "Arial",
                            FontSize = "12",
                            Color = "red"
                        }
                    }
                }
            };

            // Assert
            Assert.Equal("Paragraph", paragraph.ElementType);
            Assert.Equal("para-001", paragraph.ElementId);
            Assert.Equal("This is a test paragraph.", paragraph.PlainText);
            Assert.Equal("Heading1", paragraph.Properties.Style);
            Assert.Single(paragraph.Segments);
            Assert.Equal("seg-001", paragraph.Segments[0].SegmentId);
            Assert.Equal("Arial", paragraph.Segments[0].Properties.FontFamily);
        }

        [Fact]
        public void TableElement_ShouldInitializePropertiesCorrectly()
        {
            // Arrange & Act
            var table = new TableElement
            {
                ElementType = "Table",
                ElementId = "tbl-001",
                Rows = new List<TableRowElement>
                {
                    new TableRowElement
                    {
                        RowId = 1,
                        Cells = new List<TableCellElement>
                        {
                            new TableCellElement
                            {
                                CellId = 1,
                            }
                        }
                    }
                }
            };

            // Assert
            Assert.Equal("Table", table.ElementType);
            Assert.Equal("tbl-001", table.ElementId);
            Assert.Single(table.Rows);
            Assert.Equal(1, table.Rows[0].RowId);
            Assert.Single(table.Rows[0].Cells);
            Assert.Equal(1, table.Rows[0].Cells[0].CellId);
        }

        [Fact]
        public void TableCellElement_WithParagraphs_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var cell = new TableCellElement
            {
                CellId = 1,
                Elements = new List<ParagraphElement>
                {
                    new ParagraphElement
                    {
                        ElementType = "Paragraph",
                        ElementId = "cell-001_para_0",
                        PlainText = "Cell content",
                        Segments =
                        [
                            new Segment
                            {
                                SegmentId = "cell-001_para_0_0",
                                SegmentText = "Cell content",
                                Start = 0,
                                End = 12
                            }
                        ]
                    }
                }
            };

            // Assert
            Assert.Equal(1, cell.CellId);
            Assert.Single(cell.Elements);
            Assert.Equal("cell-001_para_0", cell.Elements[0].ElementId);
            Assert.Equal("Cell content", cell.Elements[0].PlainText);
        }

        [Fact]
        public void Segment_WithRevisionContent_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var segment = new Segment
            {
                SegmentId = "seg-001",
                SegmentText = "Revised text",
                Start = 0,
                End = 12,
                Properties = new RunProperty
                {
                    Color = "blue",
                    Bold = "true",
                    Italic = "false",
                    Underline = "single",
                    FontSize = "14",
                    FontFamily = "Times New Roman"
                },
                FieldId = "",
                HyperlinkId = "",
                ContentRevisionId = "rev-001",
                PropRevisions = new List<PropertyRevision>
                {
                    new PropertyRevision
                    {
                        RevId = "prop-rev-001",
                        RevType = "rPrChange"
                    }
                },
                CommentIds = new List<string> { "cmt-001" }
            };

            // Assert
            Assert.Equal("seg-001", segment.SegmentId);
            Assert.Equal("Revised text", segment.SegmentText);
            Assert.Equal(0, segment.Start);
            Assert.Equal(12, segment.End);
            Assert.Equal("blue", segment.Properties.Color);
            Assert.Equal("true", segment.Properties.Bold);
            Assert.Equal("single", segment.Properties.Underline);
            Assert.Equal("rev-001", segment.ContentRevisionId);
            Assert.Single(segment.PropRevisions);
            Assert.Single(segment.CommentIds);
        }

        [Fact]
        public void Annotations_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var annotations = new Annotations
            {
                Comments = new Dictionary<string, DocComment>
                {
                    ["cmt-001"] = new DocComment
                    {
                        CommentId = "cmt-001",
                        Author = "John Doe",
                        Date = "2024-06-10",
                        Text = "This needs review",
                        Targets = ["seg-001", "seg-002"]
                    }
                },
                Hyperlinks = new Dictionary<string, DocHyperlink>
                {
                    ["link-001"] = new DocHyperlink
                    {
                        HyperlinkId = "link-001",
                        Uri = "https://example.com",
                        Targets = ["seg-003"]
                    }
                }
            };

            // Assert
            Assert.Single(annotations.Comments);
            Assert.Single(annotations.Hyperlinks);
            Assert.Equal("cmt-001", annotations.Comments["cmt-001"].CommentId);
            Assert.Equal("John Doe", annotations.Comments["cmt-001"].Author);
            Assert.Equal("link-001", annotations.Hyperlinks["link-001"].HyperlinkId);
            Assert.Equal("https://example.com", annotations.Hyperlinks["link-001"].Uri);
        }

        [Fact]
        public void NativeRevision_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var revision = new NativeRevision
            {
                RevId = "rev-001",
                RevType = "InsertedRun",
                Author = "Jane Smith",
                Date = "2024-06-10T14:30:00Z"
            };

            // Assert
            Assert.Equal("rev-001", revision.RevId);
            Assert.Equal("InsertedRun", revision.RevType);
            Assert.Equal("Jane Smith", revision.Author);
            Assert.Equal("2024-06-10T14:30:00Z", revision.Date);
        }

        [Fact]
        public void RunProperty_AllProperties_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var runProperty = new RunProperty
            {
                Color = "#FF0000",
                FontSize = "12pt",
                FontFamily = "Calibri",
                Bold = "true",
                Italic = "false",
                Underline = "double"
            };

            // Assert
            Assert.Equal("#FF0000", runProperty.Color);
            Assert.Equal("12pt", runProperty.FontSize);
            Assert.Equal("Calibri", runProperty.FontFamily);
            Assert.Equal("true", runProperty.Bold);
            Assert.Equal("false", runProperty.Italic);
            Assert.Equal("double", runProperty.Underline);
        }

        [Fact]
        public void DocComment_WithMultipleTargets_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var comment = new DocComment
            {
                CommentId = "cmt-002",
                Author = "Bob Wilson",
                Date = "2024-06-10",
                Text = "Please clarify this section and add more details.",
                Targets = new List<string> { "seg-005", "seg-006", "seg-007" }
            };

            // Assert
            Assert.Equal("cmt-002", comment.CommentId);
            Assert.Equal("Bob Wilson", comment.Author);
            Assert.Equal("Please clarify this section and add more details.", comment.Text);
            Assert.Equal(3, comment.Targets.Count);
            Assert.Contains("seg-005", comment.Targets);
            Assert.Contains("seg-006", comment.Targets);
            Assert.Contains("seg-007", comment.Targets);
        }

        [Fact]
        public void DocHyperlink_WithMultipleSegments_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var hyperlink = new DocHyperlink
            {
                HyperlinkId = "link-002",
                Uri = "mailto:<EMAIL>",
                Targets = ["seg-010", "seg-011"]
            };

            // Assert
            Assert.Equal("link-002", hyperlink.HyperlinkId);
            Assert.Equal("mailto:<EMAIL>", hyperlink.Uri);
            Assert.Equal(2, hyperlink.Targets.Count);
            Assert.Contains("seg-010", hyperlink.Targets);
            Assert.Contains("seg-011", hyperlink.Targets);
        }

        [Fact]
        public void ParagraphElement_WithComplexRevisions_ShouldSerializeCorrectly()
        {
            // Arrange
            var paragraph = new ParagraphElement
            {
                ElementType = "Paragraph",
                ElementId = "para-complex-rev",
                PlainText = "This text has been revised multiple times.",
                Properties = new ParagraphProperty { Style = "Normal" },
                Segments = new List<Segment>
                {
                    new Segment
                    {
                        SegmentId = "seg-rev-001",
                        SegmentText = "This text ",
                        Start = 0,
                        End = 10,
                        Properties = new RunProperty { Color = "black" }
                    },
                    new Segment
                    {
                        SegmentId = "seg-rev-002",
                        SegmentText = "has been revised",
                        Start = 10,
                        End = 26,
                        Properties = new RunProperty { Color = "red", Bold = "true" },
                        ContentRevisionId = "ins-001",
                        PropRevisions = new List<PropertyRevision>
                        {
                            new PropertyRevision { RevId = "prop-001", RevType = "rPrChange" }
                        }
                    },
                    new Segment
                    {
                        SegmentId = "seg-rev-003",
                        SegmentText = " multiple times",
                        Start = 26,
                        End = 41,
                        Properties = new RunProperty { Color = "blue", Italic = "true" },
                        ContentRevisionId = "ins-002",
                        CommentIds = new List<string> { "cmt-rev-001" }
                    }
                }
            };

            // Act
            var json = JsonSerializer.Serialize(paragraph, new JsonSerializerOptions { WriteIndented = true });
            var deserializedParagraph = JsonSerializer.Deserialize<ParagraphElement>(json);

            // Assert
            Assert.NotNull(deserializedParagraph);
            Assert.Equal("para-complex-rev", deserializedParagraph.ElementId);
            Assert.Equal(3, deserializedParagraph.Segments.Count);
            Assert.Equal("ins-001", deserializedParagraph.Segments[1].ContentRevisionId);
            Assert.Single(deserializedParagraph.Segments[1].PropRevisions);
            Assert.Single(deserializedParagraph.Segments[2].CommentIds);
        }

        [Fact]
        public void TableElement_WithNestedContent_ShouldSerializeCorrectly()
        {
            // Arrange
            var table = new TableElement
            {
                ElementType = "Table",
                ElementId = "tbl-nested",
                Rows = new List<TableRowElement>
                {
                    new TableRowElement
                    {
                        RowId = 0,
                        Cells = new List<TableCellElement>
                        {
                            new TableCellElement
                            {
                                CellId = 0,
                                Elements = new List<ParagraphElement>
                                {
                                    new ParagraphElement
                                    {
                                        ElementType = "Paragraph",
                                        ElementId = "tbl-nested_cell_0_0_para_0",
                                        PlainText = "Header cell with hyperlink",
                                        Segments = new List<Segment>
                                        {
                                            new Segment
                                            {
                                                SegmentId = "tbl-nested_cell_0_0_para_0_0",
                                                SegmentText = "Header cell with hyperlink",
                                                Start = 0,
                                                End = 26,
                                                HyperlinkId = "tbl-link-001",
                                                Properties = new RunProperty { Color = "blue", Underline = "single" }
                                            }
                                        }
                                    }
                                }
                            },
                            new TableCellElement
                            {
                                CellId = 1,
                                Elements = new List<ParagraphElement>
                                {
                                    new ParagraphElement
                                    {
                                        ElementType = "Paragraph",
                                        ElementId = "tbl-nested_cell_0_1_para_0",
                                        PlainText = "Cell with comment",
                                        Segments = new List<Segment>
                                        {
                                            new Segment
                                            {
                                                SegmentId = "tbl-nested_cell_0_1_para_0_0",
                                                SegmentText = "Cell with comment",
                                                Start = 0,
                                                End = 17,
                                                CommentIds = new List<string> { "tbl-cmt-001" }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            // Act
            var json = JsonSerializer.Serialize(table, new JsonSerializerOptions { WriteIndented = true });
            var deserializedTable = JsonSerializer.Deserialize<TableElement>(json);

            // Assert
            Assert.NotNull(deserializedTable);
            Assert.Equal("tbl-nested", deserializedTable.ElementId);
            Assert.Single(deserializedTable.Rows);
            Assert.Equal(2, deserializedTable.Rows[0].Cells.Count);

            var firstCell = deserializedTable.Rows[0].Cells[0];
            Assert.Single(firstCell.Elements);
            Assert.Equal("tbl-link-001", firstCell.Elements[0].Segments[0].HyperlinkId);

            var secondCell = deserializedTable.Rows[0].Cells[1];
            Assert.Single(secondCell.Elements[0].Segments[0].CommentIds);
            Assert.Equal("tbl-cmt-001", secondCell.Elements[0].Segments[0].CommentIds[0]);
        }

        [Fact]
        public void DocItem_PolymorphicSerialization_WithAllTypes_ShouldWorkCorrectly()
        {
            // Arrange
            var items = new List<DocItem>
            {
                new ParagraphElement
                {
                    ElementType = "Paragraph",
                    ElementId = "para-poly",
                    PlainText = "Test paragraph"
                },
                new TableElement
                {
                    ElementType = "Table",
                    ElementId = "tbl-poly"
                },
                new Annotations
                {
                    Comments = new Dictionary<string, DocComment>
                    {
                        ["poly-cmt"] = new DocComment { CommentId = "poly-cmt", Text = "Test comment" }
                    }
                },
                new NativeRevision
                {
                    RevId = "poly-rev",
                    RevType = "DeletedRun",
                    Author = "Test Author"
                },
                new Segment
                {
                    SegmentId = "poly-seg",
                    SegmentText = "Test segment"
                }
            };

            // Act
            var json = JsonSerializer.Serialize(items, new JsonSerializerOptions { WriteIndented = true });
            var deserializedItems = JsonSerializer.Deserialize<List<DocItem>>(json);

            // Assert
            Assert.NotNull(deserializedItems);
            Assert.Equal(5, deserializedItems.Count);

            Assert.IsType<ParagraphElement>(deserializedItems[0]);
            Assert.IsType<TableElement>(deserializedItems[1]);
            Assert.IsType<Annotations>(deserializedItems[2]);
            Assert.IsType<NativeRevision>(deserializedItems[3]);
            Assert.IsType<Segment>(deserializedItems[4]);

            var paragraph = (ParagraphElement)deserializedItems[0];
            Assert.Equal("para-poly", paragraph.ElementId);

            var annotations = (Annotations)deserializedItems[2];
            Assert.Single(annotations.Comments);
            Assert.Equal("Test comment", annotations.Comments["poly-cmt"].Text);
        }

        [Fact]
        public void Segment_WithEmptyLists_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var segment = new Segment
            {
                SegmentId = "seg-empty",
                SegmentText = "Simple text",
                Start = 0,
                End = 11,
                PropRevisions = new List<PropertyRevision>(),
                CommentIds = new List<string>()
            };

            // Assert
            Assert.Equal("seg-empty", segment.SegmentId);
            Assert.Equal("Simple text", segment.SegmentText);
            Assert.Empty(segment.PropRevisions);
            Assert.Empty(segment.CommentIds);
            Assert.Equal("", segment.FieldId);
            Assert.Equal("", segment.HyperlinkId);
            Assert.Equal("", segment.ContentRevisionId);
        }

        [Fact]
        public void ParagraphProperty_WithCustomStyle_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var paragraphProperty = new ParagraphProperty
            {
                Style = "CustomHeading"
            };

            // Assert
            Assert.Equal("CustomHeading", paragraphProperty.Style);
        }

        [Fact]
        public void PropertyRevision_WithDifferentTypes_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var propertyRevisions = new List<PropertyRevision>
            {
                new PropertyRevision { RevId = "prop-001", RevType = "rPrChange" },
                new PropertyRevision { RevId = "prop-002", RevType = "pPrChange" },
                new PropertyRevision { RevId = "prop-003", RevType = "tblPrChange" }
            };

            // Assert
            Assert.Equal(3, propertyRevisions.Count);
            Assert.Equal("rPrChange", propertyRevisions[0].RevType);
            Assert.Equal("pPrChange", propertyRevisions[1].RevType);
            Assert.Equal("tblPrChange", propertyRevisions[2].RevType);
        }
    }
}