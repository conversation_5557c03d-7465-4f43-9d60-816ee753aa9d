using Common.Helper;


namespace Common.Tests.Helper
{
    public class EnvHelperTest
    {
        [Fact]
        public void Test_GetEnvVariable()
        {
            var retNull = EnvHelper.GetEnvVariable("TEST_ENV_VAR");
            Assert.Null(retNull);

            string targetVal = "TEST_VALUE";
            Environment.SetEnvironmentVariable("TEST_ENV_VAR", "TEST_VALUE");
            var ret = EnvHelper.GetEnvVariable("TEST_ENV_VAR");

            Assert.NotNull(ret);
            Assert.Equal(targetVal, ret);
        }

        [Fact]
        public void Test_GetEnvVariable_With_Default()
        {
            var defaultVal = "TestDefault";
            var ret = EnvHelper.GetEnvVariable("TEST_ENV_VAR", defaultVal);

            Assert.Equal(defaultVal, ret);
        }
    }
}