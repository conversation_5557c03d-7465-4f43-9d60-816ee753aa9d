using Common.Helper;

namespace Common.Tests.Helper
{
    public class TimestampHelperTests
    {
        [Fact]
        public void Timestamp_ShouldReturnCurrentUnixTimestamp()
        {
            // Arrange
            var expectedTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            // Act
            var actualTimestamp = TimestampHelper.Timestamp();

            // Assert
            Assert.InRange(actualTimestamp, (int)expectedTimestamp - 1, (int)expectedTimestamp + 1);
        }
    }
}
