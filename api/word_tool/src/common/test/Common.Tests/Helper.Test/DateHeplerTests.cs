using System;
using Common.Helper;
using Xunit;

namespace Common.Test.Helper
{
    public class DateHeplerTests
    {
        [Fact]
        public void SafeParseDate_ValidDateString_ReturnsParsedDate()
        {
            // Arrange
            string validDate = "2023-10-01";

            // Act
            DateTime result = DateHepler.SafeParseDate(validDate);

            // Assert
            Assert.Equal(new DateTime(2023, 10, 1), result);
        }

        [Fact]
        public void SafeParseDate_InvalidDateString_ReturnsCurrentDate()
        {
            // Arrange
            string invalidDate = "invalid-date";

            // Act
            DateTime result = DateHepler.SafeParseDate(invalidDate);

            // Assert
            Assert.True((DateTime.Now - result).TotalSeconds < 1, "Returned date is not close to current date.");
        }

        [Fact]
        public void SafeParseDate_EmptyString_ReturnsCurrentDate()
        {
            // Arrange
            string emptyDate = "";

            // Act
            DateTime result = DateHepler.SafeParseDate(emptyDate);

            // Assert
            Assert.True((DateTime.Now - result).TotalSeconds < 1, "Returned date is not close to current date.");
        }
    }
}
