﻿using System.Text;
using Common.Helper;

namespace Common.Tests.Helper
{
    public class StreamHelperTest
    {
        [Fact]
        public async Task ReadStreamToStringAsync_ShouldReturnCorrectString()
        {
            // Arrange
            string expected = "Hello, <PERSON>!";
            using var stream = new MemoryStream(Encoding.UTF8.GetBytes(expected));

            // Act
            string result = await StreamHelper.ReadStreamToStringAsync(stream);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task ReadStreamToStringAsync_ShouldReturnEmptyString_WhenStreamIsEmpty()
        {
            // Arrange
            using var stream = new MemoryStream();

            // Act
            string result = await StreamHelper.ReadStreamToStringAsync(stream);

            // Assert
            Assert.Equal(string.Empty, result);
        }
    }
}
