using Amazon;

namespace Common.Tests
{
    public class SettingsTests
    {
        [Fact]
        public void Constructor_ShouldSetDefaultRegion_WhenEnvVariableIsNotSet()
        {
            // Arrange
            Environment.SetEnvironmentVariable("AWS_REGION_NAME", null);

            // Act
            var settings = Settings.Instance;

            // Assert
            Assert.Equal(RegionEndpoint.USEast2, settings.Region);
        }

        [Fact]
        public void Constructor_ShouldSetRegionFromEnvVariable_WhenEnvVariableIsSet()
        {
            // Arrange
            Environment.SetEnvironmentVariable("AWS_REGION_NAME", "us-west-1");

            // Act
            var settings = Settings.Instance;

            // Assert
            Assert.Equal(RegionEndpoint.GetBySystemName("us-west-1"), settings.Region);
        }
    }
}
