using Common.Model;
using Amazon.Lambda.Core;
using Moq;


namespace Common.Tests.Executor.Test {
    public class ExecutorTests
    {
        private class TestExecutor : Abstract.Executor
        {
            public override Task<ResultResponse> Run()
            {
                return Task.FromResult<ResultResponse>(new ResultResponse<string>("Success", 200, "OK"));
            }
        }

        private class TestExcutorError : Abstract.Executor
        {
            public override Task<ResultResponse> Run()
            {
                throw new Exception("Error");
            }

        }

        [Fact]
        public async Task Execute_ShouldReturnSuccessResponse_WhenRunSucceeds()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger.LogInformation(It.IsAny<string>()));
            var executor = new TestExecutor
            {
                context = mockContext.Object
            };

            // Act
            var result = await executor.Execute();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(200, result.Code);
            Assert.Equal("OK", result.Message);
            Assert.Equal("Success", result.Data);
        }


        [Fact]
        public async Task Execute_ShouldReturnSuccessResponse_WhenRunError()
        {
            // Arrange
            var mockContext = new Mock<ILambdaContext>();
            mockContext.Setup(c => c.Logger.LogInformation(It.IsAny<string>()));
            var executor = new TestExcutorError
            {
                context = mockContext.Object
            };

            // Act
            var result = await executor.Execute();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(500, result.Code);
        }
    }
}