using Amazon.S3;
using Amazon.S3.Model;
using Amazon.Lambda.Core;
using Common.Service;
using Moq;


namespace Common.Tests.Service.Test
{
    public class S3ServiceTests
    {
        private readonly Mock<IAmazonS3> _mockS3Client;
        private readonly Mock<ILambdaContext> _mockContext;
        private readonly S3Service _s3Service;

        public S3ServiceTests()
        {
            _mockS3Client = new Mock<IAmazonS3>();
            _mockContext = new Mock<ILambdaContext>();
            _mockContext.Setup(c => c.<PERSON>).Returns(Mock.Of<ILambdaLogger>());
            _s3Service = new S3Service(_mockS3Client.Object, _mockContext.Object);
        }

        [Fact]
        public async Task GetObjectAsync_ReturnsResponse_WhenSuccessful()
        {
            // Arrange
            var bucket = "test-bucket";
            var key = "test-key";
            var mockResponse = new GetObjectResponse
            {
                HttpStatusCode = System.Net.HttpStatusCode.OK
            };
            _mockS3Client.Setup(s => s.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
                         .ReturnsAsync(mockResponse);

            // Act
            var result = await _s3Service.GetObjectAsync(bucket, key);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(mockResponse, result);
        }

        [Fact]
        public async Task GetObjectAsync_ReturnsNull_WhenS3ErrorOccurs()
        {
            // Arrange
            var bucket = "test-bucket";
            var key = "test-key";
            _mockS3Client.Setup(s => s.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
                         .ThrowsAsync(new AmazonS3Exception("Error"));

            // Act
            var result = await _s3Service.GetObjectAsync(bucket, key);

            // Assert
            Assert.Null(result);
        }


        [Fact]
        public async Task GetObjectAsync_ReturnsNull_WhenErrorOccurs()
        {
            // Arrange
            var bucket = "test-bucket";
            var key = "test-key";
            _mockS3Client.Setup(s => s.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
                         .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _s3Service.GetObjectAsync(bucket, key);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task PutObjectAsync_ReturnsTrue_WhenSuccessful()
        {
            // Arrange
            var bucket = "test-bucket";
            var key = "test-key";
            var filePath = "test-file-path";
            var mockResponse = new PutObjectResponse
            {
                HttpStatusCode = System.Net.HttpStatusCode.OK
            };
            _mockS3Client.Setup(s => s.PutObjectAsync(It.IsAny<PutObjectRequest>(), default))
                         .ReturnsAsync(mockResponse);

            // Act
            var result = await _s3Service.PutObjectAsync(bucket, key, filePath);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task PutObjectAsync_ReturnsFalse_WhenErrorOccurs()
        {
            // Arrange
            var bucket = "test-bucket";
            var key = "test-key";
            var filePath = "test-file-path";
            _mockS3Client.Setup(s => s.PutObjectAsync(It.IsAny<PutObjectRequest>(), default))
                         .ThrowsAsync(new Exception("Error"));

            // Act
            var result = await _s3Service.PutObjectAsync(bucket, key, filePath);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task PutObjectAsync_ReturnsFalse_WhenS3ErrorOccurs()
        {
            // Arrange
            var bucket = "test-bucket";
            var key = "test-key";
            var filePath = "test-file-path";
            _mockS3Client.Setup(s => s.PutObjectAsync(It.IsAny<PutObjectRequest>(), default))
                         .ThrowsAsync(new AmazonS3Exception("Error"));

            // Act
            var result = await _s3Service.PutObjectAsync(bucket, key, filePath);

            // Assert
            Assert.False(result);
        }
    }
}
