using Xunit;

namespace Common.Tests.Attributes
{
    /// <summary>
    /// Conditional test attribute that allows skipping tests based on conditions
    /// </summary>
    public class ConditionalFactAttribute : FactAttribute
    {
        public ConditionalFactAttribute(string skipReason)
        {
            Skip = skipReason;
        }
    }

    /// <summary>
    /// 特性用于标记不支持的功能测试
    /// </summary>
    public class UnsupportedFeatureFactAttribute : FactAttribute
    {
        public UnsupportedFeatureFactAttribute(string featureDescription)
        {
            Skip = $"Unsupported feature: {featureDescription}";
        }
    }

    /// <summary>
    /// 特性用于标记需要特定环境的测试
    /// </summary>
    public class EnvironmentSpecificFactAttribute : FactAttribute
    {
        public EnvironmentSpecificFactAttribute(string environment, string reason)
        {
            Skip = $"Requires {environment} environment: {reason}";
        }
    }

    /// <summary>
    /// 特性用于标记性能测试
    /// </summary>
    public class PerformanceTestFactAttribute : FactAttribute
    {
        public PerformanceTestFactAttribute(string reason = "Performance test - run manually")
        {
            Skip = reason;
        }
    }
}
