using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Xunit.Sdk;

namespace Common.Tests.Attributes
{
    public class RealDocumentDataAttribute : DataAttribute
    {
        private readonly string _filePath;

        public RealDocumentDataAttribute(string filePath)
        {
            _filePath = filePath;
        }

        public override IEnumerable<object[]> GetData(MethodInfo testMethod)
        {
            ArgumentNullException.ThrowIfNull(testMethod);

            var assemblyLocation = Path.GetDirectoryName(testMethod.Module.Assembly.Location);

            ArgumentException.ThrowIfNullOrEmpty(assemblyLocation, "Assembly location cannot be null or empty.");
            var path = Path.Combine(assemblyLocation, _filePath);

            if (!File.Exists(path))
            {
                throw new ArgumentException($"Could not find file at path: {path}");
            }

            return new[] { new object[] { File.ReadAllBytes(path) } };
        }
    }
}