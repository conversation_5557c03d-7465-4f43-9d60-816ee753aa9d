﻿using System.Text.Json;
using Amazon;
using Common.Model;
using Common.Helper;


namespace Common
{
    public sealed class Settings
    {
        public RegionEndpoint Region { get; set; }
        public AppSettings AppSettings { get; set; } = new AppSettings();

        private static readonly Settings _instance = new Settings();

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        private Settings()
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
        {
            GetRegion();
            GetAppSetting();
        }

        public static Settings Instance => _instance;

        private void GetRegion()
        {
            var region = EnvHelper.GetEnvVariable("AWS_REGION_NAME");
            if (string.IsNullOrEmpty(region))
            {
                Region = RegionEndpoint.USEast2;
            }
            else
            {
                Region = RegionEndpoint.GetBySystemName(region);
            }
        }

        private void GetAppSetting()
        {
            var configPath = Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
            if (File.Exists(configPath))
            {
                var configJson = File.ReadAllText(configPath);
                var appSettings = JsonSerializer.Deserialize<AppSettings>(configJson);
                if (appSettings != null)
                {
                    AppSettings = appSettings;
                }
            }
        }
    }
}
