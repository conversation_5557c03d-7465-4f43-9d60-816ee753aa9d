﻿namespace Common.Helper
{
    public static class StreamHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        public static async Task<string> ReadStreamToStringAsync(Stream stream)
        {
            using (StreamReader reader = new StreamReader(stream))
            {
                var data = await reader.ReadToEndAsync();
                return data;
            }
        }
    }
}
