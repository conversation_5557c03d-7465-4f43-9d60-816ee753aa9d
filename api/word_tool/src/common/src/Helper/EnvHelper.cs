﻿namespace Common.Helper
{
    public static class EnvHelper
    {
        /// <summary>
        /// Get environment variable.
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string? GetEnvVariable(string key)
        {
            return Environment.GetEnvironmentVariable(key) ?? null;
        }
        /// <summary>
        /// Get environment variable.
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public static string? GetEnvVariable(string key, string defaultValue)
        {
            return Environment.GetEnvironmentVariable(key) ?? defaultValue;
        }
    }
}
