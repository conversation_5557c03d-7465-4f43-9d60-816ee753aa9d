﻿using Amazon;
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.Lambda.Core;
using Common.Abstract;

namespace Common.Service
{

    public class S3Service: IS3Service
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILambdaContext _context;

        public S3Service(RegionEndpoint region, ILambdaContext context)
        {
            _s3Client = new AmazonS3Client(region);
            _context = context;
        }

        public S3Service(IAmazonS3 s3Client, ILambdaContext context)
        {
            _s3Client = s3Client;
            _context = context;
        }

        /// <summary>
        /// Get object
        /// </summary>
        /// <param name="bucket"></param>
        /// <param name="key"></param>
        /// <param name="region"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<GetObjectResponse?> GetObjectAsync(string bucket, string key)
        {
            _context.Logger.LogInformation($"Get s3 object, bucket: {bucket}, key: {key}");
            try
            {
                var request = new GetObjectRequest
                {
                    BucketName = bucket,
                    Key = key
                };

                var response = await _s3Client.GetObjectAsync(request);
                if (IsSuccessStatusCode((int)response.HttpStatusCode))
                    return response;

                _context.Logger.LogError($"Get object s3 return error code: {response.HttpStatusCode.ToString()}");
            }
            catch (AmazonS3Exception ex)
            {
                _context.Logger.LogError($"Get object s3 error occurs, ex: {ex.ToString()}");
            }
            catch (Exception ex)
            {
                _context.Logger.LogError($"Get object unexpected error, ex: {ex.ToString()}");
            }
            return null;
        }

        private static bool IsSuccessStatusCode(int statusCode)
        {
            return statusCode >= 200 && statusCode <= 299;
        }

        /// <summary>
        /// Put object
        /// </summary>
        /// <param name="bucket"></param>
        /// <param name="key"></param>
        /// <param name="filePath"></param>
        /// <param name="region"></param>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<bool> PutObjectAsync(string bucket, string key, string filePath)
        {
            _context.Logger.LogInformation($"Put s3 object, bucket: {bucket}, key: {key}");
            try
            {
                var request = new PutObjectRequest
                {
                    BucketName = bucket,
                    Key = key,
                    FilePath = filePath,
                };

                var response = await _s3Client.PutObjectAsync(request);
                if (response != null && IsSuccessStatusCode((int)response.HttpStatusCode))
                {
                    return true;
                }
                _context.Logger.LogError($"Put object s3 return error code: {response?.HttpStatusCode.ToString()}");
            }
            catch (AmazonS3Exception ex)
            {
                _context.Logger.LogError($"Put object s3 error occurs, ex: {ex.Message}");
            }
            catch (Exception ex)
            {
                _context.Logger.LogError($"Put object unexpected error, ex: {ex.ToString()}");
            }
            return false;
        }
    }
}
