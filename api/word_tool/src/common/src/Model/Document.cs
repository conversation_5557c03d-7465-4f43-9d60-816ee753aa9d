using System.Globalization;
using System.Text.Json.Serialization;


namespace Common.DocItemModel
{
    /// <summary>
    /// 列表信息类，用于表示文档段落的列表属性
    /// </summary>
    public class ListInfo
    {
        /// <summary>
        /// 获取或设置编号ID
        /// </summary>
        [JsonPropertyName("NumId")]
        public int NumId { get; set; } = 0;

        /// <summary>
        /// 获取或设置列表层级
        /// </summary>
        [JsonPropertyName("Level")]
        public int Level { get; set; } = 0;

        /// <summary>
        /// 获取或设置列表格式类型 (numFmt)
        /// </summary>
        [JsonPropertyName("ListType")]
        public string ListType { get; set; } = "";

        /// <summary>
        /// 获取或设置段落缩进
        /// </summary>
        [JsonPropertyName("Indent")]
        public int? IndentLeft { get; set; }

        [JsonPropertyName("LevelText")]
        public string? LevelText { get; set; }
    }

    /// <summary>
    /// base class for normal text and revision, support polymorphic serialization
    /// </summary>
    [JsonPolymorphic(TypeDiscriminatorPropertyName = "Key")]
    [JsonDerivedType(typeof(ParagraphElement), "ParagraphElement")]
    [JsonDerivedType(typeof(TableElement), "TableElement")]
    [JsonDerivedType(typeof(Annotations), "Annotations")]
    [JsonDerivedType(typeof(NativeRevision), "NativeRevision")]
    public abstract class DocItem
    {

    }

    /// <summary>
    /// Normal DocElements
    /// </summary>
    public class ParagraphElement : DocItem
    {
        public string ElementType { get; set; } = "";
        public string ElementId { get; set; } = "";
        public string PlainText { get; set; } = "";
        public ParagraphProperty Properties { get; set; } = new ParagraphProperty();
        public List<Segment> Segments { get; set; } = [];

    }

    /// <summary>
    /// Table DocElements
    /// </summary>
    public class TableElement : DocItem
    {
        public string ElementType { get; set; } = "";
        public string ElementId { get; set; } = "";
        public List<TableRowElement> Rows { get; set; } = [];
    }

    /// <summary>
    /// Annotations
    /// </summary>
    public class Annotations : DocItem
    {
        public Dictionary<string, DocComment> Comments { get; set; } = [];
        public Dictionary<string, DocHyperlink> Hyperlinks { get; set; } = [];
    }

    /// <summary>
    /// native revisions
    /// </summary>
    public class NativeRevision : DocItem
    {
        public string RevId { get; set; } = "";
        public string RevType { get; set; } = "";
        public string Author { get; set; } = "";
        public string Date { get; set; } = "";
    }

    /// <summary>
    /// Segment
    /// </summary>
    public class Segment : DocItem
    {
        public string SegmentId { get; set; } = "";
        public string SegmentText { get; set; } = "";
        public int Start { get; set; } = 0;
        public int End { get; set; } = 0;
        public RunProperty Properties { get; set; } = new RunProperty();
        public string FieldId { get; set; } = "";
        public string HyperlinkId { get; set; } = "";
        public string ContentRevisionId { get; set; } = "";
        public List<PropertyRevision> PropRevisions { get; set; } = [];
        public List<string> CommentIds { get; set; } = [];
    }


    /// <summary>
    /// doc item for run properties
    /// </summary>
    public class RunProperty
    {
        public string Color { get; set; } = "";
        public string FontSize { get; set; } = "";
        public string FontFamily { get; set; } = "";
        public string Bold { get; set; } = "";
        public string Italic { get; set; } = "";
        public string Underline { get; set; } = "";
    }

    /// <summary>
    /// doc item for paragraph properties
    /// </summary>
    public class ParagraphProperty
    {
        public string Style { get; set; } = "";

        public int IndentLeft { get; set; } = 0;

        /// <summary>
        /// 获取或设置段落列表信息
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public ListInfo? ListInfo { get; set; } = null;
    }

    /// <summary>
    /// property revision
    /// </summary>
    public class PropertyRevision
    {
        public string RevId { get; set; } = "";
        public string RevType { get; set; } = "";
    }

    /// <summary>
    /// comments
    /// </summary>
    public class DocComment
    {
        [JsonPropertyName("CommentId")]
        public string CommentId { get; set; } = "";
        [JsonPropertyName("Author")]
        public string Author { get; set; } = "";
        [JsonPropertyName("Date")]
        public string Date { get; set; } = "";
        [JsonPropertyName("Text")]
        public string Text { get; set; } = "";
        [JsonPropertyName("Targets")]
        public List<string> Targets { get; set; } = [];
    }
    /// <summary>
    /// Hyperlink
    /// </summary>
    public class DocHyperlink
    {
        [JsonPropertyName("HyperlinkId")]
        public string HyperlinkId { get; set; } = "";
        [JsonPropertyName("Uri")]
        public string Uri { get; set; } = "";
        [JsonPropertyName("Targets")]
        public List<string> Targets { get; set; } = [];
    }

    /// <summary>
    /// Table Row
    /// </summary>
    public class TableRowElement
    {
        public int RowId { get; set; } = 0;
        public List<TableCellElement> Cells { get; set; } = [];
    }

    /// <summary>
    /// Table Cell
    /// </summary>
    public class TableCellElement : DocItem
    {
        public int CellId { get; set; } = 0;
        public List<ParagraphElement> Elements { get; set; } = [];
    }
}
