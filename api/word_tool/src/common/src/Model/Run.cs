﻿using System.Text.Json.Serialization;


namespace Common.Model
{
    /// <summary>
    /// base class for normal text and revision, support polymorphic serialization
    /// </summary>
    [JsonPolymorphic(TypeDiscriminatorPropertyName = "Key")]
    [JsonDerivedType(typeof(NormalRun), "Run")]
    [JsonDerivedType(typeof(RevisionRun), "Revision")]
    public abstract class Run
    {
        // if you need to record more information or polymorphic identification, 
        // you can add public properties here
    }

    /// <summary>
    /// Normal Run
    /// </summary>
    public class NormalRun : Run
    {
        public string Text { get; set; } = "";
    }

    /// <summary>
    /// Revision run 
    /// </summary>
    public class RevisionRun : Run
    {
        /// <summary>Revision ID (corresponding to <w:ins id=...> or <w:del id=...> in a .docx file)</summary>
        public string Id { get; set; } = "";

        /// <summary> \"Insert\" or \"Delete\" ... </summary>
        public string Type { get; set; } = "";

        /// <summary> Revised text collection </summary>
        [JsonPropertyName("Text")]
        public List<string> Text { get; set; } = [];

        /// <summary> Revision author </summary>
        public string Author { get; set; } = "";

        /// <summary> Revision date </summary>
        public string Date { get; set; } = "";

        /// <summary> Revision links </summary>
        [JsonPropertyName("Links")]
        public List<LinkInfo> Links { get; set; } = [];
    }

    public class LinkInfo
    {
        public string Uri { get; set; } = "";
        public string DisplayText { get; set; } = "";
    }

}
