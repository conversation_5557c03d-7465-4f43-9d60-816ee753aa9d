﻿namespace Common.Model
{
    /// <summary>
    /// ResultResponse is a base class for all result responses.
    /// </summary>
    public abstract class ResultResponse
    {
        public abstract object? Data { get; }
        public abstract int Code { get; set; }
        public abstract string Message { get; set; }
        public abstract string Version { get; set; }
    }

    /// <summary>
    /// ResultResponse is a generic class for all result responses.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class ResultResponse<T> : ResultResponse
    {
        /// <summary>
        /// Result code, default is 200.
        /// </summary>
        public override int Code { get; set; } = 200;

        /// <summary>
        /// Result message, default is "success".
        /// </summary>
        public override string Message { get; set; } = "success";

        public override string Version { get; set; } = Settings.Instance.AppSettings.Version;

        public T? TypedValue { get; }
        public override object? Data => TypedValue;

        public ResultResponse(T value)
        {
            TypedValue = value;
        }

        public ResultResponse(T value, int code, string message)
        {
            TypedValue = value;
            Code = code;
            Message = message;
        }
    }

    /// <summary>
    /// ResponseFactory is a factory class for creating result responses.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public static class ResponseFactory<T>
    {
        /// <summary>
        /// Creates a success response with a custom message.
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static ResultResponse<T> success(T data)
        {
            return new ResultResponse<T>(data);
        }

        /// <summary>
        /// Creates a erro response with a custom message.
        /// </summary>
        /// <param name="data"></param>
        /// <param name="code"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public static ResultResponse<T> error(T data, int code = 400, string message = "failed")
        {
            return new ResultResponse<T>(data, code, message);
        }
    }
}
