﻿using Common.Model;
using Amazon.Lambda.Core;
using System.Diagnostics;

namespace Common.Abstract
{
    public interface IExecutor
    {
        public Task<ResultResponse> Run();
    }

    public abstract class Executor : IExecutor
    {
        #pragma warning disable CS8618
        public ILambdaContext context;

        protected readonly Settings settings = Settings.Instance;

        protected IS3Service s3Service;

        public abstract Task<ResultResponse> Run();

        protected bool IsParameterValid(S3Request request)
        {
            if (string.IsNullOrEmpty(request.Bucket) || string.IsNullOrEmpty(request.Key))
            {
                return false;
            }
            return true;
        }

        public async Task<ResultResponse> Execute()
        {
            Stopwatch stopwatch = new Stopwatch();
            context.Logger.LogInformation($"Request start...");

            stopwatch.Start();
            try
            {
                var response = await Run();
                return response;
            }
            catch (Exception ex) {
                context.Logger.LogError($"Request error: {ex.ToString()}");
                stopwatch.Stop();
            }
            finally
            {
                var elapsedTime = stopwatch.ElapsedMilliseconds;
                context.Logger.LogInformation($"Request finished: {elapsedTime}ms.");
            }
            return ResponseFactory<string>.error(
                data: "",
                code: 500,
                message: "Server internal error."
            );
        }
    }
}
